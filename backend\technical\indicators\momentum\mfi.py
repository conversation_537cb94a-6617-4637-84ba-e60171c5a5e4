from typing import Dict, Any, List
import numpy as np
import pandas as pd

from ..base_indicator import BaseIndicator
from .......core.models.market_data import MarketData

class MFIIndicator(BaseIndicator):
    """Money Flow Index indicator."""
    
    def __init__(self, period: int = 14):
        """
        Initialize MFI indicator.
        
        Args:
            period: The period for calculating MFI
        """
        super().__init__('MFI', {'period': period})
    
    def calculate(self, data: List[MarketData]) -> Dict[str, np.ndarray]:
        """Calculate MFI values."""
        df = self._prepare_data()
        if df.empty:
            return {}
        
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        volume = df['volume'].values
        period = self.params['period']
        
        # Calculate typical price
        typical_price = (high + low + close) / 3
        
        # Calculate raw money flow
        raw_money_flow = typical_price * volume
        
        # Get price changes
        price_change = pd.Series(typical_price).diff()
        
        # Separate positive and negative money flows
        positive_flow = np.where(price_change > 0, raw_money_flow, 0)
        negative_flow = np.where(price_change < 0, raw_money_flow, 0)
        
        # Calculate money flow ratio
        positive_mf = pd.Series(positive_flow).rolling(window=period).sum()
        negative_mf = pd.Series(negative_flow).rolling(window=period).sum()
        
        money_flow_ratio = positive_mf / negative_mf
        
        # Calculate MFI
        mfi = 100 - (100 / (1 + money_flow_ratio))
        
        # Calculate overbought/oversold conditions
        overbought = np.zeros_like(mfi)
        overbought[mfi >= 80] = 1
        
        oversold = np.zeros_like(mfi)
        oversold[mfi <= 20] = 1
        
        # Calculate extreme conditions
        extreme_overbought = np.zeros_like(mfi)
        extreme_overbought[mfi >= 90] = 1
        
        extreme_oversold = np.zeros_like(mfi)
        extreme_oversold[mfi <= 10] = 1
        
        # Calculate divergences
        price_high = pd.Series(typical_price).rolling(window=5).max()
        price_low = pd.Series(typical_price).rolling(window=5).min()
        mfi_high = pd.Series(mfi).rolling(window=5).max()
        mfi_low = pd.Series(mfi).rolling(window=5).min()
        
        bullish_div = (price_low < price_low.shift(1)) & (mfi_low > mfi_low.shift(1))
        bearish_div = (price_high > price_high.shift(1)) & (mfi_high < mfi_high.shift(1))
        
        divergence = np.where(bullish_div, 1, np.where(bearish_div, -1, 0))
        
        # Calculate momentum
        momentum = mfi - pd.Series(mfi).shift(period)
        
        # Calculate trend
        trend = np.zeros_like(mfi)
        trend[mfi > 50] = 1
        trend[mfi < 50] = -1
        
        # Calculate crossovers
        mid_cross = np.where(
            (mfi > 50) & (pd.Series(mfi).shift(1) <= 50), 1,
            np.where((mfi < 50) & (pd.Series(mfi).shift(1) >= 50), -1, 0)
        )
        
        # Calculate strength zones
        strength = np.zeros_like(mfi)
        strength[(mfi >= 60) & (mfi < 80)] = 1    # Strong
        strength[(mfi >= 80) & (mfi < 90)] = 2    # Very Strong
        strength[mfi >= 90] = 3                   # Extremely Strong
        strength[(mfi <= 40) & (mfi > 20)] = -1   # Weak
        strength[(mfi <= 20) & (mfi > 10)] = -2   # Very Weak
        strength[mfi <= 10] = -3                  # Extremely Weak
        
        # Calculate flow trends
        flow_trend = np.zeros_like(mfi)
        flow_trend[positive_mf > positive_mf.shift(1)] = 1
        flow_trend[negative_mf > negative_mf.shift(1)] = -1
        
        self._values = {
            'mfi': mfi.values,
            'overbought': overbought,
            'oversold': oversold,
            'extreme_overbought': extreme_overbought,
            'extreme_oversold': extreme_oversold,
            'divergence': divergence,
            'momentum': momentum.values,
            'trend': trend,
            'mid_cross': mid_cross,
            'strength': strength,
            'flow_trend': flow_trend,
            'positive_flow': positive_flow,
            'negative_flow': negative_flow,
            'money_flow_ratio': money_flow_ratio.values
        }
        return self._values
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        return True 