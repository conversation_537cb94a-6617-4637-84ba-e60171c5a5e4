<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GarudaAlgo | Dashboard</title>
  <meta name="description" content="GarudaAlgo V2 Dashboard - Access your trading tools and account information.">
  <link rel="icon" href="app_icon.ico">
  <link rel="stylesheet" href="style.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    .dashboard-section {
      padding: 80px 0;
      min-height: calc(100vh - 200px);
    }
    
    .dashboard-container {
      background: var(--bg-card);
      border-radius: 12px;
      padding: 40px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.08);
    }
    
    .dashboard-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .dashboard-title h2 {
      font-size: 28px;
      margin-bottom: 10px;
      background: var(--text-gradient);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
    
    .dashboard-actions {
      display: flex;
      gap: 15px;
    }
    
    .dashboard-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
    }
    
    .dashboard-card {
      background: var(--bg-dark);
      border-radius: 12px;
      padding: 25px;
      border: 1px solid rgba(255, 255, 255, 0.05);
      transition: all 0.3s ease;
    }
    
    .dashboard-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
      border-color: rgba(255, 255, 255, 0.1);
    }
    
    .dashboard-card h3 {
      font-size: 20px;
      margin-bottom: 15px;
      color: var(--text-primary);
    }
    
    .dashboard-card p {
      color: var(--text-secondary);
      margin-bottom: 20px;
    }
    
    .dashboard-card-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: var(--primary-color);
      margin-bottom: 20px;
      font-size: 24px;
      color: var(--text-primary);
    }
    
    .user-info {
      margin-top: 40px;
      background: var(--bg-dark);
      border-radius: 12px;
      padding: 25px;
      border: 1px solid rgba(255, 255, 255, 0.05);
    }
    
    .user-info h3 {
      font-size: 20px;
      margin-bottom: 20px;
      color: var(--text-primary);
    }
    
    .user-info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
    }
    
    .user-info-item {
      margin-bottom: 15px;
    }
    
    .user-info-item label {
      display: block;
      font-size: 14px;
      color: var(--text-secondary);
      margin-bottom: 5px;
    }
    
    .user-info-item p {
      font-size: 16px;
      color: var(--text-primary);
      font-weight: 500;
    }
    
    @media (max-width: 768px) {
      .dashboard-content {
        grid-template-columns: 1fr;
      }
      
      .user-info-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <header class="header">
    <div class="container">
      <nav class="navbar">
        <div class="logo">
          <img src="app_icon.png" alt="GarudaAlgo Logo">
          <h1>GarudaAlgo</h1>
        </div>
        <div class="nav-right">
          <div class="language-selector">
            <button id="lang-en" class="active">EN</button>
            <button id="lang-id">ID</button>
          </div>
          <button id="logout-btn" class="btn-secondary" data-lang-key="logout">Logout</button>
        </div>
      </nav>
    </div>
  </header>

  <section class="dashboard-section">
    <div class="container">
      <div class="dashboard-container">
        <div class="dashboard-header">
          <div class="dashboard-title">
            <h2 data-lang-key="dashboard">Dashboard</h2>
            <p data-lang-key="welcome-message">Welcome to your GarudaAlgo V2 dashboard</p>
          </div>
          <div class="dashboard-actions">
            <a href="#" class="btn-primary" id="download-app-btn" data-lang-key="download-app">Download App</a>
          </div>
        </div>
        
        <div class="dashboard-content">
          <div class="dashboard-card">
            <div class="dashboard-card-icon">
              <i class="fas fa-download"></i>
            </div>
            <h3 data-lang-key="download-app">Download GarudaAlgo V2</h3>
            <p data-lang-key="download-app-desc">Download the latest version of GarudaAlgo V2 for Windows.</p>
            <a href="#" class="btn-primary" id="download-windows-btn" data-lang-key="download-windows">Download for Windows</a>
          </div>
          
          <div class="dashboard-card">
            <div class="dashboard-card-icon">
              <i class="fas fa-book"></i>
            </div>
            <h3 data-lang-key="documentation">Documentation</h3>
            <p data-lang-key="documentation-desc">Learn how to use GarudaAlgo V2 with our comprehensive documentation.</p>
            <a href="#" class="btn-secondary" data-lang-key="view-docs">View Documentation</a>
          </div>
          
          <div class="dashboard-card">
            <div class="dashboard-card-icon">
              <i class="fas fa-headset"></i>
            </div>
            <h3 data-lang-key="support">Support</h3>
            <p data-lang-key="support-desc">Need help? Contact our support team for assistance.</p>
            <a href="mailto:<EMAIL>" class="btn-secondary" data-lang-key="contact-support">Contact Support</a>
          </div>
          
          <div class="dashboard-card">
            <div class="dashboard-card-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <h3 data-lang-key="trading-resources">Trading Resources</h3>
            <p data-lang-key="resources-desc">Access trading guides, strategies, and educational materials.</p>
            <a href="#" class="btn-secondary" data-lang-key="view-resources">View Resources</a>
          </div>
        </div>
        
        <div class="user-info">
          <h3 data-lang-key="account-info">Account Information</h3>
          <div class="user-info-grid">
            <div class="user-info-item">
              <label data-lang-key="email">Email Address</label>
              <p id="user-email">loading...</p>
            </div>
            <div class="user-info-item">
              <label data-lang-key="broker">Broker</label>
              <p id="user-broker">loading...</p>
            </div>
            <div class="user-info-item">
              <label data-lang-key="account-number">Account Number</label>
              <p id="user-account">loading...</p>
            </div>
            <div class="user-info-item">
              <label data-lang-key="affiliate-code">Affiliate Code</label>
              <p id="user-affiliate">loading...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <footer class="footer">
    <div class="container">
      <div class="footer-grid">
        <div class="footer-col">
          <div class="footer-logo">
            <img src="app_icon.png" alt="GarudaAlgo Logo">
            <h2>GarudaAlgo</h2>
          </div>
          <p data-lang-key="footer-tagline">Navigate the markets with algorithmic intelligence & strategic precision.</p>
        </div>
        <div class="footer-col">
          <h3 data-lang-key="quick-links">Quick Links</h3>
          <ul>
            <li><a href="index.html#features" data-lang-key="features">Features</a></li>
            <li><a href="index.html#why-algo" data-lang-key="why-algo-trading-short">Why Algo & Autonomous</a></li>
            <li><a href="index.html#faq" data-lang-key="faq-link">FAQ</a></li>
            <li><a href="#" data-lang-key="support">Support</a></li>
          </ul>
        </div>
        <div class="footer-col">
          <h3 data-lang-key="legal">Legal</h3>
          <ul>
            <li><a href="#" data-lang-key="terms">Terms & Conditions</a></li>
            <li><a href="#" data-lang-key="privacy">Privacy Policy</a></li>
            <li><a href="#" data-lang-key="refund">Refund Policy</a></li>
          </ul>
        </div>
        <div class="footer-col">
          <h3 data-lang-key="contact">Contact</h3>
          <ul>
            <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
            <li class="social-icons">
              <a href="#"><i class="fab fa-twitter"></i></a>
              <a href="#"><i class="fab fa-facebook"></i></a>
              <a href="#"><i class="fab fa-instagram"></i></a>
              <a href="#"><i class="fab fa-youtube"></i></a>
            </li>
          </ul>
        </div>
      </div>
      <div class="copyright">
        <p>&copy; 2025 GarudaAlgo. <span data-lang-key="all-rights">All rights reserved.</span></p>
      </div>
    </div>
  </footer>

  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
  <script src="firebase-config.js"></script>
  <script src="locales.json"></script>
  <script src="script.js"></script>
  <script src="dashboard.js"></script>
</body>
</html>
