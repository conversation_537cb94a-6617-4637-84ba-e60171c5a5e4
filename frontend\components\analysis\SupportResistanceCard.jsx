import React, { useState } from 'react';
import PriceLevelVisualization from './PriceLevelVisualization';
import { safeToFixed } from '../../utils/numberUtils';
import Sparkline from './Sparkline';
import '../../styles/BentoComponents.css';
import '../../styles/AnalysisBento.css';
import '../../styles/SupportResistance.css';

/**
 * SupportResistanceCard component displays support and resistance analysis
 *
 * @param {Object} analysisData - The analysis data
 * @param {string[]} [sources] - Array of sources to toggle/filter (optional)
 * @returns {JSX.Element} - The rendered support and resistance card
 */
const SupportResistanceCard = ({ analysisData, sources }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [activeSource, setActiveSource] = useState(sources && sources.length > 0 ? sources[0] : undefined);
  const [visibleSection, setVisibleSection] = useState('all'); // 'all', 'resistance', 'support'

  // Helper for color coding strength
  function getStrengthColor(strength) {
    if (!strength) return 'var(--text-secondary)';
    if (strength >= 1.5) return 'var(--success)'; // strong - green
    if (strength >= 1.0) return 'var(--warning)'; // medium - yellow
    return 'var(--error)'; // weak - red
  }

  // Helper to format level source for display
  const formatSource = (source) => {
    if (!source) return 'Unknown';
    if (source.includes(',')) {
      // Multiple sources, format as tags
      return source.split(',').map(s => s.trim());
    }
    return source;
  };

  // Filter levels by active source if provided
  const filterLevels = (levels) => {
    if (!activeSource || !levels) return levels;
    return levels.filter((lvl) => lvl.source.includes(activeSource));
  };

  // Get current price for display
  const currentPrice =
    analysisData?.current_price?.bid ||
    analysisData?.support_resistance?.current_price?.bid ||
    analysisData?.current_price?.last ||
    analysisData?.support_resistance?.current_price?.last;

  // Get nearest support and resistance
  const nearestSupport = analysisData?.support_resistance?.nearest_support;
  const nearestResistance = analysisData?.support_resistance?.nearest_resistance;

  // Get all levels for tabs
  const resistanceLevels = analysisData?.support_resistance?.resistance_levels || [];
  const supportLevels = analysisData?.support_resistance?.support_levels || [];

  // Collect sparkline data (current price and levels)
  const sparkData = [];
  if (analysisData?.support_resistance?.price_history) {
    sparkData.push(...analysisData.support_resistance.price_history);
  }

  return (
    <div className="bento-card bento-span-8 bento-height-2 support-resistance-card"
         style={{ background: 'var(--card)', border: '1px solid var(--border)', borderRadius: '8px', overflow: 'hidden' }}>
      <div className="bento-card-header" style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '10px 15px',
        borderBottom: '1px solid var(--border)'
      }}>
        <h3 className="bento-card-title" style={{ margin: 0, fontSize: '1.1rem', color: 'var(--text)' }}>Support & Resistance</h3>
        <button className="collapse-btn"
                style={{ background: 'none', border: 'none', color: 'var(--text-secondary)', fontSize: '1.2rem', cursor: 'pointer' }}
                onClick={() => setCollapsed((c) => !c)}>
          {collapsed ? '+' : '-'}
        </button>
      </div>
      {!collapsed && (
        <div className="bento-card-content" style={{ padding: '0' }}>
          {/* Price Level Visualization */}
          <PriceLevelVisualization analysisData={analysisData} />
          {/* Current Price and Key Levels Summary */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            padding: '15px 20px',
            borderBottom: '1px solid var(--border)',
            backgroundColor: 'var(--background-secondary)'
          }}>
            <div style={{ textAlign: 'center', flex: 1 }}>
              <div style={{ color: 'var(--text-secondary)', fontSize: '0.8rem', marginBottom: '4px' }}>Support</div>
              <div style={{
                fontSize: '1rem',
                fontWeight: 'bold',
                color: 'var(--success)'
              }}>
                {nearestSupport?.price != null
                  ? safeToFixed(nearestSupport.price, 5)
                  : '—'}
              </div>
            </div>

            <div style={{ textAlign: 'center', flex: 1 }}>
              <div style={{ color: 'var(--text-secondary)', fontSize: '0.8rem', marginBottom: '4px' }}>Current Price</div>
              <div style={{
                fontSize: '1.2rem',
                fontWeight: 'bold',
                color: 'var(--warning)'
              }}>
                {currentPrice != null ? safeToFixed(currentPrice, 5) : '—'}
              </div>
            </div>

            <div style={{ textAlign: 'center', flex: 1 }}>
              <div style={{ color: 'var(--text-secondary)', fontSize: '0.8rem', marginBottom: '4px' }}>Resistance</div>
              <div style={{
                fontSize: '1rem',
                fontWeight: 'bold',
                color: 'var(--error)'
              }}>
                {nearestResistance?.price != null
                  ? safeToFixed(nearestResistance.price, 5)
                  : '—'}
              </div>
            </div>
          </div>

          {/* Section Tabs */}
          <div style={{
            display: 'flex',
            borderBottom: '1px solid var(--border)',
            backgroundColor: 'var(--background-secondary)'
          }}>
            <button
              onClick={() => setVisibleSection('all')}
              style={{
                flex: 1,
                padding: '8px 0',
                background: 'none',
                border: 'none',
                borderBottom: visibleSection === 'all' ? '2px solid var(--warning)' : '2px solid transparent',
                color: visibleSection === 'all' ? 'var(--text)' : 'var(--text-secondary)',
                cursor: 'pointer'
              }}
            >
              All Levels
            </button>
            <button
              onClick={() => setVisibleSection('resistance')}
              style={{
                flex: 1,
                padding: '8px 0',
                background: 'none',
                border: 'none',
                borderBottom: visibleSection === 'resistance' ? '2px solid var(--error)' : '2px solid transparent',
                color: visibleSection === 'resistance' ? 'var(--text)' : 'var(--text-secondary)',
                cursor: 'pointer'
              }}
            >
              Resistance ({resistanceLevels.length})
            </button>
            <button
              onClick={() => setVisibleSection('support')}
              style={{
                flex: 1,
                padding: '8px 0',
                background: 'none',
                border: 'none',
                borderBottom: visibleSection === 'support' ? '2px solid var(--success)' : '2px solid transparent',
                color: visibleSection === 'support' ? 'var(--text)' : 'var(--text-secondary)',
                cursor: 'pointer'
              }}
            >
              Support ({supportLevels.length})
            </button>
          </div>

          {/* Source selection if multiple sources */}
          {sources && sources.length > 1 && (
            <div style={{
              padding: '10px 15px',
              borderBottom: '1px solid var(--border)',
              display: 'flex',
              alignItems: 'center'
            }}>
              <label style={{ marginRight: '10px', fontSize: '0.9rem', color: 'var(--text-secondary)' }}>Filter by source: </label>
              <select
                value={activeSource}
                onChange={e => setActiveSource(e.target.value)}
                style={{
                  background: 'var(--background-secondary)',
                  color: 'var(--text)',
                  border: '1px solid var(--border)',
                  borderRadius: '4px',
                  padding: '4px 8px'
                }}
              >
                {sources.map((src) => (
                  <option key={src} value={src}>{src}</option>
                ))}
              </select>
            </div>
          )}

          {/* Levels Content */}
          <div className="analysis-sr-details" style={{
            padding: '10px',
            maxHeight: '280px',
            overflowY: 'auto',
            display: 'flex',
            flexDirection: visibleSection === 'all' ? 'column' : 'row',
            flexWrap: 'wrap',
            gap: '15px'
          }}>

            {/* Show resistance levels if 'all' or 'resistance' section is active */}
            {(visibleSection === 'all' || visibleSection === 'resistance') && (
              <div>
                {visibleSection === 'all' && (
                  <div style={{
                    marginBottom: '10px',
                    paddingBottom: '5px',
                    borderBottom: '1px solid var(--border)'
                  }}>
                    <strong style={{
                      display: 'block',
                      color: 'var(--error)',
                      fontSize: '0.95rem',
                      marginBottom: '8px'
                    }}>
                      Resistance Levels
                    </strong>
                  </div>
                )}

                <div style={{
                  marginBottom: visibleSection === 'all' ? '15px' : '0',
                  flex: visibleSection === 'all' ? '1' : '1 1 48%'
                }}>
                  {filterLevels(resistanceLevels)?.length > 0 ? (
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                      {filterLevels(resistanceLevels).map((r, index) => (
                        <div key={`res-${index}`} style={{
                          display: 'flex',
                          padding: '8px',
                          backgroundColor: 'var(--background-secondary)',
                          borderRadius: '6px',
                          borderLeft: `3px solid ${getStrengthColor(r.strength)}`,
                          color: 'var(--text)'
                        }}>
                          <div style={{ flex: 1 }}>
                            <div style={{ fontSize: '1rem', fontWeight: 'bold' }}>{safeToFixed(r.price, 5)}</div>
                            <div style={{ fontSize: '0.8rem', color: 'var(--text-secondary)' }}>
                              Source: {Array.isArray(formatSource(r.source))
                                ? formatSource(r.source).map((src, i) => (
                                  <span key={i} style={{
                                    display: 'inline-block',
                                    margin: '2px 3px 2px 0',
                                    padding: '1px 4px',
                                    backgroundColor: 'var(--background-tertiary, rgba(0,0,0,0.2))',
                                    borderRadius: '3px',
                                    fontSize: '0.75rem'
                                  }}>{src}</span>
                                ))
                                : formatSource(r.source)
                              }
                            </div>
                          </div>
                          <div style={{ textAlign: 'right' }}>
                            <div style={{
                              fontSize: '0.85rem',
                              color: getStrengthColor(r.strength),
                              fontWeight: 'bold'
                            }}>
                              {r.strength_label || (r.strength
                                ? `${r.strength >= 1.5 ? 'Strong' : r.strength >= 1.0 ? 'Medium' : 'Weak'} (${safeToFixed(r.strength, 1)})`
                                : 'Unknown')}
                            </div>

                            {r.distance_percent != null && (
                              <div style={{
                                fontSize: '0.8rem',
                                color: 'var(--text-secondary)',
                                marginTop: '2px'
                              }}>
                                {safeToFixed(r.distance_percent * 100, 2)}% above
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div style={{ textAlign: 'center', padding: '15px 0', color: 'var(--text-secondary)' }}>
                      No resistance levels found.
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Show support levels if 'all' or 'support' section is active */}
            {(visibleSection === 'all' || visibleSection === 'support') && (
              <div>
                {visibleSection === 'all' && (
                  <div style={{
                    marginBottom: '10px',
                    paddingBottom: '5px',
                    borderBottom: '1px solid var(--border)'
                  }}>
                    <strong style={{
                      display: 'block',
                      color: 'var(--success)',
                      fontSize: '0.95rem',
                      marginBottom: '8px'
                    }}>
                      Support Levels
                    </strong>
                  </div>
                )}

                <div style={{
                  marginBottom: visibleSection === 'all' ? '15px' : '0',
                  flex: visibleSection === 'all' ? '1' : '1 1 48%'
                }}>
                  {filterLevels(supportLevels)?.length > 0 ? (
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                      {filterLevels(supportLevels).map((s, index) => (
                        <div key={`sup-${index}`} style={{
                          display: 'flex',
                          padding: '8px',
                          backgroundColor: 'var(--background-secondary)',
                          borderRadius: '6px',
                          borderLeft: `3px solid ${getStrengthColor(s.strength)}`,
                          color: 'var(--text)'
                        }}>
                          <div style={{ flex: 1 }}>
                            <div style={{ fontSize: '1rem', fontWeight: 'bold' }}>{safeToFixed(s.price, 5)}</div>
                            <div style={{ fontSize: '0.8rem', color: 'var(--text-secondary)' }}>
                              Source: {Array.isArray(formatSource(s.source))
                                ? formatSource(s.source).map((src, i) => (
                                  <span key={i} style={{
                                    display: 'inline-block',
                                    margin: '2px 3px 2px 0',
                                    padding: '1px 4px',
                                    backgroundColor: 'var(--background-tertiary, rgba(0,0,0,0.2))',
                                    borderRadius: '3px',
                                    fontSize: '0.75rem'
                                  }}>{src}</span>
                                ))
                                : formatSource(s.source)
                              }
                            </div>
                          </div>
                          <div style={{ textAlign: 'right' }}>
                            <div style={{
                              fontSize: '0.85rem',
                              color: getStrengthColor(s.strength),
                              fontWeight: 'bold'
                            }}>
                              {s.strength_label || (s.strength
                                ? `${s.strength >= 1.5 ? 'Strong' : s.strength >= 1.0 ? 'Medium' : 'Weak'} (${safeToFixed(s.strength, 1)})`
                                : 'Unknown')}
                            </div>

                            {s.distance_percent != null && (
                              <div style={{
                                fontSize: '0.8rem',
                                color: 'var(--text-secondary)',
                                marginTop: '2px'
                              }}>
                                {safeToFixed(s.distance_percent * 100, 2)}% below
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div style={{ textAlign: 'center', padding: '15px 0', color: 'var(--text-secondary)' }}>
                      No support levels found.
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SupportResistanceCard;
