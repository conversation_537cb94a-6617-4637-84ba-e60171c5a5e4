import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
import logging
from datetime import datetime, timedelta
import math

# Import technical indicators
from backend.technical.indicators.trend.ema import EMAIndicator
from backend.technical.indicators.trend.sma import SMAIndicator
from backend.technical.indicators.trend.macd import MACDIndicator
from backend.technical.indicators.trend.bollinger import BollingerBandsIndicator
from backend.technical.indicators.momentum.rsi import RSIIndicator
from backend.technical.indicators.volatility.atr import ATRIndicator

# Import our extractors
from .extractors import (
    extract_moving_averages,
    extract_rsi,
    extract_macd,
    extract_bollinger_bands,
    extract_atr,
    analyze_support_resistance_comprehensive,
    get_empty_sr_result,
    analyze_trend
)

# Import signal generator
from .signal_generator import generate_signal_recommendation

# Import utilities
from .utils import replace_nan_with_none

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("analysis_engine.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("AnalysisEngine")

# --- Explicitly configure the AnalysisEngine logger ---
logger.setLevel(logging.DEBUG) # Ensure DEBUG level for this logger
logger.propagate = False # Prevent messages from going to root logger handlers

# Add a dedicated StreamHandler for console output for this logger
if not any(isinstance(h, logging.StreamHandler) for h in logger.handlers):
    stream_handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)
# --- End explicit configuration ---


class AnalysisEngine:
    """
    A class to perform multi-timeframe technical analysis for trading symbols.
    """
    def __init__(self, mt5_module):
        if not hasattr(mt5_module, 'fetch_ohlc_data'):
            raise ValueError("Provided mt5_module instance must have a 'fetch_ohlc_data' method.")
        self.mt5 = mt5_module
        self.timeframe_map = {
            "M1": mt5.TIMEFRAME_M1, "M5": mt5.TIMEFRAME_M5, "M15": mt5.TIMEFRAME_M15,
            "M30": mt5.TIMEFRAME_M30, "H1": mt5.TIMEFRAME_H1, "H4": mt5.TIMEFRAME_H4,
            "D1": mt5.TIMEFRAME_D1, "W1": mt5.TIMEFRAME_W1, "MN1": mt5.TIMEFRAME_MN1
        }
        self.timeframe_seconds = {
            "M1": 60, "M5": 300, "M15": 900, "M30": 1800, "H1": 3600, "H4": 14400,
            "D1": 86400, "W1": 604800, "MN1": 2592000  # Approximate
        }
        self.current_price = None
        self.logger = logging.getLogger(__name__)

    def get_analysis_for_timeframe(self, symbol: str, timeframe: str,
                                 bars_count: int = 300) -> Dict[str, Any]:
        """
        Public method to get analysis for a single timeframe.
        """
        logger.info(f"Starting analysis for {symbol} on {timeframe}")

        if timeframe not in self.timeframe_map:
            logger.error(f"Invalid timeframe requested: {timeframe}")
            return {"success": False, "message": f"Invalid timeframe: {timeframe}"}

        # Check connection using the passed integration object
        # The AnalysisEngine should NOT attempt to initialize the connection.
        # It should rely on the connection being managed externally.
        if not self.mt5.is_connected():
            logger.warning(f"Analysis for {symbol}/{timeframe} cannot proceed: MT5 is not connected.")
            return {
                "success": False,
                "message": "MT5 connection not established. Please connect to MT5 first."
                # "status_code": 503 # Optional: to indicate service unavailable to the API layer
            }

        # Proceed with analysis if connection is established
        try:
            analysis_result = self._analyze_timeframe(symbol, timeframe, bars_count)
            logger.info(f"Analysis for {symbol}/{timeframe} completed with success={analysis_result.get('success', False)}")
            return analysis_result
        except Exception as e:
            logger.exception(f"Unexpected error during analysis for {symbol}/{timeframe}")
            return {"success": False, "message": f"Analysis failed: {str(e)}"}

    def _analyze_timeframe(self, symbol: str, timeframe: str,
                         bars_count: int) -> Dict[str, Any]:
        """
        Analyze a symbol for a specific timeframe.
        """
        try:
            tf_const = self.timeframe_map[timeframe]
            tf_seconds = self.timeframe_seconds.get(timeframe, 3600)
            
            # Adjust bars_count based on timeframe to ensure sufficient data for longer timeframes
            adjusted_bars_count = bars_count
            
            # For weekly timeframe, get at least 5 years of data (260 weeks)
            if timeframe == 'W1':
                adjusted_bars_count = max(260, bars_count)
                logger.info(f"Adjusting bars_count for W1 timeframe to {adjusted_bars_count}")
            
            # For monthly timeframe, get at least 10 years of data (120 months)
            elif timeframe == 'MN1':
                adjusted_bars_count = max(120, bars_count)
                logger.info(f"Adjusting bars_count for MN1 timeframe to {adjusted_bars_count}")
                logger.info(f"MN1 timeframe detected - will use special handling for monthly data")
                # Set a longer timeout for MN1 data which can be larger
            
            end_time = datetime.now()
            start_time = end_time - timedelta(seconds=int(tf_seconds * adjusted_bars_count * 1.5))  # Fetch slightly more data
            
            logger.info(f"Fetching {timeframe} data for {symbol} from {start_time} to {end_time} (approx. {adjusted_bars_count} bars)")
            ohlc_data = self.mt5.fetch_ohlc_data(symbol, tf_const, start_time, end_time)
            if not ohlc_data: # Check if data list is empty
                # More specific log message
                error_msg = f"No OHLC data returned for {symbol} on timeframe {timeframe} between {start_time} and {end_time}"
                logger.warning(error_msg)
                return {"success": False, "message": error_msg}
            logger.info(f"Successfully fetched {len(ohlc_data)} OHLC records for {symbol}/{timeframe}")
            
            # Add detailed logging for MN1 timeframe data
            if timeframe == 'MN1':
                logger.info(f"MN1 data sample (first 5 records): {ohlc_data[:5] if len(ohlc_data) >= 5 else ohlc_data}")
                # Check for any null or missing values in the data
                if any(not all(record.values()) for record in ohlc_data if record):
                    logger.warning(f"MN1 data contains null or missing values in some records")
                    # Log the problematic records
                    for i, record in enumerate(ohlc_data):
                        if record and not all(record.values()):
                            logger.warning(f"Record {i} has missing values: {record}")


            data = pd.DataFrame(ohlc_data)
            if 'time' in data.columns and not pd.api.types.is_datetime64_any_dtype(data['time']):
                try:
                    data['time'] = pd.to_datetime(data['time'])
                    data.set_index('time', inplace=True)  # Set time as index
                except Exception as time_e:
                    logger.warning(f"Could not convert time column to datetime or set index: {time_e}")
                    data.reset_index(drop=True, inplace=True)

            required_cols = ['open', 'high', 'low', 'close']
            if not all(col in data.columns for col in required_cols):
                missing = [col for col in required_cols if col not in data.columns]
                return {"success": False, "message": f"Missing required columns in OHLC data: {missing}"}

            # Calculate indicators using our custom implementations
            # EMA calculations
            ema_results = {}
            for period in [20, 50, 100, 200]:
                try:
                    ema = EMAIndicator(period=period).calculate(data)
                    if 'ema' in ema:
                        data[f'EMA_{period}'] = ema['ema']
                        ema_results[f'EMA_{period}'] = ema['ema']
                except Exception as e:
                    logger.error(f"Error calculating EMA_{period}: {e}")

            # SMA calculations
            sma_results = {}
            for period in [20, 50, 100, 200]:
                try:
                    sma = SMAIndicator(period=period).calculate(data)
                    if 'sma' in sma:
                        data[f'SMA_{period}'] = sma['sma']
                        sma_results[f'SMA_{period}'] = sma['sma']
                except Exception as e:
                    logger.error(f"Error calculating SMA_{period}: {e}")

            # MACD calculation
            try:
                macd_results = MACDIndicator().calculate(data)
                if 'macd' in macd_results:
                    data['MACD'] = macd_results['macd']
                    data['MACD_Signal'] = macd_results['signal']
                    data['MACD_Hist'] = macd_results['histogram']
            except Exception as e:
                logger.error(f"Error calculating MACD: {e}")

            # RSI calculation - Use our custom RSIIndicator
            try:
                rsi_indicator = RSIIndicator(period=14, source='close')
                rsi_results = rsi_indicator.calculate(data)
                
                if 'rsi' in rsi_results and len(rsi_results['rsi']) > 0:
                    data['RSI'] = rsi_results['rsi']
                    logger.info(f"Successfully calculated RSI with {np.count_nonzero(~np.isnan(rsi_results['rsi']))} valid values")
                    if 'divergence' in rsi_results:
                        logger.info(f"RSI Divergence detected: {rsi_results.get('divergence', 'None')}")
                else:
                    logger.warning("RSI calculation returned empty or invalid results")
            except Exception as e:
                logger.error(f"Error calculating RSI with custom indicator: {str(e)}")
                # Fall back to standard implementation if custom one fails
                try:
                    # Try the simple built-in RSI calculation as fallback
                    from backend.technical.indicators.trend.rsi import RSIIndicator as BackupRSI
                    rsi_results = BackupRSI().calculate(data)
                    if 'value' in rsi_results:
                        data['RSI'] = rsi_results['value']
                        logger.info("Used fallback RSI calculation")
                except Exception as fallback_e:
                    logger.error(f"Fallback RSI calculation also failed: {str(fallback_e)}")

            # Bollinger Bands calculation
            try:
                bb_results = BollingerBandsIndicator().calculate(data)
                if all(k in bb_results for k in ['upper_band', 'middle_band', 'lower_band']):
                    data['BBL'] = bb_results['lower_band']
                    data['BBM'] = bb_results['middle_band']
                    data['BBU'] = bb_results['upper_band']
                    data['BBB'] = bb_results['bandwidth']
            except Exception as e:
                logger.error(f"Error calculating Bollinger Bands: {e}")

            # Extract results and structure the output
            analysis = {}
            analysis["moving_averages"] = extract_moving_averages(data)
            analysis["rsi"] = extract_rsi(data)
            analysis["macd"] = extract_macd(data)

            # Calculate ATR *before* S/R as SupplyDemand needs it
            logger.debug("Attempting ATR calculation...")
            try:
                atr_indicator_result = ATRIndicator().calculate(data)
            except Exception as e:
                # <-- ADDED LOGS (start) -->
                logger.debug(f"ATR calculation result type: {type(atr_indicator_result)}")
                if isinstance(atr_indicator_result, dict) and 'atr' in atr_indicator_result:
                    logger.debug(f"ATR Series info before adding:\n{atr_indicator_result['atr'].info()}")
                    logger.debug(f"ATR Series head before adding:\n{atr_indicator_result['atr'].head()}")
                elif isinstance(atr_indicator_result, dict):
                     logger.debug(f"ATR calculation result keys: {atr_indicator_result.keys()}")
                else:
                    logger.debug(f"ATR calculation result (non-dict): {atr_indicator_result}")
                # <-- ADDED LOGS (end) -->
                logger.error(f"Error directly calling ATRIndicator: {e}", exc_info=True)
                atr_indicator_result = {'error': f"Exception in ATRIndicator: {e}"}

            # Add ATR to the main dataframe if calculated successfully
            if isinstance(atr_indicator_result, dict) and 'atr' in atr_indicator_result and isinstance(atr_indicator_result.get('atr'), pd.Series):
                atr_series = atr_indicator_result['atr']
                # Check if the Series is not empty and index matches before assigning
                if not atr_series.empty and atr_series.index.equals(data.index):
                    data['atr'] = atr_series
                    # Add detailed logging
                    valid_atr_count = atr_series.dropna().count()
                    total_atr_count = len(atr_series)
                    nan_atr_count = total_atr_count - valid_atr_count
                    logger.debug(f"ATR Series added to data. Total: {total_atr_count}, Valid: {valid_atr_count}, NaN: {nan_atr_count}.")
                    if valid_atr_count == 0:
                        logger.warning("ATR Series added, but contains only NaN values.")
                    # Log the last few non-NaN values
                    last_valid_atrs = atr_series.dropna().tail(3).to_list()
                    logger.debug(f"Last few valid ATR values: {last_valid_atrs}")

                elif not atr_series.empty:
                     logger.warning(f"ATR Series index mismatch (len {len(atr_series)}) with data index (len {len(data.index)}). Cannot add ATR column.")
                     if 'atr' not in data.columns:
                         data['atr'] = pd.Series(index=data.index, dtype=float) # Add NaN column
                else:
                     logger.warning("ATR calculation returned an empty Series. Cannot add ATR column.") # Changed to warning
                     if 'atr' not in data.columns:
                         data['atr'] = pd.Series(index=data.index, dtype=float) # Add NaN column

            elif isinstance(atr_indicator_result, dict) and 'error' in atr_indicator_result:
                 logger.error(f"ATR calculation returned error: {atr_indicator_result['error']}. Supply/Demand zones might be affected.")
                 if 'atr' not in data.columns:
                     data['atr'] = pd.Series(index=data.index, dtype=float) # Add NaN column
            else:
                # This case should be less likely now, but kept for safety
                # Log the actual result type for better debugging
                logger.warning(f"ATR calculation failed or returned unexpected format ({type(atr_indicator_result)}). Content: {atr_indicator_result}. Supply/Demand zones might be affected.")
                if 'atr' not in data.columns:
                     data['atr'] = pd.Series(index=data.index, dtype=float) # Add NaN column

            # <-- ADDED LOGS (start) -->
            logger.debug("DataFrame info after attempting to add ATR column:")
            # --- Safely log DataFrame info ---
            try:
                import io
                buffer = io.StringIO()
                data.info(buf=buffer)
                info_str = buffer.getvalue()
                logger.debug(f"DataFrame Info:\n{info_str}") # Log the string
            except Exception as info_e:
                logger.error(f"Could not log DataFrame info: {info_e}")
            # --- Log handlers for diagnostics ---
            logger.debug(f"Current logger handlers: {logger.handlers}")
            logger.debug(f"Root logger handlers: {logging.root.handlers}")
            # --- Original problematic line commented out ---
            # data.info(buf=logger.handlers[0].stream) # Log to file/stream
            logger.debug(f"DataFrame head after attempting to add ATR column:\n{data.head()}")
            # <-- ADDED LOGS (end) -->

            # Fetch current price info *before* S/R analysis
            current_price_data = None
            try:
                symbol_info = mt5.symbol_info_tick(symbol)
                latest_close = float(data['close'].iloc[-1]) if not data.empty else None

                if symbol_info is not None and hasattr(symbol_info, 'bid') and hasattr(symbol_info, 'ask'):
                    bid = float(symbol_info.bid) if not math.isnan(symbol_info.bid) else latest_close
                    ask = float(symbol_info.ask) if not math.isnan(symbol_info.ask) else latest_close
                    last = float(symbol_info.last) if hasattr(symbol_info, 'last') and not math.isnan(symbol_info.last) else latest_close
                    time_str = datetime.fromtimestamp(symbol_info.time).strftime('%Y-%m-%d %H:%M:%S') if hasattr(symbol_info, 'time') else \
                               data.index[-1].strftime('%Y-%m-%d %H:%M:%S') if not data.empty and isinstance(data.index[-1], pd.Timestamp) else None
                else:
                    bid = ask = last = latest_close
                    time_str = data.index[-1].strftime('%Y-%m-%d %H:%M:%S') if not data.empty and isinstance(data.index[-1], pd.Timestamp) else None

                if all(x is not None and not math.isnan(x) for x in [bid, ask, last]):
                    current_price_data = { "bid": bid, "ask": ask, "last": last, "time": time_str }
                    self.current_price = current_price_data # Store for S/R analysis
                    analysis["current_price"] = current_price_data # Add to final analysis output
                    logger.info(f"Current price set - Bid: {bid}, Ask: {ask}, Last: {last}")
                else:
                    # Use the latest close price as a fallback
                    if latest_close is not None and not math.isnan(latest_close):
                        logger.warning(f"Using latest close price ({latest_close}) as fallback for current price")
                        current_price_data = { "bid": latest_close, "ask": latest_close, "last": latest_close, "time": time_str }
                        self.current_price = current_price_data
                        analysis["current_price"] = current_price_data
                    else:
                        raise ValueError("Invalid price values fetched and no valid fallback available")

            except Exception as e:
                logger.error(f"Error getting current price for {symbol}: {str(e)}")
                analysis["current_price"] = {"error": f"Could not get valid price: {str(e)}"}
                self.current_price = None # Ensure self.current_price is None if fetch failed

            # Check if current price is valid before running comprehensive S/R
            sr_result = None # Initialize
            try:
                # Log current price for debugging
                logger.info(f"Current price before S/R analysis: {self.current_price}")

                # Ensure current_price is valid before calling S/R
                if self.current_price and isinstance(self.current_price.get('bid'), (int, float)) and self.current_price.get('bid') is not None:
                    logger.debug("About to perform S/R analysis...")
                    logger.debug(f"Pre-call self.current_price value: {self.current_price}") # Log price before call
                    # Capture result explicitly
                    sr_result = analyze_support_resistance_comprehensive(data.copy(), self.current_price) # Pass copy to avoid modification issues
                    logger.debug(f"Result received from analyze_support_resistance_comprehensive: Type={type(sr_result)}, Value={sr_result}") # Log result
                    logger.debug("Completed S/R analysis")
                else:
                    logger.warning(f"Skipping S/R analysis due to invalid or missing current price ({self.current_price}).")
                    # Assign default error structure if skipping
                    sr_result = get_empty_sr_result()
                    logger.debug(f"Assigned empty S/R result due to price issue: {sr_result}")
            except Exception as e:
                logger.exception("Exception during support/resistance analysis")
                sr_result = {"error": f"Exception during S/R call: {str(e)}"}
                logger.debug(f"Assigned error S/R result due to exception: {sr_result}")

            analysis['support_resistance'] = sr_result # Assign captured result

            # --- Other Indicators ---
            analysis["bollinger_bands"] = extract_bollinger_bands(data)
            analysis["atr"] = extract_atr(data)
            analysis["trend"] = analyze_trend(data, analysis)
            # analysis["candlestick_patterns"] = self._analyze_candlestick_patterns(data) # Add later if needed
            # analysis["volume_analysis"] = self._analyze_volume(data) # Add later if needed
            # analysis["volatility"] = self._analyze_volatility(data, analysis.get("atr")) # Add later if needed
            
            # --- Chart Pattern Detection ---
            try:
                logger.info("Detecting chart patterns...")
                patterns = []
                
                # Import pattern detectors
                from backend.technical.patterns.chart.double_top_bottom import DoubleTopBottomPatternIndicator
                from backend.technical.patterns.chart.head_and_shoulders import HeadAndShouldersPatternIndicator
                from backend.technical.patterns.chart.triangle import TrianglePatternIndicator
                
                # Create formatted market data for pattern detectors
                from pandas import DataFrame
                market_data = DataFrame({
                    'open': data['open'].values,
                    'high': data['high'].values,
                    'low': data['low'].values,
                    'close': data['close'].values,
                    'volume': data['tick_volume'].values if 'tick_volume' in data.columns else np.ones_like(data['close'].values)
                })
                
                # Detect double tops and bottoms
                try:
                    double_pattern = DoubleTopBottomPatternIndicator().calculate(market_data)
                    if 'is_pattern' in double_pattern and np.any(double_pattern['is_pattern']):
                        # Get the most recent pattern location
                        pattern_idx = np.where(double_pattern['is_pattern'])[0][-1]
                        pattern_type = double_pattern['pattern_type'][pattern_idx]
                        is_bullish = pattern_type > 0
                        
                        # Calculate strength (70-90 range)
                        strength = 70 + abs(pattern_type) * 20
                        
                        patterns.append({
                            'name': 'Double Bottom' if is_bullish else 'Double Top',
                            'type': 'bullish' if is_bullish else 'bearish',
                            'strength': float(strength),
                            'confidence': float(strength), 
                            'completion_time': datetime.now().isoformat(),
                            'detectedAt': datetime.now().isoformat(),
                            'target_price': float(current_price_data['bid'] * (1.02 if is_bullish else 0.98)) if current_price_data else None
                        })
                        logger.info(f"Detected {'Double Bottom' if is_bullish else 'Double Top'} pattern")
                except Exception as e:
                    logger.error(f"Error detecting double top/bottom patterns: {str(e)}")
                
                # Detect head and shoulders
                try:
                    hs_pattern = HeadAndShouldersPatternIndicator().calculate(market_data)
                    if 'is_pattern' in hs_pattern and np.any(hs_pattern['is_pattern']):
                        # Get the most recent pattern location
                        pattern_idx = np.where(hs_pattern['is_pattern'])[0][-1]
                        pattern_type = hs_pattern['pattern_type'][pattern_idx]
                        is_bullish = pattern_type > 0
                        
                        # Calculate strength (70-90 range)
                        strength = 75 + abs(pattern_type) * 15
                        
                        patterns.append({
                            'name': 'Inverse Head and Shoulders' if is_bullish else 'Head and Shoulders',
                            'type': 'bullish' if is_bullish else 'bearish',
                            'strength': float(strength),
                            'confidence': float(strength),
                            'completion_time': datetime.now().isoformat(),
                            'detectedAt': datetime.now().isoformat(),
                            'target_price': float(current_price_data['bid'] * (1.03 if is_bullish else 0.97)) if current_price_data else None
                        })
                        logger.info(f"Detected {'Inverse Head and Shoulders' if is_bullish else 'Head and Shoulders'} pattern")
                except Exception as e:
                    logger.error(f"Error detecting head and shoulders patterns: {str(e)}")
                
                # Detect triangles
                try:
                    triangle_pattern = TrianglePatternIndicator().calculate(market_data)
                    if 'is_pattern' in triangle_pattern and np.any(triangle_pattern['is_pattern']):
                        # Get the most recent pattern location
                        pattern_idx = np.where(triangle_pattern['is_pattern'])[0][-1]
                        pattern_type = triangle_pattern['pattern_type'][pattern_idx]
                        
                        # Interpret pattern type
                        pattern_names = {
                            1: 'Ascending Triangle',
                            2: 'Descending Triangle',
                            3: 'Symmetrical Triangle'
                        }
                        pattern_name = pattern_names.get(abs(int(pattern_type)), 'Triangle')
                        is_bullish = (pattern_type > 0 and pattern_name != 'Descending Triangle') or (pattern_name == 'Descending Triangle' and pattern_type < 0)
                        
                        # Calculate strength (65-85 range)
                        strength = 65 + abs(pattern_type) * 20
                        
                        patterns.append({
                            'name': pattern_name,
                            'type': 'bullish' if is_bullish else 'bearish',
                            'strength': float(strength),
                            'confidence': float(strength),
                            'completion_time': datetime.now().isoformat(),
                            'detectedAt': datetime.now().isoformat(),
                            'target_price': float(current_price_data['bid'] * (1.02 if is_bullish else 0.98)) if current_price_data else None
                        })
                        logger.info(f"Detected {pattern_name} pattern")
                except Exception as e:
                    logger.error(f"Error detecting triangle patterns: {str(e)}")
                
                # Add patterns to analysis
                if patterns:
                    analysis["patterns"] = patterns
                    logger.info(f"Added {len(patterns)} detected patterns to analysis")
                else:
                    logger.info("No chart patterns detected")
                
            except Exception as e:
                logger.error(f"Error during pattern detection: {str(e)}")
            
            # --- Add price data to the analysis output ---
            try:
                logger.info("Adding OHLC price data to analysis output")
                # Convert DataFrame data to dict of lists for serialization
                price_data = {
                    "open": data['open'].tolist() if 'open' in data.columns else [],
                    "high": data['high'].tolist() if 'high' in data.columns else [],
                    "low": data['low'].tolist() if 'low' in data.columns else [],
                    "close": data['close'].tolist() if 'close' in data.columns else [],
                    "volume": data['tick_volume'].tolist() if 'tick_volume' in data.columns else [],
                    "time": [str(idx) for idx in data.index.tolist()] if isinstance(data.index, pd.DatetimeIndex) else []
                }
                analysis["price_data"] = price_data
                logger.info(f"Added price data with {len(price_data['close'])} data points")
            except Exception as e:
                logger.error(f"Error adding price data to analysis: {e}")
                # Create an empty price_data structure so frontend knows what to expect
                analysis["price_data"] = {
                    "open": [], "high": [], "low": [], "close": [], "volume": [], "time": []
                }
            
            # --- Generate Trading Signal ---
            try:
                logger.info("Generating trading signal recommendation")
                signal_recommendation = generate_signal_recommendation(analysis)
                if signal_recommendation:
                    logger.info(f"Generated {signal_recommendation.get('signal')} signal with {signal_recommendation.get('confidence')}% confidence")
                    analysis["signal_recommendation"] = signal_recommendation
                else:
                    logger.warning("Could not generate trading signal recommendation")
            except Exception as e:
                logger.error(f"Error generating trading signal: {e}")
                # Add a placeholder with error info
                analysis["signal_recommendation"] = {
                    "signal": "ERROR",
                    "error": str(e),
                    "confidence": 0
                }

            # Replace NaN with None before returning
            analysis_cleaned = replace_nan_with_none(analysis)
            logger.debug(f"Final cleaned analysis data being returned: {analysis_cleaned}") # Log final data
            return {"success": True, "analysis": analysis_cleaned}

        except Exception as e:
            logger.exception(f"Exception analyzing {symbol} on {timeframe}: {str(e)}")
            return {"success": False, "message": f"Exception analyzing {symbol} on {timeframe}: {str(e)}"}
