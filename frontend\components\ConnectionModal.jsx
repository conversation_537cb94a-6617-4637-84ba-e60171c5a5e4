import React, { useState, useEffect } from 'react';
import { useNotification } from './Notification';
import '../styles/Modal.css';
import '../styles/ConnectionWizard.css';

// Add onLicenseVerified to props
const ConnectionModal = ({ isOpen, onClose, onSubmit, suggestedPath, onLicenseVerified }) => { 
  const [formData, setFormData] = useState({
    mt5Path: '',
    accountType: 'demo',
    account: '',
    password: '',
    server: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [connectionStatus, setConnectionStatus] = useState('idle'); // idle, testing, success, error
  const [testMessage, setTestMessage] = useState('');
  const notify = useNotification();

  useEffect(() => {
    // Load saved settings if available
    const loadSettings = async () => {
      try {
        // Try to load from localStorage first
        const savedSettings = localStorage.getItem('mt5Settings');
        if (savedSettings) {
          const parsedSettings = JSON.parse(savedSettings);
          setFormData({
            mt5Path: parsedSettings.path || '',
            accountType: parsedSettings.accountType || 'demo',
            account: parsedSettings.login || '',
            // Load password if it exists in saved settings (per user request)
            password: parsedSettings.password || '',
            server: parsedSettings.server || ''
          });
        }

        // If Electron API is available, try to load from there too
        if (window.api && window.api.getSettings) {
          const electronSettings = await window.api.getSettings();
          if (electronSettings) {
            setFormData(prev => ({
              ...prev,
              mt5Path: electronSettings.path || prev.mt5Path,
              accountType: electronSettings.accountType || prev.accountType,
              account: electronSettings.login || prev.account,
              server: electronSettings.server || prev.server
            }));
          }
        }
      } catch (error) {
        console.error('Failed to load settings:', error);
        notify.error('Error', 'Failed to load connection settings');
      }
    };

    if (isOpen) {
      loadSettings().then(() => {
        // After attempting to load saved settings,
        // if mt5Path is still empty and a suggestedPath is available, use it.
        setFormData(currentFormData => {
          if (!currentFormData.mt5Path && suggestedPath) {
            console.log('ConnectionModal: Using suggested MT5 path:', suggestedPath);
            return { ...currentFormData, mt5Path: suggestedPath };
          }
          return currentFormData;
        });
      });
      
      // Reset states
      setIsSubmitting(false);
      setCurrentStep(1);
      setConnectionStatus('idle');
      setTestMessage('');
    }
  }, [isOpen, suggestedPath]); // Add suggestedPath to dependency array

  const handleChange = (e) => {
    const { id, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [id]: value
    }));
  };

  const testConnection = async () => {
    console.log('testConnection function called'); // <-- Add log here
    if (isSubmitting) {
      console.log('testConnection: Already submitting, returning.');
      return;
    }
    setIsSubmitting(true);
    setConnectionStatus('testing');
    setTestMessage('Testing connection to MT5...');

    // Validate MT5 path
    if (formData.mt5Path.trim() === '') {
      setConnectionStatus('error');
      setTestMessage('Please enter the MT5 Terminal path');
      setIsSubmitting(false);
      return;
    }

    // Check if path exists (basic validation)
    if (!isValidPath(formData.mt5Path)) {
      setConnectionStatus('error');
      setTestMessage('MT5 Terminal path should point to an executable file (.exe)');
      setIsSubmitting(false);
      return;
    }

    try {
      console.log('testConnection: Attempting fetch to /api/connection/test_path'); // <-- Add log here
      // Test connection to MT5 terminal
      const response = await fetch('http://localhost:5001/api/connection/test_path', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          path: formData.mt5Path
        })
      });

      // --- Added Detailed Logging ---
      console.log('Test Path Response Status:', response.status);
      console.log('Test Path Response OK:', response.ok);
      // Log headers (useful for CORS debugging)
      console.log('Test Path Response Headers:', Object.fromEntries(response.headers.entries()));
      // Try to get raw text response first
      const responseText = await response.text();
      console.log('Test Path Response Text:', responseText);
      // --- End Added Logging ---

      // Now try to parse the text as JSON
      let data = null;
      try {
        data = JSON.parse(responseText);
        console.log('Test Path Parsed Data:', data);
      } catch (parseError) {
        console.error('Failed to parse Test Path response as JSON:', parseError);
        setConnectionStatus('error');
        setTestMessage('Received invalid response from server.');
        setIsSubmitting(false);
        return; // Exit if parsing failed
      }
      // --- End JSON Parsing ---


      if (response.ok && data && data.status === 'success') { // Check data is not null
        console.log('testConnection: Test successful.'); // <-- Add log here
        setConnectionStatus('success');
        setTestMessage('MT5 Terminal found! You can proceed to the next step.');
      } else {
        console.log('testConnection: Test failed or response not OK.', { ok: response.ok, data }); // <-- Add log here
        setConnectionStatus('error');
        setTestMessage(data?.error || 'Failed to validate MT5 Terminal path'); // Use optional chaining
      }
    } catch (error) {
      // This catches errors during the fetch itself (network, CORS, etc.) or other unexpected errors
      console.error('Connection test error (fetch or other):', error); // <-- Modify log here
      setConnectionStatus('error');
      // Provide a more specific error message if possible
      let errorMessage = 'Failed to connect to the backend server.';
      if (error instanceof TypeError && error.message === 'Failed to fetch') {
        errorMessage = 'Network error: Could not reach the backend server at http://localhost:5001. Is it running?';
      } else if (error.message) {
        errorMessage = error.message;
      }
      setTestMessage(errorMessage);
    } finally {
      console.log('testConnection: Resetting isSubmitting.'); // <-- Add log here
      setIsSubmitting(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (isSubmitting) {
      console.log('Submit already in progress, ignoring request');
      return;
    }

    setIsSubmitting(true);
    console.log('Starting connection submission process');

    // Validate form
    if (!formData.account || !formData.password || !formData.server) {
      console.warn('Form validation failed - missing required fields');
      notify.warning('Validation Error', 'Please fill in all required fields');
      setIsSubmitting(false);
      return;
    }

    try {
      // Show connecting status
      notify.info('Verifying Account', 'Verifying your account details...');
      console.log('Preparing for account verification');

      const accountNumber = formData.account;
      const serverName = formData.server;

      // Call Verification Endpoint
      try {
        const verificationResponse = await fetch('http://localhost:5001/api/verify_account', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            account_number: accountNumber,
            server_name: serverName,
          }),
        });

        const verificationData = await verificationResponse.json();

        if (!verificationResponse.ok || !verificationData.authorized) {
          let errorMessage = verificationData.message || 'Account verification failed. Please check your details or subscription.';
          if (verificationData.reason === 'not_found') {
            errorMessage = 'Account not found or server name mismatch. Please verify your inputs.';
          } else if (verificationData.reason === 'inactive_or_expired') {
            errorMessage = 'Your account is inactive or your plan has expired.';
          }
          notify.error('Verification Failed', errorMessage);
          console.error('Verification Failed:', errorMessage);
          setIsSubmitting(false);
          return; // Stop further execution
        }
        console.log('Account verification successful', verificationData);
        // Use info for general success, success for actual connection later
        notify.info('Verification Complete', verificationData.message || 'Account details processed.'); 
        
        // Call the new callback with license details
        if (onLicenseVerified && typeof onLicenseVerified === 'function') {
          onLicenseVerified({
            userName: verificationData.user_name,
            planType: verificationData.plan_type,
            planEndDate: verificationData.plan_end_date,
            status: verificationData.status
          });
        }

        // If authorized, proceed with MT5 connection
        notify.info('Connecting', 'Attempting to connect to MT5...');
        console.log('Preparing connection data for submission');

        const connectionData = {
          login: formData.account,
          password: formData.password,
          server: formData.server,
          path: formData.mt5Path,
          accountType: formData.accountType
        };

        console.log('Submitting connection data to parent component', {
          login: connectionData.login,
          server: connectionData.server,
          path: connectionData.path,
          accountType: connectionData.accountType
        });

        // Save connection settings before attempting connection
        try {
          const settingsToSave = {
            path: connectionData.path,
            login: connectionData.login,
            server: connectionData.server,
            accountType: connectionData.accountType,
            password: connectionData.password // <-- Add password here
          };
          console.log("Saving settings (including password) to localStorage:", settingsToSave);
          localStorage.setItem('mt5Settings', JSON.stringify(settingsToSave));
          console.log('Settings saved to localStorage');

          // Also save to Electron API if available
          if (window.api && window.api.saveSettings) {
            await window.api.saveSettings(settingsToSave);
            console.log('Settings saved via Electron API to user_mt5_settings.json');
          }
        } catch (saveError) {
          console.error('Failed to save settings:', saveError);
        }

        // Set a timeout to prevent the connection from hanging indefinitely
        const connectionTimeout = setTimeout(() => {
          console.warn('Connection attempt timed out after 15 seconds');
          notify.warning('Connection Timeout', 'The connection attempt is taking longer than expected. You can continue waiting or cancel.');
        }, 15000);

        try {
          // Call the onSubmit function (which will make the API call)
          await onSubmit(connectionData);
          console.log('Connection submission completed');
          clearTimeout(connectionTimeout);
        } catch (connectionError) {
          clearTimeout(connectionTimeout);
          console.error('Connection error:', connectionError);
          notify.error('Connection Error', connectionError.message || 'Failed to connect to MT5');
          // Don't rethrow - we want to handle the error here
        }

      } catch (error) {
        console.error('Error during account verification:', error);
        notify.error('Verification Error', 'An unexpected error occurred during account verification.');
        setIsSubmitting(false);
        return;
      }

    } catch (error) {
      // This catch block is for errors outside the verification and connection try blocks,
      // though most errors should be caught within them.
      console.error('Outer error in handleSubmit:', error);
      notify.error('Connection Error', error.message || 'An unexpected error occurred.');
    } finally {
      console.log('Connection submission process completed, resetting isSubmitting');
      setIsSubmitting(false);
    }
  };

  const nextStep = () => {
    if (currentStep === 1 && connectionStatus !== 'success') {
      testConnection();
      return;
    }
    setCurrentStep(prev => prev + 1);
  };

  const prevStep = () => {
    setCurrentStep(prev => prev - 1);
  };

  // Add a helper function to check if the path is valid
  const isValidPath = (path) => {
    // Basic validation - could be enhanced with more checks
    return path && path.trim() !== '' && path.toLowerCase().includes('metatrader') && path.toLowerCase().endsWith('.exe');
  }

  // We still return null if not open, but the class logic is handled below
  // if (!isOpen) return null;

  // If not open, don't render anything
  if (!isOpen) return null;

  return (
    // Always add the 'open' class since we're only rendering when open
    <div className="modal-overlay open">
      <div className="modal-container wizard-container">
        <div className="modal-header">
          <h2>Connect to MT5</h2>
          <button className="modal-close" onClick={onClose}>&times;</button>
        </div>

        <div className="wizard-progress">
          <div className="wizard-steps">
            <div className={`wizard-step ${currentStep >= 1 ? 'active' : ''} ${currentStep > 1 ? 'completed' : ''}`}>
              <div className="step-number">1</div>
              <div className="step-label">Terminal</div>
            </div>
            <div className="step-connector"></div>
            <div className={`wizard-step ${currentStep >= 2 ? 'active' : ''} ${currentStep > 2 ? 'completed' : ''}`}>
              <div className="step-number">2</div>
              <div className="step-label">Account</div>
            </div>
            <div className="step-connector"></div>
            <div className={`wizard-step ${currentStep >= 3 ? 'active' : ''}`}>
              <div className="step-number">3</div>
              <div className="step-label">Connect</div>
            </div>
          </div>
        </div>

        <div className="wizard-content">
          {currentStep === 1 && (
            <div className="wizard-step-content">
              <h3>MT5 Terminal Location</h3>
              <p className="step-description">Please specify the path to your MetaTrader 5 terminal executable file.</p>

              <div className="form-group">
                <label htmlFor="mt5Path">MT5 Terminal Path</label>
                <div className="input-with-button">
                  <input
                    type="text"
                    id="mt5Path"
                    value={formData.mt5Path}
                    onChange={handleChange}
                    placeholder="C:\Program Files\MetaTrader 5\terminal64.exe"
                  />
                  {/* Use btn and btn-secondary classes */}
                  <button
                    type="button"
                    className="button secondary"
                    onClick={testConnection}
                    disabled={isSubmitting}
                  >
                    Test
                  </button>
                </div>
              </div>

              {connectionStatus !== 'idle' && (
                <div className={`connection-status-message ${connectionStatus}`}>
                  {connectionStatus === 'testing' && <div className="spinner"></div>}
                  {connectionStatus === 'success' && <div className="success-icon">✓</div>}
                  {connectionStatus === 'error' && <div className="error-icon">✗</div>}
                  <p>{testMessage}</p>
                </div>
              )}
            </div>
          )}

          {currentStep === 2 && (
            <div className="wizard-step-content">
              <h3>MT5 Account Details</h3>
              <p className="step-description">Enter your MetaTrader 5 account credentials.</p>

              <div className="form-group">
                <label htmlFor="accountType">Account Type</label>
                <select
                  id="accountType"
                  value={formData.accountType}
                  onChange={handleChange}
                >
                  <option value="demo">Demo Account</option>
                  <option value="real">Real Account</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="account">Login ID</label>
                <input
                  type="text"
                  id="account"
                  value={formData.account}
                  onChange={handleChange}
                  placeholder="MT5 Login ID"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="server">Server</label>
                <input
                  type="text"
                  id="server"
                  value={formData.server}
                  onChange={handleChange}
                  placeholder="MT5 Server"
                  required
                />
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div className="wizard-step-content">
              <h3>Connect to MT5</h3>
              <p className="step-description">Enter your password to connect to your MT5 account.</p>

              <div className="form-group">
                <label htmlFor="password">Password</label>
                <input
                  type="password"
                  id="password"
                  value={formData.password}
                  onChange={handleChange}
                  placeholder="MT5 Password"
                  required
                  autoComplete="current-password"
                />
              </div>

              <div className="connection-summary">
                <h4>Connection Summary</h4>
                <div className="summary-item">
                  <span className="summary-label">Terminal:</span>
                  <span className="summary-value">{formData.mt5Path}</span>
                </div>
                <div className="summary-item">
                  <span className="summary-label">Account Type:</span>
                  <span className="summary-value">{formData.accountType === 'demo' ? 'Demo Account' : 'Real Account'}</span>
                </div>
                <div className="summary-item">
                  <span className="summary-label">Login ID:</span>
                  <span className="summary-value">{formData.account}</span>
                </div>
                <div className="summary-item">
                  <span className="summary-label">Server:</span>
                  <span className="summary-value">{formData.server}</span>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="wizard-actions">
          {/* Cancel button - always visible */}
          <button
            type="button"
            className="button secondary"
            onClick={onClose}
          >
            Cancel
          </button>

          {/* Back button - visible on steps 2 and 3 */}
          {currentStep > 1 && (
            <button
              type="button"
              className="button secondary"
              onClick={prevStep}
              disabled={isSubmitting}
            >
              Back
            </button>
          )}

          {/* Next/Connect button */}
          {currentStep < 3 ? (
            <button
              type="button"
              className="button primary"
              onClick={nextStep}
              disabled={isSubmitting || (currentStep === 1 && connectionStatus !== 'success')}
            >
              {isSubmitting && currentStep === 1 ? 'Testing...' : 'Next'}
            </button>
          ) : (
            <button
              type="button"
              className="button primary"
              onClick={handleSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Connecting...' : 'Connect'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ConnectionModal;
