const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld(
    'api', {
        // Settings operations
        getSettings: async () => {
            return await ipc<PERSON><PERSON>er.invoke('get-settings');
        },
        saveSettings: async (settings) => {
            return await ipcRenderer.invoke('save-settings', settings);
        },
        
        // MT5 connection operations
        getStatus: async () => {
            return await ipcRenderer.invoke('get-status');
        },
        connectMT5: async (connectionParams, paramsToSave) => {
            return await ipcRenderer.invoke('connect-mt5', connectionParams, paramsToSave);
        },
        disconnectMT5: async () => {
            return await ipcRenderer.invoke('disconnect-mt5');
        },

        // New handler for suggested path
        getSuggestedMT5Path: async () => {
            return await ipc<PERSON><PERSON><PERSON>.invoke('get-suggested-mt5-path');
        }
    }
);
