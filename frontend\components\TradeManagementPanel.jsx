import React, { useState, useEffect } from 'react';
import '../styles/trade-management.css';

const TradeManagementPanel = ({ positionsCount, refreshMonitoringData }) => {
  const [loading, setLoading] = useState(false);
  const [resultMessage, setResultMessage] = useState('');
  const [resultType, setResultType] = useState(''); // 'success' or 'error'
  const [slValue, setSlValue] = useState(100);
  const [tpValue, setTpValue] = useState(200);
  const [isAbsoluteMode, setIsAbsoluteMode] = useState(false); // false = points mode, true = absolute price mode

  // Trailing stop configuration
  const [trailingStopMode, setTrailingStopMode] = useState('percent'); // 'percent' or 'pips'
  const [trailingStopValue, setTrailingStopValue] = useState(0.5); // Default from autonomous config
  const [autonomousConfig, setAutonomousConfig] = useState(null);
  const [configLoading, setConfigLoading] = useState(true);

  // Fetch autonomous configuration on component mount
  useEffect(() => {
    const fetchAutonomousConfig = async () => {
      try {
        setConfigLoading(true);
        const response = await fetch('http://localhost:5001/api/autonomous/status');
        if (response.ok) {
          const data = await response.json();
          setAutonomousConfig(data.config);

          // Set default trailing stop value from config
          const trailingStopPercent = data.config?.trailing_stop_distance_percent || 0.5;
          setTrailingStopValue(trailingStopPercent);
        } else {
          console.error('Failed to fetch autonomous config');
        }
      } catch (error) {
        console.error('Error fetching autonomous config:', error);
      } finally {
        setConfigLoading(false);
      }
    };

    fetchAutonomousConfig();
  }, []);

  // Function to call API endpoints
  const callTradeOperation = async (endpoint) => {
    setLoading(true);
    setResultMessage('');
    setResultType('');

    try {
      // Map endpoints to appropriate API routes
      let apiUrl = '';
      let payload = {};

      // Map each operation to the correct API endpoint
      switch(endpoint) {
        case '/position/breakeven/all':
          apiUrl = 'http://localhost:5001/api/trade-ops/position/breakeven/all';
          payload = {};
          break;
        case '/position/trailingstop/all':
          apiUrl = 'http://localhost:5001/api/trade-ops/position/trailingstop/all';
          // Use configuration-based values instead of hardcoded
          if (trailingStopMode === 'percent') {
            payload = {
              trailing_distance_percent: trailingStopValue,
              mode: 'percent'
            };
          } else {
            payload = {
              trailing_distance: trailingStopValue,
              mode: 'pips'
            };
          }
          break;
        case '/position/close/profitable':
          apiUrl = 'http://localhost:5001/api/trade-ops/position/close/profitable';
          payload = {};
          break;
        case '/position/close/losing':
          apiUrl = 'http://localhost:5001/api/trade-ops/position/close/losing';
          payload = {};
          break;
        case '/position/close/buy':
          apiUrl = 'http://localhost:5001/api/trade-ops/position/close/buy';
          payload = {};
          break;
        case '/position/close/sell':
          apiUrl = 'http://localhost:5001/api/trade-ops/position/close/sell';
          payload = {};
          break;
        case '/position/close/all':
          apiUrl = 'http://localhost:5001/api/trade-ops/position/close/all';
          payload = {};
          break;
        default:
          console.error(`Unknown endpoint: ${endpoint}`);
          setResultMessage(`Error: Unknown operation`);
          setResultType('error');
          setLoading(false);
          return;
      }

      console.log(`Calling API endpoint: ${apiUrl} with payload:`, payload);
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      const data = await response.json();

      if (response.ok) {
        setResultMessage(`${data.message}`);
        setResultType('success');
        // Refresh monitoring data after successful operation
        refreshMonitoringData();
      } else {
        setResultMessage(`Error: ${data.error || 'Unknown error occurred'}`);
        setResultType('error');
      }
    } catch (error) {
      setResultMessage(`Error: ${error.message}`);
      setResultType('error');
    } finally {
      setLoading(false);
    }
  };

  // Function to handle bulk modify operation
  const handleBulkModify = async (type) => {
    setLoading(true);
    setResultMessage('');
    setResultType('');

    try {
      // Use the trade-ops API endpoint
      let endpoint = '/api/trade-ops/position/modify/bulk';
      let payload = {};

      // Set payload based on operation type and mode (points or absolute price)
      if (isAbsoluteMode) {
        // Absolute price mode
        if (type === 'sl') {
          payload = {
            sl_price: parseFloat(slValue)
          };
        } else if (type === 'tp') {
          payload = {
            tp_price: parseFloat(tpValue)
          };
        } else if (type === 'both') {
          payload = {
            sl_price: parseFloat(slValue),
            tp_price: parseFloat(tpValue)
          };
        }
      } else {
        // Points mode (relative to current price)
        if (type === 'sl') {
          payload = {
            sl_points: parseInt(slValue)
          };
        } else if (type === 'tp') {
          payload = {
            tp_points: parseInt(tpValue)
          };
        } else if (type === 'both') {
          payload = {
            sl_points: parseInt(slValue),
            tp_points: parseInt(tpValue)
          };
        }
      }

      const response = await fetch(`http://localhost:5001${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      const data = await response.json();

      if (response.ok) {
        setResultMessage(`${data.message}`);
        setResultType('success');
        // Refresh monitoring data after successful operation
        refreshMonitoringData();
      } else {
        setResultMessage(`Error: ${data.error || 'Unknown error occurred'}`);
        setResultType('error');
      }
    } catch (error) {
      setResultMessage(`Error: ${error.message}`);
      setResultType('error');
    } finally {
      setLoading(false);
    }
  };

  const isDisabled = positionsCount === 0 || loading;

  return (
    <div className="trade-management-panel">
      <h4>Trade Management</h4>

      <div className="trade-ops-grid">
        {/* Risk Management Section */}
        <div className="trade-ops-section">
          <h5>Risk Management</h5>
          <button
            className="trade-ops-button neutral"
            onClick={() => callTradeOperation('/position/breakeven/all')}
            disabled={isDisabled}
          >
            ⚙️ Set All To Break-Even
          </button>
        </div>

        {/* Trailing Stop Configuration Section */}
        <div className="trade-ops-section">
          <h5>Trailing Stop Configuration</h5>

          {configLoading ? (
            <div className="config-loading">Loading config...</div>
          ) : (
            <>
              {/* Mode Toggle Switch for Trailing Stop */}
              <div className="mode-toggle">
                <span className={trailingStopMode === 'percent' ? 'active-mode' : ''}>Percent Mode</span>
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={trailingStopMode === 'pips'}
                    onChange={() => {
                      const newMode = trailingStopMode === 'percent' ? 'pips' : 'percent';
                      setTrailingStopMode(newMode);
                      // Reset to appropriate default value
                      if (newMode === 'percent') {
                        setTrailingStopValue(autonomousConfig?.trailing_stop_distance_percent || 0.5);
                      } else {
                        setTrailingStopValue(100); // Default 100 pips
                      }
                    }}
                  />
                  <span className="slider round"></span>
                </label>
                <span className={trailingStopMode === 'pips' ? 'active-mode' : ''}>Pips Mode</span>
              </div>

              <div className="input-button-group">
                <div className="input-with-label">
                  <input
                    type="number"
                    value={trailingStopValue}
                    onChange={(e) => setTrailingStopValue(e.target.value)}
                    placeholder={trailingStopMode === 'percent' ? "Enter percentage" : "Enter pips"}
                    step={trailingStopMode === 'percent' ? "0.1" : "1"}
                    min={trailingStopMode === 'percent' ? "0.1" : "1"}
                  />
                  <small className="input-mode-label">
                    {trailingStopMode === 'percent' ? 'Trailing %' : 'Trailing Pips'}
                  </small>
                </div>
                <button
                  className="trade-ops-button neutral"
                  onClick={() => callTradeOperation('/position/trailingstop/all')}
                  disabled={isDisabled || trailingStopValue === '' || trailingStopValue <= 0}
                >
                  📈 Set Trailing Stop
                </button>
              </div>

              {autonomousConfig && (
                <div className="config-info">
                  <small>
                    From autonomous config: {autonomousConfig.trailing_stop_distance_percent || 0.5}%
                    (Trigger: {autonomousConfig.trailing_stop_trigger_percent || 1.0}%)
                  </small>
                </div>
              )}
            </>
          )}
        </div>

        {/* SL/TP Management Section */}
        <div className="trade-ops-section">
          <h5>SL/TP Management</h5>

          {/* Mode Toggle Switch */}
          <div className="mode-toggle">
            <span className={!isAbsoluteMode ? 'active-mode' : ''}>Points Mode</span>
            <label className="switch">
              <input
                type="checkbox"
                checked={isAbsoluteMode}
                onChange={() => {
                  setIsAbsoluteMode(!isAbsoluteMode);
                  // Reset values when switching modes to avoid confusion
                  if (isAbsoluteMode) {
                    // Switching to points mode
                    setSlValue(100);
                    setTpValue(200);
                  } else {
                    // Switching to absolute price mode
                    // Use empty string to prompt user to enter a valid price
                    setSlValue('');
                    setTpValue('');
                  }
                }}
              />
              <span className="slider round"></span>
            </label>
            <span className={isAbsoluteMode ? 'active-mode' : ''}>Absolute Price</span>
          </div>

          <div className="input-button-group">
            <div className="input-with-label">
              <input
                type="number"
                value={slValue}
                onChange={(e) => setSlValue(e.target.value)}
                placeholder={isAbsoluteMode ? "Enter exact price" : "Distance in points"}
                step={isAbsoluteMode ? "0.00001" : "1"}
              />
              <small className="input-mode-label">{isAbsoluteMode ? 'SL Price' : 'SL Points'}</small>
            </div>
            <button
              onClick={() => handleBulkModify('sl')}
              disabled={isDisabled || slValue === ''}
              className="sl-button"
            >
              Set SL
            </button>
          </div>
          <div className="input-button-group">
            <div className="input-with-label">
              <input
                type="number"
                value={tpValue}
                onChange={(e) => setTpValue(e.target.value)}
                placeholder={isAbsoluteMode ? "Enter exact price" : "Distance in points"}
                step={isAbsoluteMode ? "0.00001" : "1"}
              />
              <small className="input-mode-label">{isAbsoluteMode ? 'TP Price' : 'TP Points'}</small>
            </div>
            <button
              onClick={() => handleBulkModify('tp')}
              disabled={isDisabled || tpValue === ''}
              className="tp-button"
            >
              Set TP
            </button>
          </div>
          <button
            className="trade-ops-button both-button"
            onClick={() => handleBulkModify('both')}
            disabled={isDisabled || slValue === '' || tpValue === ''}
          >
            <span className="icon">⚙️</span> Set Both SL/TP
          </button>
        </div>

        {/* Profit/Loss Management Section */}
        <div className="trade-ops-section">
          <h5>Profit/Loss Management</h5>
          <button
            className="trade-ops-button positive"
            onClick={() => callTradeOperation('/position/close/profitable')}
            disabled={isDisabled}
          >
            💰 Close All Profitable
          </button>
          <button
            className="trade-ops-button dangerous"
            onClick={() => callTradeOperation('/position/close/losing')}
            disabled={isDisabled}
          >
            📉 Close All Losing
          </button>
        </div>

        {/* Position Type Management Section */}
        <div className="trade-ops-section">
          <h5>Position Type Management</h5>
          <button
            className="trade-ops-button neutral"
            onClick={() => callTradeOperation('/position/close/buy')}
            disabled={isDisabled}
          >
            🔺 Close All Buy Positions
          </button>
          <button
            className="trade-ops-button neutral"
            onClick={() => callTradeOperation('/position/close/sell')}
            disabled={isDisabled}
          >
            🔻 Close All Sell Positions
          </button>
          <button
            className="trade-ops-button dangerous"
            onClick={() => callTradeOperation('/position/close/all')}
            disabled={isDisabled}
          >
            ❌ Close All Positions
          </button>
        </div>
      </div>

      {/* Results message */}
      {resultMessage && (
        <div className={`results-message ${resultType}`}>
          {resultMessage}
        </div>
      )}
    </div>
  );
};

export default TradeManagementPanel;
