from flask import Blueprint, request, jsonify
from datetime import datetime, timezone
import logging
import time
from typing import Dict, Any, <PERSON>ple
import os
import json
# MetaTrader5 is imported through MT5Integration

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) # This line helps find 'backend' in dev
from ..mt5_integration import MT5Integration # Relative import from parent (backend)
from ..mt5_integration.exceptions import MT5Error, MT5ConnectionError, MT5AuthenticationError # Relative import
from ..credential_manager import CredentialsManager, CredentialError # Relative import from parent (backend)

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
connection_bp = Blueprint('connection', __name__)

# Initialize global instances
credentials_manager = CredentialsManager()

# mt5_instance will be retrieved from Flask app config in each request

# Get the root directory of the application
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Path to user settings file
# This path logic should ideally align perfectly with ConnectionComponent's USER_SETTINGS_PATH
# For now, we'll replicate a similar logic, but this is a point for future centralization.
data_root_api = os.getenv('APPDATA')
if not data_root_api:
    data_root_api = os.path.expanduser("~")
API_DATA_DIR = os.path.join(data_root_api, 'GarudaAlgo')
# Ensure the directory exists (important if this code runs before ConnectionComponent creates it)
os.makedirs(API_DATA_DIR, exist_ok=True) 
USER_SETTINGS_PATH = os.path.join(API_DATA_DIR, "user_mt5_settings.json")

# DEFAULT_SETTINGS_PATH is no longer used as per previous refactoring.
# DEFAULT_SETTINGS_PATH = os.path.join(ROOT_DIR, "mt5_settings.json") 

# Add debug logging for settings paths
logger.info(f"[connection.py] User settings path: {USER_SETTINGS_PATH}")
# logger.info(f"[connection.py] Default settings path: {DEFAULT_SETTINGS_PATH}") # Removed

def error_response(message: str, status_code: int = 400) -> Tuple[Dict[str, Any], int]:
    """Helper function to create error responses"""
    return {
        "status": "error",
        "error": message
    }, status_code

@connection_bp.before_request
def check_server_status():
    """Ensure server is ready to handle requests"""
    try:
        # Skip check for specific endpoints that don't require MT5 connection
        if request.endpoint in ['connection.test_mt5_path', 'connection.reload_settings', 'connection.get_current_settings']: # Added get_current_settings
            return None

        from flask import current_app
        mt5_instance = current_app.config.get('MT5_INSTANCE')

        if not mt5_instance:
            logger.error("MT5 instance not initialized")
            return error_response("MT5 service not initialized", 503)

        # Check if MT5 is running and connected
        try:
            status = mt5_instance.get_connection_info()
            logger.info(f"Connection status check: {status}")

            # Don't return an error response, just log the status
            # This allows the frontend to handle the connection status
            if not status or not status.get('connected', False):
                logger.warning(f"MT5 not connected. Status: {status}")
                # Continue processing instead of returning an error
        except Exception as status_err:
            logger.error(f"Error getting MT5 connection info: {status_err}")
            # Continue processing instead of returning an error

    except Exception as e:
        logger.exception("Server status check failed")
        return error_response(f"Service unavailable: {str(e)}", 503)

@connection_bp.route('/connect', methods=['POST'])
def connect():
    """
    Establish connection to MT5 terminal using provided credentials.

    Expected request body:
    {
        "account": "123456",
        "password": "secure_pass",
        "server": "MetaQuotes-Demo"
    }
    """
    try:
        from flask import current_app
        mt5_instance = current_app.config.get('MT5_INSTANCE')

        if not mt5_instance:
            logger.error("MT5 instance not initialized")
            return error_response("MT5 service not initialized", 503)

        data = request.get_json()
        if not data:
            return error_response("No data provided", 400)

        # Validate required fields
        required_fields = ['account', 'password', 'server']
        if not all(field in data for field in required_fields):
            return error_response("Missing required fields: account, password, server", 400)

        # Store credentials securely
        try:
            credentials_manager.save_credentials(
                account=str(data['account']),
                password=data['password'],
                server=data['server']
            )
        except CredentialError as e:
            logger.error(f"Failed to store credentials: {e}")
            return error_response(f"Failed to store credentials: {str(e)}", 500)

        # Attempt MT5 connection
        try:
            # Include path parameter if provided
            path = data.get('path')
            result = mt5_instance.initialize(
                login=int(data['account']),
                password=data['password'],
                server=data['server'],
                path=path
            )

            if result['success']:
                # Get detailed account info for response
                if mt5_instance.is_connected():
                    # Get connection info which includes enhanced account details
                    connection_info = mt5_instance.get_connection_info()

                    return jsonify({
                        "status": "connected",
                        "account_info": connection_info.get("account_info", {
                            "balance": 0.0,
                            "equity": 0.0
                        })
                    }), 200
                else:
                    # Fallback if connection was lost immediately after initialization
                    return jsonify({
                        "status": "connected",
                        "account_info": {
                            "balance": 0.0,
                            "equity": 0.0
                        }
                    }), 200

        except MT5AuthenticationError as e:
            logger.error(f"Authentication failed: {e}")
            return error_response(str(e), 401)
        except MT5ConnectionError as e:
            logger.error(f"Connection failed: {e}")
            return error_response(str(e), 503)
        except MT5Error as e:
            logger.error(f"MT5 error: {e}")
            return error_response(str(e), 500)

    except Exception as e:
        logger.exception("Unexpected error in connect endpoint")
        return error_response(f"Internal server error: {str(e)}", 500)

@connection_bp.route('/disconnect', methods=['POST'])
def disconnect():
    """Terminate connection to MT5 terminal"""
    try:
        from flask import current_app
        mt5_instance = current_app.config.get('MT5_INSTANCE')

        if not mt5_instance:
            logger.error("MT5 instance not initialized")
            return error_response("MT5 service not initialized", 503)

        # Always allow disconnect attempts, even if not connected
        if not mt5_instance.is_connected():
            logger.info("Disconnect requested but no active connection found. Returning success anyway.")
            return jsonify({
                "status": "disconnected",
                "message": "Already disconnected"
            }), 200

        result = mt5_instance.disconnect()

        if result['success']:
            return jsonify({
                "status": "disconnected"
            }), 200
        else:
            return error_response(result['message'], 500)

    except Exception as e:
        logger.exception("Unexpected error in disconnect endpoint")
        return error_response(f"Internal server error: {str(e)}", 500)

@connection_bp.route('/connection_status', methods=['GET'])
def get_connection_status():
    """Get detailed MT5 connection status"""
    try:
        from flask import current_app
        mt5_instance = current_app.config.get('MT5_INSTANCE')

        if not mt5_instance:
            logger.error("MT5 instance not initialized")
            return error_response("MT5 service not initialized", 503)

        start_time = time.time()

        # Get comprehensive status
        status = mt5_instance.get_connection_info()

        # Add response timing
        status["_debug"] = {
            "response_time_ms": round((time.time() - start_time) * 1000, 2)
        }

        # Always return 200 OK for the status endpoint, even if not connected
        # This allows the frontend to display the actual connection state
        return jsonify(status), 200

    except Exception as e:
        logger.exception("Failed to get connection status")
        return error_response(f"Internal server error: {str(e)}", 500)

@connection_bp.route('/test_path', methods=['POST'])
def test_mt5_path():
    """Test if MT5 terminal exists at the specified path"""
    try:
        data = request.get_json()
        if not data or 'path' not in data:
            return error_response("Missing required parameter: path")

        path = data['path']

        # Basic validation
        if not path or not path.strip():
            return error_response("Path cannot be empty")

        # Check if file exists
        if not os.path.exists(path):
            return error_response(f"File not found: {path}")

        # Check if it's a file (not a directory)
        if not os.path.isfile(path):
            return error_response(f"{path} is not a file")

        # Check if it looks like an MT5 terminal
        if not ('metatrader' in path.lower() and path.lower().endswith('.exe')):
            return error_response(f"{path} does not appear to be a MetaTrader terminal")

        return jsonify({
            "status": "success",
            "message": "Valid MT5 terminal path"
        })

    except Exception as e:
        logger.error(f"Error testing MT5 path: {e}")
        return error_response(f"Error testing path: {str(e)}", 500)

@connection_bp.route('/switch_account', methods=['POST'])
def switch_account():
    """Switch to a different MT5 account"""
    try:
        data = request.get_json()
        if not data:
            return error_response("No data provided", 400)

        # Validate required fields
        required_fields = ['account', 'password', 'server']
        if not all(field in data for field in required_fields):
            return error_response("Missing required fields: account, password, server", 400)

        # Get MT5 instance from app config
        from flask import current_app
        mt5_instance = current_app.config.get('MT5_INSTANCE')

        if not mt5_instance:
            logger.error("MT5 instance not initialized")
            return error_response("MT5 service not initialized", 503)

        # Log the action (without sensitive data)
        logger.info(f"Account switch request received for account {data['account']} on server {data['server']}")

        # Save the new credentials to user_mt5_settings.json
        try:
            # Create settings object
            settings = {
                "login": int(data['account']),
                "password": data['password'],
                "server": data['server']
            }

            # Add path if provided
            if 'path' in data and data['path']:
                settings['path'] = data['path']

            # Ensure the settings directory exists
            settings_dir = os.path.dirname(USER_SETTINGS_PATH)
            if not os.path.exists(settings_dir):
                os.makedirs(settings_dir)

            # Save to file
            with open(USER_SETTINGS_PATH, 'w') as f:
                json.dump(settings, f, indent=4)

            logger.info(f"Saved new account settings to {USER_SETTINGS_PATH}")
        except Exception as e:
            logger.error(f"Failed to save account settings: {e}")
            return error_response(f"Failed to save account settings: {str(e)}", 500)

        # Ensure proper disconnection from previous session
        try:
            # Force a complete shutdown and reconnection
            disconnect_result = mt5_instance.disconnect()
            logger.info(f"Disconnected from previous MT5 session: {disconnect_result}")

            # Add a small delay to ensure complete disconnection
            import time
            time.sleep(1)
        except Exception as e:
            logger.warning(f"Error disconnecting from MT5: {e}")
            # Continue anyway - we'll try to reconnect

        # Connect to the new account directly (don't rely on settings file)
        try:
            # Log the connection attempt (without password)
            logger.info(f"Attempting to connect to account {data['account']} on server {data['server']}")

            init_result = mt5_instance.initialize(
                login=int(data['account']),
                password=data['password'],
                server=data['server'],
                path=data.get('path')
            )

            if init_result.get("success", False):
                # Get connection info which includes enhanced account details
                connection_info = mt5_instance.get_connection_info()
                logger.info(f"Successfully switched to account {data['account']} on server {data['server']}")
                return jsonify({
                    "status": "connected",  # Use 'connected' for consistency with other endpoints
                    "message": f"Successfully switched to account {data['account']} on server {data['server']}",
                    "account_info": connection_info.get("account_info", {})
                })
            else:
                error_msg = init_result.get("message", "Unknown error")
                logger.error(f"Failed to switch account: {error_msg}")
                return error_response(f"Failed to switch account: {error_msg}")
        except Exception as e:
            logger.error(f"Error switching account: {e}")
            return error_response(f"Error switching account: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error in switch_account: {e}")
        return error_response(f"Unexpected error: {str(e)}", 500)

@connection_bp.route('/reload_settings', methods=['POST'])
def reload_settings():
    """Force the backend to reload MT5 settings"""
    try:
        # Only use USER_SETTINGS_PATH
        settings_path = USER_SETTINGS_PATH

        # Log the action
        logger.info(f"Reload settings request received. Attempting to use: {settings_path}")

        # Get MT5 instance from app config
        from flask import current_app
        mt5_instance = current_app.config.get('MT5_INSTANCE')

        if not mt5_instance:
            logger.error("MT5 instance not initialized")
            return error_response("MT5 service not initialized", 503)

        # Check if the user settings file exists
        if not os.path.exists(settings_path):
            logger.warning(f"User settings file not found at {settings_path}. Cannot reload settings.")
            return error_response(f"User settings file not found. Please connect and save settings first.", 404)

        # Log the settings file content for debugging (if it exists)
        try:
            with open(settings_path, 'r') as f:
                settings_content = f.read()
                # Mask password for security
                # Attempt to parse JSON to safely access password
                try:
                    temp_settings = json.loads(settings_content)
                    if "password" in temp_settings:
                        temp_settings["password"] = "*****"
                    masked_content = json.dumps(temp_settings)
                except json.JSONDecodeError:
                    # If not valid JSON, log as is (or a portion)
                    masked_content = settings_content[:200] + "..." if len(settings_content) > 200 else settings_content
                logger.info(f"Settings file content from {settings_path}: {masked_content}")
        except Exception as e:
            logger.warning(f"Could not read settings file {settings_path}: {e}")
            # Proceed, as the initialize method will try to load it again

        # Ensure proper disconnection from previous session
        try:
            # Force a complete shutdown and reconnection
            disconnect_result = mt5_instance.disconnect()
            logger.info(f"Disconnected from previous MT5 session: {disconnect_result}")

            # Add a small delay to ensure complete disconnection
            import time
            time.sleep(1)
        except Exception as e:
            logger.warning(f"Error disconnecting from MT5: {e}")
            # Continue anyway - we'll try to reconnect

        # Initialize with new settings
        try:
            logger.info(f"Initializing MT5 with settings from: {settings_path}")

            # Try to load settings from file first
            try:
                # The MT5Integration.initialize() method with no arguments or only settings_path
                # is designed to load from USER_SETTINGS_PATH by default if settings_path is None,
                # or from the specified settings_path.
                # Since we've confirmed USER_SETTINGS_PATH exists, we can directly call initialize
                # and let it handle loading from that path.
                # The ConnectionComponent._load_settings will use self.settings_path which is USER_SETTINGS_PATH.
                
                # We can pass USER_SETTINGS_PATH to ensure it uses that, or rely on its internal default.
                # To be explicit:
                init_result = mt5_instance.initialize(settings_path=settings_path)
                logger.info(f"Attempted to initialize MT5 with settings from: {settings_path}")

            except Exception as e: # Catch potential errors during the initialize call itself
                logger.error(f"Error during mt5_instance.initialize with {settings_path}: {e}")
                return error_response(f"Failed to initialize MT5 with settings: {str(e)}", 500)

            if init_result.get("success", False):
                # Get connection info which includes enhanced account details
                connection_info = mt5_instance.get_connection_info()
                logger.info("Successfully reloaded MT5 settings")
                return jsonify({
                    "status": "connected",  # Use 'connected' for consistency with other endpoints
                    "message": "MT5 settings reloaded successfully",
                    "account_info": connection_info.get("account_info", {})
                })
            else:
                error_msg = init_result.get("message", "Unknown error")
                logger.error(f"Failed to reload MT5 settings: {error_msg}")
                return error_response(f"Failed to reload settings: {error_msg}")
        except Exception as e:
            logger.error(f"Error reloading MT5 settings: {e}")
            return error_response(f"Error reloading settings: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error in reload_settings: {e}")
        return error_response(f"Unexpected error: {str(e)}", 500)

@connection_bp.route('/get_current_settings', methods=['GET'])
def get_current_settings():
    """
    Reads and returns the content of user_mt5_settings.json.
    This is used by the frontend to check for existing configurations on startup.
    """
    try:
        if os.path.exists(USER_SETTINGS_PATH):
            with open(USER_SETTINGS_PATH, 'r') as f:
                settings = json.load(f)
            # For security, consider not returning the password, or returning a masked version.
            # However, the frontend App.jsx logic expects it to decide if settings are "complete".
            # If password is not returned, frontend logic would need adjustment.
            # For now, returning all, assuming local trusted environment.
            logger.info(f"Returning current settings from {USER_SETTINGS_PATH}")
            return jsonify(settings), 200
        else:
            logger.info(f"User settings file not found at {USER_SETTINGS_PATH}. Returning empty.")
            return jsonify({}), 200 # Return empty object if no settings file
    except json.JSONDecodeError:
        logger.error(f"Error decoding JSON from {USER_SETTINGS_PATH}. Returning empty.")
        return jsonify({}), 200 # Return empty if file is corrupt
    except Exception as e:
        logger.exception(f"Error reading current settings from {USER_SETTINGS_PATH}")
        return error_response(f"Error reading settings: {str(e)}", 500)
