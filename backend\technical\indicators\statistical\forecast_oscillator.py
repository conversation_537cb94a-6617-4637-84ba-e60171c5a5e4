from typing import Dict, Any
import numpy as np
import pandas as pd
from scipy import stats

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class ForecastOscillatorIndicator(BaseIndicator):
    """Forecast Oscillator indicator."""

    def __init__(self, period: int = 14, source: str = 'close'):
        """
        Initialize Forecast Oscillator indicator.

        Args:
            period: The lookback period for linear regression calculation.
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
        """
        super().__init__({
            'period': period,
            'source': source
        })

    def _calculate_lri(self, source_data: pd.Series, period: int) -> pd.Series:
        """Helper to calculate Linear Regression Indicator (LRI)."""
        n = len(source_data)
        lri = np.full(n, np.nan)
        x = np.arange(period)

        for i in range(period - 1, n):
            y_window = source_data.iloc[i - period + 1 : i + 1].values
            if len(y_window) != period or np.isnan(y_window).any():
                continue
            try:
                slope, intercept, _, _, _ = stats.linregress(x, y_window)
                lri[i] = intercept + slope * (period - 1)
            except ValueError:
                continue
        return pd.Series(lri, index=source_data.index)


    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Forecast Oscillator values."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period']:
             return {'forecast_oscillator': np.array([])}

        period = self.params['period']
        source_col = self.params['source'].lower()

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Calculate Linear Regression Indicator (LRI)
        lri = self._calculate_lri(source_data, period)

        # Calculate Forecast Oscillator
        # Avoid division by zero
        source_safe = source_data.replace(0, np.nan)
        forecast_osc = 100 * (source_safe - lri) / source_safe
        forecast_osc = forecast_osc.fillna(0) # Fill NaNs

        self._values = {
            'forecast_oscillator': forecast_osc.values,
            'lri': lri.values # Optional: return intermediate LRI
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 2: # Linregress needs at least 2 points
            raise ValueError("Period must be at least 2 for Forecast Oscillator")
        return True