from typing import Dict, Any, List
import numpy as np
import pandas as pd

from ..base_indicator import BaseIndicator
from .......core.models.market_data import MarketData

class MAEnvelopesIndicator(BaseIndicator):
    """Moving Average Envelopes indicator."""
    
    def __init__(self, period: int = 20, deviation: float = 2.5,
                 ma_type: str = 'sma', source: str = 'close'):
        """
        Initialize Moving Average Envelopes indicator.
        
        Args:
            period: The period for calculating the moving average
            deviation: The percentage deviation for the envelopes
            ma_type: The type of moving average ('sma' or 'ema')
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4')
        """
        super().__init__('MAE', {
            'period': period,
            'deviation': deviation,
            'ma_type': ma_type,
            'source': source
        })
    
    def calculate(self, data: List[MarketData]) -> Dict[str, np.ndarray]:
        """Calculate Moving Average Envelopes values."""
        df = self._prepare_data()
        if df.empty:
            return {}
        
        # Get source data
        source = self.params['source'].lower()
        if source == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source]
        
        period = self.params['period']
        deviation = self.params['deviation']
        ma_type = self.params['ma_type'].lower()
        
        # Calculate middle band (moving average)
        if ma_type == 'sma':
            middle = source_data.rolling(window=period).mean()
        else:  # ema
            middle = source_data.ewm(span=period, adjust=False).mean()
        
        # Calculate upper and lower bands
        upper = middle * (1 + deviation / 100)
        lower = middle * (1 - deviation / 100)
        
        # Calculate trend
        trend = np.where(source_data > upper, 1,
                        np.where(source_data < lower, -1, 0))
        
        # Calculate distance from price to middle band
        distance = ((source_data - middle) / middle) * 100
        
        # Calculate volatility
        volatility = ((upper - lower) / middle) * 100
        
        # Calculate crossovers
        crossover = np.where(
            (source_data > middle) & (source_data.shift(1) <= middle.shift(1)), 1,
            np.where((source_data < middle) & (source_data.shift(1) >= middle.shift(1)), -1, 0)
        )
        
        # Calculate reversal signals
        reversal = np.zeros_like(source_data)
        reversal[source_data >= upper] = -1  # Overbought
        reversal[source_data <= lower] = 1   # Oversold
        
        # Calculate trend strength
        trend_strength = np.abs(distance) * (volatility / 100)
        
        self._values = {
            'middle': middle.values,
            'upper': upper.values,
            'lower': lower.values,
            'trend': trend,
            'distance': distance.values,
            'volatility': volatility.values,
            'crossover': crossover,
            'reversal': reversal,
            'trend_strength': trend_strength,
            'source': source_data.values
        }
        return self._values
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        valid_ma_types = ['sma', 'ema']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['ma_type'].lower() not in valid_ma_types:
            raise ValueError(f"MA type must be one of {valid_ma_types}")
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        if self.params['deviation'] <= 0:
            raise ValueError("Deviation must be greater than 0")
        return True 