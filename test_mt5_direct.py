import MetaTrader5 as mt5
import time

def test_mt5_connection():
    print("Testing MT5 connection...")
    
    # Initialize MT5
    print("Initializing MT5...")
    if not mt5.initialize(path="C:\\Program Files\\MetaTrader 5\\terminal64.exe"):
        print(f"Failed to initialize MT5: {mt5.last_error()}")
        return False
    
    print("MT5 initialized successfully.")
    
    # Check if MT5 is connected
    print("Checking terminal info...")
    terminal_info = mt5.terminal_info()
    if terminal_info is None:
        print("Failed to get terminal info.")
        mt5.shutdown()
        return False
    
    print(f"Terminal info: {terminal_info}")
    print(f"Connected: {terminal_info.connected}")
    
    # Try to login
    print("\nAttempting to login...")
    login_result = mt5.login(login=*********, password="{2Rfj>0D", server="FBS-Demo")
    
    if not login_result:
        print(f"Failed to login: {mt5.last_error()}")
        mt5.shutdown()
        return False
    
    print("Login successful!")
    
    # Get account info
    print("\nGetting account info...")
    account_info = mt5.account_info()
    if account_info is None:
        print("Failed to get account info.")
        mt5.shutdown()
        return False
    
    print(f"Account info: {account_info}")
    print(f"Balance: {account_info.balance}")
    print(f"Equity: {account_info.equity}")
    
    # Cleanup
    print("\nShutting down MT5...")
    mt5.shutdown()
    return True

if __name__ == "__main__":
    result = test_mt5_connection()
    print(f"\nTest result: {'Success' if result else 'Failed'}")
