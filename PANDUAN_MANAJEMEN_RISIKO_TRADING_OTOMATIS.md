# Sistem Manajemen Risiko Trading Otomatis

## 📋 Daftar Isi
- [<PERSON><PERSON><PERSON><PERSON>](#gambaran-umum)
- [7 Lapisan <PERSON>isiko](#7-lapisan-manajemen-risiko)
- [Parameter Konfigurasi](#parameter-konfigurasi)
- [Pengalih Mode Risiko](#pengalih-mode-risiko)
- [Sistem Entry Spacing](#sistem-entry-spacing)
- [<PERSON><PERSON><PERSON>](#contoh-praktis)
- [Praktik Terbaik](#praktik-terbaik)
- [<PERSON><PERSON><PERSON><PERSON> Masalah](#pemecahan-masalah)

## 🎯 Gambaran Umum

Sistem trading otomatis Garuda Algo V2 dilengkapi dengan **sistem manajemen risiko 7 lapisan** yang dirancang untuk melindungi modal trading Anda melalui berbagai mekanisme keamanan yang independen. Sistem ini memberikan kontrol risiko tingkat institusional dengan antarmuka yang mudah digunakan.

### Fitur Utama
- **Perlindungan multi-lapisan**: 7 kontrol risiko independen
- **Mode risiko fleksibel**: Opsi persentase atau jumlah tetap
- **Kalkulasi real-time**: Tampilan nilai ekuivalen langsung
- **Kesadaran mata uang**: Deteksi mata uang otomatis (IDR, USD, EUR, dll.)
- **UI profesional**: Pengalih dan kontrol yang intuitif

## 🛡️ 7 Lapisan Manajemen Risiko

### Lapisan 1: Filter Kualitas Sinyal
**Parameter**: Minimum Confidence Sinyal (30% - 95%)
- **Tujuan**: Hanya eksekusi trading dengan tingkat kepercayaan yang cukup
- **Cara kerja**: Menyaring sinyal trading berkualitas rendah
- **Contoh**: Minimum 60% = hanya trading dengan confidence 60%+ yang dieksekusi
- **Manfaat risiko**: Mengurangi sinyal palsu dan trading berkualitas buruk

### Lapisan 2: Kontrol Risiko Per Trading
**Parameter**: Risk Per Trade (Persentase atau Jumlah Tetap)
- **Tujuan**: Mengontrol ukuran posisi untuk setiap trading individual
- **Mode**:
  - Mode Persentase: 1% dari saldo akun
  - Mode Jumlah: Tetap Rp100.000 per trading
- **Cara kerja**: Menghitung lot size sehingga kerugian maksimum = risiko yang ditentukan
- **Contoh**: Risiko 1% pada akun Rp10M = maksimum kerugian Rp100.000 per trading

### Lapisan 3: Batas Kuantitas Posisi
**Parameter**: Max Open Trades (1-10)
- **Tujuan**: Membatasi jumlah posisi bersamaan
- **Cara kerja**: Mencegah pembukaan trading baru ketika batas tercapai
- **Contoh**: Maksimum 3 trading = maksimum 3 posisi terbuka bersamaan
- **Manfaat risiko**: Mencegah over-trading dan eksposur berlebihan

### Lapisan 4: Batas Risiko Total Portfolio
**Parameter**: Max Total Risk (Persentase atau Jumlah Tetap)
- **Tujuan**: **KONTROL KEAMANAN UTAMA** - mencegah eksposur total berlebihan
- **Mode**:
  - Mode Persentase: 5% dari saldo akun
  - Mode Jumlah: Tetap Rp500.000 total risiko
- **Cara kerja**: Menjumlahkan potensi kerugian dari SEMUA posisi terbuka
- **Fungsi kritis**: Menghentikan trading baru jika total risiko melebihi batas

### Lapisan 5: Perlindungan Price Spacing
**Parameter**: Entry Spacing Berbasis ATR
- **Tujuan**: Mencegah trading terlalu dekat dalam harga
- **Cara kerja**: Menggunakan Average True Range (ATR) untuk menghitung jarak minimum
- **Manfaat risiko**: Mengurangi risiko korelasi antara level harga serupa
- **Adaptif**: Otomatis menyesuaikan dengan volatilitas pasar

### Lapisan 6: Perlindungan Time Spacing
**Parameter**: Entry Spacing Berbasis Waktu
- **Tujuan**: Menerapkan periode minimum antara entry trading
- **Cara kerja**: Periode cooldown antara trading
- **Cooldown adaptif**: Meningkatkan waktu tunggu setelah kerugian berturut-turut
- **Manfaat risiko**: Mencegah rapid-fire trading selama periode volatil

### Lapisan 7: Perlindungan Losing Streak
**Parameter**: Sistem Anti-Martingale
- **Tujuan**: Otomatis mengurangi risiko selama losing streak
- **Cara kerja**: Mengurangi ukuran posisi setelah kerugian berturut-turut
- **Volume multiplier**: [1.0, 0.9, 0.8, 0.7, 0.6, 0.5]
- **Manfaat risiko**: Melindungi modal ketika strategi underperform

## ⚙️ Parameter Konfigurasi

### Parameter Trading Dasar
| Parameter | Rentang | Default | Deskripsi |
|-----------|---------|---------|-----------|
| Strategi | Pilihan | - | Pilih strategi trading |
| Simbol | Pilihan | EURUSD | Instrumen trading |
| Timeframe | M1-MN1 | H1 | Timeframe chart untuk analisis |

### Parameter Kontrol Risiko
| Parameter | Rentang | Default | Deskripsi |
|-----------|---------|---------|-----------|
| Risk Per Trade | 0.1%-10% atau Jumlah Tetap | 1% | Risiko trading individual |
| Max Open Trades | 1-10 | 3 | Maksimum posisi bersamaan |
| Min Signal Confidence | 30%-95% | 60% | Ambang batas kualitas sinyal minimum |
| Max Total Risk | 1%-20% atau Jumlah Tetap | 5% | Batas risiko total portfolio |

### Parameter Risiko Lanjutan
| Parameter | Rentang | Default | Deskripsi |
|-----------|---------|---------|-----------|
| Breakeven Trigger | 0.1%-5% atau Pips | 0.5% | Ambang batas pindah SL ke breakeven |
| Trailing Stop Trigger | 0.1%-5% atau Pips | 1.0% | Ambang batas mulai trailing stop |
| Trailing Stop Distance | 0.1%-5% atau Pips | 0.5% | Jarak trailing stop |
| Max Profit Target | 0+ | 100 | Hentikan bot setelah target profit |
| Max Loss Limit | 0+ | 50 | Hentikan bot setelah batas kerugian |

## 🔄 Pengalih Mode Risiko

### Pengalih Risk Per Trade
**Tujuan**: Pilih antara risiko berbasis persentase atau jumlah tetap per trading

**Mode Persentase**:
- Input: `1%`
- Kalkulasi: 1% dari saldo akun
- Contoh: 1% dari Rp10.000.000 = Rp100.000 risiko per trading
- Tampilan: `≈ Rp100.000`

**Mode Jumlah**:
- Input: `Rp50.000`
- Kalkulasi: Jumlah tetap terlepas dari saldo
- Contoh: Selalu risiko tepat Rp50.000 per trading
- Tampilan: `≈ 0.50% dari saldo`

### Pengalih Max Total Risk
**Tujuan**: Pilih antara risiko total portfolio berbasis persentase atau jumlah tetap

**Mode Persentase**:
- Input: `5%`
- Kalkulasi: 5% dari saldo akun
- Contoh: 5% dari Rp10.000.000 = Rp500.000 batas total risiko
- Tampilan: `≈ Rp500.000`

**Mode Jumlah**:
- Input: `Rp300.000`
- Kalkulasi: Jumlah total risiko tetap
- Contoh: Tidak pernah melebihi Rp300.000 eksposur total
- Tampilan: `≈ 3.00% dari saldo`

### Kapan Menggunakan Setiap Mode

**Gunakan Mode Persentase Ketika**:
- Saldo akun sering berubah
- Ingin risiko konsisten relatif terhadap ukuran akun
- Lebih suka manajemen risiko tradisional (1%, 2%, dll.)
- Ukuran akun kecil hingga menengah

**Gunakan Mode Jumlah Ketika**:
- Ingin jumlah risiko tetap dan dapat diprediksi
- Saldo akun besar dan stabil
- Lebih suka kontrol mata uang/rupiah absolut
- Menggunakan strategi money management spesifik

## 📏 Sistem Entry Spacing

### Spacing Berbasis ATR
- **Tujuan**: Mencegah trading pada level harga serupa
- **Kalkulasi**: Menggunakan Average True Range untuk spacing adaptif pasar
- **Manfaat**: Mengurangi korelasi antara trading
- **Konfigurasi**: Periode ATR (14), multiplier (2.0), minimum pips (15)

### Spacing Berbasis Waktu
- **Tujuan**: Menerapkan waktu minimum antara entry
- **Cooldown dasar**: 5 menit antara trading
- **Loss multiplier**: Meningkatkan cooldown setelah kerugian
- **Cooldown maksimum**: Maksimum 1 jam waktu tunggu
- **Manfaat**: Mencegah overtrading selama periode volatil

### Pengaturan Anti-Martingale
- **Spacing multiplier**: [1.0, 1.5, 2.0, 2.5, 3.0, 4.0]
- **Volume multiplier**: [1.0, 0.9, 0.8, 0.7, 0.6, 0.5]
- **Reset on win**: Reset multiplier setelah trading profit
- **Track by symbol**: Tracking independen per pair trading

## 💡 Contoh Praktis

### Contoh 1: Akun IDR Konservatif
**Akun**: Rp10.000.000 IDR
**Konfigurasi**:
- Risk Per Trade: 0.5% (Rp50.000)
- Max Open Trades: 2
- Max Total Risk: 2% (Rp200.000)
- Min Confidence: 70%

**Hasil**: Sangat aman, hanya trading berkualitas tinggi

### Contoh 2: Akun USD Moderat
**Akun**: $10.000 USD
**Konfigurasi**:
- Risk Per Trade: 1% ($100)
- Max Open Trades: 3
- Max Total Risk: 5% ($500)
- Min Confidence: 60%

**Hasil**: Pendekatan risk/reward seimbang

### Contoh 3: Jumlah Tetap Agresif
**Akun**: Rp50.000.000 IDR
**Konfigurasi**:
- Risk Per Trade: Tetap Rp200.000 (0.4%)
- Max Open Trades: 5
- Max Total Risk: Tetap Rp800.000 (1.6%)
- Min Confidence: 50%

**Hasil**: Lebih banyak trading, jumlah risiko tetap

## 📚 Praktik Terbaik

### Manajemen Risiko
1. **Mulai Konservatif**: Mulai dengan persentase risiko yang lebih rendah
2. **Test Menyeluruh**: Gunakan akun demo sebelum trading live
3. **Monitor Performa**: Review rutin metrik risiko
4. **Adjust Bertahap**: Buat perubahan kecil secara bertahap
5. **Hormati Batas**: Jangan pernah override mekanisme keamanan

### Tips Konfigurasi
1. **Sesuaikan Toleransi Risiko**: Selaraskan pengaturan dengan tingkat kenyamanan Anda
2. **Pertimbangkan Kondisi Pasar**: Sesuaikan untuk volatilitas
3. **Perhitungkan Korelasi**: Gunakan entry spacing secara efektif
4. **Rencanakan Drawdown**: Tetapkan batas kerugian yang sesuai
5. **Review Rutin**: Update pengaturan berdasarkan performa

### Kesalahan Umum yang Harus Dihindari
1. **Over-leveraging**: Menetapkan risiko terlalu tinggi
2. **Mengabaikan Korelasi**: Terlalu banyak trading serupa
3. **Perubahan Sering**: Terus menyesuaikan parameter
4. **Keputusan Berbasis Emosi**: Override aturan sistem
5. **Testing Tidak Memadai**: Backtesting tidak cukup

## 🎓 Skenario Tutorial

### Skenario 1: Setup Trader Pemula
**Tujuan**: Pengenalan aman ke trading otomatis
**Langkah**:
1. Set Risk Per Trade ke 0.5% (Mode Persentase)
2. Set Max Open Trades ke 1
3. Set Max Total Risk ke 1% (Mode Persentase)
4. Set Min Confidence ke 80%
5. Aktifkan semua proteksi entry spacing
6. Mulai dengan pair major (EURUSD, GBPUSD)

### Skenario 2: Trader Berpengalaman
**Tujuan**: Optimasi risk/reward seimbang
**Langkah**:
1. Set Risk Per Trade ke 1-2% (Mode Persentase)
2. Set Max Open Trades ke 3-5
3. Set Max Total Risk ke 5-8% (Mode Persentase)
4. Set Min Confidence ke 60-70%
5. Fine-tune entry spacing berdasarkan strategi
6. Monitor dan sesuaikan berdasarkan performa

### Skenario 3: Manajemen Akun Besar
**Tujuan**: Kontrol risiko jumlah tetap
**Langkah**:
1. Set Risk Per Trade ke Jumlah Tetap (misal Rp500.000)
2. Set Max Open Trades ke 5-10
3. Set Max Total Risk ke Jumlah Tetap (misal Rp2.000.000)
4. Set Min Confidence ke 50-60%
5. Gunakan mode jumlah untuk risiko dapat diprediksi
6. Scale jumlah berdasarkan pertumbuhan akun

## 📊 Formula Kalkulasi Risiko

### Kalkulasi Ukuran Posisi
```
Mode Persentase:
Ukuran Posisi = (Saldo Akun × Risiko %) / (Harga Entry - Harga Stop Loss)

Mode Jumlah:
Ukuran Posisi = Jumlah Risiko / (Harga Entry - Harga Stop Loss)
```

### Kalkulasi Total Risiko
```
Total Risiko = Σ(Ukuran Posisi × |Harga Entry - Harga Stop Loss|)
```

### Kalkulasi Nilai Ekuivalen
```
Jumlah dari Persentase:
Jumlah = (Persentase / 100) × Saldo Akun

Persentase dari Jumlah:
Persentase = (Jumlah / Saldo Akun) × 100
```

## 🌍 Dukungan Multi-Mata Uang

### Mata Uang yang Didukung
- **Major**: USD ($), EUR (€), GBP (£), JPY (¥), CHF, CAD (C$), AUD (A$), NZD (NZ$)
- **ASEAN**: IDR (Rp), MYR (RM), PHP (₱), VND (₫), THB (฿), SGD (S$)
- **Lainnya**: CNY (¥), INR (₹), BRL (R$), ZAR (R), KRW (₩), TRY (₺)

### Deteksi Mata Uang
- Deteksi otomatis dari akun MT5
- Tampilan simbol mata uang real-time
- Format yang tepat untuk setiap mata uang
- Dukungan untuk tempat desimal berbeda

## 🔍 Monitoring dan Analitik

### Metrik Kunci untuk Dipantau
1. **Eksposur Risiko Saat Ini**: Persentase total risiko real-time
2. **Win Rate**: Persentase trading yang profitable
3. **Rata-rata Risiko per Trading**: Aktual vs. risiko yang dikonfigurasi
4. **Drawdown**: Periode kerugian berturut-turut maksimum
5. **Risk-Adjusted Returns**: Profit relatif terhadap risiko yang diambil

### Indikator Dashboard
- **Hijau**: Sistem beroperasi normal
- **Kuning**: Mendekati batas risiko
- **Merah**: Batas risiko tercapai atau masalah sistem
- **Biru**: Informasi dan statistik

### Analisis Performa
- Tracking P&L harian/mingguan/bulanan
- Eksposur risiko dari waktu ke waktu
- Efektivitas entry spacing
- Korelasi confidence sinyal dengan kesuksesan

## 🚨 Prosedur Darurat

### Tindakan Segera
1. **Hentikan Semua Trading**: Gunakan tombol emergency stop
2. **Tutup Posisi**: Penutupan posisi manual jika diperlukan
3. **Periksa Log**: Review log sistem untuk error
4. **Verifikasi Koneksi**: Pastikan konektivitas MT5
5. **Hubungi Support**: Jika masalah berlanjut

### Pelanggaran Batas Risiko
- Sistem otomatis menghentikan trading baru
- Posisi yang ada tetap aktif
- Intervensi manual mungkin diperlukan
- Review dan sesuaikan parameter risiko

### Pemulihan Sistem
1. Identifikasi akar penyebab masalah
2. Sesuaikan parameter risiko jika diperlukan
3. Test dengan pengaturan risiko minimal
4. Kembali ke operasi normal secara bertahap
5. Monitor ketat setelah restart

## 🔧 Pemecahan Masalah

### Masalah Umum

**Masalah**: Tidak ada trading yang dieksekusi
**Penyebab**:
- Min confidence terlalu tinggi
- Entry spacing terlalu restriktif
- Max total risk tercapai
**Solusi**: Turunkan ambang batas confidence, sesuaikan spacing, periksa eksposur risiko

**Masalah**: Terlalu banyak trading yang rugi
**Penyebab**:
- Min confidence terlalu rendah
- Kondisi pasar buruk
- Strategi tidak cocok untuk pasar saat ini
**Solusi**: Naikkan ambang batas confidence, review performa strategi

**Masalah**: Kalkulasi risiko tampak salah
**Penyebab**:
- Saldo akun tidak terupdate
- Masalah konversi mata uang
- Kalkulasi lot size tidak benar
**Solusi**: Refresh data akun, periksa koneksi MT5, verifikasi spesifikasi simbol

### Support dan Update
- Periksa log sistem untuk pesan error detail
- Monitor dashboard trading otomatis
- Review statistik entry spacing
- Verifikasi status koneksi MT5
- Hubungi support untuk masalah persisten

---

**Terakhir Diupdate**: Desember 2024
**Versi**: Garuda Algo V2
**Penulis**: Tim Sistem Trading Garuda

**Disclaimer**: Trading melibatkan risiko kerugian yang substansial. Performa masa lalu tidak menjamin hasil masa depan. Selalu trading dengan uang yang Anda mampu untuk kehilangan.
