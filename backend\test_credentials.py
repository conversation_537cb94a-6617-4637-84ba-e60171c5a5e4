"""
Unit tests for the credential manager implementation.
"""
import os
import json
import pytest
from pathlib import Path
import keyring
import keyring.errors
from credential_manager import (
    CredentialsManager,
    CredentialError,
    KeychainError,
    EncryptionError,
    ValidationError
)

@pytest.fixture
def temp_storage(tmp_path):
    """Fixture to provide temporary storage directory."""
    return str(tmp_path)

@pytest.fixture
def credentials_manager(temp_storage):
    """Fixture to provide a CredentialsManager instance."""
    return CredentialsManager(storage_dir=temp_storage)

@pytest.fixture
def valid_credentials():
    """Fixture to provide valid test credentials."""
    return {
        "account": "********",
        "password": "testPassword123",
        "server": "TestServer"
    }

def test_credential_validation(credentials_manager):
    """Test credential validation logic."""
    # Valid credentials should not raise
    credentials_manager._validate_credentials("********", "validPass123", "TestServer")
    
    # Invalid account (non-digits)
    with pytest.raises(ValidationError):
        credentials_manager._validate_credentials("abc123", "validPass123", "TestServer")
    
    # Short password
    with pytest.raises(ValidationError):
        credentials_manager._validate_credentials("********", "short", "TestServer")
    
    # Missing server
    with pytest.raises(ValidationError):
        credentials_manager._validate_credentials("********", "validPass123", "")

def test_save_and_retrieve_keychain(credentials_manager, valid_credentials, monkeypatch):
    """Test saving and retrieving credentials using OS keychain."""
    stored_data = {}
    
    # Mock keyring functions
    def mock_set_password(service, username, password):
        stored_data['password'] = password
        
    def mock_get_password(service, username):
        return stored_data.get('password')
    
    monkeypatch.setattr(keyring, 'set_password', mock_set_password)
    monkeypatch.setattr(keyring, 'get_password', mock_get_password)
    
    # Save credentials
    credentials_manager.save_credentials(
        valid_credentials['account'],
        valid_credentials['password'],
        valid_credentials['server']
    )
    
    # Retrieve and verify
    retrieved = credentials_manager.get_credentials()
    assert retrieved['account'] == valid_credentials['account']
    assert retrieved['password'] == valid_credentials['password']
    assert retrieved['server'] == valid_credentials['server']

def test_file_fallback(credentials_manager, valid_credentials, monkeypatch):
    """Test encrypted file fallback when keychain is unavailable."""
    def mock_set_password(*args):
        raise keyring.errors.KeyringError()
        
    def mock_get_password(*args):
        raise keyring.errors.KeyringError()
    
    monkeypatch.setattr(keyring, 'set_password', mock_set_password)
    monkeypatch.setattr(keyring, 'get_password', mock_get_password)
    
    # Save should fall back to file
    credentials_manager.save_credentials(
        valid_credentials['account'],
        valid_credentials['password'],
        valid_credentials['server']
    )
    
    # Verify file exists
    file_path = os.path.join(credentials_manager.storage_dir, 
                            credentials_manager.FALLBACK_FILE)
    assert os.path.exists(file_path)
    
    # Verify file permissions
    assert oct(os.stat(file_path).st_mode & 0o777) == oct(0o600)
    
    # Retrieve and verify
    retrieved = credentials_manager.get_credentials()
    assert retrieved['account'] == valid_credentials['account']
    assert retrieved['password'] == valid_credentials['password']
    assert retrieved['server'] == valid_credentials['server']

def test_encryption_key_management(temp_storage):
    """Test encryption key generation and storage."""
    manager = CredentialsManager(storage_dir=temp_storage)
    key_path = os.path.join(temp_storage, manager.KEY_FILE)
    
    # Key file should exist
    assert os.path.exists(key_path)
    
    # Key file should have correct permissions
    assert oct(os.stat(key_path).st_mode & 0o777) == oct(0o600)
    
    # Creating new manager should reuse existing key
    original_key = manager.key
    new_manager = CredentialsManager(storage_dir=temp_storage)
    assert new_manager.key == original_key

def test_delete_credentials(credentials_manager, valid_credentials, monkeypatch):
    """Test credential deletion."""
    stored_data = {}
    
    def mock_set_password(service, username, password):
        stored_data['password'] = password
        
    def mock_get_password(service, username):
        return stored_data.get('password')
        
    def mock_delete_password(service, username):
        stored_data.clear()
    
    monkeypatch.setattr(keyring, 'set_password', mock_set_password)
    monkeypatch.setattr(keyring, 'get_password', mock_get_password)
    monkeypatch.setattr(keyring, 'delete_password', mock_delete_password)
    
    # Save credentials
    credentials_manager.save_credentials(
        valid_credentials['account'],
        valid_credentials['password'],
        valid_credentials['server']
    )
    
    # Delete credentials
    assert credentials_manager.delete_credentials()
    
    # Verify credentials are gone
    with pytest.raises(CredentialError):
        credentials_manager.get_credentials()

def test_has_credentials(credentials_manager, valid_credentials, monkeypatch):
    """Test credentials existence check."""
    assert not credentials_manager.has_credentials()
    
    # Save credentials
    credentials_manager.save_credentials(
        valid_credentials['account'],
        valid_credentials['password'],
        valid_credentials['server']
    )
    
    assert credentials_manager.has_credentials()
    
    # Delete credentials
    credentials_manager.delete_credentials()
    assert not credentials_manager.has_credentials()

def test_storage_directory_creation(temp_storage):
    """Test storage directory creation and permissions."""
    test_dir = os.path.join(temp_storage, "custom_storage")
    manager = CredentialsManager(storage_dir=test_dir)
    
    # Directory should exist
    assert os.path.exists(test_dir)
    
    # Directory should have correct permissions
    assert oct(os.stat(test_dir).st_mode & 0o777) == oct(0o700)

def test_error_handling(credentials_manager):
    """Test various error conditions."""
    # Invalid credentials
    with pytest.raises(ValidationError):
        credentials_manager.save_credentials("invalid", "pass", "server")
    
    # Missing credentials
    with pytest.raises(CredentialError):
        credentials_manager.get_credentials()

if __name__ == '__main__':
    pytest.main(['-v', __file__])