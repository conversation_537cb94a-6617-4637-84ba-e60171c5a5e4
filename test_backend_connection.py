import requests
import json
import time

def test_backend_connection():
    """Test the backend API connection to MT5"""
    print("Testing backend API connection to MT5...")
    
    # Base URL for the backend API
    base_url = "http://localhost:5001"
    
    # Step 1: Check if the backend is running
    try:
        response = requests.get(f"{base_url}/health")
        print(f"Backend health status: {response.status_code}")
        if response.ok:
            print(f"Backend response: {response.json()}")
        else:
            print(f"Backend error: {response.text}")
            print("Make sure the backend server is running on port 5001")
            return
    except Exception as e:
        print(f"Error connecting to backend: {e}")
        print("Make sure the backend server is running on port 5001")
        return
    
    # Step 2: Test the MT5 path
    print("\nTesting MT5 terminal path...")
    try:
        path_data = {
            "path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe"
        }
        response = requests.post(
            f"{base_url}/api/connection/test_path",
            json=path_data
        )
        print(f"Path test status: {response.status_code}")
        if response.ok:
            print(f"Path test response: {response.json()}")
        else:
            print(f"Path test error: {response.text}")
    except Exception as e:
        print(f"Error testing path: {e}")
    
    # Step 3: Get connection status
    print("\nGetting connection status...")
    try:
        response = requests.get(f"{base_url}/api/connection/connection_status")
        print(f"Connection status code: {response.status_code}")
        if response.ok:
            print(f"Connection status: {json.dumps(response.json(), indent=2)}")
        else:
            print(f"Connection status error: {response.text}")
    except Exception as e:
        print(f"Error getting connection status: {e}")
    
    # Step 4: Try to connect to MT5
    print("\nAttempting to connect to MT5...")
    try:
        connection_data = {
            "account": *********,
            "password": "{2Rfj>0D",
            "server": "FBS-Demo",
            "path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe"
        }
        response = requests.post(
            f"{base_url}/api/connection/connect",
            json=connection_data
        )
        print(f"Connection response status: {response.status_code}")
        if response.ok:
            print(f"Connection response: {json.dumps(response.json(), indent=2)}")
        else:
            print(f"Connection error: {response.text}")
    except Exception as e:
        print(f"Error connecting to MT5: {e}")
    
    # Step 5: Get connection status again
    print("\nGetting connection status after connect attempt...")
    try:
        response = requests.get(f"{base_url}/api/connection/connection_status")
        print(f"Connection status code: {response.status_code}")
        if response.ok:
            print(f"Connection status: {json.dumps(response.json(), indent=2)}")
        else:
            print(f"Connection status error: {response.text}")
    except Exception as e:
        print(f"Error getting connection status: {e}")

if __name__ == "__main__":
    test_backend_connection()
