"""
Connection component for MT5 integration.
"""

import MetaTrader5 as mt5
import time
import logging
import json
import os
import sys # Added for path logic
from datetime import datetime
from typing import Dict, Any, Optional

from ..types import ConnectionState
from ..exceptions import MT5ConnectionError, MT5AuthenticationError, MT5SettingsError, MT5Error
# Updated import: get_firestore_client handles initialization
from backend.firebase_utils import get_firestore_client, get_active_license_details 

# Configure logging
logger = logging.getLogger("MT5Integration.Connection")

# Path logic similar to the main mt5_integration.py
# This should ideally be centralized or passed in, but for now, we replicate for self-containment.
data_root = os.getenv('APPDATA')
if not data_root:
    data_root = os.path.expanduser("~")
    logger.warning(
        f"APPDATA environment variable not found. Using home directory as data root: {data_root}")

DATA_DIR = os.path.join(data_root, 'GarudaAlgo')
os.makedirs(DATA_DIR, exist_ok=True)

USER_SETTINGS_PATH = os.path.join(DATA_DIR, "user_mt5_settings.json")
# DEFAULT_SETTINGS_PATH is removed as per user request to simplify to one settings file.

class ConnectionComponent:
    """Component for handling MT5 connection."""

    def __init__(self):
        """Initialize the connection component."""
        self._state = ConnectionState.DISCONNECTED
        self._last_error = None
        self._connection_time = None
        self._reconnect_attempts = 0
        self._max_reconnect_attempts = 3
        self._last_check = 0
        self._check_interval = 5  # seconds
        self._license_info: Optional[Dict[str, Any]] = None

        # Ensure Firebase is initialized by attempting to get the client
        if not get_firestore_client(): # CORRECTED
            logger.error("Firebase could not be initialized by ConnectionComponent (get_firestore_client failed). License checks may fail.")

    @property
    def state(self) -> ConnectionState:
        """Get the current connection state."""
        return self._state

    @property
    def connection_latency(self) -> Optional[float]:
        """Calculate current connection latency in milliseconds."""
        if self._state != ConnectionState.CONNECTED:
            return None
        try:
            start_time = time.time()
            mt5.terminal_info() # Simple check
            latency = (time.time() - start_time) * 1000
            return latency
        except Exception as e:
            logger.warning(f"Could not calculate latency: {e}")
            return None

    def _load_settings(self, settings_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Load connection settings from a JSON file.
        Tries user settings first, then default, then provided path.
        Args:
            settings_path: Optional specific path to the settings file.
        Returns:
            Dict containing the loaded settings
        Raises:
            MT5SettingsError: If file cannot be found or contains invalid JSON
        """
        path_to_try = None
        if settings_path: # Explicit path given
            path_to_try = settings_path
            logger.info(f"Attempting to load settings from explicit path: {path_to_try}")
        elif os.path.exists(USER_SETTINGS_PATH):
            path_to_try = USER_SETTINGS_PATH
            logger.info(f"Attempting to load user settings from: {path_to_try}")
        # Removed fallback to DEFAULT_SETTINGS_PATH
        
        if not path_to_try:
            logger.warning("No settings file path determined (explicit or user).")
            raise MT5SettingsError("No settings file found (checked user and no explicit path provided).")

        try:
            # Handle relative/absolute paths - USER_SETTINGS_PATH is absolute
            if not os.path.isabs(path_to_try):
                # This case should ideally not happen if using the predefined paths
                logger.warning(f"Settings path {path_to_try} is not absolute. Resolving relative to CWD: {os.getcwd()}")
                path_to_try = os.path.join(os.getcwd(), path_to_try)

            with open(path_to_try, 'r') as f:
                settings = json.load(f)
            logger.info(f"Successfully loaded settings from {path_to_try}")
            return settings

        except FileNotFoundError:
            logger.warning(f"Settings file not found at {path_to_try}.")
            raise MT5SettingsError(f"Settings file not found at {path_to_try}")
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from {path_to_try}: {str(e)}")
            raise MT5SettingsError(f"Error decoding settings file from {path_to_try}: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error loading settings from {path_to_try}: {str(e)}")
            raise MT5SettingsError(f"Error loading settings from {path_to_try}: {str(e)}")

    def _save_settings(self, login: int, password: str, server: str, path: Optional[str]) -> None:
        """Saves connection settings to the user settings file."""
        settings_to_save = {
            "login": login,
            "password": password, # Note: Storing password in plain text.
            "server": server,
            "path": path
        }
        try:
            with open(USER_SETTINGS_PATH, 'w') as f:
                json.dump(settings_to_save, f, indent=4)
            logger.info(f"Connection settings saved to {USER_SETTINGS_PATH}")
        except IOError as e:
            logger.error(f"Failed to save settings to {USER_SETTINGS_PATH}: {e}")
            # Not raising an error here, as the connection itself might be successful.

    def initialize(self, path: Optional[str] = None, login: Optional[int] = None,
                  password: Optional[str] = None, server: Optional[str] = None,
                  timeout: Optional[int] = None, settings_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Initialize connection to MetaTrader 5 terminal.

        Connection details can be provided directly as arguments or loaded from settings file.
        Arguments take precedence over settings file values.
        """
        self._state = ConnectionState.CONNECTING
        self._last_error = None
        self._license_info = None # Reset license info
        start_time = time.time()

        is_manual_connection_attempt = all([login, password, server])
        loaded_settings = {}

        try:
            if not is_manual_connection_attempt:
                # If settings_path is provided, load from there.
                # Else, _load_settings will try USER_SETTINGS_PATH then DEFAULT_SETTINGS_PATH.
                try:
                    loaded_settings = self._load_settings(settings_path) # Pass explicit settings_path if provided
                    if loaded_settings:
                        logger.info(f"Successfully loaded settings: {json.dumps({k: '***' if k == 'password' else v for k, v in loaded_settings.items()})}")
                except MT5SettingsError as e:
                    logger.warning(f"Could not load settings (path: {settings_path}): {e}. Proceeding based on other parameters.")
            
            # Combine loaded settings with provided arguments (args take precedence)
            final_path = path or loaded_settings.get("path")
            final_login = login or loaded_settings.get("login")
            final_server = server or loaded_settings.get("server")
            # Password logic: Use direct password if given, else use loaded password if login/server match loaded ones.
            local_password = password # Prioritize direct password
            if not local_password and loaded_settings and \
               final_login == loaded_settings.get("login") and \
               final_server == loaded_settings.get("server"):
                local_password = loaded_settings.get("password")

            final_timeout = timeout if timeout is not None else loaded_settings.get("timeout", 5000) # Default 5000ms

            if final_login is None or local_password is None or final_server is None:
                err_msg = "Missing essential connection details: Login, Password, and Server must be provided."
                logger.warning(err_msg + " Not attempting connection.")
                self._state = ConnectionState.DISCONNECTED
                return {"success": False, "message": err_msg, "state": self._state.value}

            # --- License Check ---
            logger.info(f"Verifying license status for account {final_login}...")
            # Ensure Firebase is up by getting the client; get_active_license_details will also do this,
            # but an explicit check here can provide an earlier, more specific log if it fails.
            if not get_firestore_client(): # CORRECTED
                 logger.warning("Firebase not initialized (get_firestore_client failed before license check). License check might be skipped or fail by get_active_license_details.")
            
            # Pass final_server to get_active_license_details
            logger.info(f"Calling get_active_license_details for account {final_login} and server {final_server}")
            license_details = get_active_license_details(int(final_login), server_name=final_server)
            if not license_details:
                err_msg = f"Account {final_login} (server: {final_server}) does not have an active license or was not found. Please check your subscription."
                logger.error(err_msg)
                self._state = ConnectionState.ERROR
                self._last_error = err_msg
                return {"success": False, "message": err_msg, "state": self._state.value, "license_info": None}
            self._license_info = license_details
            logger.info(f"License for account {final_login} is active. Details: {self._license_info}")
            # --- End License Check ---

            logger.info("Initializing MT5 library...")
            init_params = {}
            if final_path: init_params["path"] = final_path
            if final_timeout is not None: init_params["timeout"] = int(final_timeout)

            initialized = mt5.initialize(**init_params)
            if not initialized:
                error = mt5.last_error()
                self._state = ConnectionState.ERROR
                self._last_error = f"Failed to initialize MT5: {error}"
                logger.error(self._last_error)
                raise MT5ConnectionError(self._last_error)
            logger.info("MT5 library initialized successfully.")

            logger.info(f"Attempting login for account {final_login} on server '{final_server}'.")
            login_params = {
                "login": int(final_login),
                "password": local_password,
                "server": final_server,
                "timeout": int(final_timeout)
            }
            login_successful = mt5.login(**login_params)
            # local_password = None # Clear password from memory - already done by Python's scoping if not stored in self

            if not login_successful:
                error = mt5.last_error()
                mt5.shutdown()
                self._state = ConnectionState.ERROR
                self._last_error = f"Failed to login: {error}"
                logger.error(f"Failed to login account {final_login}: {self._last_error}")
                raise MT5AuthenticationError(self._last_error)
            logger.info(f"Login successful for account {final_login}.")

            logger.info("Validating initial connection...")
            if not self.validate_connection(): # This calls mt5.terminal_info() and mt5.account_info()
                error = mt5.last_error() # Get last error if validation failed
                self._state = ConnectionState.ERROR
                error_msg = f"Connection validation failed. Last MT5 error: {error if error else 'Unknown validation error'}"
                self._last_error = error_msg
                logger.error(error_msg)
                try: mt5.shutdown()
                except Exception as sd_err: logger.warning(f"Exception during shutdown after validation failure: {sd_err}")
                raise MT5ConnectionError(error_msg)
            logger.info("Initial connection validated successfully.")

            self._state = ConnectionState.CONNECTED
            self._connection_time = time.time() - start_time
            term_info_obj = mt5.terminal_info()
            terminal_info_dict = term_info_obj._asdict() if term_info_obj else {}
            logger.info(f"Successfully connected to MT5 in {self._connection_time:.2f} seconds.")

            if is_manual_connection_attempt and final_login and local_password and final_server:
                 self._save_settings(int(final_login), local_password, final_server, final_path)

            return {
                "success": True,
                "message": "Successfully connected to MT5",
                "terminal_info": terminal_info_dict,
                "connection_time": self._connection_time,
                "license_info": self._license_info
            }

        except (MT5Error, ValueError) as e: # Catch specific errors like ValueError from missing details
            logger.error(f"Error during initialization: {e}", exc_info=True)
            if self._state != ConnectionState.CONNECTED: # Avoid overwriting CONNECTED state if error occurs late
                self._state = ConnectionState.ERROR
            if not self._last_error: self._last_error = str(e)
            # Re-raise to be caught by the main MT5Integration class or API layer
            # This allows the caller to know about the failure.
            raise
        except Exception as e: # Catch any other unexpected errors
            self._state = ConnectionState.ERROR
            self._last_error = f"Unexpected error during initialization: {str(e)}"
            logger.exception("Unexpected exception during MT5 initialization")
            try:
                # Check if mt5 library was initialized before trying to shutdown
                if mt5.version(): # mt5.version() returns None if not initialized
                    mt5.shutdown()
            except Exception as sd_ex:
                logger.warning(f"Exception during MT5 shutdown after unexpected error: {sd_ex}")
            raise MT5ConnectionError(self._last_error) # Wrap in MT5ConnectionError

    def validate_connection(self) -> bool:
        """
        Validate basic connection health and account access.
        Does NOT check latency, as that's for ongoing monitoring.

        Returns:
            bool: True if connection is healthy, False otherwise
        """
        try:
            term_info = mt5.terminal_info()
            if term_info is None:
                logger.warning("Validation failed: Terminal info is None.")
                return False

            account_info = mt5.account_info()
            if account_info is None:
                logger.warning("Validation failed: Account info is None.")
                return False

            return True
        except Exception as e:
            logger.error(f"Exception during connection validation: {e}", exc_info=True)
            return False

    def disconnect(self) -> Dict[str, Any]:
        """Disconnect from MetaTrader 5 terminal."""
        logger.info("Disconnecting from MT5...")

        if self._state == ConnectionState.DISCONNECTED:
            return {
                "success": True,
                "message": "Already disconnected"
            }

        try:
            start_time = time.time()
            mt5.shutdown()
            disconnect_time = time.time() - start_time
            self._state = ConnectionState.DISCONNECTED
            self._last_error = None
            logger.info(f"Successfully disconnected from MT5 in {disconnect_time:.2f} seconds.")

            return {
                "success": True,
                "message": "Successfully disconnected",
                "disconnect_time": disconnect_time
            }

        except Exception as e:
            logger.error("Exception during MT5 shutdown")
            logger.exception(e)
            self._state = ConnectionState.ERROR
            self._last_error = str(e)

            return {
                "success": False,
                "message": f"Error during disconnect: {str(e)}"
            }

    def is_connected(self) -> bool:
        """
        Check if currently connected to MT5.

        This does a real check of the connection, not just the stored state.
        """
        try:
            term_info = mt5.terminal_info()
            if term_info is None:
                logger.warning("is_connected check failed: terminal_info is None.")
                self._state = ConnectionState.ERROR
                return False

            # Check if connected property exists and is True
            if hasattr(term_info, 'connected') and not term_info.connected:
                logger.warning("is_connected check failed: terminal_info.connected is False.")
                self._state = ConnectionState.ERROR
                return False

            # Additional validation - try to get account info
            account_info = mt5.account_info()
            if account_info is None:
                logger.warning("is_connected check failed: account_info is None.")
                self._state = ConnectionState.ERROR
                return False

            # If we got here, we're connected
            if self._state != ConnectionState.CONNECTED:
                self._state = ConnectionState.CONNECTED
                logger.info("Connection state updated to CONNECTED")

            return True
        except Exception as e:
            logger.warning(f"Exception during is_connected check: {e}, marking state as ERROR.")
            self._state = ConnectionState.ERROR
            return False

    def get_connection_info(self) -> Dict[str, Any]:
        """Get current connection state and details."""
        try:
            # First check if we're connected
            is_connected = self.is_connected()

            # Get terminal info if connected
            terminal_info = None
            if is_connected:
                try:
                    term_info_obj = mt5.terminal_info()
                    if term_info_obj:
                        terminal_info = term_info_obj._asdict()
                except Exception as term_err:
                    logger.error(f"Error getting terminal info: {term_err}")

            info = {
                "state": self._state.value,
                "connected": is_connected,
                "latency_ms": self.connection_latency,
                "last_error": self._last_error,
                "connection_time": self._connection_time,
                "terminal_info": terminal_info
            }

            return info
        except Exception as e:
            logger.error(f"Error in get_connection_info: {e}")
            return {
                "state": ConnectionState.ERROR.value,
                "connected": False,
                "last_error": str(e),
                "error_location": "get_connection_info"
            }
