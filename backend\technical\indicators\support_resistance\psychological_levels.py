from typing import Dict, Any
import numpy as np
import pandas as pd
import math

from backend.technical.base_indicator import BaseIndicator

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

class PsychologicalLevelsIndicator(BaseIndicator):
    """Psychological Levels (Round Numbers) indicator."""

    def __init__(self, num_levels: int = 3, precision_factor: float = 1.0):
        """
        Initialize Psychological Levels indicator.

        Args:
            num_levels: Number of round number levels to find above and below the current price.
            precision_factor: Determines the 'roundness' (e.g., 1 for whole numbers,
                              10 for numbers ending in .0, 100 for .00, 0.1 for tens, etc.).
                              Lower factor = 'rounder' numbers.
        """
        super().__init__({
            'num_levels': num_levels,
            'precision_factor': precision_factor
        })

    def _get_round_number(self, price: float, factor: float, direction: str) -> float:
        """Calculates the next round number above or below the price."""
        if factor <= 0:
            return price # Avoid division by zero or nonsensical factors

        # Scale price by the inverse factor to work with integers for rounding
        scaled_price = price * factor

        if direction == 'up':
            # Find the next integer up and scale back
            round_scaled = math.ceil(scaled_price)
            # If price is already a round number, get the next one
            if abs(round_scaled - scaled_price) < 1e-9: # Check for floating point equality
                 round_scaled += 1
            return round_scaled / factor
        else: # direction == 'down'
            # Find the next integer down and scale back
            round_scaled = math.floor(scaled_price)
             # If price is already a round number, get the next one
            if abs(round_scaled - scaled_price) < 1e-9:
                 round_scaled -= 1
            return round_scaled / factor


    def calculate(self, data: pd.DataFrame, current_price=None) -> Dict[str, Any]:
        """Calculate psychological round number levels near the current price."""
        df = data.copy()
        if df.empty and current_price is None:
             # Return empty arrays for all expected keys
             keys = [f'level_up_{i+1}' for i in range(self.params['num_levels'])] + \
                    [f'level_down_{i+1}' for i in range(self.params['num_levels'])]
             return {key: np.array([]) for key in keys}

        num_levels = self.params['num_levels']
        precision_factor = self.params['precision_factor']

        # Use provided current price if available, otherwise use last close
        if current_price is not None and isinstance(current_price, (int, float)):
            price_value = float(current_price)
        elif current_price is not None and isinstance(current_price, dict) and 'bid' in current_price:
            price_value = float(current_price['bid'])
        elif not df.empty:
            price_value = df['close'].iloc[-1]
        else:
            return {'resistance': [], 'support': []}

        if math.isnan(price_value) or price_value <= 0:
            return {'resistance': [], 'support': []}

        # Find levels above (resistance)
        resistance_levels = []
        last_level_up = price_value
        for j in range(num_levels):
            next_level = self._get_round_number(last_level_up, precision_factor, 'up')
            resistance_levels.append({
                'price': next_level,
                'type': 'Psychological',
                'strength': 1.0
            })
            last_level_up = next_level

        # Find levels below (support)
        support_levels = []
        last_level_down = price_value
        for j in range(num_levels):
            next_level = self._get_round_number(last_level_down, precision_factor, 'down')
            support_levels.append({
                'price': next_level,
                'type': 'Psychological',
                'strength': 1.0
            })
            last_level_down = next_level

        # For backward compatibility, also include the original array format
        self._values = {
            'resistance': resistance_levels,
            'support': support_levels
        }

        # Add the original format for backward compatibility
        n = len(df) if not df.empty else 1
        for j in range(num_levels):
            level_up = np.full(n, resistance_levels[j]['price'] if j < len(resistance_levels) else np.nan)
            level_down = np.full(n, support_levels[j]['price'] if j < len(support_levels) else np.nan)
            self._values[f'level_up_{j+1}'] = level_up
            self._values[f'level_down_{j+1}'] = level_down

        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if not isinstance(self.params['num_levels'], int) or self.params['num_levels'] < 1:
            raise ValueError("Number of levels must be a positive integer")
        if not isinstance(self.params['precision_factor'], (int, float)) or self.params['precision_factor'] <= 0:
             raise ValueError("Precision factor must be a positive number")
        return True
