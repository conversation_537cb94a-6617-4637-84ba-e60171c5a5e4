from typing import Dict, Any, List
import numpy as np
import pandas as pd

from ..base_indicator import BaseIndicator
from .......core.models.market_data import MarketData

class KeltnerChannelsIndicator(BaseIndicator):
    """Keltner Channels indicator."""
    
    def __init__(self, period: int = 20, atr_period: int = 10,
                 multiplier: float = 2.0, ma_type: str = 'ema'):
        """
        Initialize Keltner Channels indicator.
        
        Args:
            period: The period for calculating the middle line MA
            atr_period: The period for calculating ATR
            multiplier: The multiplier for ATR to set channel width
            ma_type: The type of moving average ('sma' or 'ema')
        """
        super().__init__('KC', {
            'period': period,
            'atr_period': atr_period,
            'multiplier': multiplier,
            'ma_type': ma_type
        })
    
    def calculate(self, data: List[MarketData]) -> Dict[str, np.ndarray]:
        """Calculate Keltner Channels values."""
        df = self._prepare_data()
        if df.empty:
            return {}
        
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        period = self.params['period']
        atr_period = self.params['atr_period']
        multiplier = self.params['multiplier']
        ma_type = self.params['ma_type'].lower()
        
        # Calculate True Range
        tr1 = high[1:] - low[1:]
        tr2 = np.abs(high[1:] - close[:-1])
        tr3 = np.abs(low[1:] - close[:-1])
        tr = np.maximum(np.maximum(tr1, tr2), tr3)
        tr = np.insert(tr, 0, high[0] - low[0])
        
        # Calculate ATR
        atr = pd.Series(tr).rolling(window=atr_period).mean()
        
        # Calculate middle line (moving average of typical price)
        typical_price = (high + low + close) / 3
        if ma_type == 'sma':
            middle = pd.Series(typical_price).rolling(window=period).mean()
        else:  # ema
            middle = pd.Series(typical_price).ewm(span=period, adjust=False).mean()
        
        # Calculate upper and lower bands
        upper = middle + (multiplier * atr)
        lower = middle - (multiplier * atr)
        
        # Calculate trend
        trend = np.where(close > upper, 1,
                        np.where(close < lower, -1, 0))
        
        # Calculate distance from price to middle line
        distance = ((close - middle) / middle) * 100
        
        # Calculate channel width
        width = ((upper - lower) / middle) * 100
        
        # Calculate crossovers
        crossover = np.where(
            (close > middle) & (pd.Series(close).shift(1) <= middle.shift(1)), 1,
            np.where((close < middle) & (pd.Series(close).shift(1) >= middle.shift(1)), -1, 0)
        )
        
        # Calculate reversal signals
        reversal = np.zeros_like(close)
        reversal[close >= upper] = -1  # Overbought
        reversal[close <= lower] = 1   # Oversold
        
        # Calculate trend strength
        trend_strength = np.abs(distance) * (width / 100)
        
        # Calculate volatility state
        volatility_state = np.zeros_like(close)
        mean_width = pd.Series(width).rolling(window=period).mean()
        volatility_state[width > mean_width * 1.1] = 1  # High volatility
        volatility_state[width < mean_width * 0.9] = -1  # Low volatility
        
        self._values = {
            'middle': middle.values,
            'upper': upper.values,
            'lower': lower.values,
            'trend': trend,
            'distance': distance,
            'width': width,
            'crossover': crossover,
            'reversal': reversal,
            'trend_strength': trend_strength,
            'volatility_state': volatility_state,
            'atr': atr.values
        }
        return self._values
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_ma_types = ['sma', 'ema']
        if self.params['ma_type'].lower() not in valid_ma_types:
            raise ValueError(f"MA type must be one of {valid_ma_types}")
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        if self.params['atr_period'] < 1:
            raise ValueError("ATR period must be greater than 0")
        if self.params['multiplier'] <= 0:
            raise ValueError("Multiplier must be greater than 0")
        return True 