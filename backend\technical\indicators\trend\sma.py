from typing import Dict, Any, List
import numpy as np
import pandas as pd

from backend.technical.base_indicator import BaseIndicator

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
# from src.core.models.market_data import MarketData # Removed potentially unused import

class SMAIndicator(BaseIndicator):
    """Simple Moving Average indicator."""

    def __init__(self, period: int = 20, source: str = 'close'):
        """
        Initialize SMA indicator.

        Args:
            period: The period for calculating SMA
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4')
        """
        super().__init__({'period': period, 'source': source})
        self.name = 'SMA' # Store name if needed

    def calculate(self, data: pd.DataFrame) -> Dict[str, np.ndarray]: # Changed type hint to pd.DataFrame
        """Calculate SMA values."""
        # Removed: df = data.to_dataframe() - data is now expected to be a DataFrame
        df = data
        if df.empty or self.params['period'] > len(df): # Check if period is valid for data length
            print(f"SMAIndicator Error: Insufficient data for period {self.params['period']}.")
            # Return dictionary with expected keys but NaN arrays of appropriate length
            nan_array = np.full(len(df), np.nan)
            return {
                'sma': nan_array,
                'slope': nan_array,
                'distance': nan_array,
                'crossover': np.zeros(len(df), dtype=int), # Crossover defaults to 0
                'source': nan_array
            }

        # Get source data
        source_col = self.params['source'].lower()
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        elif source_col in df.columns:
             source_data = df[source_col]
        else:
            print(f"SMAIndicator Error: Source column '{source_col}' not found in DataFrame.")
            nan_array = np.full(len(df), np.nan)
            return {
                'sma': nan_array, 'slope': nan_array, 'distance': nan_array,
                'crossover': np.zeros(len(df), dtype=int), 'source': nan_array
            }

        # Calculate SMA
        sma = source_data.rolling(window=self.params['period']).mean()

        # Calculate slope for trend direction (handle potential NaNs)
        slope = pd.Series(np.gradient(sma.fillna(method='bfill').fillna(method='ffill')), index=sma.index) # Fill NaNs before gradient

        # Calculate distance from price (handle potential NaNs/zeros in sma)
        sma_safe = sma.replace(0, np.nan) # Avoid division by zero
        distance = ((source_data - sma) / sma_safe) * 100
        distance = distance.fillna(0) # Fill resulting NaNs with 0 distance

        # Calculate crossovers (handle potential NaNs)
        source_shifted = source_data.shift(1)
        sma_shifted = sma.shift(1)
        crossover = np.zeros(len(df), dtype=int) # Initialize with zeros
        # Conditions for crossover (ensure no NaNs involved in comparison)
        valid_indices = source_data.notna() & sma.notna() & source_shifted.notna() & sma_shifted.notna()
        crossover[valid_indices & (source_data > sma) & (source_shifted <= sma_shifted)] = 1
        crossover[valid_indices & (source_data < sma) & (source_shifted >= sma_shifted)] = -1


        return {
            'sma': sma.values,
            'slope': slope.values,
            'distance': distance.values,
            'crossover': crossover,
            'source': source_data.values
        }

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        return True
