from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class ThreeOutsidePatternIndicator(BaseIndicator):
    """Three Outside Up/Down pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """Initialize the indicator.
        
        Args:
            params: Dictionary of parameters
                - body_ratio: Minimum ratio of body to total range (default: 0.6)
                - shadow_ratio: Maximum ratio of shadow to total range (default: 0.2)
        """
        super().__init__(params)
        self._values = {}
        
    def calculate(self, market_data: MarketData) -> Dict[str, Any]:
        """Calculate the Three Outside Up/Down pattern.
        
        Args:
            market_data: MarketData object containing OHLCV data
            
        Returns:
            Dictionary containing:
                - is_pattern: Boolean indicating if pattern is present
                - pattern_type: String indicating pattern type ('up' or 'down')
                - pattern_id: String identifying the pattern
                - strength: Float between 0 and 1 indicating pattern strength
                - trend: Integer (-1 for bearish, 1 for bullish)
                - reliability: Integer (-1 for unreliable, 1 for reliable)
        """
        df = market_data.to_dataframe()
        
        # Calculate candle components
        body = df['close'] - df['open']
        body_abs = abs(body)
        total_range = df['high'] - df['low']
        upper_shadow = df['high'] - df[['open', 'close']].max(axis=1)
        lower_shadow = df[['open', 'close']].min(axis=1) - df['low']
        
        # Initialize pattern arrays
        is_pattern = np.zeros(len(df), dtype=bool)
        pattern_type = np.full(len(df), '')
        pattern_id = np.full(len(df), '')
        strength = np.zeros(len(df))
        trend = np.zeros(len(df))
        reliability = np.zeros(len(df))
        
        # Look for patterns
        for i in range(2, len(df)):
            # Get candle components using iloc
            first_body = body.iloc[i-2]
            first_body_abs = body_abs.iloc[i-2]
            first_total_range = total_range.iloc[i-2]
            first_body_ratio = first_body_abs / first_total_range
            
            second_body = body.iloc[i-1]
            second_body_abs = body_abs.iloc[i-1]
            second_total_range = total_range.iloc[i-1]
            second_upper_shadow = upper_shadow.iloc[i-1]
            second_lower_shadow = lower_shadow.iloc[i-1]
            
            third_body = body.iloc[i]
            third_body_abs = body_abs.iloc[i]
            third_total_range = total_range.iloc[i]
            
            # Check for Three Outside Up pattern
            if (first_body < 0 and  # First candle is bearish
                second_body > 0 and  # Second candle is bullish
                second_body > abs(first_body) and  # Second candle engulfs first
                third_body > 0 and  # Third candle is bullish
                third_body > second_body):  # Third candle closes above second
                
                is_pattern[i] = True
                pattern_type[i] = 'up'
                pattern_id[i] = f'three_outside_up_{i}'
                
                # Calculate strength based on body ratios
                strength[i] = min(1.0, (
                    (second_body_abs / second_total_range) +
                    (third_body_abs / third_total_range) +
                    (abs(first_body) / first_total_range)
                ) / 3)
                
                trend[i] = 1  # Bullish trend
                
                # Calculate reliability based on future price movement
                future_return = (df['close'].iloc[min(i+5, len(df)-1)] - df['close'].iloc[i]) / df['close'].iloc[i]
                reliability[i] = 1 if future_return > 0 else -1
                
            # Check for Three Outside Down pattern
            elif (first_body > 0 and  # First candle is bullish
                  second_body < 0 and  # Second candle is bearish
                  abs(second_body) > first_body and  # Second candle engulfs first
                  third_body < 0 and  # Third candle is bearish
                  third_body < second_body):  # Third candle closes below second
                
                is_pattern[i] = True
                pattern_type[i] = 'down'
                pattern_id[i] = f'three_outside_down_{i}'
                
                # Calculate strength based on body ratios
                strength[i] = min(1.0, (
                    (second_body_abs / second_total_range) +
                    (third_body_abs / third_total_range) +
                    (abs(first_body) / first_total_range)
                ) / 3)
                
                trend[i] = -1  # Bearish trend
                
                # Calculate reliability based on future price movement
                future_return = (df['close'].iloc[min(i+5, len(df)-1)] - df['close'].iloc[i]) / df['close'].iloc[i]
                reliability[i] = 1 if future_return < 0 else -1
        
        return {
            'is_pattern': is_pattern,
            'pattern_type': pattern_type,
            'pattern_id': pattern_id,
            'strength': strength,
            'trend': trend,
            'reliability': reliability
        }
        
    def validate_params(self) -> bool:
        """Validate the parameters.
        
        Returns:
            Boolean indicating if parameters are valid
        """
        if not self.params:
            return True
            
        body_ratio = self.params.get('body_ratio', 0.6)
        shadow_ratio = self.params.get('shadow_ratio', 0.2)
        
        return (0 < body_ratio <= 1 and
                0 < shadow_ratio <= 1 and
                body_ratio + shadow_ratio <= 1) 