const { exec } = require('child_process');
const { platform } = require('os');

// Start Vite dev server
console.log('Starting Vite dev server...');
const viteProcess = exec(
  platform() === 'win32' ? 'npm.cmd run react-dev' : 'npm run react-dev',
  { stdio: 'inherit' }
);

// Wait a bit for Vite to start
setTimeout(() => {
  console.log('Starting Electron app...');
  // Start Electron in dev mode
  const electronProcess = exec(
    platform() === 'win32' ? 'npm.cmd run dev-win' : 'npm run dev',
    { stdio: 'inherit' }
  );

  // Handle process termination
  electronProcess.on('close', () => {
    if (viteProcess && viteProcess.kill) {
      viteProcess.kill();
    }
    process.exit();
  });
}, 3000);

// Handle script termination
process.on('SIGINT', () => {
  if (viteProcess && viteProcess.kill) {
    viteProcess.kill();
  }
  process.exit();
});
