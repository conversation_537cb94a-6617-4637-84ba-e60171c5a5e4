import subprocess
import sys
import time
import atexit
import signal

# Define processes
backend_process = None
frontend_process = None

def start_backend():
    """Start the FastAPI backend server"""
    global backend_process

    # For Windows, use a direct approach with cwd parameter
    cmd = "python run_server.py"

    print("Starting backend server...")
    backend_process = subprocess.Popen(cmd, shell=True, cwd="backend")
    print("Backend server started.")

def start_frontend():
    """Start the Electron frontend"""
    global frontend_process

    # Start Electron from the root directory
    cmd = "npm start"

    print("Starting frontend application...")
    frontend_process = subprocess.Popen(cmd, shell=True)
    print("Frontend application started.")

def cleanup():
    """Clean up processes on exit"""
    if backend_process:
        print("Stopping backend server...")
        try:
            backend_process.terminate()
            # Wait for process to terminate
            backend_process.wait(timeout=5)
        except Exception as e:
            print(f"Error stopping backend server: {e}")
            # Force kill if terminate fails
            try:
                backend_process.kill()
            except:
                pass

    if frontend_process:
        print("Stopping frontend application...")
        try:
            frontend_process.terminate()
            # Wait for process to terminate
            frontend_process.wait(timeout=5)
        except Exception as e:
            print(f"Error stopping frontend application: {e}")
            # Force kill if terminate fails
            try:
                frontend_process.kill()
            except:
                pass

    print("All processes stopped.")

def signal_handler(sig, frame):
    """Handle signals like SIGINT (Ctrl+C) and SIGTERM"""
    print(f"\nReceived signal {sig}. Stopping application...")
    cleanup()
    sys.exit(0)

if __name__ == "__main__":
    # Register cleanup function
    atexit.register(cleanup)

    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # Termination request

    try:
        # Start backend
        start_backend()

        # Wait for backend to start
        print("Waiting for backend to start...")
        time.sleep(3)

        # Start frontend
        start_frontend()

        # Keep the script running
        print("Application is running. Press Ctrl+C to stop.")
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        print("\nStopping application...")
        sys.exit(0)
