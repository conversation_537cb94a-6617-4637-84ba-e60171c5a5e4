// Firebase configuration
// Using the actual Firebase project credentials
// Updated Firebase Web App Configuration
const firebaseConfig = {
  apiKey: "AIzaSyDy4ZLFuoYNKK6JNeweiJmsXuhMFjA_WqU",
  authDomain: "garuda-algo.firebaseapp.com",
  projectId: "garuda-algo",
  storageBucket: "garuda-algo.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:323e876335aa3975510a5f"
  // measurementId is not present in the new config provided by the user
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();
const db = firebase.firestore();

// Registration functions
function registerUser(email, password, brokerAccount, affiliateCode) {
  return auth.createUserWithEmailAndPassword(email, password)
    .then((userCredential) => {
      // Add user data to Firestore
      return db.collection('users').doc(userCredential.user.uid).set({
        email: email,
        brokerAccount: brokerAccount,
        affiliateCode: affiliateCode,
        verified: false,
        registrationDate: firebase.firestore.FieldValue.serverTimestamp()
      });
    });
}

// Login function
function loginUser(email, password) {
  return auth.signInWithEmailAndPassword(email, password);
}

// Password reset function
function resetPassword(email) {
  return auth.sendPasswordResetEmail(email);
}

// Check if user is verified
function checkUserVerification(userId) {
  return db.collection('users').doc(userId).get()
    .then((doc) => {
      if (doc.exists) {
        return doc.data().verified;
      } else {
        return false;
      }
    });
}

// Check if broker account is under affiliate
function checkAffiliateAccount(brokerAccount, affiliateCode) {
  // This is a placeholder function
  // In a real implementation, you would check against your affiliate API
  // or a database of valid affiliate accounts

  // For now, we'll simulate a check with some predefined affiliate codes
  const validAffiliateCodes = {
    'EXNESS': ['63cflrfv9z'],
    'HFM': ['********']
  };

  // Extract broker name from account (this is just an example)
  const brokerName = brokerAccount.substring(0, 6).toUpperCase();

  if (validAffiliateCodes[brokerName] &&
      validAffiliateCodes[brokerName].includes(affiliateCode)) {
    return Promise.resolve(true);
  } else {
    return Promise.resolve(false);
  }
}

// Export functions
window.firebaseAuth = {
  registerUser,
  loginUser,
  checkUserVerification,
  checkAffiliateAccount
};
