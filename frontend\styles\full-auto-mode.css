/* Full Auto Mode Styles */
.full-auto-mode-page {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background: var(--bg-primary, #0a0a0a);
  color: var(--text-primary, #ffffff);
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-header h1 {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(135deg, #3772ff 0%, #00d4ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
}

.page-description {
  font-size: 1.2rem;
  color: var(--text-secondary, #b0b0b0);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Error Message */
.error-message {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(239, 68, 68, 0.1) 100%);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--danger, #ef4444);
}

.error-icon {
  font-size: 1.5rem;
}

/* Setup Section */
.setup-section {
  margin-bottom: 3rem;
}

.setup-card {
  background: linear-gradient(135deg, rgba(55, 114, 255, 0.1) 0%, rgba(0, 212, 255, 0.05) 100%);
  border: 1px solid rgba(55, 114, 255, 0.2);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.setup-card h2 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: var(--primary, #3772ff);
}

/* Form Group */
.form-group {
  margin-bottom: 2rem;
}

.form-group label {
  display: block;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.form-group input {
  width: 100%;
  max-width: 400px;
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary, #3772ff);
  box-shadow: 0 0 0 2px rgba(55, 114, 255, 0.2);
}

.form-help {
  display: block;
  font-size: 0.85rem;
  color: var(--text-tertiary, #6b7280);
  margin-top: 0.5rem;
}

/* Quick Select Section */
.quick-select-section {
  margin-bottom: 2rem;
}

.quick-select-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.quick-select-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.quick-select-btn {
  padding: 1rem;
  border: 1px solid;
  border-radius: 12px;
  background: transparent;
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.quick-select-btn.conservative {
  border-color: rgba(16, 185, 129, 0.3);
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
}

.quick-select-btn.conservative:hover {
  border-color: rgba(16, 185, 129, 0.5);
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(16, 185, 129, 0.1) 100%);
}

.quick-select-btn.balanced {
  border-color: rgba(59, 130, 246, 0.3);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
}

.quick-select-btn.balanced:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(59, 130, 246, 0.1) 100%);
}

.quick-select-btn.aggressive {
  border-color: rgba(239, 68, 68, 0.3);
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%);
}

.quick-select-btn.aggressive:hover {
  border-color: rgba(239, 68, 68, 0.5);
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(239, 68, 68, 0.1) 100%);
}

.quick-select-btn.clear {
  border-color: rgba(156, 163, 175, 0.3);
  background: linear-gradient(135deg, rgba(156, 163, 175, 0.1) 0%, rgba(156, 163, 175, 0.05) 100%);
}

.quick-select-btn.clear:hover {
  border-color: rgba(156, 163, 175, 0.5);
  background: linear-gradient(135deg, rgba(156, 163, 175, 0.2) 0%, rgba(156, 163, 175, 0.1) 100%);
}

/* Symbol Selection */
.symbol-selection-section {
  margin-bottom: 2rem;
}

.symbol-selection-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.symbol-category {
  margin-bottom: 2rem;
}

.symbol-category h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

.symbol-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 0.75rem;
}

.symbol-card {
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.symbol-card:hover {
  border-color: var(--primary, #3772ff);
  background: rgba(55, 114, 255, 0.1);
  transform: translateY(-2px);
}

.symbol-card.selected {
  border-color: var(--success, #10b981);
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(16, 185, 129, 0.1) 100%);
}

.symbol-name {
  font-weight: 600;
  font-size: 0.9rem;
}

.selected-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  color: var(--success, #10b981);
  font-weight: bold;
  font-size: 1rem;
}

/* Selected Summary */
.selected-summary {
  margin-bottom: 2rem;
  padding: 1rem;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 12px;
}

.selected-summary h4 {
  margin-bottom: 1rem;
  color: var(--success, #10b981);
}

.selected-symbols {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.selected-symbol-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(16, 185, 129, 0.2);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--success, #10b981);
}

.remove-symbol {
  background: none;
  border: none;
  color: var(--success, #10b981);
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: bold;
  padding: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-symbol:hover {
  background: rgba(16, 185, 129, 0.3);
}

/* Start Section */
.start-section {
  text-align: center;
}

.start-auto-mode-btn {
  padding: 1.25rem 3rem;
  font-size: 1.2rem;
  font-weight: 700;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #3772ff 0%, #00d4ff 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 2rem;
  min-width: 250px;
}

.start-auto-mode-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(55, 114, 255, 0.3);
}

.start-auto-mode-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.auto-mode-features {
  max-width: 600px;
  margin: 0 auto;
  text-align: left;
}

.auto-mode-features h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--primary, #3772ff);
  text-align: center;
}

.auto-mode-features ul {
  list-style: none;
  padding: 0;
}

.auto-mode-features li {
  padding: 0.5rem 0;
  font-size: 1rem;
  color: var(--text-secondary);
}

/* Running Section */
.running-section {
  margin-bottom: 3rem;
}

.status-card {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.status-card h2 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: var(--success, #10b981);
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-label {
  font-weight: 600;
  color: var(--text-secondary);
}

.status-value {
  font-weight: 700;
  color: var(--text-primary);
}

.status-value.positive {
  color: var(--success, #10b981);
}

.status-value.negative {
  color: var(--danger, #ef4444);
}

.control-section {
  text-align: center;
}

.stop-auto-mode-btn {
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 700;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stop-auto-mode-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
}

.stop-auto-mode-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Info Panel */
.info-panel {
  background: linear-gradient(135deg, rgba(156, 163, 175, 0.1) 0%, rgba(156, 163, 175, 0.05) 100%);
  border: 1px solid rgba(156, 163, 175, 0.2);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.info-panel h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.info-content p {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.feature-item {
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.feature-item h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--primary, #3772ff);
}

.feature-item p {
  font-size: 0.95rem;
  line-height: 1.5;
  color: var(--text-tertiary);
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .full-auto-mode-page {
    padding: 1rem;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
  
  .quick-select-buttons {
    grid-template-columns: 1fr;
  }
  
  .symbol-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .feature-grid {
    grid-template-columns: 1fr;
  }
}
