"""
Main entry point for the Garuda Algo Admin Panel application.
"""
import sys
import os
import logging

# Add project root to path to allow absolute imports like 'src.utils' and 'admin.ui'
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMainWindow, QTabWidget

# Import admin-specific UI components using absolute paths from root
from admin.ui.widgets.account_manager import AccountManagerWidget
from admin.ui.widgets.session_monitor import SessionMonitorWidget

# Import shared utilities using absolute paths from root
# Use standard logging instead of custom modules
import logging
# Import auth module for initialization
from admin.core import firebase_auth

# Define AdminAppWindow here (or import from admin.ui.admin_window if preferred)
class AdminAppWindow(QMainWindow):
    """Admin Panel Main Window."""
    def __init__(self): # Removed settings parameter
        super().__init__()
        # self.settings = settings # Removed unused attribute assignment
        logging.info("AdminAppWindow: Initializing...")
        self.setWindowTitle("Garuda Algo - Admin Panel")
        self.setMinimumSize(1000, 700)
        self._init_ui()
        logging.info("AdminAppWindow: Initialization complete.")

    def _init_ui(self):
        """Create and add the main admin UI tabs."""
        logging.info("Initializing Admin UI tabs...")
        self.tabs = QTabWidget()
        self.setCentralWidget(self.tabs)

        # Instantiate actual admin widgets
        self.account_manager_tab = AccountManagerWidget(parent=self)
        self.session_monitor_tab = SessionMonitorWidget(parent=self)

        # Add tabs
        self.tabs.addTab(self.account_manager_tab, "Account Management")
        self.tabs.addTab(self.session_monitor_tab, "Session Monitoring")

        # Connect account updates to session monitor user filter
        self.account_manager_tab.accounts_updated.connect(self.session_monitor_tab.update_user_filter_combo)
        logging.info("Admin UI tabs initialized and signals connected.")

    def closeEvent(self, event):
        logging.info("Admin Panel closing.")
        super().closeEvent(event)


def main_admin():
    """Main function to run the Admin Panel application."""
    # Set up logging with a separate log file for the admin panel
    admin_log_filename = "garuda_algo_admin.log"
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),  # Console handler
            logging.FileHandler(admin_log_filename)  # File handler
        ]
    )
    logging.info("--- Garuda Algo Admin Panel Starting ---")

    # Load configuration (might share some settings)
    # Configuration loading might happen within Firebase init or elsewhere if needed
    # logging.info("Admin Panel: Configuration loaded.") # Commented out as settings variable was removed

    # Initialize Firebase (required for admin functions)
    if not firebase_auth.initialize_firebase():
        logging.critical("Admin Panel: Firebase initialization failed. Cannot continue.")
        # Optionally show a critical error dialog before exiting
        # QMessageBox.critical(None, "Firebase Error", "Failed to initialize Firebase. Check credentials.")
        sys.exit(1)
    logging.info("Admin Panel: Firebase initialized.")

    # Initialize Qt Application
    app = QApplication(sys.argv)
    app.setOrganizationName("GarudaAlgo")
    app.setApplicationName("AdminPanel") # Distinct name

    # Create and show the main admin window
    try:
        logging.info("Creating AdminAppWindow...")
        window = AdminAppWindow() # Removed settings argument
        window.show()
        logging.info("AdminAppWindow shown.")
    except Exception as e:
        logging.critical(f"Failed to create or show AdminAppWindow: {e}", exc_info=True)
        sys.exit(1)

    # Start the Qt event loop
    logging.info("Starting Admin Panel Qt event loop...")
    exit_code = app.exec_()
    logging.info(f"Admin Panel exited with code {exit_code}.")
    sys.exit(exit_code)

if __name__ == "__main__":
    # Path adjustment is now done at the top of the file
    main_admin()
