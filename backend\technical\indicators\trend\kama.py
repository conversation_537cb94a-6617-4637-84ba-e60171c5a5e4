from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class KAMAIndicator(BaseIndicator):
    """<PERSON>'s Adaptive Moving Average (KAMA) indicator."""

    def __init__(self, period: int = 10, fast_ema_period: int = 2,
                 slow_ema_period: int = 30, source: str = 'close'):
        """
        Initialize Kaufman's Adaptive Moving Average indicator.

        Args:
            period: The period for the Efficiency Ratio (ER) calculation.
            fast_ema_period: The period for the fastest EMA smoothing constant.
            slow_ema_period: The period for the slowest EMA smoothing constant.
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
        """
        super().__init__({
            'period': period,
            'fast_ema_period': fast_ema_period,
            'slow_ema_period': slow_ema_period,
            'source': source
        })

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate <PERSON>'s Adaptive Moving Average."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period']:
             return {'kama': np.array([])}

        period = self.params['period']
        fast_ema_period = self.params['fast_ema_period']
        slow_ema_period = self.params['slow_ema_period']
        source_col = self.params['source'].lower()

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Calculate Efficiency Ratio (ER)
        change = source_data.diff(period).abs()
        volatility = source_data.diff().abs().rolling(window=period).sum()
        # Avoid division by zero
        volatility_safe = volatility.replace(0, np.nan)
        er = (change / volatility_safe).fillna(0) # Fill NaNs if volatility is zero

        # Calculate Smoothing Constant (SC)
        fast_sc = 2 / (fast_ema_period + 1)
        slow_sc = 2 / (slow_ema_period + 1)
        sc = (er * (fast_sc - slow_sc) + slow_sc) ** 2

        # Calculate KAMA iteratively
        kama_values = pd.Series(np.nan, index=source_data.index)
        # Initialize first KAMA value (use simple average or first price)
        first_valid_idx = source_data.first_valid_index()
        if first_valid_idx is not None:
             kama_values.loc[first_valid_idx] = source_data.loc[first_valid_idx]

             # Iterate from the second valid index
             for i in range(source_data.index.get_loc(first_valid_idx) + 1, len(source_data)):
                 idx = source_data.index[i]
                 prev_idx = source_data.index[i-1]
                 if pd.notna(kama_values.loc[prev_idx]) and pd.notna(sc.loc[idx]) and pd.notna(source_data.loc[idx]):
                     kama_values.loc[idx] = kama_values.loc[prev_idx] + sc.loc[idx] * (source_data.loc[idx] - kama_values.loc[prev_idx])
                 elif pd.isna(kama_values.loc[prev_idx]) and pd.notna(source_data.loc[idx]): # Handle initial NaNs in KAMA
                     kama_values.loc[idx] = source_data.loc[idx]


        self._values = {
            'kama': kama_values.values,
            'efficiency_ratio': er.values, # Optional
            'smoothing_constant': sc.values # Optional
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 1:
            raise ValueError("ER Period must be greater than 0")
        if self.params['fast_ema_period'] < 1:
            raise ValueError("Fast EMA Period must be greater than 0")
        if self.params['slow_ema_period'] < 1:
            raise ValueError("Slow EMA Period must be greater than 0")
        if self.params['fast_ema_period'] >= self.params['slow_ema_period']:
             raise ValueError("Fast EMA Period must be less than Slow EMA Period")
        return True