import MetaTrader5 as mt5
from enum import Enum # Keep for other potential states if any, or remove if ConnectionState is fully moved
import time
import logging
import json
import os
import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union, Any, NamedTuple
import sys

# Component imports
from .components.connection import ConnectionComponent
from .types import ConnectionState, AccountInfo, SymbolInfo
# Import all exceptions from the dedicated exceptions module
from .exceptions import (
    MT5Error, 
    MT5ConnectionError, 
    MT5AuthenticationError, 
    MT5TimeoutError, # Ensure this is defined in exceptions.py if used
    MT5SettingsError, 
    MT5SymbolError
)

# Firebase utils are now primarily used by ConnectionComponent, but keep if other parts need it.
# from backend.firebase_utils import get_active_license_details # This is now handled in ConnectionComponent

# Centralized path logic is now within ConnectionComponent for settings.
# Logging setup for the main module
# data_root and data_dir logic for logging path can remain here if this module logs independently.
app_data_root = os.getenv('APPDATA')
if not app_data_root:
    app_data_root = os.path.expanduser("~")
    # Use a more specific logger name for this setup warning if needed
    logging.getLogger("MT5Integration.Setup").warning(
        f"APPDATA environment variable not found. Using home directory as data root for logs: {app_data_root}")

log_dir = os.path.join(app_data_root, 'GarudaAlgo', 'logs') # Specific logs directory
os.makedirs(log_dir, exist_ok=True)

# Configure logging for this main MT5Integration module
# Other components will have their own loggers (e.g., "MT5Integration.Connection")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(log_dir, "mt5_integration_main.log")), # Main log file
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("MT5Integration") # Logger for this specific file/class

# ConnectionState is now imported from .types

# Custom MT5Error exceptions are now imported from .exceptions
# class MT5Error(Exception):
#     """Base class for MT5 integration errors"""
#     pass
# 
# class MT5ConnectionError(MT5Error):
#     """Raised when connection to MT5 terminal fails"""
#     pass
# 
# class MT5AuthenticationError(MT5Error):
#     """Raised when authentication with MT5 server fails"""
#     pass
# 
# class MT5TimeoutError(MT5Error): 
#     """Raised when an MT5 operation times out"""
#     pass
# 
# class MT5SettingsError(MT5Error):
#     """Raised when there are issues with settings loading or validation"""
#     pass
# 
# class MT5SymbolError(MT5Error):
#     """Raised when there are issues with symbol operations"""
#     pass

# AccountInfo and SymbolInfo are now imported from .types
# class AccountInfo(NamedTuple):
#     """Account information data structure"""
#     balance: float
#     equity: float
#     name: str
#     leverage: int
#     server: str
#     last_updated: datetime

# class SymbolInfo(NamedTuple):
#     """Symbol information data structure"""
#     name: str
#     digits: int  # Number of decimal places
#     trade_mode: str  # forex, futures, etc.
#     currency_base: str
#     currency_profit: str
#     currency_margin: str
#     visible: bool
#     custom: bool
#     last_updated: datetime

class MT5Integration:
    def __init__(self):
        self.logger = logging.getLogger("MT5Integration.Main") # More specific logger name
        self.connection = ConnectionComponent() # Instantiate the connection component
        
        # Initialize enhanced trading component with advanced trade management features
        try:
            # First try to import the enhanced version
            from .components.trading_enhanced import TradingComponent
            self.logger.info("Using enhanced trading component with advanced trade management")
        except ImportError:
            # Fall back to original if enhanced not available
            from .components.trading import TradingComponent
            self.logger.warning("Enhanced trading component not found, using basic version")
        
        # Initialize trading component
        self.trading = TradingComponent(self.connection)
        
        # Initialize exit strategies component
        try:
            from .components.exit_strategies import ExitStrategiesComponent
            self.exit_strategies = ExitStrategiesComponent(self.connection, self.trading)
            self.logger.info("Exit strategies component initialized")
        except ImportError:
            self.exit_strategies = None
            self.logger.warning("Exit strategies component not available")
        
        # Caching for account and symbols remains in the main class for now,
        # as they are not directly part of the connection lifecycle itself.
        self._account_info_cache: Optional[AccountInfo] = None
        self._symbols_cache: Optional[Dict[str, SymbolInfo]] = None
        self._cache_duration = timedelta(seconds=5)  # Default cache duration for account info
        self._symbols_cache_duration = timedelta(minutes=5)  # Symbols cache for 5 minutes
        
        # _license_info is now managed by ConnectionComponent
        # self._last_error, _state, _connection_time etc. are also managed by ConnectionComponent

    @property
    def state(self) -> ConnectionState:
        """Delegates to ConnectionComponent's state."""
        return self.connection.state

    @property
    def connection_latency(self) -> Optional[float]:
        """Delegates to ConnectionComponent's connection_latency."""
        return self.connection.connection_latency

    # _load_settings and _save_settings are now internal to ConnectionComponent
    # def _load_settings(self, ...) -> ...:
    # def _save_settings(self, ...) -> None:

    def initialize(self, path: Optional[str] = None, login: Optional[int] = None,
                  password: Optional[str] = None, server: Optional[str] = None,
                  timeout: Optional[int] = None, settings_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Initialize connection to MetaTrader 5 terminal using the ConnectionComponent.
        """
        self.logger.info("MT5Integration: Initiating connection process.")
        try:
            # Delegate to the connection component's initialize method
            result = self.connection.initialize(
                path=path,
                login=login,
                password=password,
                server=server,
                timeout=timeout,
                settings_path=settings_path
            )
            # self.connection._license_info will be set by its initialize method
            self.logger.info(f"MT5Integration: Connection process completed. Success: {result.get('success')}")
            return result
        except (MT5ConnectionError, MT5AuthenticationError, MT5SettingsError, ValueError) as e:
            self.logger.error(f"MT5Integration: Connection failed during initialization: {e}")
            # The ConnectionComponent's state and last_error should already be set.
            # Re-raise the specific error to be handled by the caller (e.g., API endpoint)
            raise
        except Exception as e:
            self.logger.error(f"MT5Integration: Unexpected error during initialization: {e}", exc_info=True)
            # Ensure state reflects error if not already set by component
            if self.connection.state != ConnectionState.ERROR:
                 # This part is tricky, as component might not have an explicit error state setter for this case
                 # For now, rely on component's error handling.
                 pass
            raise MT5ConnectionError(f"Unexpected error during MT5Integration initialization: {e}")


    def validate_connection(self) -> bool:
        """Delegates to ConnectionComponent's validate_connection."""
        if not self.connection:
            self.logger.warning("Validate connection called but ConnectionComponent not initialized.")
            return False
        return self.connection.validate_connection()

    def disconnect(self) -> Dict[str, Any]:
        """Delegates to ConnectionComponent's disconnect."""
        self.logger.info("MT5Integration: Initiating disconnection.")
        if not self.connection:
            return {"success": True, "message": "ConnectionComponent not initialized, effectively disconnected."}
        
        # Clear local caches upon disconnection
        self._account_info_cache = None
        self._symbols_cache = None
        # self.connection._license_info is managed by ConnectionComponent, will be None after disconnect/re-init
        
        return self.connection.disconnect()

    def is_connected(self) -> bool:
        """
        Check if connected to MetaTrader 5 terminal.
        
        Returns:
            bool: True if connected, False otherwise
        """
        return self.connection.is_connected()
        
    def modify_position(self, ticket: int, sl: Optional[float] = None, tp: Optional[float] = None) -> Dict[str, Any]:
        """
        Modify stop loss and/or take profit of an open position.
        
        Args:
            ticket: Position ticket
            sl: New stop loss price (None to keep current)
            tp: New take profit price (None to keep current)
            
        Returns:
            Dict with modify result
        """
        return self.trading.modify_position(ticket=ticket, sl=sl, tp=tp)

    @property
    def _license_info(self) -> Optional[Dict[str, Any]]:
        """Access license_info from the connection component."""
        if self.connection:
            return self.connection._license_info
        return None

    def get_account_info(self, use_cache: bool = True) -> Optional[AccountInfo]:
        """
        Get current account information, with optional caching.
        This method remains in MT5Integration as it's about data fetching, not connection itself.
        Args:
            use_cache: Whether to use cached account info (if available and not expired)
        Returns:
            AccountInfo tuple if successful, None if failed or not connected
        """
        try:
            if use_cache and self._account_info_cache and \
               (datetime.now() - self._account_info_cache.last_updated < self._cache_duration):
                self.logger.debug("Returning cached account info")
                return self._account_info_cache

            if not self.is_connected(): # Uses the delegated is_connected
                self.logger.warning("Cannot get account info: Not connected")
                return None

            account_info_mt5 = mt5.account_info() # Direct MT5 call
            if not account_info_mt5:
                self.logger.error("Failed to retrieve account info from MT5")
                return None
            
            # terminal_info = mt5.terminal_info() # Not strictly needed for AccountInfo NamedTuple
            # if not terminal_info:
            #     self.logger.error("Failed to retrieve terminal info from MT5 for account info context")
            #     return None # Or proceed without it if not critical for AccountInfo fields

            current_time = datetime.now()
            info = AccountInfo(
                balance=float(account_info_mt5.balance),
                equity=float(account_info_mt5.equity),
                name=account_info_mt5.name,
                leverage=account_info_mt5.leverage,
                server=account_info_mt5.server,
                last_updated=current_time
            )
            self._account_info_cache = info
            return info
        except Exception as e:
            self.logger.error(f"Error retrieving account info: {e}", exc_info=True)
            return None

    def get_connection_info(self) -> Dict[str, Any]:
        """
        Get current connection state and details, including account and license info.
        Combines info from ConnectionComponent with account details if connected.
        """
        if not self.connection:
            self.logger.error("get_connection_info called but ConnectionComponent not initialized.")
            return {"state": ConnectionState.ERROR.value, "connected": False, "last_error": "ConnectionComponent not initialized."}

        # Get base connection info from the component
        conn_info = self.connection.get_connection_info()
        
        # Add license info from the component's stored license details
        conn_info["license_info"] = self.connection._license_info # Access directly or via a property

        # If connected, try to add enhanced account information
        if conn_info.get("connected"):
            try:
                # Use the local get_account_info which handles its own caching
                account_summary = self.get_account_info(use_cache=True) 
                
                if account_summary:
                    # For more detailed live data for the status endpoint:
                    mt5_account_info_live = mt5.account_info()
                    positions_count = 0
                    try:
                        positions = mt5.positions_get()
                        if positions is not None: positions_count = len(positions)
                    except Exception as e_pos:
                        self.logger.warning(f"Error getting positions count for connection_info: {e_pos}")

                    conn_info["account_info"] = {
                        "balance": float(mt5_account_info_live.balance) if mt5_account_info_live else account_summary.balance,
                        "equity": float(mt5_account_info_live.equity) if mt5_account_info_live else account_summary.equity,
                        "name": account_summary.name, # From cached/basic AccountInfo
                        "leverage": account_summary.leverage, # From cached/basic AccountInfo
                        "server": account_summary.server, # From cached/basic AccountInfo
                        "positions": positions_count,
                        "margin": float(mt5_account_info_live.margin) if mt5_account_info_live else 0,
                        "margin_free": float(mt5_account_info_live.margin_free) if mt5_account_info_live else 0,
                        "margin_level": float(mt5_account_info_live.margin_level) if mt5_account_info_live else 0,
                        "currency": mt5_account_info_live.currency if mt5_account_info_live else "USD", # Default if live fails
                        "trade_mode": mt5_account_info_live.trade_mode if mt5_account_info_live else 0  # 0 for Demo, 1 for Real
                    }
                else:
                    conn_info["account_info"] = None
            except Exception as acc_err:
                self.logger.error(f"Error enhancing connection_info with account details: {acc_err}", exc_info=True)
                conn_info["account_info_error"] = str(acc_err)
                conn_info["account_info"] = None
        else: # Not connected
            conn_info["account_info"] = None
            
        return conn_info

    def get_symbols(self, include_metadata: bool = False, use_cache: bool = True, force_refresh: bool = False) -> Union[List[str], List[SymbolInfo]]:
        """
        Get list of available trading symbols from MT5.

        Args:
            include_metadata: If True, returns full SymbolInfo objects instead of just symbol names
            use_cache: Whether to use cached symbol info (if available and not expired)
            force_refresh: Force refresh of cache even if not expired

        Returns:
            List of symbol names or SymbolInfo objects if include_metadata=True

        Raises:
            MT5ConnectionError: If not connected to MT5
            MT5SymbolError: If symbol retrieval fails
        """
        if not self.is_connected():
            logger.error("Cannot fetch symbols: Not connected to MT5")
            raise MT5ConnectionError("Not connected to MT5")

        try:
            current_time = datetime.now()

            # Check cache if enabled and not forcing refresh
            if (use_cache and not force_refresh and self._symbols_cache and
                (current_time - next(iter(self._symbols_cache.values())).last_updated < self._symbols_cache_duration)):
                logger.debug("Returning cached symbol information")
                if include_metadata:
                    return list(self._symbols_cache.values())
                return sorted(self._symbols_cache.keys())

            symbol_dict = {}

            # Try to get all symbols first
            logger.info("Attempting to fetch all available symbols...")
            raw_symbols = mt5.symbols_get()

            if raw_symbols:
                logger.info(f"Found {len(raw_symbols)} symbols in general fetch")
                for symbol_info_mt5 in raw_symbols:
                    try:
                        symbol_info = SymbolInfo(
                            name=symbol_info_mt5.name,
                            digits=symbol_info_mt5.digits,
                            trade_mode=symbol_info_mt5.trade_mode,
                            currency_base=symbol_info_mt5.currency_base,
                            currency_profit=symbol_info_mt5.currency_profit,
                            currency_margin=symbol_info_mt5.currency_margin,
                            visible=symbol_info_mt5.visible,
                            custom=symbol_info_mt5.custom,
                            last_updated=current_time
                        )
                        symbol_dict[symbol_info_mt5.name] = symbol_info
                    except Exception as e:
                        logger.error(f"Error processing symbol {symbol_info_mt5.name}: {e}")

            if not symbol_dict:  # If general fetch failed or returned no symbols
                # Try specific groups if general fetch fails
                logger.info("General fetch failed, trying specific symbol groups...")
                symbol_groups = ["Forex", "CFD", "Crypto", "Futures", "Metals"]

                for group in symbol_groups:
                    try:
                        group_symbols = mt5.symbols_get(group=group)
                        if group_symbols:
                            logger.info(f"Found {len(group_symbols)} symbols in {group} group")
                            for symbol_info_mt5 in group_symbols:
                                try:
                                    if symbol_info_mt5.name not in symbol_dict:  # Avoid duplicates
                                        symbol_info = SymbolInfo(
                                            name=symbol_info_mt5.name,
                                            digits=symbol_info_mt5.digits,
                                            trade_mode=symbol_info_mt5.trade_mode,
                                            currency_base=symbol_info_mt5.currency_base,
                                            currency_profit=symbol_info_mt5.currency_profit,
                                            currency_margin=symbol_info_mt5.currency_margin,
                                            visible=symbol_info_mt5.visible,
                                            custom=symbol_info_mt5.custom,
                                            last_updated=current_time
                                        )
                                        symbol_dict[symbol_info_mt5.name] = symbol_info
                                except Exception as e:
                                    logger.error(f"Error processing symbol {symbol_info_mt5.name} from {group} group: {e}")
                    except Exception as e:
                        logger.warning(f"Error fetching {group} group: {e}")

            # If both attempts failed, try Market Watch as last resort
            if not symbol_dict:
                logger.info("No symbols found through direct fetch, trying Market Watch...")
                total_symbols = mt5.symbols_total()

                if total_symbols is None or total_symbols == 0:
                    logger.warning("mt5.symbols_total() returned 0 or None. No symbols found in Market Watch.")
                    raise MT5SymbolError("No symbols found. Please check MT5 connection and symbol availability.")

                for i in range(total_symbols):
                    try:
                        symbol_info_mt5 = mt5.symbol_info_by_index(i)
                        if symbol_info_mt5:
                            symbol_info = SymbolInfo(
                                name=symbol_info_mt5.name,
                                digits=symbol_info_mt5.digits,
                                trade_mode=symbol_info_mt5.trade_mode,
                                currency_base=symbol_info_mt5.currency_base,
                                currency_profit=symbol_info_mt5.currency_profit,
                                currency_margin=symbol_info_mt5.currency_margin,
                                visible=symbol_info_mt5.visible,
                                custom=symbol_info_mt5.custom,
                                last_updated=current_time
                            )
                            symbol_dict[symbol_info_mt5.name] = symbol_info
                    except Exception as e:
                        logger.error(f"Error processing symbol at index {i}: {e}")

            if not symbol_dict:
                terminal_state = mt5.terminal_info()
                error_msg = (
                    "Failed to retrieve any symbols. "
                    f"Terminal connected: {terminal_state.connected if terminal_state else 'Unknown'}, "
                    f"Trade allowed: {terminal_state.trade_allowed if terminal_state else 'Unknown'}, "
                    f"Market data: {mt5.last_error()}"
                )
                logger.error(error_msg)
                raise MT5SymbolError(error_msg)

            # Update cache
            self._symbols_cache = symbol_dict

            # Log success without timing info since we don't need it
            logger.info(f"Successfully fetched {len(symbol_dict)} symbols")

            if include_metadata:
                return list(symbol_dict.values())
            return sorted(symbol_dict.keys())

        except MT5Error:
            raise
        except Exception as e:
            logger.exception("Unexpected error fetching symbols")
            raise MT5SymbolError(f"Unexpected error fetching symbols: {e}")

    def clear_symbol_cache(self) -> None:
        """Clear the symbol cache to force fresh data on next fetch"""
        self._symbols_cache = None
        logger.info("Symbol cache cleared")

    def fetch_ohlc_data(self, symbol: str, timeframe: int, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """
        Fetch OHLC data for a given symbol and timeframe.

        Args:
            symbol: The trading symbol (e.g., 'EURUSD')
            timeframe: The timeframe in seconds (e.g., mt5.TIMEFRAME_M1 for 1 minute)
            start_time: The start time for the data range
            end_time: The end time for the data range

        Returns:
            A list of dictionaries containing OHLC data for the specified range.

        Raises:
            MT5ConnectionError: If not connected to MT5
            MT5SymbolError: If symbol retrieval fails
        """
        if not self.is_connected():
            logger.error('Cannot fetch OHLC data: Not connected to MT5')
            raise MT5ConnectionError('Not connected to MT5')

        try:
            logger.info(f'Fetching OHLC data for {symbol} on {timeframe} from {start_time} to {end_time}...')
            start_time_ts = int(start_time.timestamp()) # Renamed to avoid conflict
            end_time_ts = int(end_time.timestamp()) # Renamed to avoid conflict
            ohlc_data = mt5.copy_rates_range(symbol, timeframe, start_time_ts, end_time_ts)

            if ohlc_data is None or len(ohlc_data) == 0:
                error = mt5.last_error()
                logger.error(f'Failed to fetch OHLC data: {error}')
                raise MT5SymbolError(f'Failed to fetch OHLC data: {error}')

            # Convert to list of dictionaries
            ohlc_list = []
            for rate in ohlc_data:
                ohlc_list.append({
                    'time': datetime.fromtimestamp(rate['time']).isoformat(),
                    'open': float(rate['open']),
                    'high': float(rate['high']),
                    'low': float(rate['low']),
                    'close': float(rate['close']),
                    'tick_volume': int(rate['tick_volume']),
                    'spread': int(rate['spread']),
                    'real_volume': int(rate['real_volume'])
                })

            logger.info(f'Successfully fetched {len(ohlc_list)} OHLC data points for {symbol} on {timeframe}')
            return ohlc_list
        except MT5Error:
            raise
        except Exception as e:
            logger.exception('Unexpected error fetching OHLC data')
            raise MT5SymbolError(f'Unexpected error fetching OHLC data: {e}')

    def get_current_price(self, symbol: str) -> Dict[str, Any]:
        """
        Get the current bid/ask price for a symbol

        Args:
            symbol (str): The trading symbol (e.g., "EURUSD")

        Returns:
            Dict with bid, ask, and last prices
        """
        try:
            # Check if we're connected first
            if not self.is_connected():
                logger.error(f"Cannot get price for {symbol}: Not connected to MT5")
                return {"error": "Not connected to MT5", "bid": None, "ask": None, "last": None}

            # Get the ticker info
            ticker = mt5.symbol_info_tick(symbol)
            if ticker is None:
                error_msg = mt5.last_error()
                logger.error(f"Failed to get price for {symbol}: {error_msg}")
                return {"error": f"Failed to get price: {error_msg}", "bid": None, "ask": None, "last": None}

            # Get the last price from the symbol info if available
            symbol_info_obj = mt5.symbol_info(symbol) # Renamed to avoid conflict
            last_price = None
            if symbol_info_obj and hasattr(symbol_info_obj, 'last'):
                last_price = float(symbol_info_obj.last) if not math.isnan(symbol_info_obj.last) else None

            # Get bid and ask prices
            bid_price = float(ticker.bid) if not math.isnan(ticker.bid) else None
            ask_price = float(ticker.ask) if not math.isnan(ticker.ask) else None
            last_tick_price = float(ticker.last) if hasattr(ticker, 'last') and not math.isnan(ticker.last) else None

            # Use last_tick_price as fallback for last_price
            if last_price is None:
                last_price = last_tick_price

            # Log the prices for debugging
            logger.info(f"Prices for {symbol}: bid={bid_price}, ask={ask_price}, last={last_price}")

            return {
                "bid": bid_price,
                "ask": ask_price,
                "last": last_price
            }
        except Exception as e:
            logger.error(f"Error getting price for {symbol}: {e}")
            return {"error": f"Error getting price: {e}", "bid": None, "ask": None, "last": None}

    def get_status(self) -> Dict[str, Any]:
        """
        Get current connection status.
        This is an alias for get_connection_info() to maintain API compatibility.

        Returns:
            Dict containing connection status information
        """
        try:
            return self.get_connection_info()
        except Exception as e:
            logger.error(f"Error in get_status: {e}")
            return {
                "state": ConnectionState.ERROR.value,
                "connected": False,
                "last_error": str(e),
                "error_location": "get_status"
            }

    def get_trade_history(self, period: str = 'week', symbol: Optional[str] = None) -> Dict[str, Any]:
        """
        Fetch closed trades (history) for a given period and symbol, and compute summary statistics.
        Args:
            period: One of 'day', 'week', 'month', 'quarter', 'year', 'all'
            symbol: Optional symbol filter
        Returns:
            Dict with 'history' (list of trades) and 'stats' (summary)
        """
        if not self.is_connected():
            raise MT5ConnectionError('Not connected to MT5')

        now = datetime.now()
        if period == 'day':
            start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        elif period == 'week':
            start = now - timedelta(days=now.weekday())
            start = start.replace(hour=0, minute=0, second=0, microsecond=0)
        elif period == 'month':
            start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        elif period == 'quarter':
            month = (now.month - 1) // 3 * 3 + 1
            start = now.replace(month=month, day=1, hour=0, minute=0, second=0, microsecond=0)
        elif period == 'year':
            start = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
        else:  # 'all'
            start = datetime(1970, 1, 1)
        end = now

        # Fetch closed deals
        deals = mt5.history_deals_get(start, end, symbol=symbol) if symbol else mt5.history_deals_get(start, end)
        if deals is None:
            error = mt5.last_error()
            logger.error(f'Failed to fetch trade history: {error}')
            raise MT5Error(f'Failed to fetch trade history: {error}')

        trades = []
        for deal in deals:
            d = deal._asdict()
            # Only include closed trades (DEAL_TYPE_BUY/SELL, not balance, credit, etc.)
            if d['type'] not in (mt5.DEAL_TYPE_BUY, mt5.DEAL_TYPE_SELL):
                continue
            open_time = datetime.fromtimestamp(d['time'])
            close_time = datetime.fromtimestamp(d['time'])  # For deals, open/close is the same
            trades.append({
                'ticket': d['ticket'],
                'symbol': d['symbol'],
                'type': 'BUY' if d['type'] == mt5.DEAL_TYPE_BUY else 'SELL',
                'volume': d['volume'],
                'open_time': open_time.isoformat(),
                'close_time': close_time.isoformat(),
                'duration_minutes': 0,  # Not available for deals
                'open_price': d['price'],
                'close_price': d['price'],
                'sl': d.get('sl', None),
                'tp': d.get('tp', None),
                'profit': d['profit'],
                'pips': abs(d['profit'] / d['volume']) if d['volume'] else 0,
                'comment': d.get('comment', '')
            })

        # Compute stats
        total_trades = len(trades)
        wins = [t for t in trades if t['profit'] > 0]
        losses = [t for t in trades if t['profit'] < 0]
        net_profit = sum(t['profit'] for t in trades)
        gross_profit = sum(t['profit'] for t in wins)
        gross_loss = abs(sum(t['profit'] for t in losses))
        profit_factor = (gross_profit / gross_loss) if gross_loss > 0 else float('inf')
        avg_profit = (gross_profit / len(wins)) if wins else 0
        avg_loss = (gross_loss / len(losses)) if losses else 0
        largest_win = max((t['profit'] for t in wins), default=0)
        largest_loss = min((t['profit'] for t in losses), default=0)
        avg_hold_time = 0  # Not available for deals
        win_rate = (len(wins) / total_trades) if total_trades else 0

        stats = {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'net_profit': net_profit,
            'avg_profit': avg_profit,
            'avg_loss': avg_loss,
            'largest_win': largest_win,
            'largest_loss': largest_loss,
            'avg_hold_time_minutes': avg_hold_time
        }
        return {'history': trades, 'stats': stats}
