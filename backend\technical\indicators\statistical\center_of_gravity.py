from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class CenterOfGravityIndicator(BaseIndicator):
    """Center of Gravity (COG) indicator."""

    def __init__(self, period: int = 10, source: str = 'close'):
        """
        Initialize Center of Gravity indicator.

        Args:
            period: The lookback period for calculation.
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
        """
        super().__init__({
            'period': period,
            'source': source
        })

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Center of Gravity values."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period']:
             return {'cog': np.array([]), 'signal': np.array([])}

        period = self.params['period']
        source_col = self.params['source'].lower()

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Calculate Center of Gravity
        weights = np.arange(1, period + 1)
        # Numerator: Sum of (Price * Position)
        numerator = source_data.rolling(window=period).apply(
            lambda x: np.sum(x * weights), raw=True
        )
        # Denominator: Sum of Prices
        denominator = source_data.rolling(window=period).sum()

        # COG calculation (negative sign and offset as per common formula)
        cog = -(numerator / denominator.replace(0, np.nan)) + (period + 1) / 2
        cog = cog.fillna(0) # Fill initial NaNs

        # Signal line (simple moving average of COG)
        signal = cog.rolling(window=period).mean() # Using same period for signal is common

        self._values = {
            'cog': cog.values,
            'signal': signal.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        return True