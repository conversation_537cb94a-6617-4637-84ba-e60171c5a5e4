import React from 'react';
import '../../styles/TrendArrows.css';

/**
 * TrendArrow component displays an arrow indicating market trend direction
 * 
 * @param {string} direction - The trend direction ('buy', 'sell', 'neutral', etc.)
 * @param {boolean} animated - Whether the arrow should be animated
 * @returns {JSX.Element} - The rendered trend arrow
 */
const TrendArrow = ({ direction, animated = false }) => {
  let arrowClass = 'neutral';
  let arrowSvg = null;

  if (!direction) {
    arrowClass = 'neutral';
    arrowSvg = (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <line x1="5" y1="12" x2="19" y2="12"></line>
      </svg>
    );
  } else if (direction.toLowerCase().includes('buy') || direction.toLowerCase().includes('bullish')) {
    arrowClass = 'up';
    arrowSvg = (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <polyline points="18 15 12 9 6 15"></polyline>
      </svg>
    );
  } else if (direction.toLowerCase().includes('sell') || direction.toLowerCase().includes('bearish')) {
    arrowClass = 'down';
    arrowSvg = (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <polyline points="6 9 12 15 18 9"></polyline>
      </svg>
    );
  }

  return (
    <span className={`trend-arrow ${arrowClass} ${animated ? 'animated' : ''}`}>
      {arrowSvg}
    </span>
  );
};

export default TrendArrow;
