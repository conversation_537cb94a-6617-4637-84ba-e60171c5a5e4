import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './styles.css';

// Import error handling utilities
import { patchNumberPrototype, setupGlobalErrorHandler } from './utils/errorHandling';

// Apply patches and error handlers before rendering
patchNumberPrototype();
setupGlobalErrorHandler();

// Add global safety functions
window.safeToFixed = (value, decimals = 2, fallback = 'N/A') => {
  if (value === undefined || value === null || isNaN(value)) {
    console.warn(`safeToFixed called with invalid value: ${value}`);
    return fallback;
  }
  try {
    return Number(value).toFixed(decimals);
  } catch (e) {
    console.error(`Error in safeToFixed with value ${value}:`, e);
    return fallback;
  }
};

// Add special handling for Chart.js
window.createSafeFormatter = (decimals = 2) => {
  return (value) => {
    if (value === undefined || value === null || isNaN(value)) {
      return 'N/A';
    }
    try {
      return Number(value).toFixed(decimals);
    } catch (e) {
      return 'N/A';
    }
  };
};

console.log('Global safety functions installed');

// Create root element
const root = ReactDOM.createRoot(document.getElementById('root'));

// Render the App component
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
