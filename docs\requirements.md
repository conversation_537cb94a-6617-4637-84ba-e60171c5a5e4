# Post-Login Workflow Requirements

## Overview
After successful MT5 connection/login, users should be automatically redirected to either:
1. Dashboard (default view)
2. Analysis page (if coming from specific workflows)

## User Stories
1. As a trader, I want to be automatically taken to my dashboard after successful login so I can quickly see my account status
2. As an analyst, I want to be taken directly to the analysis page when logging in from research workflows

## Technical Requirements

### Redirection Logic
- After successful connection (status = 'connected'):
  - Default: Redirect to Dashboard tab
  - Special case: If URL contains `?redirect=analysis`, go to Analysis tab
- Implement in ConnectionTab.js by:
  ```javascript
  this.connectionManager.onStatusChange = (status) => {
      this.updateStatus(status);
      this.setConnected(status === ConnectionStates.CONNECTED);
      
      if (status === ConnectionStates.CONNECTED) {
          const urlParams = new URLSearchParams(window.location.search);
          const redirectTo = urlParams.get('redirect') || 'dashboard';
          window.electronAPI.navigateToTab(redirectTo);
      }
  };
  ```

### Data Requirements
- Dashboard must receive:
  - Account balance/equity (already available from ConnectionTab)
  - Current positions
  - Recent trade history
- Analysis page must receive:
  - Selected symbol (from URL params)
  - Timeframe (from URL params)
  - Historical data

### UI Requirements
#### Dashboard
- Show account summary (balance, equity)
- Display current positions
- Show recent trade history
- Include quick access to common trading actions

#### Analysis Page
- Show multi-timeframe chart
- Display technical indicators
- Include analysis tools
- Allow saving analysis templates

## Success Metrics
1. 95% of logins result in correct redirection
2. Dashboard loads within 2 seconds post-login
3. Analysis page loads required data within 3 seconds

## Dependencies
1. Electron API extension for tab navigation:
   ```javascript
   // In preload.js
   contextBridge.exposeInMainWorld('electronAPI', {
       navigateToTab: (tabId) => ipcRenderer.send('navigate-to-tab', tabId)
   });
   ```

2. Backend API endpoints:
   - `/api/positions` - For dashboard data
   - `/api/ohlc?symbol=X&timeframe=Y` - For analysis data