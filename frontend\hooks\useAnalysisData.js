import { useState, useEffect } from 'react';

/**
 * useAnalysisData - Custom hook to fetch and manage analysis data for a symbol/timeframe.
 * @param {string} symbol
 * @param {string} timeframe
 * @returns {[data, loading, error]}
 */
export function useAnalysisData(symbol, timeframe) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!symbol) return;
    setLoading(true);
    setError(null);
    setData(null);
    fetch(`http://localhost:5001/api/analysis?symbol=${symbol}&timeframe=${timeframe}`)
      .then(res => {
        if (!res.ok) throw new Error('Failed to fetch analysis data');
        return res.json();
      })
      .then(setData)
      .catch(setError)
      .finally(() => setLoading(false));
  }, [symbol, timeframe]);

  return [data, loading, error];
}
