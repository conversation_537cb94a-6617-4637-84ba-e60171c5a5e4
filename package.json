{"name": "garudaalgo", "version": "1.1.0", "main": "main.js", "scripts": {"start": "electron .", "dev": "cross-env NODE_ENV=development electron .", "dev-win": "set NODE_ENV=development && electron .", "build": "vite build --config frontend/vite.config.js", "preview": "vite preview --config frontend/vite.config.js", "react-dev": "vite --config frontend/vite.config.js", "react-electron": "node start-dev.js", "react-dev-win": "start-dev.bat", "test": "echo \"Error: no test specified\" && exit 1", "package": "electron-builder", "package-win": "electron-builder --win --x64"}, "keywords": [], "author": "", "license": "ISC", "description": "AI Trade Assistant with MT5 Integration", "dependencies": {"chart.js": "^4.4.8", "chartjs-plugin-annotation": "^3.1.0", "electron-store": "^8.1.0", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-router-dom": "^7.5.2"}, "devDependencies": {"@vitejs/plugin-react": "^4.3.4", "cross-env": "^7.0.3", "electron": "^29.1.0", "electron-builder": "^26.0.12", "vite": "^6.2.5"}, "build": {"appId": "com.garudaalgo.app", "productName": "GarudaAlgo Trader", "copyright": "Copyright © 2024 <PERSON><PERSON>", "directories": {"output": "release-new", "buildResources": "assets"}, "files": ["main.js", "frontend/preload.js", "frontend/dist/**/*", "!node_modules/.bin", "!*.map"], "extraResources": [{"from": "dist/garuda_backend.exe", "to": "garuda_backend.exe"}], "win": {"target": ["nsis"], "icon": "assets/app_icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true}, "mac": {"target": "dmg", "icon": "assets/app_icon.png"}, "linux": {"target": "AppImage", "icon": "assets/app_icon.png"}}}