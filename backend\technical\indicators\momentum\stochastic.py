from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class StochasticIndicator(BaseIndicator):
    """Stochastic Oscillator indicator."""
    
    def __init__(self, k_period: int = 14, d_period: int = 3,
                 smooth_k: int = 3, ma_type: str = 'sma'):
        """
        Initialize Stochastic Oscillator indicator.
        
        Args:
            k_period: The period for %K calculation
            d_period: The period for %D calculation (signal line)
            smooth_k: The period for smoothing %K
            ma_type: The type of moving average ('sma' or 'ema')
        """
        super().__init__({
            'k_period': k_period,
            'd_period': d_period,
            'smooth_k': smooth_k,
            'ma_type': ma_type
        })
    
    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Stochastic Oscillator values."""
        df = data.to_dataframe()
        if df.empty:
            return {}
        
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        k_period = self.params['k_period']
        d_period = self.params['d_period']
        smooth_k = self.params['smooth_k']
        ma_type = self.params['ma_type'].lower()
        
        # Calculate %K
        lowest_low = pd.Series(low).rolling(window=k_period).min()
        highest_high = pd.Series(high).rolling(window=k_period).max()
        
        k_raw = ((close - lowest_low) / (highest_high - lowest_low)) * 100
        
        # Smooth %K if required
        if smooth_k > 1:
            if ma_type == 'sma':
                k_smooth = pd.Series(k_raw).rolling(window=smooth_k).mean()
            else:  # ema
                k_smooth = pd.Series(k_raw).ewm(span=smooth_k, adjust=False).mean()
        else:
            k_smooth = k_raw
        
        # Calculate %D (signal line)
        if ma_type == 'sma':
            d = pd.Series(k_smooth).rolling(window=d_period).mean()
        else:  # ema
            d = pd.Series(k_smooth).ewm(span=d_period, adjust=False).mean()
        
        # Calculate overbought/oversold conditions
        overbought = np.zeros_like(k_smooth)
        overbought[k_smooth >= 80] = 1
        
        oversold = np.zeros_like(k_smooth)
        oversold[k_smooth <= 20] = 1
        
        # Calculate divergences
        price_high = pd.Series(close).rolling(window=5).max()
        price_low = pd.Series(close).rolling(window=5).min()
        stoch_high = pd.Series(k_smooth).rolling(window=5).max()
        stoch_low = pd.Series(k_smooth).rolling(window=5).min()
        
        bullish_div = (price_low < price_low.shift(1)) & (stoch_low > stoch_low.shift(1))
        bearish_div = (price_high > price_high.shift(1)) & (stoch_high < stoch_high.shift(1))
        
        divergence = np.where(bullish_div, 1, np.where(bearish_div, -1, 0))
        
        # Calculate crossovers
        k_cross_d = np.where(
            (k_smooth > d) & (pd.Series(k_smooth).shift(1) <= pd.Series(d).shift(1)), 1,
            np.where((k_smooth < d) & (pd.Series(k_smooth).shift(1) >= pd.Series(d).shift(1)), -1, 0)
        )
        
        mid_cross = np.where(
            (k_smooth > 50) & (pd.Series(k_smooth).shift(1) <= 50), 1,
            np.where((k_smooth < 50) & (pd.Series(k_smooth).shift(1) >= 50), -1, 0)
        )
        
        # Calculate momentum
        momentum = k_smooth - pd.Series(k_smooth).shift(k_period)
        
        # Calculate trend
        trend = np.zeros_like(k_smooth)
        trend[k_smooth > d] = 1
        trend[k_smooth < d] = -1
        
        # Calculate strength zones
        strength = np.zeros_like(k_smooth)
        strength[(k_smooth >= 60) & (k_smooth < 80)] = 1    # Strong
        strength[k_smooth >= 80] = 2                        # Very Strong
        strength[(k_smooth <= 40) & (k_smooth > 20)] = -1   # Weak
        strength[k_smooth <= 20] = -2                       # Very Weak
        
        self._values = {
            'k': k_smooth.values,
            'd': d.values,
            'overbought': overbought,
            'oversold': oversold,
            'divergence': divergence,
            'k_cross_d': k_cross_d,
            'mid_cross': mid_cross,
            'momentum': momentum.values,
            'trend': trend,
            'strength': strength
        }
        return self._values
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_ma_types = ['sma', 'ema']
        if self.params['ma_type'].lower() not in valid_ma_types:
            raise ValueError(f"MA type must be one of {valid_ma_types}")
        if self.params['k_period'] < 1:
            raise ValueError("K period must be greater than 0")
        if self.params['d_period'] < 1:
            raise ValueError("D period must be greater than 0")
        if self.params['smooth_k'] < 1:
            raise ValueError("Smooth K period must be greater than 0")
        return True 