import firebase_admin
from firebase_admin import credentials, firestore
import os
import logging
from google.cloud.firestore_v1.base_query import Field<PERSON>ilter # Added for FieldFilter
from datetime import datetime, timezone # Added timezone
from typing import Dict, Any, Optional # Added Optional and Dict
logger = logging.getLogger(__name__)

db: Optional[firestore.Client] = None # Add type hint for clarity

def get_firestore_client() -> Optional[firestore.Client]:
    """
    Initializes Firebase if not already done, and returns the Firestore client.
    """
    global db
    if db is None:
        if not initialize_firebase_internal(): # Call a renamed internal init
            return None
    return db

def initialize_firebase_internal(): # Renamed original function
    """
    Initializes the Firebase Admin SDK if not already initialized.
    Returns True if initialization is successful or already done, False otherwise.
    """
    global db
    if not firebase_admin._apps: # Check if Firebase app is already initialized
        try:
            # Path to your service account key file, located in the backend folder
            cred_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'garuda-algo-firebase-credentials.json')
            
            if not os.path.exists(cred_path):
                logger.error(f"Firebase credentials file not found at {cred_path}")
                return False

            cred = credentials.Certificate(cred_path)
            app = firebase_admin.initialize_app(cred) # Store the app instance
            db = firestore.client()
            project_id = app.project_id # Get project_id from the app instance
            logger.info(f"Firebase Admin SDK initialized successfully. Project ID: {project_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize Firebase Admin SDK: {e}")
            return False
    else:
        # Already initialized, ensure db client is available (e.g., after reloader)
        if db is None:
            db = firestore.client()
        logger.info("Firebase Admin SDK was already initialized.")
        return True

def get_active_license_details(login_id: int, server_name: Optional[str] = None) -> Optional[Dict[str, Any]]: # Add server_name
    """
    Checks if the MT5 account's license is active in Firestore.
    Queries 'mt5_licenses' collection for a document with 'account_number' == str(login_id).
    Optionally filters by server_name if provided.
    Checks 'status' field for "active" and validates 'plan_end_date'. 

    Args:
        login_id: The MT5 account login ID.
        server_name: Optional. The server name associated with the account.

    Returns:
        A dictionary with license details (user_name, plan_type, plan_end_date, status)
        if active, otherwise None.
    """
    if db is None:
        logger.error("Firestore client is not initialized. Cannot check account license status.")
        return None

    processed_login_id = str(login_id).strip()
    collection_name = 'authorized_accounts' # Corrected collection name

    query_filters = [FieldFilter('account_number', '==', processed_login_id)]
    effective_server_name_for_query = None # For logging
    if server_name:
        effective_server_name_for_query = server_name.strip()
        query_filters.append(FieldFilter('server_name', '==', effective_server_name_for_query))
    else:
        logger.warning(
            f"Querying license for account {processed_login_id} without server_name. "
            "This might lead to ambiguous results if server_name is a key differentiator."
        )
    
    # Enhanced logging for query details
    logger.info(f"Firestore Query Details for license check: Collection='{collection_name}', "
                f"Filter account_number=='{processed_login_id}' (type: {type(processed_login_id)}), "
                f"Filter server_name=='{effective_server_name_for_query}' (type: {type(effective_server_name_for_query) if effective_server_name_for_query else 'N/A'})")
    logger.debug(f"Full query_filters list: {query_filters}")

    try:
        docs_stream = db.collection(collection_name) \
            .where(filter=firestore.And(query_filters)) \
            .limit(1) \
            .stream()

        for doc in docs_stream:
            account_data = doc.to_dict()
            status_field = account_data.get('status')
            is_status_active = (status_field == 'active')

            plan_end_timestamp = account_data.get('plan_end_date') # Firestore Timestamp
            is_plan_valid = False

            if plan_end_timestamp:
                current_time_utc = datetime.now(timezone.utc)
                # Ensure plan_end_timestamp is timezone-aware
                if isinstance(plan_end_timestamp, datetime) and plan_end_timestamp.tzinfo is None:
                    plan_end_timestamp = plan_end_timestamp.replace(tzinfo=timezone.utc)
                is_plan_valid = plan_end_timestamp > current_time_utc
            
            if is_status_active and is_plan_valid:
                logger.info(f"Active license found for account {processed_login_id}" + (f" on server {server_name}" if server_name else ""))
                return {
                    "user_name": account_data.get("user_name"),
                    "plan_type": account_data.get("plan_type"),
                    "plan_end_date": plan_end_timestamp.isoformat() if plan_end_timestamp else None,
                    "status": status_field
                }
            else:
                logger.warning(f"License for account {processed_login_id}" + (f" on server {server_name}" if server_name else "") + f" is not active or expired. Status: {status_field}, Plan Valid: {is_plan_valid}")
                return None # Not active or expired

        # If loop finishes without returning, no document was found
        logger.warning(f"No license document found for account {processed_login_id}" + (f" on server {server_name}" if server_name else ""))
        return None
    except Exception as e:
        logger.error(f"Error querying Firestore for license (account: {processed_login_id}, server: {server_name}): {e}")
        return None

    # --- Assuming the file content is now the one from the successful diff ---
    # (The one with logging and list conversion, but without the FieldFilter syntax change)

    # --- Executing the signature change ---
    # This diff is based on the file content from the successful diff.

    # --- Executing the query change (after signature is changed) ---
    # This diff will be executed in a subsequent step after the signature change is confirmed.

    # --- Executing the ConnectionComponent.initialize change ---
    # This diff will be executed in a subsequent step after the signature change is confirmed.

    # --- Executing the first diff: Signature change in firebase_utils.py ---

    # --- Re-reading firebase_utils.py to get the exact content for the search block ---
    # (This is necessary because the previous diff failed and the file was reverted)
