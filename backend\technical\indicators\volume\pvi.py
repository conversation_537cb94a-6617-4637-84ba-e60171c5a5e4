from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class PVIIndicator(BaseIndicator):
    """Positive Volume Index (PVI) indicator."""

    def __init__(self):
        """Initialize Positive Volume Index indicator."""
        super().__init__({}) # No parameters

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Positive Volume Index."""
        df = data.to_dataframe()
        if df.empty or 'volume' not in df.columns or len(df) < 2:
             return {'pvi': np.array([])}

        close = df['close']
        volume = df['volume']

        # Calculate percentage change in closing price
        close_pct_change = close.pct_change()

        # Calculate volume change
        volume_change = volume.diff() # Use diff instead of pct_change from reference

        # Initialize PVI series (start at 1000)
        pvi = pd.Series(np.nan, index=df.index)
        pvi.iloc[0] = 1000.0

        # Calculate PVI iteratively
        for i in range(1, len(df)):
            if volume_change.iloc[i] > 0: # If volume increased
                pvi.iloc[i] = pvi.iloc[i-1] * (1 + close_pct_change.iloc[i])
            else: # If volume decreased or stayed the same
                pvi.iloc[i] = pvi.iloc[i-1]

        pvi = pvi.fillna(1000.0) # Fill any remaining NaNs (should only be initial if any)

        self._values = {
            'pvi': pvi.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        # No parameters to validate
        return True