from flask import Blueprint, request, jsonify, current_app
import os
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) # Helps find 'backend' in dev
from ..mt5_integration import MT5Integration # Relative import from parent (backend)
from ..analysis import AnalysisEngine # Relative import from parent (backend)
from datetime import datetime, timedelta
import logging # Import logging
import time

market_data_bp = Blueprint('market_data', __name__)
logger = logging.getLogger(__name__) # Add logger for this blueprint

@market_data_bp.route('/account', methods=['GET'])
def get_account_data():
    try:
        mt5 = current_app.config.get('MT5_INSTANCE')
        if not mt5:
            logger.error("MT5 instance not available in app config for /account.")
            return jsonify({'error': 'MT5 service not configured'}), 503

        if not mt5.is_connected():
            logger.warning("MT5 instance is not connected. Cannot fetch account data.")
            return jsonify({'error': 'MT5 not connected. Please connect via the application.'}), 503

        # Get enhanced account information (includes currency)
        enhanced_account_info = mt5.get_enhanced_account_info()
        if 'error' in enhanced_account_info:
            return jsonify({'error': enhanced_account_info['error']}), 500

        # Return account data including currency
        return jsonify({
            'balance': enhanced_account_info['balance'],
            'equity': enhanced_account_info['equity'],
            'name': enhanced_account_info['name'],
            'leverage': enhanced_account_info['leverage'],
            'currency': enhanced_account_info['currency'],
            'margin': enhanced_account_info.get('margin', 0),
            'margin_free': enhanced_account_info.get('margin_free', 0),
            'margin_level': enhanced_account_info.get('margin_level', 0),
            'positions': enhanced_account_info.get('positions', 0),
            'trade_mode': enhanced_account_info.get('trade_mode', 0)
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@market_data_bp.route('/symbols', methods=['GET'])
def get_symbols():
    """Fetches the list of available symbols from the connected MT5 terminal."""
    logger.info("[API Symbols] Request received...")
    try:
        mt5_conn = current_app.config.get('MT5_INSTANCE')
        if not mt5_conn:
            logger.error("[API Symbols] MT5 instance not available in app config.")
            return jsonify({'error': 'MT5 service not configured'}), 503

        if not mt5_conn.is_connected():
            logger.warning("[API Symbols] MT5 instance is not connected. Cannot fetch symbols.")
            # Return empty list or an error, an empty list might be better for frontend if it expects an array
            return jsonify([]), 200 # Or return error: jsonify({'error': 'MT5 not connected.'}), 503

        logger.info("[API Symbols] MT5 is connected. Attempting to fetch symbols...")
        symbols_list = mt5_conn.get_symbols(use_cache=False, force_refresh=True)
        logger.info(f"[API Symbols] Successfully fetched {len(symbols_list)} symbols.")
        return jsonify(symbols_list), 200

    except Exception as e:
        logger.exception(f"[API Symbols] Error in /symbols endpoint: {str(e)}")
        error_detail = str(e)
        if hasattr(e, 'message'):
             error_detail = e.message
        return jsonify({'error': f"An error occurred while fetching symbols: {error_detail}"}), 500
    # No finally block needed to keep connection active, as we are using the shared instance.

@market_data_bp.route('/ohlc', methods=['GET'])
def get_ohlc_data():
    symbol = request.args.get('symbol')
    timeframe = request.args.get('timeframe')

    if not symbol or not timeframe:
        return jsonify({'error': 'Symbol and timeframe are required'}), 400

    try:
        # Set default time range (last 24 hours)
        end_time = datetime.now()
        start_time = end_time - timedelta(days=1)

        try:
            mt5 = current_app.config.get('MT5_INSTANCE')
            if not mt5:
                logger.error(f"MT5 instance not available for OHLC data {symbol}/{timeframe}")
                return jsonify({'error': 'MT5 service not configured'}), 503

            if not mt5.is_connected():
                logger.warning(f"MT5 not connected. Cannot fetch OHLC for {symbol}/{timeframe}")
                return jsonify({'error': 'MT5 not connected. Please connect via the application.'}), 503

            # Fetch OHLC data
            ohlc_data = mt5.fetch_ohlc_data(
                symbol=symbol,
                timeframe=int(timeframe), # Ensure timeframe is int
                start_time=start_time,
                end_time=end_time
            )
            return jsonify(ohlc_data), 200 # Return data directly

        except ValueError: # Catches error if timeframe cannot be converted to int
            logger.error(f"Invalid timeframe value for OHLC data: {timeframe}")
            return jsonify({'error': 'Invalid timeframe value. Must be an integer.'}), 400
        except Exception as e: # Catch other potential errors from fetch_ohlc_data
            logger.exception(f"Error fetching OHLC data for {symbol}/{timeframe}: {e}")
            return jsonify({'error': str(e)}), 500

    except Exception as e: # General catch for unexpected issues like invalid args
        logger.exception(f"Unexpected error in get_ohlc_data for {symbol}/{timeframe}: {e}")
        return jsonify({'error': f'An unexpected error occurred: {str(e)}'}), 500


@market_data_bp.route('/analysis', methods=['GET'])
def get_analysis_data():
    """
    Perform technical analysis for a given symbol and timeframe.
    """
    symbol = request.args.get('symbol')
    timeframe = request.args.get('timeframe')

    if not symbol or not timeframe:
        return jsonify({'error': 'Symbol and timeframe parameters required'}), 400

    try:
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        analyzer = AnalysisEngine(mt5_conn)

        # Get current price first
        current_price = mt5_conn.get_current_price(symbol)
        logger.info(f"Current price data for {symbol}: {current_price}")

        # Add current price to analyzer
        analyzer.current_price = current_price

        # Perform analysis
        analysis_result = analyzer.get_analysis_for_timeframe(symbol, timeframe)

        if not analysis_result.get("success"):
            error_msg = analysis_result.get("message", "Analysis failed")
            logger.error(f"Analysis failed for {symbol}/{timeframe}: {error_msg}")

            if "connection required" in error_msg.lower():
                return jsonify({'error': error_msg}), 503
            if "invalid timeframe" in error_msg.lower():
                return jsonify({'error': error_msg}), 400
            if "no data" in error_msg.lower():
                return jsonify({'error': error_msg}), 404

            return jsonify({'error': error_msg}), 500

        # Ensure current price is included in response
        logger.info("Preparing response with current price...")
        if "analysis" in analysis_result:
            logger.info("Found 'analysis' key in result")
            analysis_result["analysis"]["current_price"] = current_price
            response = analysis_result["analysis"]
        elif "data" in analysis_result:
            logger.info("Found 'data' key in result")
            analysis_result["data"]["current_price"] = current_price
            response = analysis_result["data"]
        else:
            logger.info("Using raw result (no 'analysis' or 'data' key)")
            response = analysis_result

        logger.info("=== Analysis Request Completed Successfully ===")
        return jsonify(response)

    except Exception as e:
        logger.exception(f"Analysis failed for {symbol}/{timeframe}")
        return jsonify({'error': str(e)}), 500


@market_data_bp.route('/current_price', methods=['GET'])
def get_current_price():
    """
    Get the current price for a symbol.
    """
    symbol = request.args.get('symbol')

    if not symbol:
        return jsonify({'error': 'Symbol parameter required'}), 400

    try:
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check if MT5 is connected
        if not mt5_conn.is_connected():
            logger.warning(f"Cannot get price for {symbol}: MT5 not connected")
            return jsonify({
                'error': 'MT5 not connected',
                'bid': None,
                'ask': None,
                'last': None,
                'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }), 200  # Return 200 with error info instead of 500

        # Get current price
        price_data = mt5_conn.get_current_price(symbol)

        # Check if price_data contains an error
        if 'error' in price_data and not price_data.get('bid'):
            logger.error(f"Error getting price for {symbol}: {price_data['error']}")
            return jsonify({
                'error': price_data['error'],
                'bid': None,
                'ask': None,
                'last': None,
                'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }), 200  # Return 200 with error info instead of 500

        # Add timestamp if not already present
        if 'time' not in price_data:
            price_data['time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        logger.info(f"Current price data for {symbol}: {price_data}")

        return jsonify(price_data)

    except Exception as e:
        logger.exception(f"Error getting current price for {symbol}")
        return jsonify({'error': str(e)}), 500
