from typing import Dict, Any, List
import numpy as np
import pandas as pd
from scipy.signal import find_peaks

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class TriplePatternsIndicator(BaseIndicator):
    """Triple Top and Triple Bottom chart pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - min_distance: Parameter description (default: 10)
                - price_tolerance: Parameter description (default: 0.02)
                - min_depth: Parameter description (default: 0.03)
        """
        default_params = {
            "min_distance": 10,
            "price_tolerance": 0.02,
            "min_depth": 0.03,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)


    def _is_triple_top(self, prices: np.ndarray, peaks: np.ndarray,
                      troughs: np.ndarray) -> tuple:
        """Identify Triple Top patterns."""
        is_pattern = np.zeros_like(prices)
        pattern_type = np.zeros_like(prices)
        
        if len(peaks) >= 3:
            for i in range(len(peaks)-2):
                # Get three consecutive peaks
                peak1, peak2, peak3 = peaks[i:i+3]
                
                # Check if peaks are at similar levels
                peak_prices = prices[[peak1, peak2, peak3]]
                max_diff = np.max(peak_prices) - np.min(peak_prices)
                avg_price = np.mean(peak_prices)
                
                if max_diff / avg_price <= self.params['price_tolerance']:
                    # Find troughs between peaks
                    trough1 = troughs[(troughs > peak1) & (troughs < peak2)]
                    trough2 = troughs[(troughs > peak2) & (troughs < peak3)]
                    
                    if len(trough1) > 0 and len(trough2) > 0:
                        trough1 = trough1[-1]
                        trough2 = trough2[-1]
                        
                        # Check if troughs are lower than peaks
                        trough_prices = prices[[trough1, trough2]]
                        depth1 = (avg_price - prices[trough1]) / avg_price
                        depth2 = (avg_price - prices[trough2]) / avg_price
                        
                        if depth1 >= self.params['min_depth'] and depth2 >= self.params['min_depth']:
                            # Mark the pattern
                            start_idx = peak1
                            end_idx = peak3
                            is_pattern[start_idx:end_idx+1] = 1
                            pattern_type[start_idx:end_idx+1] = -1  # Bearish pattern
        
        return is_pattern, pattern_type

    def _is_triple_bottom(self, prices: np.ndarray, peaks: np.ndarray,
                         troughs: np.ndarray) -> tuple:
        """Identify Triple Bottom patterns."""
        is_pattern = np.zeros_like(prices)
        pattern_type = np.zeros_like(prices)
        
        if len(troughs) >= 3:
            for i in range(len(troughs)-2):
                # Get three consecutive troughs
                trough1, trough2, trough3 = troughs[i:i+3]
                
                # Check if troughs are at similar levels
                trough_prices = prices[[trough1, trough2, trough3]]
                max_diff = np.max(trough_prices) - np.min(trough_prices)
                avg_price = np.mean(trough_prices)
                
                if max_diff / avg_price <= self.params['price_tolerance']:
                    # Find peaks between troughs
                    peak1 = peaks[(peaks > trough1) & (peaks < trough2)]
                    peak2 = peaks[(peaks > trough2) & (peaks < trough3)]
                    
                    if len(peak1) > 0 and len(peak2) > 0:
                        peak1 = peak1[-1]
                        peak2 = peak2[-1]
                        
                        # Check if peaks are higher than troughs
                        peak_prices = prices[[peak1, peak2]]
                        depth1 = (prices[peak1] - avg_price) / avg_price
                        depth2 = (prices[peak2] - avg_price) / avg_price
                        
                        if depth1 >= self.params['min_depth'] and depth2 >= self.params['min_depth']:
                            # Mark the pattern
                            start_idx = trough1
                            end_idx = trough3
                            is_pattern[start_idx:end_idx+1] = 1
                            pattern_type[start_idx:end_idx+1] = 1  # Bullish pattern
        
        return is_pattern, pattern_type

    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        close = df['close'].values
        is_pattern = np.zeros_like(close)
        pattern_type = np.zeros_like(close)
        
        # Find peaks and troughs
        peaks, _ = find_peaks(close, distance=self.params['min_distance'])
        troughs, _ = find_peaks(-close, distance=self.params['min_distance'])
        
        # Identify patterns
        is_tt, tt_type = self._is_triple_top(close, peaks, troughs)
        is_tb, tb_type = self._is_triple_bottom(close, peaks, troughs)
        
        # Combine patterns using logical operations
        is_pattern = np.logical_or(is_tt, is_tb)
        pattern_type = np.where(is_tt, tt_type, tb_type)
        
        # Calculate pattern strength
        strength = np.zeros_like(close)
        for i in range(len(close)):
            if is_pattern[i]:
                # Calculate the height of the pattern relative to price
                if pattern_type[i] > 0:  # Triple Bottom
                    pattern_height = max(close[i:i+self.params['min_distance']]) - min(close[i:i+self.params['min_distance']])
                else:  # Triple Top
                    pattern_height = max(close[i:i+self.params['min_distance']]) - min(close[i:i+self.params['min_distance']])
                strength[i] = pattern_height / close[i]
        
        # Calculate trend context
        trend = np.full_like(close, -1)  # Default to -1
        for i in range(20, len(close)):  # Start from 20 to have enough data for SMA
            sma = np.mean(close[i-20:i])
            trend[i] = 1 if close[i] > sma else -1
        
        # Calculate pattern reliability
        reliability = np.full_like(close, -1, dtype=float)  # Default to -1 for no pattern
        future_window = 20
        
        for i in range(len(close) - future_window):
            if is_pattern[i]:
                future_returns = (df['close'].iloc[i+1:i+future_window+1].values - 
                                df['close'].iloc[i]) / df['close'].iloc[i]
                
                if pattern_type[i] > 0:  # Bullish pattern
                    max_return = np.max(future_returns)
                    reliability[i] = 1 if max_return > 0 else -1
                else:  # Bearish pattern
                    min_return = np.min(future_returns)
                    reliability[i] = 1 if min_return < 0 else -1
        
        return {
            'is_pattern': is_pattern.astype(int),
            'pattern_type': pattern_type,  # 1 for Triple Bottom, -1 for Triple Top
            'strength': strength,
            'trend': trend,
            'reliability': reliability,
            'peaks': peaks,
            'troughs': troughs
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['min_distance'] < 5:
            raise ValueError("Minimum distance must be at least 5 periods")
        if not 0 < self.params['price_tolerance'] < 1:
            raise ValueError("Price tolerance must be between 0 and 1")
        if not 0 < self.params['min_depth'] < 1:
            raise ValueError("Minimum depth must be between 0 and 1")
        return True 