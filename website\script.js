// DOM Elements
const langEN = document.getElementById('lang-en');
const langID = document.getElementById('lang-id');
const downloadBtn = document.getElementById('download-btn');
const modal = document.getElementById('download-modal');
const closeModal = document.querySelector('.close-modal');
const prevTestimonial = document.getElementById('prev-testimonial');
const nextTestimonial = document.getElementById('next-testimonial');
const dots = document.querySelectorAll('.dot');
const header = document.querySelector('.header');
const body = document.body;

// Current language and testimonial state
let currentLang = 'en';
let currentTestimonial = 0;
let lastScrollTop = 0;

// Check browser language to set default
function detectLanguage() {
  const browserLang = navigator.language || navigator.userLanguage;

  // If the browser language is Indonesian, switch to Indonesian
  if (browserLang.includes('id')) {
    switchLanguage('id');
  }
}

// Language switcher
function switchLanguage(lang) {
  currentLang = lang;

  // Update active button
  if (lang === 'en') {
    langEN.classList.add('active');
    langID.classList.remove('active');
  } else {
    langID.classList.add('active');
    langEN.classList.remove('active');
  }

  // Update all text elements with data-lang-key attribute
  const elements = document.querySelectorAll('[data-lang-key]');
  elements.forEach(element => {
    const key = element.getAttribute('data-lang-key');
    if (locales[key] && locales[key][lang]) {
      element.textContent = locales[key][lang];
    }
  });
}

// Testimonial slider
function showTestimonial(n) {
  const testimonials = document.querySelectorAll('.testimonial-slide');

  // Hide all testimonials
  testimonials.forEach(testimonial => {
    testimonial.classList.remove('active');
  });

  // Update dots
  dots.forEach(dot => {
    dot.classList.remove('active');
  });

  // Show current testimonial
  currentTestimonial = (n + testimonials.length) % testimonials.length;
  testimonials[currentTestimonial].classList.add('active');
  dots[currentTestimonial].classList.add('active');
}

// Handle scroll events for header
function handleScroll() {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

  // Add scrolled class when user scrolls down
  if (scrollTop > 50) {
    header.classList.add('scrolled');
  } else {
    header.classList.remove('scrolled');
  }

  lastScrollTop = scrollTop;
}

// Smooth scroll to anchor
function smoothScroll(target) {
  const element = document.querySelector(target);
  if (!element) return;

  window.scrollTo({
    top: element.offsetTop - 80,
    behavior: 'smooth'
  });
}

// Initialize the app
function init() {
  // Detect language
  detectLanguage();

  // Add event listeners for language switchers if they exist
  if (langEN && langID) {
    langEN.addEventListener('click', () => switchLanguage('en'));
    langID.addEventListener('click', () => switchLanguage('id'));
  }

  // Modal with gated download (only on pages with download button)
  if (downloadBtn && modal) {
    downloadBtn.addEventListener('click', async (e) => {
      e.preventDefault();
      // Ensure user is authenticated
      const user = auth.currentUser;
      if (!user) {
        window.location.href = 'login.html';
        return;
      }
      
      try {
        // Check registration status
        const doc = await db.collection('users').doc(user.uid).get();
        if (!doc.exists) {
          alert('No registration found. Please register first.');
          window.location.href = 'register.html';
          return;
        }
        const data = doc.data();
        if (!data.verified) {
          alert('Your account is pending activation. Please wait for approval.');
          return;
        }
        // Open download modal
        modal.classList.add('show');
        document.body.style.overflow = 'hidden'; // Prevent scrolling when modal is open
      } catch (error) {
        console.error('Error checking user status:', error);
        alert('Error checking registration status. Please try again.');
      }
    });
  }

  if (modal && closeModal) {
    closeModal.addEventListener('click', () => {
      modal.classList.remove('show');
      document.body.style.overflow = ''; // Re-enable scrolling
    });
  }

  // Close modal when clicking outside
  window.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.classList.remove('show');
      document.body.style.overflow = ''; // Re-enable scrolling
    }
  });

  // Testimonials (only on pages with testimonials)
  if (prevTestimonial && nextTestimonial && dots.length > 0) {
    prevTestimonial.addEventListener('click', () => {
      showTestimonial(currentTestimonial - 1);
    });

    nextTestimonial.addEventListener('click', () => {
      showTestimonial(currentTestimonial + 1);
    });

    // Dot navigation
    dots.forEach(dot => {
      dot.addEventListener('click', () => {
        const index = parseInt(dot.getAttribute('data-index'));
        showTestimonial(index);
      });
    });

    // Auto-advance testimonials
    setInterval(() => {
      showTestimonial(currentTestimonial + 1);
    }, 5000);
  }

  // Scroll event for header
  window.addEventListener('scroll', handleScroll);

  // Smooth scroll for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
      if (this.getAttribute('href') !== '#') {
        e.preventDefault();
        smoothScroll(this.getAttribute('href'));
      }
    });
  });

  // Initialize header state on page load
  handleScroll();

  // Add animation classes on scroll
  const animateOnScroll = () => {
    const elements = document.querySelectorAll('.benefit-card, .feature-card, .pricing-card, .step, .faq-item');

    elements.forEach(element => {
      const elementTop = element.getBoundingClientRect().top;
      const elementVisible = 150;

      if (elementTop < window.innerHeight - elementVisible) {
        element.classList.add('animate');
      }
    });
  };

  window.addEventListener('scroll', animateOnScroll);
  animateOnScroll(); // Run once on page load
}

// Handle FAQ accordion
function initFAQ() {
  const faqItems = document.querySelectorAll('.faq-item');

  faqItems.forEach(item => {
    const question = item.querySelector('.faq-question');

    question.addEventListener('click', () => {
      // Close all other items
      faqItems.forEach(otherItem => {
        if (otherItem !== item && otherItem.classList.contains('active')) {
          otherItem.classList.remove('active');
        }
      });

      // Toggle current item
      item.classList.toggle('active');
    });
  });

  // Open first FAQ item by default
  if (faqItems.length > 0) {
    faqItems[0].classList.add('active');
  }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  init();
  initFAQ();
});
