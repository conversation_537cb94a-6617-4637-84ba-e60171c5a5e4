from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class NVIIndicator(BaseIndicator):
    """Negative Volume Index (NVI) indicator."""

    def __init__(self):
        """Initialize Negative Volume Index indicator."""
        super().__init__({}) # No parameters

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Negative Volume Index."""
        df = data.to_dataframe()
        if df.empty or 'volume' not in df.columns or len(df) < 2:
             return {'nvi': np.array([])}

        close = df['close']
        volume = df['volume']

        # Calculate percentage change in closing price
        close_pct_change = close.pct_change()

        # Calculate volume change
        volume_change = volume.diff() # Use diff instead of pct_change from reference

        # Initialize NVI series (start at 1000)
        nvi = pd.Series(np.nan, index=df.index)
        nvi.iloc[0] = 1000.0

        # Calculate NVI iteratively
        for i in range(1, len(df)):
            if volume_change.iloc[i] < 0: # If volume decreased
                nvi.iloc[i] = nvi.iloc[i-1] * (1 + close_pct_change.iloc[i])
            else: # If volume increased or stayed the same
                nvi.iloc[i] = nvi.iloc[i-1]

        nvi = nvi.fillna(1000.0) # Fill any remaining NaNs (should only be initial if any)

        self._values = {
            'nvi': nvi.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        # No parameters to validate
        return True