from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.analysis.technical.indicators.volatility.atr import ATRIndicator # NATR uses ATR
from src.core.models.market_data import MarketData

class NATRIndicator(BaseIndicator):
    """Normalized Average True Range (NATR) indicator."""

    def __init__(self, period: int = 14):
        """
        Initialize Normalized Average True Range indicator.

        Args:
            period: The period for the underlying ATR calculation.
        """
        super().__init__({'period': period})
        # Internal ATR indicator (using standard SMA for ATR in NATR)
        self._atr_indicator = ATRIndicator(period=period, ma_type='sma')

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Normalized Average True Range."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period']:
             return {'natr': np.array([])}

        period = self.params['period']
        close = df['close']

        # Calculate ATR
        atr_result = self._atr_indicator.calculate(data)
        atr = pd.Series(atr_result['atr'], index=df.index) # Ensure index alignment

        # Calculate NATR = (ATR / Close) * 100
        # Avoid division by zero
        close_safe = close.replace(0, np.nan)
        natr_values = (atr / close_safe) * 100
        natr_values = natr_values.fillna(0) # Fill initial NaNs

        self._values = {
            'natr': natr_values.values,
            'atr': atr.values # Optional: return intermediate ATR
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        return True