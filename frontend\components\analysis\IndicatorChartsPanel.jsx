import React, { useMemo } from 'react';
import { Line } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, BarElement, BarController, Filler } from 'chart.js'; // Added Filler for fill option
import { safeToFixed } from '../../utils/numberUtils';
import { createSafeTooltipCallbacks } from '../../utils/chartUtils';
import { sanitizeChartData } from '../../utils/chartSafetyUtils';

// Ensure proper Chart.js controller registration for all chart types
ChartJS.register(
  CategoryScale, 
  LinearScale, 
  PointElement, 
  LineElement, 
  BarElement,
  BarController, // Critical for bar charts in MACD histogram
  Title, 
  Tooltip, 
  Legend, 
  Filler
);

/**
 * IndicatorChartsPanel - Displays a separate chart for each technical indicator (RSI, MACD, etc.)
 * @param {Object} analysisData - The analysis data containing indicator values
 * @param {string[]} indicators - Array of indicator names to display (e.g. ['rsi', 'macd'])
 * @param {number|string} height - Chart height
 * @param {number|string} width - Chart width
 */
function IndicatorChartsPanel({ analysisData, indicators = ['rsi', 'macd'], height = 220, width = '100%' }) {
  // Check if core data is available
  if (!analysisData) {
    return <div className="indicator-charts-panel">Loading indicator data...</div>;
  }

  // Generate time labels for the chart (last 30 periods)
  const labels = useMemo(() => {
    // Try to get time information from multiple possible sources
    const timeframe = timeframeFromURL() || 'H1';
    
    // Try multiple paths to get the current time
    const currentTime = analysisData?.support_resistance?.current_price?.time ||
                      analysisData?.current_price?.time ||
                      analysisData?.price_data?.time?.[analysisData.price_data.time.length - 1] ||
                      new Date().toISOString();
    
    console.log(`[IndicatorChartsPanel] Using current time: ${currentTime}`);
    
    // Format labels based on timeframe
    const date = new Date(currentTime);
    const interval = timeframeToMinutes(timeframe);
    
    return Array(30).fill(0).map((_, i) => {
      const pointDate = new Date(date.getTime() - (29 - i) * interval * 60 * 1000);
      
      // Format based on timeframe
      if (timeframe === 'W1' || timeframe === 'MN1') {
        // For weekly timeframes, show week number or month name
        if (timeframe === 'W1') {
          // Format like "W23 2024" (Week 23 of 2024)
          const weekNum = Math.ceil((pointDate.getDate() + new Date(pointDate.getFullYear(), pointDate.getMonth(), 0).getDay()) / 7);
          return `W${weekNum} ${pointDate.getFullYear()}`;
        } else {
          // Format like "Jun 2024"
          return pointDate.toLocaleDateString([], { month: 'short', year: 'numeric' });
        }
      } else {
        // For intraday timeframes, show time only
        if (timeframe.startsWith('M') || timeframe.startsWith('H')) {
          return pointDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        } 
        // For daily timeframe, show date only
        else {
          return pointDate.toLocaleDateString([], { month: 'short', day: 'numeric' });
        }
      }
    });
  }, [analysisData]);

  // Helper function to extract timeframe from URL
  function timeframeFromURL() {
    const url = new URL(window.location.href);
    return url.searchParams.get('timeframe') || 'H1';
  }

  // Convert timeframe to minutes
  function timeframeToMinutes(timeframe) {
    const map = {
      'M1': 1,
      'M5': 5,
      'M15': 15,
      'M30': 30,
      'H1': 60,
      'H4': 240,
      'D1': 1440,
      'W1': 10080, // 1440 * 7 = 10080 minutes in a week
      'MN1': 43200 // Approximately 30 days (1440 * 30)
    };
    return map[timeframe] || 60; // Default to H1 if not found
  }

  // Generate synthetic data for an indicator based on its current value
  function generateSyntheticData(currentValue, rangePercent = 0.2, length = 30, trend = 'flat') {
    if (currentValue === undefined || currentValue === null) return null;

    // Convert string values to numbers if needed
    const value = typeof currentValue === 'string' ? parseFloat(currentValue) : currentValue;
    if (isNaN(value)) return null;

    const range = Math.abs(value) * rangePercent;
    const minValue = value - range;
    const maxValue = value + range;

    const result = [];

    switch(trend) {
      case 'up':
        // Rising trend ending at currentValue
        for (let i = 0; i < length; i++) {
          const progress = i / (length - 1);
          result.push(minValue + progress * (value - minValue) + (Math.random() - 0.5) * range * 0.3);
        }
        break;
      case 'down':
        // Falling trend ending at currentValue
        for (let i = 0; i < length; i++) {
          const progress = i / (length - 1);
          result.push(maxValue - progress * (maxValue - value) + (Math.random() - 0.5) * range * 0.3);
        }
        break;
      case 'flat':
      default:
        // Random fluctuation around currentValue
        for (let i = 0; i < length; i++) {
          result.push(value + (Math.random() - 0.5) * range);
        }
    }

    return result;
  }

  // Metadata for different indicators
  const indicatorMeta = {
    rsi: {
      label: 'RSI (Relative Strength Index)',
      color: '#4fc3f7', // Lighter blue for better visibility on dark background
      dataKey: 'value',
      type: 'line',
      syntheticParams: {
        rangePercent: 0.15,
        trend: 'flat'
      },
      yAxisConfig: {
        min: 0,
        max: 100,
        ticks: {
          stepSize: 25, // Show 0, 25, 50, 75, 100 on the axis
          callback: function(value) {
            if (value === 30 || value === 70) {
              // Highlight overbought/oversold levels
              return ['↑'+value+'↑'];
            }
            return value;
          }
        }
      }
    },
    macd: {
      label: 'MACD (Moving Average Convergence Divergence)',
      dataKeys: ['macd', 'signal', 'histogram'],
      colors: ['#ff9f43', '#ea80fc', '#b2dfdb'], // Warmer orange, vibrant purple, soft teal
      types: ['line', 'line', 'bar'],
      syntheticParams: {
        rangePercent: 0.5,
        trends: ['down', 'down', 'down']
      },
      yAxisConfig: { min: undefined, max: undefined }
    },
    bollinger_bands: {
      label: 'Bollinger Bands',
      dataKeys: ['upper', 'middle', 'lower'],
      colors: ['#4CAF50', '#42a5f5', '#ef5350'], // Green, blue, red with better contrast
      types: ['line', 'line', 'line'],
      syntheticParams: {
        rangePercent: 0.1,
        trends: ['flat', 'flat', 'flat']
      },
      yAxisConfig: { min: undefined, max: undefined }
    },
    atr: {
      label: 'Average True Range (ATR)',
      color: '#ba68c8', // Lighter purple for better visibility
      dataKey: 'value',
      type: 'line',
      syntheticParams: {
        rangePercent: 0.3,
        trend: 'flat'
      },
      yAxisConfig: { min: 0 }
    }
  };

  return (
    <div className="indicator-charts-panel" style={{ marginTop: '20px' }}>
      {indicators.map((name) => {
        const meta = indicatorMeta[name];

        // Check if metadata exists for this indicator
        if (!meta) {
          console.warn(`[IndicatorChartsPanel] No metadata configured for indicator: ${name}`);
          return (
            <div key={name} className="indicator-chart-card">
              <h4>{name.toUpperCase()}</h4>
              <div className="indicator-no-data">Configuration missing for {name}</div>
            </div>
          );
        }

        let datasets = [];
        let chartLabels = [...labels]; // Start with time labels or empty array
        let isValidDataFound = false;
        let firstDataLength = 0;

        // Handle single data key indicators (like RSI)
        if (meta.dataKey) {
          const singleValue = analysisData[name]?.[meta.dataKey];
          // Check if we have a value (could be number or array)
          if (singleValue !== undefined && singleValue !== null) {
            let dataArray;

            // Special handling for RSI since it's a critical indicator
            if (name === 'rsi') {
              console.log(`[IndicatorChartsPanel] RSI current value: ${singleValue}`);

              // First check if we have an array of values
              if (Array.isArray(singleValue) && singleValue.length > 0) {
                dataArray = singleValue;
                console.log(`[IndicatorChartsPanel] Using actual RSI array data of length ${dataArray.length}`);
              } else {
                // For RSI, ensure the value is treated as a number
                let rsiValue;

                // Try to get RSI value from different possible sources
                if (typeof singleValue === 'number') {
                  rsiValue = singleValue;
                } else if (typeof singleValue === 'string' && !isNaN(parseFloat(singleValue))) {
                  rsiValue = parseFloat(singleValue);
                } else if (analysisData.rsi?.current !== undefined) {
                  rsiValue = parseFloat(analysisData.rsi.current);
                } else if (analysisData.rsi?.value !== undefined && typeof analysisData.rsi.value === 'number') {
                  rsiValue = analysisData.rsi.value;
                } else {
                  // Default to a middle value if nothing else is available
                  rsiValue = 50;
                }

                if (!isNaN(rsiValue)) {
                  // Generate data with narrow range for RSI (always between 0-100)
                  dataArray = generateSyntheticData(
                    rsiValue,
                    0.15, // Use smaller variation for RSI
                    labels.length,
                    'flat' // RSI typically doesn't have strong trends in short timeframes
                  );
                  console.log(`[IndicatorChartsPanel] Created synthetic RSI data around value ${rsiValue}`);
                }
              }
            }
            // Normal handling for other indicators
            else if (Array.isArray(singleValue) && singleValue.length > 0) {
              // We actually have an array! Use it directly
              dataArray = singleValue;
              console.log(`[IndicatorChartsPanel] Using actual ${name}.${meta.dataKey} array data of length ${dataArray.length}`);
            } else {
              // Generate synthetic data if we only have a single value
              dataArray = generateSyntheticData(
                singleValue,
                meta.syntheticParams?.rangePercent || 0.2,
                labels.length,
                meta.syntheticParams?.trend || 'flat'
              );
              console.log(`[IndicatorChartsPanel] Created synthetic ${name} data around value ${singleValue}`);
            }

            if (dataArray) {
              datasets.push({
                type: meta.type || 'line',
                label: meta.label,
                data: dataArray,
                borderColor: meta.color,
                backgroundColor: meta.type === 'bar' ? meta.color : 'rgba(0,0,0,0.02)',
                tension: 0.3,
                pointRadius: 0,
                order: meta.type === 'line' ? 1 : 2 // Ensure lines draw over bars if mixed
              });
              isValidDataFound = true;
              firstDataLength = dataArray.length;
            }
          }
        }
        // Handle multiple data key indicators (like MACD)
        else if (meta.dataKeys && Array.isArray(meta.dataKeys)) {
          meta.dataKeys.forEach((key, index) => {
            const singleValue = analysisData[name]?.[key];
            if (singleValue !== undefined && singleValue !== null) {
              let dataArray;

              // Special handling for MACD
              if (name === 'macd') {
                // First check if we have an array of values
                if (Array.isArray(singleValue) && singleValue.length > 0) {
                  dataArray = singleValue;
                  console.log(`[IndicatorChartsPanel] Using actual MACD ${key} array data of length ${dataArray.length}`);
                } else {
                  // For MACD, ensure the value is treated as a number
                  let macdValue;

                  // Try to get MACD value from different possible sources
                  if (typeof singleValue === 'number') {
                    macdValue = singleValue;
                  } else if (typeof singleValue === 'string' && !isNaN(parseFloat(singleValue))) {
                    macdValue = parseFloat(singleValue);
                  } else if (analysisData.macd?.current?.[key] !== undefined) {
                    macdValue = parseFloat(analysisData.macd.current[key]);
                  } else {
                    // Default values for MACD components
                    if (key === 'macd') macdValue = 0.0005;
                    else if (key === 'signal') macdValue = 0.0003;
                    else if (key === 'histogram') macdValue = 0.0002;
                    else macdValue = 0;
                  }

                  if (!isNaN(macdValue)) {
                    // Generate data with appropriate range for MACD
                    const trend = meta.syntheticParams?.trends ?
                      meta.syntheticParams.trends[index] || 'flat' : 'flat';

                    dataArray = generateSyntheticData(
                      macdValue,
                      meta.syntheticParams?.rangePercent || 0.2,
                      labels.length,
                      trend
                    );
                    console.log(`[IndicatorChartsPanel] Created synthetic macd.${key} data around value ${macdValue}`);
                  }
                }
              }
              // Handle other indicators
              else if (Array.isArray(singleValue) && singleValue.length > 0) {
                // We have an array! Use it directly
                dataArray = singleValue;
              } else {
                // Generate synthetic data if we only have a single value
                const trend = meta.syntheticParams?.trends ?
                  meta.syntheticParams.trends[index] || 'flat' : 'flat';

                dataArray = generateSyntheticData(
                  singleValue,
                  meta.syntheticParams?.rangePercent || 0.2,
                  labels.length,
                  trend
                );
                console.log(`[IndicatorChartsPanel] Created synthetic ${name}.${key} data around value ${singleValue}`);
              }

              if (dataArray) {
                datasets.push({
                  type: meta.types?.[index] || 'line',
                  label: `${key.charAt(0).toUpperCase() + key.slice(1)}`, // Capitalize key name
                  data: dataArray,
                  borderColor: meta.colors?.[index] || '#888',
                  backgroundColor: meta.types?.[index] === 'bar' ?
                    (meta.colors?.[index] || '#888') : 'rgba(0,0,0,0.02)',
                  tension: 0.3,
                  pointRadius: 0,
                  yAxisID: 'y', // Use the primary y-axis
                  order: (meta.types?.[index] === 'line' ? 1 : 2) // Draw lines on top
                });
                isValidDataFound = true;
                if (firstDataLength === 0) firstDataLength = dataArray.length; // Get length from first dataset
              }
            }
          });
        }

        // If no valid data was found for this indicator, show message
        if (!isValidDataFound) {
          return (
            <div key={name} className="indicator-chart-card">
              <h4>{meta.label || name.toUpperCase()}</h4>
              <div className="indicator-no-data">No data available</div>
            </div>
          );
        }

        // Use our pre-generated time labels
        chartLabels = [...labels];

        // If data length doesn't match labels length, adjust and ensure all data is sanitized
        if (chartLabels.length !== firstDataLength) {
            console.warn(`[IndicatorChartsPanel] Label count (${chartLabels.length}) doesn't match data count (${firstDataLength}) for ${name}. Adjusting.`);

            // If we have more data than labels, trim the data and ensure it's sanitized
            if (firstDataLength > chartLabels.length) {
              // For large datasets like monthly, take most recent data points
              datasets.forEach(dataset => {
                // Take most recent N points to match labels length
                let trimmedData = dataset.data.slice(-chartLabels.length);
                // Ensure all data is sanitized to prevent undefined/null values
                dataset.data = sanitizeChartData(trimmedData);
              });
            } else {
              // If we have more labels than data, adjust labels
              chartLabels = chartLabels.slice(-firstDataLength);
            }
        } else {
            // Even if lengths match, still ensure all data is sanitized
            datasets.forEach(dataset => {
                dataset.data = sanitizeChartData(dataset.data);
            });
        }

        const chartData = {
          labels: chartLabels,
          datasets: datasets
        };

        const options = {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: datasets.length > 1,
              position: 'top',
              labels: {
                color: '#ccc',
                font: { size: 12 },
                usePointStyle: true,
                padding: 15
              }
            },
            title: {
              display: true,
              text: meta.label,
              font: { size: 16, weight: '500' },
              color: '#eee',
              padding: { bottom: 15 }
            },
            tooltip: {
              mode: 'index',
              intersect: false,
              backgroundColor: 'rgba(20, 25, 39, 0.9)',
              titleColor: '#fff',
              bodyColor: '#ccc',
              borderColor: '#2a2f45',
              borderWidth: 1,
              cornerRadius: 4,
              displayColors: true,
              padding: 8,
              bodyFont: { size: 12 },
              titleFont: { size: 13, weight: 'bold' }
            }
          },
          scales: {
            x: {
              ticks: {
                maxTicksLimit: 10,
                autoSkip: true,
                color: '#999',
                font: { size: 10 }
              },
              grid: {
                display: true,
                color: 'rgba(70, 80, 100, 0.15)',
                borderColor: '#2a2f45'
              }
            },
            y: {
              ...(meta.yAxisConfig || {}),
              ticks: {
                color: '#999',
                font: { size: 10 },
                ...(meta.yAxisConfig?.ticks || {})
              },
              grid: {
                display: true,
                color: 'rgba(70, 80, 100, 0.15)',
                borderColor: '#2a2f45'
              }
            }
          },
          animation: {
            duration: 800,
            easing: 'easeOutQuart'
          },
          interaction: {
            mode: 'nearest',
            axis: 'x',
            intersect: false
          },
          elements: {
            line: {
              tension: 0.25,  // Smoother curves
              borderWidth: 2
            },
            point: {
              radius: 0,      // Hide points by default
              hoverRadius: 4  // Show on hover
            }
          }
        };

        return (
          <div key={name} className="indicator-chart-card" style={{ marginBottom: '20px', padding: '15px', border: '1px solid #2a2f45', borderRadius: '8px', background: '#1e2130' }}>
            <div style={{ height: `${height}px`, width, position: 'relative' }}>
              <Line data={chartData} options={options} />
            </div>
          </div>
        );
      })}
    </div>
  );
}

export default IndicatorChartsPanel;
