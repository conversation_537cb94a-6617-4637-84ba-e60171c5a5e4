# Story BE-05: Enhance Connection Status API

## Description
Update the `/connection_status` endpoint to provide robust account information retrieval and enhanced error handling. The endpoint should reliably return balance and equity information when connected, while gracefully handling various error conditions and providing clear status information.

## Technical Context
- Building on existing `/connection_status` endpoint in `backend/api/connection.py`
- Uses MT5 integration module for account info retrieval
- Part of the connection management feature set
- Critical for displaying account status in frontend Connection Tab

## Dependencies
- BE-02 (MT5 Integration) completed - uses account info retrieval functionality
- BE-04 (Connection API) completed - enhancing existing endpoint

## Acceptance Criteria

### 1. Response Enhancement
- [ ] Update `/connection_status` response structure to include:
  ```json
  {
    "status": "string",  // "connected", "disconnected", "error"
    "last_connected": "ISO timestamp",
    "account_info": {
      "balance": number,
      "equity": number,
      "name": string,     // Account holder name
      "leverage": number, // Account leverage
      "server": string   // Connected server
    },
    "error": string | null
  }
  ```
- [ ] Return `null` for `account_info` when not connected
- [ ] Ensure all numeric values use appropriate precision for currency amounts

### 2. Error Handling
- [ ] Implement comprehensive error handling for:
  - MT5 terminal not running
  - Connection lost scenarios
  - Invalid account state
  - Failed data retrieval
- [ ] Return appropriate HTTP status codes:
  - 200: Successfully retrieved status
  - 503: MT5 terminal unavailable
  - 500: Internal server error
- [ ] Include detailed error messages in response
- [ ] Log all errors with proper detail level

### 3. Performance
- [ ] Endpoint response time < 300ms (excluding MT5 latency)
- [ ] Implement efficient account info caching if needed
- [ ] Add request timing logging for monitoring
- [ ] Ensure no blocking operations in the request path

### 4. Testing Requirements
- [ ] Unit tests for all error scenarios
- [ ] Integration tests with MT5 terminal
- [ ] Performance tests for response time validation
- [ ] Test cases for data accuracy verification
- [ ] Update `test_connection_api.py` with new test cases

### 5. Documentation
- [ ] Update API documentation with new response format
- [ ] Document error codes and messages
- [ ] Add example responses for different scenarios
- [ ] Include performance characteristics

## Implementation Notes
1. Use MT5Integration.get_account_info() for data retrieval
2. Consider implementing status caching to improve performance
3. Maintain backwards compatibility for existing clients
4. Follow established error handling patterns
5. Use existing logging configuration

## Example Usage
```python
# Success Response
{
    "status": "connected",
    "last_connected": "2025-04-09T14:30:00Z",
    "account_info": {
        "balance": 10000.00,
        "equity": 10050.25,
        "name": "John Doe",
        "leverage": 100,
        "server": "MetaQuotes-Demo"
    },
    "error": null
}

# Error Response
{
    "status": "error",
    "last_connected": "2025-04-09T14:25:00Z",
    "account_info": null,
    "error": "MT5 terminal not running"
}