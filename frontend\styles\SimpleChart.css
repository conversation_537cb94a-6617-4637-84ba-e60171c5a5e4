.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-muted);
  font-size: 0.9rem;
}

.chart-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--error);
  font-size: 0.9rem;
  text-align: center;
  padding: 0 20px;
}

/* Chart controls */
.chart-controls,
.indicator-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-left: 20px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-right: 15px;
}

.control-label {
  font-size: 0.8rem;
  color: var(--text-muted);
  white-space: nowrap;
}

.chart-control-btn {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  padding: 2px 8px;
  font-size: 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chart-control-btn:hover {
  background-color: var(--bg-hover);
}

.chart-control-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Chart styling */
.bento-card-content {
  background-color: var(--bg-card);
  border-radius: 0 0 8px 8px;
  padding: 10px;
  position: relative;
}

/* Chart tooltip styling */
.chartjs-tooltip {
  background-color: var(--bg-tooltip) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 4px !important;
  color: var(--text-primary) !important;
  font-size: 12px !important;
  padding: 8px !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;
}

/* Chart axis styling */
.chartjs-render-monitor {
  border-radius: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .chart-controls,
  .indicator-controls {
    flex-direction: column;
    gap: 5px;
  }

  .control-group {
    margin-right: 0;
    margin-bottom: 5px;
  }
}
