import React, { useRef, useEffect } from 'react';
import { Chart, registerables } from 'chart.js';
import { Line } from 'react-chartjs-2';

// Register the chart.js components we need
Chart.register(...registerables);

/**
 * SimpleLineChart component for displaying price data
 *
 * @param {Object} analysisData - The analysis data containing price information
 * @param {string} selectedSymbol - The currently selected symbol
 * @returns {JSX.Element} - The rendered line chart
 */
const SimpleLineChart = ({ analysisData, selectedSymbol }) => {
  // Only proceed if we have valid data
  if (!analysisData || !analysisData.price_data || !analysisData.price_data.close || !Array.isArray(analysisData.price_data.close) || analysisData.price_data.close.length === 0) {
    return (
      <div className="bento-card bento-span-12 bento-height-2">
        <div className="bento-card-header">
          <h3 className="bento-card-title">Price Chart</h3>
        </div>
        <div className="bento-card-content" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
          <p>No price data available</p>
        </div>
      </div>
    );
  }

  // Extract data from analysisData
  const { time, close } = analysisData.price_data;

  // Format time labels or create sequential labels if time data is not available
  const labels = time && time.length > 0 ?
    time.map(t => {
      const date = new Date(t * 1000); // Convert UNIX timestamp to JS Date
      return date.toLocaleTimeString();
    }) :
    close.map((_, index) => `Point ${index + 1}`);

  // Prepare chart data
  const data = {
    labels,
    datasets: [
      {
        label: `${selectedSymbol} Price`,
        data: close,
        borderColor: '#4299e1',
        backgroundColor: 'rgba(66, 153, 225, 0.1)',
        borderWidth: 2,
        pointRadius: 0,
        pointHoverRadius: 5,
        tension: 0.1
      }
    ]
  };

  // Chart options
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      tooltip: {
        enabled: true,
        mode: 'index',
        intersect: false,
      },
      legend: {
        position: 'top',
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          maxRotation: 0,
          autoSkip: true,
          maxTicksLimit: 10,
        }
      },
      y: {
        position: 'right',
        grid: {
          color: 'rgba(160, 174, 192, 0.1)',
        }
      }
    }
  };

  return (
    <div className="bento-card bento-span-12 bento-height-2">
      <div className="bento-card-header">
        <h3 className="bento-card-title">{selectedSymbol} Price Chart</h3>
      </div>
      <div className="bento-card-content" style={{ height: '200px' }}>
        <Line data={data} options={options} />
      </div>
    </div>
  );
};

export default SimpleLineChart;
