.simple-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.simple-modal {
  background-color: #ffffff !important;
  color: #333333 !important;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  /* No animation to avoid rendering issues */
  opacity: 1 !important;
  transform: none !important;
}

@keyframes simpleModalIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.simple-modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.simple-modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: #333333;
}

.simple-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
}

.simple-modal-body {
  padding: 20px;
}

.simple-modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.simple-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.simple-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.simple-button.primary {
  background-color: #3b82f6;
  color: white;
}

.simple-button.primary:hover:not(:disabled) {
  background-color: #2563eb;
}

.simple-button.secondary {
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  color: #374151;
}

.simple-button.secondary:hover:not(:disabled) {
  background-color: #e5e7eb;
}

/* Form styles */
.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background-color: #ffffff;
  color: #333333;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
