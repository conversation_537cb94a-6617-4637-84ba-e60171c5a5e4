Flask>=2.0 # Use a recent stable version of Flask
python-dotenv>=0.15 # For environment variable configuration
MetaTrader5>=5.0.45 # For MT5 terminal interaction
pytz>=2021.1 # For timezone handling
keyring>=24.0.0 # For OS keychain integration
cryptography>=41.0.0 # For secure encryption
pandas>=1.3.0 # For data manipulation in analysis engine
flask-cors>=3.0.10 # For handling Cross-Origin Resource Sharing
# TA-Lib>=0.4.24 # Removed TA-Lib dependency
# pandas-ta>=0.3.14b # Removed pandas-ta, using custom indicators

firebase-admin>=6.8.0
pyinstaller>=5.9.0 # For packaging the application
numpy>=1.21.0 # For numerical computations in technical indicators
scipy>=1.7.0 # For statistical functions in technical indicators
waitress>=2.1.2 # WSGI server for production deployment
PyQt5>=5.15.4 # Required for Admin Panel GUI
