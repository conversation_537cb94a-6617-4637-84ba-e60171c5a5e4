# Story BE-04: Create Connection API Endpoints

## Description
Implement Flask API endpoints to manage MT5 connection operations, providing a secure interface between the frontend and the MT5 integration module. These endpoints will handle connection requests, disconnection, and status checks while properly utilizing the credential manager for secure credential handling.

## Technical Context
- Uses Flask for API endpoints
- Integrates with `mt5_integration.py` for MT5 operations
- Integrates with `CredentialsManager` for secure credential handling
- Follows RESTful API design patterns
- All communication restricted to localhost
- JSON format for request/response data

## Dependencies
- BE-01: Backend project structure must be complete
- BE-02: MT5 integration module must be implemented
- BE-03: Credential storage system must be operational

## Acceptance Criteria

### 1. POST /connect Endpoint
- [ ] Implements endpoint accepting MT5 credentials (account, password, server)
- [ ] Validates all required fields in request body
- [ ] Securely stores credentials using CredentialsManager
- [ ] Attempts connection using mt5_integration module
- [ ] Returns appropriate HTTP status codes:
  - 200: Successfully connected
  - 400: Invalid/missing parameters
  - 401: Invalid credentials
  - 503: MT5 terminal unreachable
- [ ] Response includes:
  - Success/failure status
  - Error message if applicable
  - Account information on success (balance, equity)

### 2. POST /disconnect Endpoint
- [ ] Implements endpoint for terminating MT5 connection
- [ ] Properly closes connection via mt5_integration
- [ ] Returns appropriate HTTP status codes:
  - 200: Successfully disconnected
  - 409: No active connection to disconnect
- [ ] Includes success/failure status in response

### 3. GET /connection_status Endpoint
- [ ] Implements endpoint to check current connection state
- [ ] Uses mt5_integration to verify actual connection status
- [ ] Returns appropriate HTTP status codes:
  - 200: Status retrieved successfully
- [ ] Response includes:
  - Connection state (DISCONNECTED, CONNECTING, CONNECTED, ERROR)
  - Error details if in ERROR state
  - Account information if CONNECTED (balance, equity)
  - Last connection attempt timestamp

### 4. Error Handling
- [ ] Implements comprehensive error handling for all endpoints
- [ ] Properly formats error responses in JSON
- [ ] Logs errors with appropriate detail level
- [ ] Handles timeout scenarios gracefully
- [ ] Provides clear error messages suitable for frontend display

### 5. Security Implementation
- [ ] Restricts API access to localhost only
- [ ] Sanitizes all input data
- [ ] Implements rate limiting for connection attempts
- [ ] Ensures credentials are handled securely in memory
- [ ] Logs security-relevant events without exposing sensitive data

### 6. Testing Requirements
- [ ] Unit tests for all endpoints
- [ ] Tests for all error scenarios
- [ ] Integration tests with MT5 integration module
- [ ] Integration tests with CredentialsManager
- [ ] Load testing for concurrent connection attempts

## API Specifications

### POST /connect
```json
Request:
{
    "account": "123456",
    "password": "secure_pass",
    "server": "MetaQuotes-Demo"
}

Success Response (200):
{
    "status": "connected",
    "account_info": {
        "balance": 10000.0,
        "equity": 10050.0
    }
}

Error Response (4xx/5xx):
{
    "status": "error",
    "error": "detailed_error_message"
}
```

### POST /disconnect
```json
Request: (empty body)

Success Response (200):
{
    "status": "disconnected"
}

Error Response (4xx):
{
    "status": "error",
    "error": "detailed_error_message"
}
```

### GET /connection_status
```json
Response (200):
{
    "status": "connected", // or "disconnected", "connecting", "error"
    "last_connected": "2025-04-09T23:30:00Z",
    "account_info": {
        "balance": 10000.0,
        "equity": 10050.0
    },
    "error": null // error message if status is "error"
}
```

## Performance Requirements
- Connection endpoint must respond within 5 seconds (success or failure)
- Status check endpoint must respond within 300ms
- Disconnect endpoint must respond within 2 seconds

## Implementation Notes
1. Use Flask blueprints to organize connection-related endpoints
2. Implement connection state caching to optimize status checks
3. Consider using background tasks for long-running operations
4. Follow existing logging patterns from BE-02
5. Use proper HTTP status codes consistently across endpoints

## Example Usage
```python
# Blueprint structure
from flask import Blueprint, request, jsonify

connection_bp = Blueprint('connection', __name__)

@connection_bp.route('/connect', methods=['POST'])
def connect():
    credentials = request.get_json()
    # Validate input
    # Store credentials
    # Attempt connection
    # Return response

@connection_bp.route('/disconnect', methods=['POST'])
def disconnect():
    # Implement disconnect logic
    # Return response

@connection_bp.route('/connection_status', methods=['GET'])
def get_status():
    # Check connection status
    # Return status response