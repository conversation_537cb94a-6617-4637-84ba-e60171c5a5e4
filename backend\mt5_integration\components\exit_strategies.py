"""Exit strategies component for the MT5 integration.

This module provides algorithmic exit strategies that integrate with the existing
analysis engine to help avoid fakeouts and improve trade performance.
"""

import logging
import time
from typing import Dict, Any, List, Optional, Tuple, Union
import MetaTrader5 as mt5
import threading
import numpy as np

from ....analysis_engine import AnalysisEngine
from ....market_data import MarketDataProvider

class ExitStrategiesComponent:
    """Component that provides algorithmic exit strategies for MT5 positions."""

    def __init__(self, connection, trading):
        """Initialize the exit strategies component.

        Args:
            connection: The connection component
            trading: The trading component for executing trades
        """
        self.logger = logging.getLogger("MT5Integration.ExitStrategies")
        self.connection = connection
        self.trading = trading
        self.analysis_engine = AnalysisEngine()
        self.market_data = MarketDataProvider()
        self.exit_threads = {}
        self.exit_strategies = {
            "trend_reversal": self._trend_reversal_exit,
            "volatility_breakout": self._volatility_breakout_exit,
            "support_resistance": self._support_resistance_exit,
            "ma_crossover": self._ma_crossover_exit,
            "rsi_overbought_oversold": self._rsi_exit
        }

    def apply_exit_strategy(self, position_id: int, strategy_name: str, 
                          params: Dict[str, Any], monitoring_interval: int = 60) -> Dict[str, Any]:
        """Apply an exit strategy to a position with continuous monitoring.

        Args:
            position_id: The position ID to apply the strategy to
            strategy_name: The name of the strategy to apply
            params: Parameters for the strategy
            monitoring_interval: How often to check conditions (seconds)

        Returns:
            Dict with status information
        """
        if strategy_name not in self.exit_strategies:
            return {"success": False, "message": f"Unknown strategy: {strategy_name}"}

        # Stop any existing monitoring for this position
        self.stop_monitoring(position_id)

        # Get the position details
        position = self.trading.get_position(position_id)
        if not position or not position.get("success", False):
            return {"success": False, "message": f"Position not found: {position_id}"}

        # Start monitoring thread
        thread = threading.Thread(
            target=self._monitor_position,
            args=(position_id, strategy_name, params, monitoring_interval),
            daemon=True
        )
        thread.start()
        self.exit_threads[position_id] = {
            "thread": thread,
            "stop_flag": False,
            "strategy": strategy_name,
            "params": params
        }

        return {
            "success": True, 
            "message": f"Exit strategy '{strategy_name}' applied to position {position_id}",
            "position_id": position_id,
            "strategy": strategy_name,
            "params": params
        }

    def stop_monitoring(self, position_id: int) -> Dict[str, Any]:
        """Stop monitoring a position for exit conditions.

        Args:
            position_id: The position ID to stop monitoring

        Returns:
            Dict with status information
        """
        if position_id in self.exit_threads:
            self.exit_threads[position_id]["stop_flag"] = True
            del self.exit_threads[position_id]
            return {"success": True, "message": f"Stopped monitoring position {position_id}"}
        return {"success": False, "message": f"Position {position_id} not being monitored"}

    def list_active_strategies(self) -> Dict[str, Any]:
        """List all positions with active exit strategies.

        Returns:
            Dict with list of monitored positions and their strategies
        """
        monitored = {}
        for pos_id, data in self.exit_threads.items():
            monitored[pos_id] = {
                "strategy": data["strategy"],
                "params": data["params"]
            }
        return {
            "success": True,
            "monitored_positions": monitored
        }

    def _monitor_position(self, position_id: int, strategy_name: str, 
                         params: Dict[str, Any], interval: int):
        """Monitor a position for exit conditions.

        Args:
            position_id: The position ID to monitor
            strategy_name: The exit strategy to apply
            params: Parameters for the strategy
            interval: How often to check conditions (seconds)
        """
        strategy_function = self.exit_strategies.get(strategy_name)
        if not strategy_function:
            self.logger.error(f"Unknown strategy: {strategy_name}")
            return

        self.logger.info(f"Starting {strategy_name} monitoring for position {position_id}")
        
        while not self.exit_threads.get(position_id, {}).get("stop_flag", True):
            try:
                # Check if position still exists
                position = self.trading.get_position(position_id)
                if not position or not position.get("success", False):
                    self.logger.info(f"Position {position_id} no longer exists, stopping monitoring")
                    break

                # Call the strategy function
                result = strategy_function(position_id, params)
                if result and result.get("exit_triggered", False):
                    self.logger.info(f"Exit triggered for position {position_id} by {strategy_name}: {result['reason']}")
                    close_result = self.trading.close_position(position_id, comment=f"Auto exit: {result['reason']}")
                    if close_result.get("success", False):
                        self.logger.info(f"Successfully closed position {position_id}")
                        break
                    else:
                        self.logger.error(f"Failed to close position {position_id}: {close_result.get('message')}")

            except Exception as e:
                self.logger.exception(f"Error in monitoring thread for position {position_id}: {str(e)}")
            
            time.sleep(interval)

        # Clean up
        if position_id in self.exit_threads:
            del self.exit_threads[position_id]
        self.logger.info(f"Stopped monitoring position {position_id}")

    def _trend_reversal_exit(self, position_id: int, params: Dict[str, Any]) -> Dict[str, Any]:
        """Exit strategy based on trend reversal detected by the analysis engine.

        This strategy helps avoid fakeouts by detecting when a trend might be reversing.

        Args:
            position_id: The position ID
            params: Parameters for the strategy, including:
                - symbol: The trading symbol
                - timeframe: The timeframe to analyze
                - reversal_strength: Minimum strength to trigger (1-10)

        Returns:
            Dict with exit decision
        """
        try:
            position_data = self.trading.get_position(position_id)
            if not position_data or not position_data.get("success", False):
                return {"exit_triggered": False, "message": "Position not found"}

            position = position_data.get("position", {})
            symbol = params.get("symbol", position.get("symbol"))
            timeframe = params.get("timeframe", "H1")
            reversal_strength = params.get("reversal_strength", 7)  # Default threshold

            # Get analysis from the engine
            analysis = self.analysis_engine.get_market_analysis(symbol, timeframe)
            
            if not analysis or not analysis.get("success", False):
                return {"exit_triggered": False, "message": "Failed to get analysis"}

            # Extract trend information
            trend_data = analysis.get("analysis", {}).get("trend", {})
            trend_direction = trend_data.get("direction", "neutral")
            trend_strength = trend_data.get("strength", 5)
            potential_reversal = trend_data.get("potential_reversal", False)
            reversal_signals = trend_data.get("reversal_signals", 0)

            # Check if we should exit based on position type and trend
            position_type = position.get("type")
            
            # For BUY positions, exit if downtrend with strong reversal signals
            if position_type == mt5.POSITION_TYPE_BUY and (
                (trend_direction == "bearish" and trend_strength >= reversal_strength) or
                (potential_reversal and reversal_signals >= reversal_strength)
            ):
                return {
                    "exit_triggered": True,
                    "reason": f"Trend reversal detected (bearish strength: {trend_strength}, signals: {reversal_signals})"
                }
            
            # For SELL positions, exit if uptrend with strong reversal signals
            elif position_type == mt5.POSITION_TYPE_SELL and (
                (trend_direction == "bullish" and trend_strength >= reversal_strength) or
                (potential_reversal and reversal_signals >= reversal_strength)
            ):
                return {
                    "exit_triggered": True,
                    "reason": f"Trend reversal detected (bullish strength: {trend_strength}, signals: {reversal_signals})"
                }

            return {"exit_triggered": False, "message": "No trend reversal detected"}

        except Exception as e:
            self.logger.exception(f"Error in trend reversal exit for position {position_id}: {str(e)}")
            return {"exit_triggered": False, "message": f"Error: {str(e)}"}

    def _volatility_breakout_exit(self, position_id: int, params: Dict[str, Any]) -> Dict[str, Any]:
        """Exit strategy based on volatility breakouts.

        Exits when volatility significantly increases in the opposite direction of the position,
        which often indicates a potential fakeout reversal.

        Args:
            position_id: The position ID
            params: Parameters including:
                - symbol: The trading symbol
                - timeframe: The timeframe to analyze
                - volatility_factor: Multiple of average volatility to trigger exit (e.g., 1.5)

        Returns:
            Dict with exit decision
        """
        try:
            position_data = self.trading.get_position(position_id)
            if not position_data or not position_data.get("success", False):
                return {"exit_triggered": False, "message": "Position not found"}

            position = position_data.get("position", {})
            symbol = params.get("symbol", position.get("symbol"))
            timeframe = params.get("timeframe", "H1")
            volatility_factor = params.get("volatility_factor", 1.5)  # Default threshold

            # Get market data for volatility calculation
            market_data = self.market_data.get_ohlc_data(symbol, timeframe, 20)  # Last 20 candles
            
            if not market_data or not market_data.get("success", False):
                return {"exit_triggered": False, "message": "Failed to get market data"}

            candles = market_data.get("data", [])
            if len(candles) < 10:  # Need enough data for calculation
                return {"exit_triggered": False, "message": "Not enough data for volatility calculation"}

            # Calculate average true range (ATR) for volatility
            highs = np.array([candle.get("high", 0) for candle in candles])
            lows = np.array([candle.get("low", 0) for candle in candles])
            closes = np.array([candle.get("close", 0) for candle in candles[:-1]])
            
            # True Range calculation
            tr1 = np.abs(highs[1:] - lows[1:])
            tr2 = np.abs(highs[1:] - closes)
            tr3 = np.abs(lows[1:] - closes)
            
            tr = np.maximum(tr1, np.maximum(tr2, tr3))
            atr = np.mean(tr[-14:])  # 14-period ATR
            
            # Get current price and position entry price
            current_price = candles[-1].get("close", 0)
            entry_price = position.get("price_open", 0)
            position_type = position.get("type")
            
            # Check for volatility breakout in opposite direction
            if position_type == mt5.POSITION_TYPE_BUY:
                price_move = entry_price - current_price
                if price_move > (atr * volatility_factor):
                    return {
                        "exit_triggered": True,
                        "reason": f"Volatility breakout detected (down move: {price_move:.5f}, ATR threshold: {atr * volatility_factor:.5f})"
                    }
            
            elif position_type == mt5.POSITION_TYPE_SELL:
                price_move = current_price - entry_price
                if price_move > (atr * volatility_factor):
                    return {
                        "exit_triggered": True,
                        "reason": f"Volatility breakout detected (up move: {price_move:.5f}, ATR threshold: {atr * volatility_factor:.5f})"
                    }
            
            return {"exit_triggered": False, "message": "No volatility breakout detected"}

        except Exception as e:
            self.logger.exception(f"Error in volatility breakout exit for position {position_id}: {str(e)}")
            return {"exit_triggered": False, "message": f"Error: {str(e)}"}

    def _support_resistance_exit(self, position_id: int, params: Dict[str, Any]) -> Dict[str, Any]:
        """Exit strategy based on price breaking key support/resistance levels.

        Uses the analysis engine's support/resistance levels to exit when price
        breaks a level in the wrong direction for the position.

        Args:
            position_id: The position ID
            params: Parameters including:
                - symbol: The trading symbol
                - timeframe: The timeframe to analyze
                - confirmation_candles: Number of candles needed to confirm a break (default 2)

        Returns:
            Dict with exit decision
        """
        try:
            position_data = self.trading.get_position(position_id)
            if not position_data or not position_data.get("success", False):
                return {"exit_triggered": False, "message": "Position not found"}

            position = position_data.get("position", {})
            symbol = params.get("symbol", position.get("symbol"))
            timeframe = params.get("timeframe", "H1")
            confirmation_candles = params.get("confirmation_candles", 2)

            # Get analysis from the engine
            analysis = self.analysis_engine.get_market_analysis(symbol, timeframe)
            
            if not analysis or not analysis.get("success", False):
                return {"exit_triggered": False, "message": "Failed to get analysis"}

            # Get support/resistance levels
            levels = analysis.get("analysis", {}).get("support_resistance", {}).get("levels", [])
            if not levels:
                return {"exit_triggered": False, "message": "No support/resistance levels found"}

            # Get market data for confirmation
            market_data = self.market_data.get_ohlc_data(symbol, timeframe, confirmation_candles + 5)
            if not market_data or not market_data.get("success", False):
                return {"exit_triggered": False, "message": "Failed to get market data"}

            candles = market_data.get("data", [])
            if len(candles) < confirmation_candles + 1:
                return {"exit_triggered": False, "message": "Not enough data for confirmation"}

            # Get current price
            current_price = candles[-1].get("close", 0)
            position_type = position.get("type")

            # For BUY positions, check if price broke below a support level
            if position_type == mt5.POSITION_TYPE_BUY:
                # Find closest support below entry price
                supports = [level.get("price") for level in levels if level.get("type") == "support"]
                if not supports:
                    return {"exit_triggered": False, "message": "No support levels found"}
                
                # Check if price is below any important support
                broken_support = None
                for support in sorted(supports, reverse=True):  # Start with highest support
                    if current_price < support:
                        # Check confirmation - price must stay below for confirmation_candles
                        confirmed = True
                        for i in range(1, confirmation_candles + 1):
                            if i >= len(candles):
                                confirmed = False
                                break
                            if candles[-i].get("close", 0) > support:
                                confirmed = False
                                break
                        
                        if confirmed:
                            broken_support = support
                            break
                
                if broken_support:
                    return {
                        "exit_triggered": True,
                        "reason": f"Support level broken: price {current_price:.5f} below support at {broken_support:.5f}"
                    }

            # For SELL positions, check if price broke above a resistance level
            elif position_type == mt5.POSITION_TYPE_SELL:
                # Find closest resistance above entry price
                resistances = [level.get("price") for level in levels if level.get("type") == "resistance"]
                if not resistances:
                    return {"exit_triggered": False, "message": "No resistance levels found"}
                
                # Check if price is above any important resistance
                broken_resistance = None
                for resistance in sorted(resistances):  # Start with lowest resistance
                    if current_price > resistance:
                        # Check confirmation - price must stay above for confirmation_candles
                        confirmed = True
                        for i in range(1, confirmation_candles + 1):
                            if i >= len(candles):
                                confirmed = False
                                break
                            if candles[-i].get("close", 0) < resistance:
                                confirmed = False
                                break
                        
                        if confirmed:
                            broken_resistance = resistance
                            break
                
                if broken_resistance:
                    return {
                        "exit_triggered": True,
                        "reason": f"Resistance level broken: price {current_price:.5f} above resistance at {broken_resistance:.5f}"
                    }

            return {"exit_triggered": False, "message": "No support/resistance level breaks detected"}

        except Exception as e:
            self.logger.exception(f"Error in support/resistance exit for position {position_id}: {str(e)}")
            return {"exit_triggered": False, "message": f"Error: {str(e)}"}

    def _ma_crossover_exit(self, position_id: int, params: Dict[str, Any]) -> Dict[str, Any]:
        """Exit strategy based on moving average crossovers.

        Uses the analysis engine's moving averages to exit when a bearish crossover
        occurs for a long position or a bullish crossover for a short position.

        Args:
            position_id: The position ID
            params: Parameters including:
                - symbol: The trading symbol
                - timeframe: The timeframe to analyze
                - fast_ma: Fast moving average period (default 9)
                - slow_ma: Slow moving average period (default 21)

        Returns:
            Dict with exit decision
        """
        try:
            position_data = self.trading.get_position(position_id)
            if not position_data or not position_data.get("success", False):
                return {"exit_triggered": False, "message": "Position not found"}

            position = position_data.get("position", {})
            symbol = params.get("symbol", position.get("symbol"))
            timeframe = params.get("timeframe", "H1")
            fast_ma = params.get("fast_ma", 9)
            slow_ma = params.get("slow_ma", 21)

            # Get analysis from the engine
            analysis = self.analysis_engine.get_market_analysis(symbol, timeframe)
            
            if not analysis or not analysis.get("success", False):
                return {"exit_triggered": False, "message": "Failed to get analysis"}

            # Extract moving average information
            ma_data = analysis.get("analysis", {}).get("moving_averages", {})
            
            # Get the specific MAs we need
            mas = ma_data.get("values", {})
            if not mas:
                # Fallback to calculation if not provided by analysis engine
                market_data = self.market_data.get_ohlc_data(symbol, timeframe, slow_ma + 10)
                if not market_data or not market_data.get("success", False):
                    return {"exit_triggered": False, "message": "Failed to get market data"}
                
                candles = market_data.get("data", [])
                closes = np.array([candle.get("close", 0) for candle in candles])
                
                # Calculate MAs
                fast_ma_values = np.convolve(closes, np.ones(fast_ma)/fast_ma, mode='valid')
                slow_ma_values = np.convolve(closes, np.ones(slow_ma)/slow_ma, mode='valid')
                
                # Check for crossover
                position_type = position.get("type")
                
                # Need at least 2 values to check crossover
                if len(fast_ma_values) < 2 or len(slow_ma_values) < 2:
                    return {"exit_triggered": False, "message": "Not enough data for MA calculation"}
                
                # Check current and previous relationship
                current_fast = fast_ma_values[-1]
                current_slow = slow_ma_values[-1]
                prev_fast = fast_ma_values[-2]
                prev_slow = slow_ma_values[-2]
                
                # For BUY positions, exit on bearish crossover (fast MA crosses below slow MA)
                if position_type == mt5.POSITION_TYPE_BUY and prev_fast > prev_slow and current_fast < current_slow:
                    return {
                        "exit_triggered": True,
                        "reason": f"Bearish MA crossover: fast MA ({fast_ma}) crossed below slow MA ({slow_ma})"
                    }
                
                # For SELL positions, exit on bullish crossover (fast MA crosses above slow MA)
                elif position_type == mt5.POSITION_TYPE_SELL and prev_fast < prev_slow and current_fast > current_slow:
                    return {
                        "exit_triggered": True,
                        "reason": f"Bullish MA crossover: fast MA ({fast_ma}) crossed above slow MA ({slow_ma})"
                    }
            else:
                # Use analysis engine data
                crossovers = ma_data.get("crossovers", [])
                position_type = position.get("type")
                
                for crossover in crossovers:
                    if (crossover.get("type") == "bearish" and 
                        position_type == mt5.POSITION_TYPE_BUY and 
                        crossover.get("fast_period") == fast_ma and 
                        crossover.get("slow_period") == slow_ma):
                        return {
                            "exit_triggered": True,
                            "reason": f"Bearish MA crossover: {fast_ma} crossed below {slow_ma}"
                        }
                    elif (crossover.get("type") == "bullish" and 
                          position_type == mt5.POSITION_TYPE_SELL and 
                          crossover.get("fast_period") == fast_ma and 
                          crossover.get("slow_period") == slow_ma):
                        return {
                            "exit_triggered": True,
                            "reason": f"Bullish MA crossover: {fast_ma} crossed above {slow_ma}"
                        }
            
            return {"exit_triggered": False, "message": "No MA crossover detected"}

        except Exception as e:
            self.logger.exception(f"Error in MA crossover exit for position {position_id}: {str(e)}")
            return {"exit_triggered": False, "message": f"Error: {str(e)}"}

    def _rsi_exit(self, position_id: int, params: Dict[str, Any]) -> Dict[str, Any]:
        """Exit strategy based on RSI overbought/oversold levels.

        Uses the analysis engine's RSI indicator to exit when RSI enters
        overbought territory for shorts or oversold for longs.

        Args:
            position_id: The position ID
            params: Parameters including:
                - symbol: The trading symbol
                - timeframe: The timeframe to analyze
                - rsi_period: RSI period (default 14)
                - overbought: Overbought threshold (default 70)
                - oversold: Oversold threshold (default 30)

        Returns:
            Dict with exit decision
        """
        try:
            position_data = self.trading.get_position(position_id)
            if not position_data or not position_data.get("success", False):
                return {"exit_triggered": False, "message": "Position not found"}

            position = position_data.get("position", {})
            symbol = params.get("symbol", position.get("symbol"))
            timeframe = params.get("timeframe", "H1")
            rsi_period = params.get("rsi_period", 14)
            overbought = params.get("overbought", 70)
            oversold = params.get("oversold", 30)

            # Get analysis from the engine
            analysis = self.analysis_engine.get_market_analysis(symbol, timeframe)
            
            if not analysis or not analysis.get("success", False):
                return {"exit_triggered": False, "message": "Failed to get analysis"}

            # Extract RSI information
            indicators = analysis.get("analysis", {}).get("indicators", {})
            rsi_value = indicators.get("rsi", {}).get("value")
            
            if rsi_value is None:
                return {"exit_triggered": False, "message": "RSI value not available"}

            position_type = position.get("type")
            
            # For BUY positions, exit on oversold
            if position_type == mt5.POSITION_TYPE_BUY and rsi_value < oversold:
                return {
                    "exit_triggered": True,
                    "reason": f"RSI oversold: value {rsi_value:.2f} below threshold {oversold}"
                }
            
            # For SELL positions, exit on overbought
            elif position_type == mt5.POSITION_TYPE_SELL and rsi_value > overbought:
                return {
                    "exit_triggered": True,
                    "reason": f"RSI overbought: value {rsi_value:.2f} above threshold {overbought}"
                }
            
            return {"exit_triggered": False, "message": f"RSI ({rsi_value:.2f}) not at exit levels"}

        except Exception as e:
            self.logger.exception(f"Error in RSI exit for position {position_id}: {str(e)}")
            return {"exit_triggered": False, "message": f"Error: {str(e)}"}
