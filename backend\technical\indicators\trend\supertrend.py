from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.analysis.technical.indicators.volatility.atr import ATRIndicator # Supertrend uses ATR
from src.core.models.market_data import MarketData

class SupertrendIndicator(BaseIndicator):
    """Supertrend indicator."""

    def __init__(self, atr_period: int = 10, atr_multiplier: float = 3.0):
        """
        Initialize Supertrend indicator.

        Args:
            atr_period: The period for the underlying ATR calculation.
            atr_multiplier: The multiplier for the ATR value.
        """
        super().__init__({
            'atr_period': atr_period,
            'atr_multiplier': atr_multiplier
        })
        # Internal ATR indicator
        self._atr_indicator = ATRIndicator(period=atr_period, ma_type='sma') # Standard Supertrend uses SMA for ATR

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Supertrend values."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['atr_period']:
             return {'supertrend': np.array([]), 'trend': np.array([])}

        atr_period = self.params['atr_period']
        atr_multiplier = self.params['atr_multiplier']

        high = df['high']
        low = df['low']
        close = df['close']

        # Calculate ATR
        atr_result = self._atr_indicator.calculate(data)
        atr = pd.Series(atr_result['atr'], index=df.index) # Ensure index alignment

        # Calculate basic upper and lower bands
        hl_mean = (high + low) / 2
        basic_upper = hl_mean + (atr_multiplier * atr)
        basic_lower = hl_mean - (atr_multiplier * atr)

        # Initialize final bands and supertrend arrays
        n = len(df)
        final_upper = np.full(n, np.nan)
        final_lower = np.full(n, np.nan)
        supertrend = np.full(n, np.nan)
        trend = np.full(n, 1) # Start with uptrend assumption

        # Calculate Supertrend iteratively
        for i in range(1, n):
            # Final upper band calculation
            if basic_upper.iloc[i] < final_upper[i-1] or close.iloc[i-1] > final_upper[i-1]:
                final_upper[i] = basic_upper.iloc[i]
            else:
                final_upper[i] = final_upper[i-1]

            # Final lower band calculation
            if basic_lower.iloc[i] > final_lower[i-1] or close.iloc[i-1] < final_lower[i-1]:
                final_lower[i] = basic_lower.iloc[i]
            else:
                final_lower[i] = final_lower[i-1]

            # Determine current trend and Supertrend value
            if trend[i-1] == 1: # Previous trend was up
                supertrend[i] = final_lower[i]
                if close.iloc[i] < final_lower[i]:
                    trend[i] = -1
                    supertrend[i] = final_upper[i]
            else: # Previous trend was down
                supertrend[i] = final_upper[i]
                if close.iloc[i] > final_upper[i]:
                    trend[i] = 1
                    supertrend[i] = final_lower[i]

        # Handle the first element (can't calculate based on previous)
        # A common approach is to initialize based on the first calculated trend
        if n > 0:
             final_upper[0] = basic_upper.iloc[0] if pd.notna(basic_upper.iloc[0]) else np.nan
             final_lower[0] = basic_lower.iloc[0] if pd.notna(basic_lower.iloc[0]) else np.nan
             # Initial supertrend value depends on initial trend assumption (here: up)
             supertrend[0] = final_lower[0]


        self._values = {
            'supertrend': supertrend,
            'trend': trend, # 1 for uptrend, -1 for downtrend
            'upper_band': final_upper, # Final upper band used
            'lower_band': final_lower  # Final lower band used
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['atr_period'] < 1:
            raise ValueError("ATR Period must be greater than 0")
        if self.params['atr_multiplier'] <= 0:
            raise ValueError("ATR Multiplier must be positive")
        return True