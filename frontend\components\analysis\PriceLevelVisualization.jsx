import React from 'react';
import { safeToFixed } from '../../utils/numberUtils';

/**
 * PriceLevelVisualization component for visualizing price in relation to support and resistance
 *
 * @param {Object} analysisData - The analysis data containing price and support/resistance information
 * @returns {JSX.Element} - The rendered price level visualization
 */
const PriceLevelVisualization = ({ analysisData }) => {
  // Get the actual numeric current price, preferring bid, then last, then close from the main analysis data
  const currentPriceValue =
    (analysisData.current_price?.bid !== null && analysisData.current_price?.bid !== undefined) ? analysisData.current_price.bid :
    (analysisData.support_resistance?.current_price?.bid !== null && analysisData.support_resistance?.current_price?.bid !== undefined) ? analysisData.support_resistance.current_price.bid :
    (analysisData.current_price?.last !== null && analysisData.current_price?.last !== undefined) ? analysisData.current_price.last :
    (analysisData.support_resistance?.current_price?.last !== null && analysisData.support_resistance?.current_price?.last !== undefined) ? analysisData.support_resistance.current_price.last :
    (analysisData.price_data?.close && analysisData.price_data.close.length > 0) ? analysisData.price_data.close[analysisData.price_data.close.length - 1] :
    null;

  // Get nearest support and resistance
  const nearestSupport = analysisData.support_resistance?.nearest_support;
  const nearestResistance = analysisData.support_resistance?.nearest_resistance;

  // Calculate position (percentage from left)
  let position = 50; // Default to middle

  if (nearestSupport?.price != null && nearestResistance?.price != null && currentPriceValue != null) {
    // Calculate position based on actual values
    const range = nearestResistance.price - nearestSupport.price;
    if (range > 0) {
      position = ((currentPriceValue - nearestSupport.price) / range) * 100;
      // Clamp between 0 and 100
      position = Math.max(0, Math.min(100, position));
    } else {
      // Handle edge case where support and resistance are the same or inverted
      position = currentPriceValue >= nearestResistance.price ? 100 : 0;
    }
  } else if (nearestSupport?.price != null && currentPriceValue != null && nearestResistance?.price == null) {
    // If price is above support and no resistance
    position = currentPriceValue > nearestSupport.price ? 75 : 25; // Position relative to support
  } else if (nearestSupport?.price == null && currentPriceValue != null && nearestResistance?.price != null) {
    // If price is below resistance and no support
    position = currentPriceValue < nearestResistance.price ? 25 : 75; // Position relative to resistance
  }

  return (
    <div style={{ padding: '15px', backgroundColor: 'rgba(0,0,0,0.1)', borderBottom: '1px solid rgba(255,255,255,0.05)' }}>

      {/* Horizontal price bar with meaningful position */}
      <div style={{
        position: 'relative',
        height: '30px',
        backgroundColor: '#2a2f45',
        borderRadius: '6px',
        marginBottom: '15px',
        overflow: 'hidden',
        border: '1px solid rgba(255,255,255,0.1)'
      }}>
        {/* Support/Resistance zones */}
        {nearestSupport?.price != null && nearestResistance?.price != null && (
          <div style={{
            position: 'absolute',
            left: '0',
            top: '0',
            width: '100%',
            height: '100%',
            background: 'linear-gradient(to right, rgba(46,204,64,0.1) 0%, rgba(42,47,69,0.05) 50%, rgba(255,65,54,0.1) 100%)'
          }}></div>
        )}

        {/* Current price marker */}
        <div style={{
          position: 'absolute',
          left: `${position}%`,
          top: '0',
          width: '3px',
          height: '100%',
          backgroundColor: '#ffd700',
          transform: 'translateX(-50%)',
          boxShadow: '0 0 8px rgba(255, 215, 0, 0.5)',
          zIndex: 2
        }}></div>

        {/* Price labels */}
        {nearestSupport?.price != null && (
          <div style={{
            position: 'absolute',
            left: '4px',
            bottom: '4px',
            fontSize: '0.7rem',
            color: '#2ecc40',
            fontWeight: 'bold',
            zIndex: 2
          }}>
            {safeToFixed(nearestSupport.price, 5)}
          </div>
        )}

        {nearestResistance?.price != null && (
          <div style={{
            position: 'absolute',
            right: '4px',
            bottom: '4px',
            fontSize: '0.7rem',
            color: '#ff4136',
            fontWeight: 'bold',
            zIndex: 2
          }}>
            {safeToFixed(nearestResistance.price, 5)}
          </div>
        )}

        {/* Current price label */}
        {currentPriceValue != null && (
          <div style={{
            position: 'absolute',
            left: `${position}%`,
            top: '2px',
            transform: 'translateX(-50%)',
            fontSize: '0.7rem',
            color: '#ffd700',
            fontWeight: 'bold',
            backgroundColor: 'rgba(0,0,0,0.4)',
            padding: '2px 4px',
            borderRadius: '2px',
            whiteSpace: 'nowrap',
            zIndex: 3
          }}>
            {safeToFixed(currentPriceValue, 5)}
          </div>
        )}
      </div>

      {/* Distance stats with icons */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        fontSize: '0.8rem'
      }}>
        {nearestSupport?.price != null && currentPriceValue != null && (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '5px',
            color: '#aaa'
          }}>
            <span style={{ color: '#2ecc40', fontSize: '1rem' }}>▲</span>
            <div>
              <div style={{ marginBottom: '2px' }}>Distance to Support</div>
              <div style={{ fontWeight: 'bold', color: '#2ecc40' }}>
                {safeToFixed(Math.abs(((currentPriceValue - nearestSupport.price) / currentPriceValue) * 100), 2, '0.00')}%
              </div>
            </div>
          </div>
        )}

        {nearestResistance?.price != null && currentPriceValue != null && (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '5px',
            color: '#aaa',
            textAlign: 'right'
          }}>
            <div>
              <div style={{ marginBottom: '2px' }}>Distance to Resistance</div>
              <div style={{ fontWeight: 'bold', color: '#ff4136' }}>
                {safeToFixed(Math.abs(((nearestResistance.price - currentPriceValue) / currentPriceValue) * 100), 2, '0.00')}%
              </div>
            </div>
            <span style={{ color: '#ff4136', fontSize: '1rem' }}>▼</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default PriceLevelVisualization;
