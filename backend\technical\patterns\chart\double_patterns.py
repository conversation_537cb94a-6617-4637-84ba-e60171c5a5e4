from typing import Dict, Any, List
import numpy as np
import pandas as pd
from scipy.signal import find_peaks

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class DoublePatternsIndicator(BaseIndicator):
    """Pattern indicator for Double Top/Bottom patterns."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize Double Top/Bottom pattern indicator.
        
        Args:
            params: Dictionary containing parameters:
                - min_peak_distance: Minimum distance between peaks (default: 10)
                - min_peak_height: Minimum height of peaks relative to price (default: 0.02)
                - symmetry_tolerance: Maximum allowed asymmetry ratio (default: 0.02 or 2%)
                - valley_depth: Minimum depth of valley between peaks (default: 0.01 or 1%)
        """
        super().__init__(params)
        self.min_peak_distance = self.params.get('min_peak_distance', 10)
        self.min_peak_height = self.params.get('min_peak_height', 0.02)
        self.symmetry_tolerance = self.params.get('symmetry_tolerance', 0.02)
        self.valley_depth = self.params.get('valley_depth', 0.01)

    def _find_peaks_and_troughs(self, prices: np.ndarray, min_distance: int,
                              min_height: float) -> tuple:
        """Find peaks and troughs in price data."""
        # Find peaks
        peaks, _ = find_peaks(prices, distance=min_distance,
                            height=np.mean(prices) * min_height)
        
        # Find troughs (inverse of peaks)
        troughs, _ = find_peaks(-prices, distance=min_distance,
                              height=np.mean(prices) * min_height)
        
        return peaks, troughs
    
    def _is_double_top(self, prices: np.ndarray, peaks: np.ndarray,
                      troughs: np.ndarray) -> tuple:
        """Identify Double Top patterns."""
        is_pattern = np.zeros_like(prices)
        pattern_type = np.zeros_like(prices)
        pattern_id = np.zeros_like(prices)
        
        if len(peaks) >= 2:
            for i in range(len(peaks)-1):
                # Get two consecutive peaks
                left_peak = peaks[i]
                right_peak = peaks[i+1]
                
                # Get the trough between peaks
                valley = troughs[troughs > left_peak]
                valley = valley[valley < right_peak]
                
                if len(valley) > 0:
                    valley = valley[0]
                    
                    # Check if it's a valid Double Top pattern
                    if (prices[left_peak] > prices[valley] and
                        prices[right_peak] > prices[valley] and
                        abs(prices[left_peak] - prices[right_peak]) / prices[left_peak] < self.symmetry_tolerance and
                        (prices[valley] - min(prices[left_peak], prices[right_peak])) / prices[valley] < -self.valley_depth):
                        
                        # Mark the pattern
                        is_pattern[left_peak:right_peak+1] = 1
                        pattern_type[left_peak:right_peak+1] = -1  # Bearish pattern
                        pattern_id[left_peak:right_peak+1] = i
        
        return is_pattern, pattern_type, pattern_id
    
    def _is_double_bottom(self, prices: np.ndarray, peaks: np.ndarray,
                         troughs: np.ndarray) -> tuple:
        """Identify Double Bottom patterns."""
        is_pattern = np.zeros_like(prices)
        pattern_type = np.zeros_like(prices)
        pattern_id = np.zeros_like(prices)
        
        if len(troughs) >= 2:
            for i in range(len(troughs)-1):
                # Get two consecutive troughs
                left_trough = troughs[i]
                right_trough = troughs[i+1]
                
                # Get the peak between troughs
                peak = peaks[peaks > left_trough]
                peak = peak[peak < right_trough]
                
                if len(peak) > 0:
                    peak = peak[0]
                    
                    # Check if it's a valid Double Bottom pattern
                    if (prices[left_trough] < prices[peak] and
                        prices[right_trough] < prices[peak] and
                        abs(prices[left_trough] - prices[right_trough]) / prices[left_trough] < self.symmetry_tolerance and
                        (prices[peak] - max(prices[left_trough], prices[right_trough])) / prices[peak] > self.valley_depth):
                        
                        # Mark the pattern
                        is_pattern[left_trough:right_trough+1] = 1
                        pattern_type[left_trough:right_trough+1] = 1  # Bullish pattern
                        pattern_id[left_trough:right_trough+1] = i
        
        return is_pattern, pattern_type, pattern_id
    
    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """
        Calculate Double Top/Bottom pattern values.
        
        Args:
            market_data: Market data containing OHLCV values
            
        Returns:
            Dictionary containing:
                - is_pattern: Boolean array indicating pattern presence
                - pattern_type: Integer array indicating pattern type (1: Double Bottom, -1: Double Top)
                - pattern_id: Integer array for pattern instance grouping
                - strength: Float array indicating pattern strength
                - trend: Integer array indicating trend context
                - reliability: Float array indicating pattern reliability
        """
        df = market_data.to_dataframe()
        close = df['close'].values
        
        # Find peaks and troughs
        peaks, troughs = self._find_peaks_and_troughs(close, self.min_peak_distance, self.min_peak_height)
        
        # Identify patterns
        is_dt, dt_type, dt_id = self._is_double_top(close, peaks, troughs)
        is_db, db_type, db_id = self._is_double_bottom(close, peaks, troughs)
        
        # Combine patterns using logical operations
        is_pattern = np.logical_or(is_dt, is_db)
        pattern_type = np.where(is_dt, dt_type, db_type)
        pattern_id = np.where(is_dt, dt_id, db_id)
        
        # Calculate pattern strength
        strength = np.zeros_like(close)
        for i in range(len(close)):
            if is_pattern[i]:
                # Calculate the height of the pattern relative to price
                if pattern_type[i] < 0:  # Bearish pattern
                    height = close[peaks[peaks > i][0]] - close[troughs[troughs > i][0]]
                else:  # Bullish pattern
                    height = close[peaks[peaks > i][0]] - close[troughs[troughs > i][0]]
                strength[i] = min(1.0, height / close[i])
        
        # Calculate trend context using 20-period SMA
        sma20 = df['close'].rolling(window=20).mean()
        trend = np.where(df['close'] > sma20, 1, -1)
        
        # Calculate pattern reliability based on future price movement
        reliability = np.full_like(close, -1, dtype=float)  # Default to -1 for no pattern
        future_window = 20
        
        for i in range(len(close) - future_window):
            if is_pattern[i]:
                future_returns = (df['close'].iloc[i+1:i+future_window+1].values - 
                                df['close'].iloc[i]) / df['close'].iloc[i]
                
                if pattern_type[i] == 1:  # Bullish pattern
                    max_return = np.max(future_returns)
                    reliability[i] = 1 if max_return > 0 else -1
                else:  # Bearish pattern
                    min_return = np.min(future_returns)
                    reliability[i] = 1 if min_return < 0 else -1
        
        return {
            'is_pattern': is_pattern,
            'pattern_type': pattern_type,
            'pattern_id': pattern_id,
            'strength': strength,
            'trend': trend,
            'reliability': reliability
        }
    
    def validate_params(self) -> None:
        """
        Validate indicator parameters.
        
        Raises:
            ValueError: If parameters are invalid
        """
        if self.min_peak_distance < 2:
            raise ValueError("min_peak_distance must be at least 2")
        if not 0 < self.min_peak_height < 1:
            raise ValueError("min_peak_height must be between 0 and 1")
        if not 0 < self.symmetry_tolerance < 1:
            raise ValueError("symmetry_tolerance must be between 0 and 1")
        if not 0 < self.valley_depth < 1:
            raise ValueError("valley_depth must be between 0 and 1") 