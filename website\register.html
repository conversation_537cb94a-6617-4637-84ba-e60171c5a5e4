<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GarudaAlgo | Registration</title>
  <meta name="description" content="Register your broker account with GarudaAlgo - Navigate the markets with algorithmic intelligence & strategic precision.">
  <link rel="icon" href="app_icon.ico">
  <link rel="stylesheet" href="style.css">
  <link rel="stylesheet" href="registration-steps.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    /* Registration layout */
    .registration-section {
      min-height: calc(100vh - 200px);
      padding: 2rem 1rem;
      display: flex;
      align-items: center;
    }

    .registration-container {
      width: 100%;
      max-width: 600px;
      margin: 0 auto;
      background: var(--bg-card);
      border-radius: 1rem;
      padding: 2.5rem;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.08);
    }

    /* Form header */
    .registration-header {
      text-align: center;
      margin-bottom: 2.5rem;
    }

    .registration-header h2 {
      font-size: 1.75rem;
      line-height: 1.2;
      margin-bottom: 0.75rem;
      background: var(--text-gradient);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      font-weight: 600;
    }

    /* Form groups */
    .registration-form .form-group {
      margin-bottom: 1.5rem;
    }

    .registration-form label {
      display: block;
      font-size: 0.875rem;
      margin-bottom: 8px;
      font-weight: 500;
    }

    /* Form field styles */
    .form-group {
      position: relative;
      margin-bottom: 1.5rem;
    }

    /* Form inputs */
    .registration-form input {
      width: 100%;
      padding: 0.875rem 1rem;
      border-radius: 0.75rem;
      background: var(--bg-dark);
      border: 2px solid rgba(255, 255, 255, 0.1);
      color: var(--text-primary);
      font-size: 0.875rem;
      transition: all 0.2s ease;
    }

    .registration-form input:hover:not(:focus) {
      border-color: rgba(255, 255, 255, 0.2);
    }

    .registration-form input:focus {
      border-color: var(--primary);
      outline: none;
      box-shadow: 0 0 0 3px var(--primary-alpha);
    }

    /* Input validation states */
    .registration-form input:hover:not(:focus):not(.valid):not(.invalid) {
      border-color: rgba(255, 255, 255, 0.2);
    }

    .registration-form input.valid {
      border-color: var(--success);
    }

    .registration-form input.valid:focus {
      border-color: var(--success);
      box-shadow: 0 0 0 3px var(--success-alpha);
    }

    .registration-form input.invalid {
      border-color: var(--error);
    }

    .registration-form input.invalid:focus {
      border-color: var(--error);
      box-shadow: 0 0 0 3px var(--error-alpha);
    }

    /* Input icons */
    .input-icon {
      position: absolute;
      right: 1rem;
      top: 50%;
      transform: translateY(-50%);
      color: var(--text-secondary);
      transition: all 0.2s ease;
      pointer-events: none;
    }

    .form-group.valid .input-icon {
      color: #2ed573;
    }

    .form-group.invalid .input-icon {
      color: var(--error);
    }

    .form-group.valid .input-icon {
      color: var(--success);
    }

    /* Error message styles */
    .error-message {
      color: var(--error);
      font-size: 0.75rem;
      margin-top: 0.375rem;
      display: none;
      opacity: 0;
      transform: translateY(-0.25rem);
      transition: all 0.2s ease;
    }

    .error-message.show {
      display: block;
      opacity: 1;
      transform: translateY(0);
    }

    @keyframes slideDown {
      from {
        opacity: 0;
        transform: translateY(-0.5rem);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes slideDown {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Form buttons */
    .form-buttons {
      margin-top: 2rem;
    }

    .form-button {
      width: 100%;
      padding: 0.875rem 1.5rem;
      border-radius: 0.75rem;
      background: var(--primary);
      color: white;
      font-weight: 500;
      font-size: 0.875rem;
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
    }

    .form-button:hover:not(:disabled) {
      background: var(--primary-dark);
      transform: translateY(-1px);
    }

    .form-button:active:not(:disabled) {
      transform: translateY(0);
    }

    .form-button:focus-visible {
      outline: none;
      box-shadow: 0 0 0 3px var(--primary-alpha);
    }

    .form-button:disabled {
      background: var(--primary-muted);
      cursor: not-allowed;
    }

    .form-button .spinner {
      opacity: 0;
      width: 16px;
      height: 16px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top-color: white;
      border-radius: 50%;
      animation: spin 0.6s linear infinite;
      transition: opacity 0.2s ease;
    }

    .form-button.loading .spinner {
      opacity: 1;
    }

    /* Animations */
    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }

    /* Form buttons */
    .form-button {
      width: 100%;
      padding: 0.875rem 1.5rem;
      border-radius: 0.75rem;
      background: var(--primary);
      color: white;
      font-weight: 500;
      font-size: 0.875rem;
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
    }

    .form-button:hover:not(:disabled) {
      background: var(--primary-dark);
      transform: translateY(-1px);
    }

    .form-button:active:not(:disabled) {
      transform: translateY(0);
    }

    .form-button:focus-visible {
      outline: none;
      box-shadow: 0 0 0 3px var(--primary-alpha);
    }

    .form-button:disabled {
      background: var(--primary-muted);
      cursor: not-allowed;
    }

    .form-button.loading {
      pointer-events: none;
      opacity: 0.8;
    }

    .form-button .spinner {
      width: 16px;
      height: 16px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top-color: white;
      border-radius: 50%;
      animation: spin 0.6s linear infinite;
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    .form-button.loading .spinner {
      opacity: 1;
    }

    /* Password strength indicator */
    .password-strength {
      height: 4px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 4px;
      margin-top: 0.5rem;
      overflow: hidden;
      position: relative;
    }

    .password-strength-bar {
      height: 100%;
      width: 0;
      border-radius: inherit;
      transition: all 0.3s ease;
      background: var(--error);
    }

    .password-strength-bar.weak {
      width: 33.33%;
      background: var(--error);
    }

    .password-strength-bar.medium {
      width: 66.67%;
      background: var(--warning);
    }

    .password-strength-bar.strong {
      width: 100%;
      background: var(--success);
    }

    /* Password strength text */
    .password-strength-text {
      font-size: 0.75rem;
      margin-top: 0.25rem;
      transition: all 0.2s ease;
    }

    .password-strength-text.weak {
      color: var(--error);
    }

    .password-strength-text.medium {
      color: var(--warning);
    }

    .password-strength-text.strong {
      color: var(--success);
    }

    /* Password strength animations */
    @keyframes strengthIn {
      from {
        transform: scale(0.95);
        opacity: 0;
      }
      to {
        transform: scale(1);
        opacity: 1;
      }
    }

    .password-strength-bar {
      height: 100%;
      width: 0;
      transition: all 0.3s ease;
    }

    .strength-weak {
      background: #ff5757;
      width: 33%;
    }
    
    .strength-medium {
      background: #ffd32a;
      width: 66%;
    }
    
    .strength-strong {
      background: #2ed573;
      width: 100%;
    }

    .registration-form .btn-primary {
      width: 100%;
      margin-top: 10px;
    }

    .form-note {
      margin-top: 20px;
      font-size: 14px;
      color: var(--text-secondary);
      text-align: center;
    }

    .form-note a {
      color: var(--primary-light);
      text-decoration: none;
    }

    .form-note a:hover {
      text-decoration: underline;
    }

    /* Success message styles */
    .success-message {
      text-align: center;
      padding: 2.5rem;
      display: none;
      flex-direction: column;
      align-items: center;
      gap: 1.5rem;
    }

    .success-icon {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: rgba(46, 213, 115, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1rem;
    }

    .success-icon i {
      font-size: 2.5rem;
      color: #2ed573;
      filter: drop-shadow(0 0 10px rgba(46, 213, 115, 0.3));
    }

    .success-content {
      max-width: 400px;
    }

    .success-content h3 {
      color: #2ed573;
      font-size: 1.5rem;
      margin-bottom: 1rem;
      font-weight: 600;
    }

    .success-steps {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 1rem;
      margin: 2rem 0;
    }

    .success-step {
      background: rgba(255, 255, 255, 0.03);
      padding: 1.25rem 1rem;
      border-radius: 12px;
      text-align: center;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .success-step i {
      color: #2ed573;
      font-size: 1.5rem;
      margin-bottom: 0.75rem;
    }

    .success-step p {
      color: var(--text-primary);
      font-size: 0.875rem;
      line-height: 1.4;
      margin: 0;
    }

    .success-message-desc {
      color: var(--text-secondary);
      font-size: 0.875rem;
      line-height: 1.6;
    }

    .countdown {
      margin-top: 1.5rem;
      padding: 0.75rem 1.5rem;
      background: rgba(46, 213, 115, 0.1);
      border-radius: 20px;
      color: #2ed573;
      font-size: 0.875rem;
      font-weight: 500;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }

    .affiliate-notice {
      background: rgba(240, 199, 94, 0.1);
      border: 1px solid rgba(240, 199, 94, 0.3);
      border-radius: 8px;
      padding: 15px;
      margin-top: 15px;
      display: flex;
      align-items: flex-start;
      gap: 12px;
    }

    .affiliate-notice i {
      color: #F0C75E;
      font-size: 20px;
      margin-top: 2px;
    }

    .affiliate-notice p {
      color: var(--text-primary);
      font-size: 14px;
      line-height: 1.5;
      margin: 0;
    }

    .affiliate-info {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .affiliate-info h3 {
      font-size: 18px;
      margin-bottom: 15px;
      color: var(--text-primary);
    }

    .affiliate-links {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      justify-content: center;
    }

    .affiliate-links a {
      display: block;
      transition: transform 0.3s ease;
    }

    .affiliate-links a:hover {
      transform: translateY(-5px);
    }

    .affiliate-links img {
      max-width: 100%;
      height: auto;
      border-radius: 8px;
    }

    .error-message {
      color: #ff5757;
      font-size: 14px;
      margin-top: 5px;
      display: none;
    }

    /* Registration steps */
    .steps-container {
      margin: 2rem 0;
    }

    .registration-steps {
      display: flex;
      justify-content: space-between;
      max-width: 600px;
      margin: 2rem auto;
      padding: 0 2rem;
      position: relative;
    }

    .step {
      flex: 1;
      text-align: center;
      position: relative;
      z-index: 1;
    }

    .step-number {
      width: 32px;
      height: 32px;
      margin: 0 auto 0.5rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--bg-dark);
      border: 2px solid rgba(255, 255, 255, 0.1);
      color: var(--text-secondary);
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .step.active .step-number {
      background: var(--primary);
      border-color: var(--primary-light);
      color: white;
      box-shadow: 0 0 15px var(--primary-light);
    }

    .step-text {
      font-size: 0.875rem;
      color: var(--text-secondary);
      font-weight: 400;
      transition: all 0.3s ease;
    }

    .step.active .step-text {
      color: var(--text-primary);
      font-weight: 500;
    }

    /* Progress line */
    .registration-steps::before {
      content: '';
      position: absolute;
      top: 15px;
      left: 60px;
      right: 60px;
      height: 2px;
      background: rgba(255, 255, 255, 0.1);
      z-index: 0;
    }

    .registration-steps::after {
      content: '';
      position: absolute;
      top: 15px;
      left: 60px;
      width: var(--progress, 0%);
      height: 2px;
      background: var(--primary);
      transition: width 0.3s ease;
      z-index: 0;
      z-index: 1;
    }

    .registration-steps.started::before {
      background: var(--primary);
      transition: background 0.3s ease-out;
    }

    .step-number {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: var(--bg-dark);
      border: 2px solid rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      margin-bottom: 0.5rem;
      transition: all 0.3s ease;
    }

    .step-text {
      transition: all 0.3s ease;
      position: relative;
      font-size: 0.875rem;
      color: var(--text-secondary);
    }

    .step.active .step-text {
      color: var(--text-primary);
      font-weight: 500;
    }

    .step.active .step-text::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 0;
      width: 100%;
      height: 2px;
      background: var(--primary);
      transform: scaleX(0);
      transform-origin: left;
      animation: textUnderline 0.5s ease-out forwards;
    }

    @keyframes textUnderline {
      to {
        transform: scaleX(1);
      }
    }

    /* Responsive styles */
    @media (max-width: 640px) {
      .registration-container {
        padding: 1.5rem;
        margin: 1rem;
      }

      .registration-header h2 {
        font-size: 1.5rem;
      }

      .form-button {
        padding: 0.75rem 1rem;
      }

      .registration-steps {
        padding: 0 1rem;
      }

      .step-text {
        font-size: 0.75rem;
      }
    }

    /* Dark mode enhancements */
    @media (prefers-color-scheme: dark) {
      .registration-container {
        background: rgba(31, 41, 55, 0.7);
        backdrop-filter: blur(10px);
      }

      .form-button:focus-visible {
        box-shadow: 0 0 0 3px var(--primary-alpha), 0 0 0 6px rgba(0, 0, 0, 0.3);
      }
    }

    .step:not(.active) {
      opacity: 0.5;
      transform: translateY(10px);
      position: relative;
    }

    .step.active {
      opacity: 1;
      transform: translateY(0);
    }

    @keyframes stepProgress {
      from {
        width: 0;
        opacity: 0;
      }
      to {
        width: 100%;
        opacity: 1;
      }
    }

    .registration-steps::before {
      animation: stepProgress 1.5s ease-out forwards;
    }

    @keyframes numberPulse {
      0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(108, 99, 255, 0.4);
      }
      70% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(108, 99, 255, 0);
      }
      100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(108, 99, 255, 0);
      }
    }

    .step.active .step-number {
      animation: numberPulse 2s infinite;
      background: var(--primary);
      border-color: var(--primary-light);
      color: white;
    }

    .step-text {
      transition: all 0.3s ease;
      position: relative;
    }

    @keyframes textUnderline {
      from {
        transform: scaleX(0);
      }
      to {
        transform: scaleX(1);
      }
    }

    .step.active .step-text::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 0;
      width: 100%;
      height: 2px;
      background: var(--primary);
      transform-origin: left;
      animation: textUnderline 0.5s ease-out forwards;
    }

    .step-number {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: var(--bg-dark);
      border: 2px solid rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      margin-bottom: 0.5rem;
      transition: all 0.3s ease;
      position: relative;
      z-index: 2;
    }

    .step.active .step-text::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 0;
      width: 100%;
      height: 2px;
      background: var(--primary);
      transform: scaleX(0);
      animation: textUnderline 0.5s ease-out forwards;
    }

    @keyframes textUnderline {
      to {
        transform: scaleX(1);
      }
    }

    .step-number {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: var(--bg-dark);
      display: grid;
      place-items: center;
      border: 2px solid rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      margin-bottom: 0.5rem;
      transition: all 0.3s ease;
    }

    .step.active .step-number {
      background: var(--primary);
      border-color: var(--primary-light);
      color: white;
    }

    .step-text {
      font-size: 0.875rem;
      color: var(--text-secondary);
      transition: color 0.3s ease;
    }

    .step.active .step-text {
      color: var(--text-primary);
    }

    /* Success message improvements */
    .success-message {
      background: linear-gradient(135deg, rgba(46, 213, 115, 0.1), rgba(46, 213, 115, 0.05));
      backdrop-filter: blur(8px);
      border: 1px solid rgba(46, 213, 115, 0.2);
      padding: 2rem;
      border-radius: 12px;
      margin-bottom: 2rem;
      display: none;
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: 1rem;
      animation: fadeScale 0.3s ease-out;
    }

    .success-message i {
      font-size: 3rem;
      color: #2ed573;
      margin-bottom: 0.5rem;
    }

    .success-message h3 {
      color: #2ed573;
      margin-bottom: 0.5rem;
      font-size: 1.5rem;
    }

    .success-message p {
      color: var(--text-primary);
      font-size: 1rem;
      line-height: 1.6;
      margin: 0;
      max-width: 400px;
    }

    @keyframes fadeScale {
      from {
        opacity: 0;
        transform: scale(0.95);
      }
      to {
        opacity: 1;
        transform: scale(1);
      }
    }

    /* Fixed registration step styles */
    /* Clean step styles */
    .registration-steps {
      display: flex;
      justify-content: space-between;
      padding: 2rem 1rem;
      position: relative;
    }

    .step {
      flex: 1;
      text-align: center;
      position: relative;
    }

    .step-number {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: var(--bg-dark);
      border: 2px solid rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      margin-bottom: 0.5rem;
      transition: all 0.3s ease;
    }

    .step-text {
      font-size: 0.875rem;
      color: var(--text-secondary);
      transition: color 0.3s ease;
    }

    .step.active .step-number {
      background: var(--primary);
      border-color: var(--primary-light);
      color: white;
      box-shadow: 0 0 15px var(--primary-light);
    }

    .step.active .step-text {
      color: var(--text-primary);
      font-weight: 500;
    }
  </style>
</head>
<body>
  <header class="header">
    <div class="container">
      <nav class="navbar">
        <div class="logo">
          <img src="app_icon.png" alt="GarudaAlgo Logo">
          <h1>GarudaAlgo</h1>
        </div>
        <div class="nav-right">
          <div class="language-selector">
            <button id="lang-en" class="active">EN</button>
            <button id="lang-id">ID</button>
          </div>
          <a href="index.html" class="btn-secondary" data-lang-key="back-to-home">Back to Home</a>
        </div>
      </nav>
    </div>
  </header>

  <section class="registration-section">
    <div class="container">
      <div class="registration-container">
        <div class="registration-header">
          <h2 data-lang-key="register-account">Register Your Account</h2>
          <p data-lang-key="register-desc">Register your broker account to use GarudaAlgo V2</p>
          <div class="registration-steps">
            <div class="step active">
              <span class="step-number">1</span>
              <span class="step-text">Create Account</span>
            </div>
            <div class="step">
              <span class="step-number">2</span>
              <span class="step-text">Verify Email</span>
            </div>
            <div class="step">
              <span class="step-number">3</span>
              <span class="step-text">Complete Profile</span>
            </div>
          </div>
        </div>

        <div class="success-message" id="success-message" data-lang-key="registration-success">
          <div class="success-icon">
            <i class="fas fa-check-circle"></i>
          </div>
          <div class="success-content">
            <h3 data-lang-key="registration-success-title">Registration Successful! 🎉</h3>
            <div class="success-steps">
              <div class="success-step">
                <i class="fas fa-envelope"></i>
                <p>Verification email sent</p>
              </div>
              <div class="success-step">
                <i class="fas fa-arrow-right"></i>
                <p>Check your inbox</p>
              </div>
              <div class="success-step">
                <i class="fas fa-check"></i>
                <p>Verify your email</p>
              </div>
            </div>
            <p class="success-message-desc" data-lang-key="registration-success-desc">
              Please check your email to verify your account. You'll be redirected to the login page shortly.
            </p>
          </div>
        </div>

        <form class="registration-form" id="registration-form">
          <div class="form-group">
            <label for="email" data-lang-key="email">Email Address</label>
            <div class="input-wrapper">
              <input type="email" id="email" name="email" required autocomplete="email">
              <i class="input-icon fas fa-check"></i>
            </div>
            <div class="error-message" id="email-error" data-lang-key="email-error">
              Please enter a valid email address
            </div>
          </div>

          <div class="form-group">
            <label for="password" data-lang-key="password">Password</label>
            <div class="input-wrapper">
              <input type="password" id="password" name="password" required autocomplete="new-password">
              <i class="input-icon fas fa-eye" id="toggle-password"></i>
            </div>
            <div class="password-strength">
              <div class="password-strength-bar"></div>
            </div>
            <div class="error-message" id="password-error" data-lang-key="password-error">
              Password must be at least 8 characters
            </div>
          </div>

          <div class="form-group">
            <label for="confirm-password" data-lang-key="confirm-password">Confirm Password</label>
            <div class="input-wrapper">
              <input type="password" id="confirm-password" name="confirm-password" required autocomplete="new-password">
              <i class="input-icon fas fa-check"></i>
            </div>
            <div class="error-message" id="confirm-password-error" data-lang-key="password-match-error">
              Passwords do not match
            </div>
          </div>

          <!-- Removed broker & affiliate inputs for simplified registration -->

          <button type="submit" class="btn-primary" data-lang-key="register">Register & Verify Email</button>
        </form>

        <p class="form-note" data-lang-key="already-registered">Already registered? <a href="login.html" data-lang-key="login">Login here</a></p>

        <div class="affiliate-info">
          <h3 data-lang-key="not-affiliated">Not affiliated with us yet?</h3>
          <p data-lang-key="join-affiliate">Join our affiliate program to use GarudaAlgo V2:</p>

          <div class="affiliate-links">
            <a href="https://one.exnesstrack.org/intl/en/a/63cflrfv9z" target="_blank">
              <img src="https://d3dpet1g0ty5ed.cloudfront.net/EN_Spreads_Keep_20more_20of_20what_20you_20make_3_33_Google_1200x628.jpg" alt="Exness Affiliate" width="280">
            </a>

            <a href="https://banner-api.hfm.com/link/5e774297?regulator=HFSV&refid=30445790" target="_blank">
              <img src="https://banner-api.hfm.com/banner/5e774297?regulator=HFSV&refid=30445790" alt="HFM Affiliate" width="280">
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <footer class="footer">
    <div class="container">
      <div class="footer-grid">
        <div class="footer-col">
          <div class="footer-logo">
            <img src="app_icon.png" alt="GarudaAlgo Logo">
            <h2>GarudaAlgo</h2>
          </div>
          <p data-lang-key="footer-tagline">Navigate the markets with algorithmic intelligence & strategic precision.</p>
        </div>
        <div class="footer-col">
          <h3 data-lang-key="quick-links">Quick Links</h3>
          <ul>
            <li><a href="index.html#features" data-lang-key="features">Features</a></li>
            <li><a href="index.html#why-algo" data-lang-key="why-algo-trading-short">Why Algo & Autonomous</a></li>
            <li><a href="index.html#faq" data-lang-key="faq-link">FAQ</a></li>
            <li><a href="index.html#download" data-lang-key="download">Download</a></li>
            <li><a href="#" data-lang-key="support">Support</a></li>
          </ul>
        </div>
        <div class="footer-col">
          <h3 data-lang-key="legal">Legal</h3>
          <ul>
            <li><a href="#" data-lang-key="terms">Terms & Conditions</a></li>
            <li><a href="#" data-lang-key="privacy">Privacy Policy</a></li>
            <li><a href="#" data-lang-key="refund">Refund Policy</a></li>
          </ul>
        </div>
        <div class="footer-col">
          <h3 data-lang-key="contact">Contact</h3>
          <ul>
            <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
            <li class="social-icons">
              <a href="#"><i class="fab fa-twitter"></i></a>
              <a href="#"><i class="fab fa-facebook"></i></a>
              <a href="#"><i class="fab fa-instagram"></i></a>
              <a href="#"><i class="fab fa-youtube"></i></a>
            </li>
          </ul>
        </div>
      </div>
      <div class="copyright">
        <p>&copy; 2025 GarudaAlgo. <span data-lang-key="all-rights">All rights reserved.</span></p>
      </div>
    </div>
  </footer>

  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
  <script src="firebase-config.js"></script>
  <script src="locales.json"></script>
  <script src="script.js"></script>
  <script src="register.js"></script>
</body>
</html>
