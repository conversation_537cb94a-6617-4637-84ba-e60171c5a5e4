import React from 'react';
import '../styles/PageNavigation.css';

const PageNavigation = ({ currentPage, canGoBack, onBack, onNextStep }) => {
  // Define page titles and next step availability
  const pageInfo = {
    'analysis': {
      title: 'Market Analysis',
      description: 'Analyze market conditions and identify potential trading opportunities',
      hasNextStep: true,
      nextStepLabel: 'Get Recommendations'
    },
    'recommendation': {
      title: 'Trade Recommendations',
      description: 'View AI-generated trade recommendations based on market analysis',
      hasNextStep: true,
      nextStepLabel: 'Execute Trade'
    },
    'execution': {
      title: 'Trade Execution',
      description: 'Execute trades based on your analysis and recommendations',
      hasNextStep: true,
      nextStepLabel: 'View History'
    },
    'autonomous': {
      title: 'Autonomous Trading',
      description: 'Configure and monitor AI-powered autonomous trading strategies',
      hasNextStep: false
    },
    'history': {
      title: 'Trading History',
      description: 'Review your past trades and performance metrics',
      hasNextStep: false
    },
    'settings': {
      title: 'Settings',
      description: 'Configure application and MT5 connection settings',
      hasNextStep: false
    },
    'profile': {
      title: 'User Profile',
      description: 'View your account and license information',
      hasNextStep: false
    },
    'help': {
      title: 'Help & Support',
      description: 'Find answers to common questions and learn about the application',
      hasNextStep: false
    }
  };

  const currentPageInfo = pageInfo[currentPage] || {
    title: 'Unknown Page',
    description: '',
    hasNextStep: false
  };

  return (
    <div className="page-navigation">
      <div className="page-navigation-header">
        <div className="page-title-section">
          <h2 className="page-title">{currentPageInfo.title}</h2>
          <p className="page-description">{currentPageInfo.description}</p>
        </div>
        <div className="page-actions">
          {canGoBack && (
            <button
              className="button secondary back-button"
              onClick={onBack}
            >
              <span className="back-icon">←</span> Back
            </button>
          )}
          {currentPageInfo.hasNextStep && (
            <button
              className="button primary next-button"
              onClick={onNextStep}
            >
              {currentPageInfo.nextStepLabel} <span className="next-icon">→</span>
            </button>
          )}
        </div>
      </div>
      {!['settings', 'autonomous', 'profile', 'help'].includes(currentPage) && (
        <div className="page-navigation-progress">
        <div className="progress-steps">
          <div className={`progress-step ${currentPage === 'analysis' ? 'active' : ''} ${['recommendation', 'execution', 'history'].includes(currentPage) ? 'completed' : ''}`}>
            <div className="step-indicator">1</div>
            <div className="step-label">Analysis</div>
          </div>
          <div className="progress-connector"></div>
          <div className={`progress-step ${currentPage === 'recommendation' ? 'active' : ''} ${['execution', 'history'].includes(currentPage) ? 'completed' : ''}`}>
            <div className="step-indicator">2</div>
            <div className="step-label">Recommendations</div>
          </div>
          <div className="progress-connector"></div>
          <div className={`progress-step ${currentPage === 'execution' ? 'active' : ''} ${['history'].includes(currentPage) ? 'completed' : ''}`}>
            <div className="step-indicator">3</div>
            <div className="step-label">Execution</div>
          </div>
          <div className="progress-connector"></div>
          <div className={`progress-step ${currentPage === 'history' ? 'active' : ''}`}>
            <div className="step-indicator">4</div>
            <div className="step-label">History</div>
          </div>
        </div>
      </div>
      )}
    </div>
  );
};

export default PageNavigation;
