import unittest
from unittest.mock import patch, MagicMock, PropertyMock, call, mock_open
import time
import json
import os
from datetime import datetime, timedelta
from backend.mt5_integration import (
    MT5Integration,
    ConnectionState,
    MT5ConnectionError,
    MT5AuthenticationError,
    MT5TimeoutError,
    MT5SettingsError,
    MT5SymbolError,
    SymbolInfo
)

# Define constants for MagicMock attributes
ACC_LOGIN = 12345
ACC_SERVER = "TestServ"
ACC_NAME = "AccName"
ACC_COMPANY = "Comp"
ACC_CURRENCY = "USD"
ACC_BALANCE = 10000.0
ACC_EQUITY = 11000.0
ACC_MARGIN = 1000.0
ACC_MARGIN_FREE = 9000.0
ACC_MARGIN_LEVEL = 1100.0

# Constants for settings file test data
SETTINGS_LOGIN = 98765
SETTINGS_SERVER = "SettingsServer"
SETTINGS_PASSWORD = "SettingsPassword"
SETTINGS_PATH = "C:\\Settings\\Path"
SETTINGS_TIMEOUT = 10000
DEFAULT_SETTINGS_FILE = "backend/mt5_settings.json"

def create_mock_terminal_info(connected=True):
    mock_info = MagicMock(connected=connected)
    mock_info._asdict.return_value = {"connected": connected, "name": "MockTerminal"}
    return mock_info

def create_mock_account_info(login=ACC_LOGIN):
    mock_info = MagicMock(
        login=login, server=ACC_SERVER, name=ACC_NAME, company=ACC_COMPANY, currency=ACC_CURRENCY,
        balance=ACC_BALANCE, equity=ACC_EQUITY, margin=ACC_MARGIN, margin_free=ACC_MARGIN_FREE, margin_level=ACC_MARGIN_LEVEL
    )
    mock_info._asdict.return_value = {
        "login": login, "server": ACC_SERVER, "name": ACC_NAME, "company": ACC_COMPANY, "currency": ACC_CURRENCY,
        "balance": ACC_BALANCE, "equity": ACC_EQUITY, "margin": ACC_MARGIN, "margin_free": ACC_MARGIN_FREE, "margin_level": ACC_MARGIN_LEVEL
    }
    return mock_info

class TestMT5Integration(unittest.TestCase):
    def setUp(self):
        self.mt5_client = MT5Integration()
        self.original_cwd = os.getcwd()

    def tearDown(self):
        try:
            with patch('backend.mt5_integration.mt5.terminal_info', return_value=None):
                self.mt5_client.disconnect()
        except:
            pass
        os.chdir(self.original_cwd)

    @patch('backend.mt5_integration.mt5')
    def test_initialization_failure_lib_init(self, mock_mt5):
        mock_mt5.terminal_info.return_value = None
        mock_mt5.initialize.return_value = False
        mock_mt5.last_error.return_value = (1, "Initialization failed")
        with self.assertRaisesRegex(MT5ConnectionError, "Failed to initialize MT5"):
            self.mt5_client.initialize(login=1, password='p', server='s')
        self.assertEqual(self.mt5_client.state, ConnectionState.ERROR)
        self.assertEqual(self.mt5_client._last_error, "Failed to initialize MT5: (1, 'Initialization failed')")
        mock_mt5.shutdown.assert_not_called()

    @patch('backend.mt5_integration.mt5')
    def test_authentication_failure(self, mock_mt5):
        mock_mt5.terminal_info.side_effect = [None, create_mock_terminal_info(connected=True)]
        mock_mt5.initialize.return_value = True
        mock_mt5.login.return_value = False
        mock_mt5.last_error.return_value = (2, "Login failed")
        mock_mt5.shutdown.return_value = True
        with self.assertRaisesRegex(MT5AuthenticationError, "Failed to login"):
            self.mt5_client.initialize(login=ACC_LOGIN, password="test", server=ACC_SERVER)
        self.assertEqual(self.mt5_client.state, ConnectionState.ERROR)
        self.assertEqual(self.mt5_client._last_error, "Failed to login: (2, 'Login failed')")
        mock_mt5.shutdown.assert_called_once()

    @patch('backend.mt5_integration.mt5')
    def test_initialization_validation_failure_terminal(self, mock_mt5):
        mock_mt5.terminal_info.side_effect = [None, None]
        mock_mt5.initialize.return_value = True
        mock_mt5.login.return_value = True
        mock_mt5.account_info.return_value = create_mock_account_info()
        mock_mt5.last_error.return_value = (3, "Terminal info failed")
        mock_mt5.shutdown.return_value = True
        with self.assertRaisesRegex(MT5ConnectionError, "Connection validation failed"):
            self.mt5_client.initialize(login=ACC_LOGIN, password="test", server=ACC_SERVER)
        self.assertEqual(self.mt5_client.state, ConnectionState.ERROR)
        self.assertEqual(self.mt5_client._last_error, "Connection validation failed. Last MT5 error: (3, 'Terminal info failed')")
        mock_mt5.shutdown.assert_called_once()

    @patch('backend.mt5_integration.mt5')
    def test_initialization_validation_failure_account(self, mock_mt5):
        mock_mt5.terminal_info.side_effect = [None, create_mock_terminal_info(connected=True)]
        mock_mt5.initialize.return_value = True
        mock_mt5.login.return_value = True
        mock_mt5.account_info.return_value = None
        mock_mt5.last_error.return_value = (4, "Account info failed")
        mock_mt5.shutdown.return_value = True
        with self.assertRaisesRegex(MT5ConnectionError, "Connection validation failed"):
            self.mt5_client.initialize(login=ACC_LOGIN, password="test", server=ACC_SERVER)
        self.assertEqual(self.mt5_client.state, ConnectionState.ERROR)
        self.assertEqual(self.mt5_client._last_error, "Connection validation failed. Last MT5 error: (4, 'Account info failed')")
        mock_mt5.shutdown.assert_called_once()

    @patch('backend.mt5_integration.mt5')
    def test_disconnect_success_when_connected(self, mock_mt5):
        mock_mt5.terminal_info.return_value = create_mock_terminal_info(connected=True)
        mock_mt5.shutdown.return_value = True
        self.mt5_client._state = ConnectionState.CONNECTED
        result = self.mt5_client.disconnect()
        self.assertTrue(result["success"])
        self.assertEqual(self.mt5_client.state, ConnectionState.DISCONNECTED)
        mock_mt5.shutdown.assert_called_once()

    @patch('backend.mt5_integration.mt5')
    def test_disconnect_success_when_already_disconnected(self, mock_mt5):
        mock_mt5.terminal_info.return_value = None
        self.mt5_client._state = ConnectionState.DISCONNECTED
        result = self.mt5_client.disconnect()
        self.assertTrue(result["success"])
        self.assertEqual(result["message"], "Already disconnected")
        mock_mt5.shutdown.assert_not_called()

    @patch('backend.mt5_integration.mt5')
    def test_disconnect_failure(self, mock_mt5):
        mock_mt5.terminal_info.return_value = create_mock_terminal_info(connected=True)
        mock_mt5.shutdown.side_effect = Exception("Shutdown error")
        self.mt5_client._state = ConnectionState.CONNECTED
        result = self.mt5_client.disconnect()
        self.assertFalse(result["success"])
        self.assertEqual(self.mt5_client.state, ConnectionState.ERROR)

    @patch('backend.mt5_integration.mt5')
    def test_is_connected_true(self, mock_mt5):
        mock_mt5.terminal_info.return_value = create_mock_terminal_info(connected=True)
        self.mt5_client._state = ConnectionState.CONNECTED
        self.assertTrue(self.mt5_client.is_connected())
        self.assertEqual(self.mt5_client.state, ConnectionState.CONNECTED)

    @patch('backend.mt5_integration.mt5')
    def test_is_connected_false_wrong_state(self, mock_mt5):
        self.mt5_client._state = ConnectionState.DISCONNECTED
        self.assertFalse(self.mt5_client.is_connected())
        self.mt5_client._state = ConnectionState.CONNECTING
        self.assertFalse(self.mt5_client.is_connected())
        self.mt5_client._state = ConnectionState.ERROR
        self.assertFalse(self.mt5_client.is_connected())

    @patch('backend.mt5_integration.mt5')
    def test_is_connected_false_terminal_unresponsive(self, mock_mt5):
        mock_mt5.terminal_info.return_value = None
        self.mt5_client._state = ConnectionState.CONNECTED
        self.assertFalse(self.mt5_client.is_connected())
        self.assertEqual(self.mt5_client.state, ConnectionState.ERROR)

    @patch('backend.mt5_integration.mt5')
    def test_is_connected_false_terminal_exception(self, mock_mt5):
        mock_mt5.terminal_info.side_effect = Exception("Terminal check failed")
        self.mt5_client._state = ConnectionState.CONNECTED
        self.assertFalse(self.mt5_client.is_connected())
        self.assertEqual(self.mt5_client.state, ConnectionState.ERROR)

    @patch('backend.mt5_integration.mt5')
    @patch('backend.mt5_integration.MT5Integration.connection_latency', new_callable=PropertyMock)
    def test_get_connection_info_connected(self, mock_latency, mock_mt5):
        mock_terminal_info_connected = create_mock_terminal_info(connected=True)
        mock_account_info_obj = create_mock_account_info()
        mock_mt5.terminal_info.side_effect = [mock_terminal_info_connected, mock_terminal_info_connected]
        mock_mt5.account_info.return_value = mock_account_info_obj
        mock_latency.return_value = 55.5
        self.mt5_client._state = ConnectionState.CONNECTED
        self.mt5_client._connection_time = 1.234
        info = self.mt5_client.get_connection_info()
        self.assertEqual(info["state"], ConnectionState.CONNECTED.value)
        self.assertTrue(info["connected"])
        self.assertEqual(info["latency_ms"], 55.5)

    @patch('backend.mt5_integration.mt5')
    @patch('backend.mt5_integration.MT5Integration.connection_latency', new_callable=PropertyMock)
    def test_get_connection_info_disconnected(self, mock_latency, mock_mt5):
        mock_latency.return_value = None
        self.mt5_client._state = ConnectionState.DISCONNECTED
        self.mt5_client._last_error = "Some previous error"
        mock_mt5.terminal_info.return_value = None
        info = self.mt5_client.get_connection_info()
        self.assertEqual(info["state"], ConnectionState.DISCONNECTED.value)
        self.assertFalse(info["connected"])
        self.assertIsNone(info["latency_ms"])
        mock_mt5.terminal_info.assert_not_called()

    @patch('backend.mt5_integration.mt5')
    def test_get_symbols_success(self, mock_mt5):
        """Test successful symbol fetching"""
        # Setup mock symbols
        mock_symbol1 = MagicMock(name='EURUSD', digits=5, trade_mode='forex',
                                currency_base='EUR', currency_profit='USD',
                                currency_margin='USD', trade_allowed=True,
                                visible=True, custom=False)
        mock_symbol2 = MagicMock(name='GBPUSD', digits=5, trade_mode='forex',
                                currency_base='GBP', currency_profit='USD',
                                currency_margin='USD', trade_allowed=True,
                                visible=True, custom=False)
        mock_mt5.symbols_get.return_value = [mock_symbol1, mock_symbol2]
        mock_mt5.terminal_info.return_value = create_mock_terminal_info(connected=True)

        # Test basic symbol fetch
        self.mt5_client._state = ConnectionState.CONNECTED
        symbols = self.mt5_client.get_symbols()
        self.assertEqual(symbols, ['EURUSD', 'GBPUSD'])

        # Test fetch with metadata
        symbols_with_meta = self.mt5_client.get_symbols(include_metadata=True)
        self.assertEqual(len(symbols_with_meta), 2)
        self.assertIsInstance(symbols_with_meta[0], SymbolInfo)
        self.assertEqual(symbols_with_meta[0].name, 'EURUSD')

    @patch('backend.mt5_integration.mt5')
    def test_get_symbols_caching(self, mock_mt5):
        """Test symbol caching behavior"""
        # Setup initial fetch
        mock_symbol = MagicMock(name='EURUSD', digits=5, trade_mode='forex',
                               currency_base='EUR', currency_profit='USD',
                               currency_margin='USD', trade_allowed=True,
                               visible=True, custom=False)
        mock_mt5.symbols_get.return_value = [mock_symbol]
        mock_mt5.terminal_info.return_value = create_mock_terminal_info(connected=True)
        
        self.mt5_client._state = ConnectionState.CONNECTED
        
        # First fetch should hit MT5
        symbols1 = self.mt5_client.get_symbols()
        self.assertEqual(mock_mt5.symbols_get.call_count, 1)
        
        # Second fetch should use cache
        symbols2 = self.mt5_client.get_symbols()
        self.assertEqual(mock_mt5.symbols_get.call_count, 1)  # Call count unchanged
        
        # Force refresh should hit MT5 again
        symbols3 = self.mt5_client.get_symbols(force_refresh=True)
        self.assertEqual(mock_mt5.symbols_get.call_count, 2)

    @patch('backend.mt5_integration.mt5')
    def test_get_symbols_not_connected(self, mock_mt5):
        """Test symbol fetch when not connected"""
        self.mt5_client._state = ConnectionState.DISCONNECTED
        with self.assertRaises(MT5ConnectionError):
            self.mt5_client.get_symbols()
        mock_mt5.symbols_get.assert_not_called()

    @patch('backend.mt5_integration.mt5')
    def test_get_symbols_mt5_error(self, mock_mt5):
        """Test symbol fetch when MT5 returns error"""
        mock_mt5.terminal_info.return_value = create_mock_terminal_info(connected=True)
        mock_mt5.symbols_get.return_value = None
        mock_mt5.last_error.return_value = (1, "Failed to get symbols")
        
        self.mt5_client._state = ConnectionState.CONNECTED
        with self.assertRaises(MT5SymbolError):
            self.mt5_client.get_symbols()

    @patch('backend.mt5_integration.mt5')
    def test_get_symbols_empty_list(self, mock_mt5):
        """Test symbol fetch when MT5 returns empty list"""
        mock_mt5.terminal_info.return_value = create_mock_terminal_info(connected=True)
        mock_mt5.symbols_get.return_value = []
        mock_mt5.last_error.return_value = (0, "No symbols")
        
        self.mt5_client._state = ConnectionState.CONNECTED
        with self.assertRaises(MT5SymbolError):
            self.mt5_client.get_symbols()

    @patch('backend.mt5_integration.mt5')
    def test_get_symbols_performance(self, mock_mt5):
        """Test symbol fetch performance with large symbol list"""
        # Create 1000 mock symbols
        mock_symbols = []
        for i in range(1000):
            mock_symbol = MagicMock(
                name=f'SYMBOL{i}', digits=5, trade_mode='forex',
                currency_base='USD', currency_profit='EUR',
                currency_margin='USD', trade_allowed=True,
                visible=True, custom=False
            )
            mock_symbols.append(mock_symbol)
            
        mock_mt5.terminal_info.return_value = create_mock_terminal_info(connected=True)
        mock_mt5.symbols_get.return_value = mock_symbols
        
        self.mt5_client._state = ConnectionState.CONNECTED
        
        # Measure fetch time
        start_time = time.time()
        symbols = self.mt5_client.get_symbols()
        fetch_time = time.time() - start_time
        
        self.assertEqual(len(symbols), 1000)
        self.assertLess(fetch_time, 0.5)  # Should complete within 500ms

    def test_clear_symbol_cache(self):
        """Test clearing symbol cache"""
        self.mt5_client._symbols_cache = {'EURUSD': MagicMock()}
        self.mt5_client.clear_symbol_cache()
        self.assertIsNone(self.mt5_client._symbols_cache)

if __name__ == '__main__':
    unittest.main()