from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class IchimokuIndicator(BaseIndicator):
    """Ichimoku Cloud (Ichimoku Kinko Hyo) indicator."""
    
    def __init__(self, tenkan_period: int = 9, kijun_period: int = 26,
                 senkou_b_period: int = 52, displacement: int = 26):
        """
        Initialize Ichimoku Cloud indicator.
        
        Args:
            tenkan_period: Period for Tenkan-sen (Conversion Line)
            kijun_period: Period for Kijun-sen (Base Line)
            senkou_b_period: Period for Senkou Span B
            displacement: Displacement period for Senkou Span A/B (Cloud)
        """
        super().__init__({
            'tenkan_period': tenkan_period,
            'kijun_period': kijun_period,
            'senkou_b_period': senkou_b_period,
            'displacement': displacement
        })
    
    def _get_period_middle(self, high: np.ndarray, low: np.ndarray,
                          period: int) -> np.ndarray:
        """Calculate period middle value ((period high + period low) / 2)."""
        period_high = pd.Series(high).rolling(window=period).max()
        period_low = pd.Series(low).rolling(window=period).min()
        return (period_high + period_low) / 2
    
    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Ichimoku Cloud values."""
        df = data.to_dataframe()
        if df.empty:
            return {}
        
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        
        # Get parameters
        tenkan_period = self.params['tenkan_period']
        kijun_period = self.params['kijun_period']
        senkou_b_period = self.params['senkou_b_period']
        displacement = self.params['displacement']
        
        # Calculate Tenkan-sen (Conversion Line)
        tenkan = self._get_period_middle(high, low, tenkan_period)
        
        # Calculate Kijun-sen (Base Line)
        kijun = self._get_period_middle(high, low, kijun_period)
        
        # Calculate Senkou Span A (Leading Span A)
        senkou_a = ((tenkan + kijun) / 2).shift(displacement)
        
        # Calculate Senkou Span B (Leading Span B)
        senkou_b = self._get_period_middle(high, low, senkou_b_period).shift(displacement)
        
        # Calculate Chikou Span (Lagging Span)
        chikou = pd.Series(close).shift(-displacement)
        
        # Calculate cloud colors (traditional: green when A > B, red when B > A)
        cloud_color = np.where(senkou_a > senkou_b, 1,
                             np.where(senkou_b > senkou_a, -1, 0))
        
        # Calculate trend based on price position relative to cloud
        trend = np.zeros_like(close)
        trend[close > senkou_a] = 1
        trend[close < senkou_b] = -1
        
        # Calculate crossovers
        tenkan_cross = np.where(
            (tenkan > kijun) & (pd.Series(tenkan).shift(1) <= pd.Series(kijun).shift(1)), 1,
            np.where((tenkan < kijun) & (pd.Series(tenkan).shift(1) >= pd.Series(kijun).shift(1)), -1, 0)
        )
        
        # Calculate trend strength
        cloud_thickness = np.abs(senkou_a - senkou_b)
        price_position = close - ((senkou_a + senkou_b) / 2)
        trend_strength = (price_position / ((senkou_a + senkou_b) / 2)) * (cloud_thickness / close)
        
        # Calculate support/resistance levels
        support = np.where(trend >= 0, kijun, senkou_b)
        resistance = np.where(trend <= 0, kijun, senkou_a)
        
        # Calculate time-price distance
        future_cloud_top = np.maximum(
            pd.Series(senkou_a).shift(-displacement),
            pd.Series(senkou_b).shift(-displacement)
        )
        future_cloud_bottom = np.minimum(
            pd.Series(senkou_a).shift(-displacement),
            pd.Series(senkou_b).shift(-displacement)
        )
        
        self._values = {
            'tenkan': tenkan.values,
            'kijun': kijun.values,
            'senkou_a': senkou_a.values,
            'senkou_b': senkou_b.values,
            'chikou': chikou.values,
            'cloud_color': cloud_color,
            'trend': trend,
            'trend_strength': trend_strength.values,
            'tenkan_cross': tenkan_cross,
            'cloud_thickness': cloud_thickness.values,
            'support': support,
            'resistance': resistance,
            'future_cloud_top': future_cloud_top.values,
            'future_cloud_bottom': future_cloud_bottom.values
        }
        return self._values
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['tenkan_period'] < 1:
            raise ValueError("Tenkan period must be greater than 0")
        if self.params['kijun_period'] < 1:
            raise ValueError("Kijun period must be greater than 0")
        if self.params['senkou_b_period'] < 1:
            raise ValueError("Senkou B period must be greater than 0")
        if self.params['displacement'] < 1:
            raise ValueError("Displacement must be greater than 0")
        if self.params['tenkan_period'] >= self.params['kijun_period']:
            raise ValueError("Tenkan period must be less than Kijun period")
        if self.params['kijun_period'] >= self.params['senkou_b_period']:
            raise ValueError("Kijun period must be less than Senkou B period")
        return True 