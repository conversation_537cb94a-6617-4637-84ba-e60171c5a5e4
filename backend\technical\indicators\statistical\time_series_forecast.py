from typing import Dict, Any
import numpy as np
import pandas as pd
from scipy import stats

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class TimeSeriesForecastIndicator(BaseIndicator):
    """Time Series Forecast (TSF) indicator."""

    def __init__(self, period: int = 14, forecast_period: int = 5, source: str = 'close'):
        """
        Initialize Time Series Forecast indicator.

        Args:
            period: The lookback period for linear regression calculation.
            forecast_period: Number of periods to forecast forward.
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
        """
        super().__init__({
            'period': period,
            'forecast_period': forecast_period,
            'source': source
        })

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Time Series Forecast values."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period']:
             return {'tsf': np.array([])}

        period = self.params['period']
        forecast_period = self.params['forecast_period']
        source_col = self.params['source'].lower()

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Initialize result array
        n = len(source_data)
        tsf = np.full(n, np.nan)
        x = np.arange(period)

        for i in range(period - 1, n):
            y_window = source_data.iloc[i - period + 1 : i + 1].values
            if len(y_window) != period or np.isnan(y_window).any():
                continue
            try:
                slope, intercept, _, _, _ = stats.linregress(x, y_window)
                # Calculate TSF (projected value forward by forecast_period)
                tsf[i] = intercept + slope * (period - 1 + forecast_period)
            except ValueError:
                continue

        self._values = {
            'tsf': tsf
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 2: # Linregress needs at least 2 points
            raise ValueError("Period must be at least 2 for Time Series Forecast")
        if self.params['forecast_period'] < 0:
             raise ValueError("Forecast Period cannot be negative")
        return True