from typing import Dict, Any, List
import numpy as np
import pandas as pd
from scipy.signal import find_peaks
from scipy.optimize import curve_fit

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class CupAndHandlePatternIndicator(BaseIndicator):
    """Cup and Handle chart pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - min_cup_depth: Parameter description (default: 0.1)
                - max_handle_depth: Parameter description (default: 0.05)
                - min_cup_duration: Parameter description (default: 30)
                - max_handle_duration: Parameter description (default: 15)
                - symmetry_tolerance: Parameter description (default: 0.2)
        """
        default_params = {
            "min_cup_depth": 0.1,
            "max_handle_depth": 0.05,
            "min_cup_duration": 30,
            "max_handle_duration": 15,
            "symmetry_tolerance": 0.2,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)


    def _fit_cup_curve(self, x: np.ndarray, y: np.ndarray) -> tuple:
        """Fit a quadratic curve to potential cup formation."""
        def quadratic(x, a, b, c):
            return a * x**2 + b * x + c
        
        try:
            popt, _ = curve_fit(quadratic, x, y)
            return popt
        except:
            return None

    def _is_cup_and_handle(self, prices: np.ndarray, start_idx: int,
                          is_inverted: bool = False) -> tuple:
        """Identify Cup and Handle patterns."""
        min_cup_duration = self.params['min_cup_duration']
        max_handle_duration = self.params['max_handle_duration']
        min_cup_depth = self.params['min_cup_depth']
        max_handle_depth = self.params['max_handle_depth']
        symmetry_tolerance = self.params['symmetry_tolerance']
        
        if len(prices) - start_idx < min_cup_duration:
            return False, 0, 0
        
        # Extract potential cup region
        cup_prices = prices[start_idx:start_idx + min_cup_duration]
        x = np.arange(len(cup_prices))
        
        # Fit quadratic curve
        params = self._fit_cup_curve(x, cup_prices)
        if params is None:
            return False, 0, 0
            
        a, b, c = params
        
        # Check cup characteristics
        if (not is_inverted and a < 0) or (is_inverted and a > 0):
            return False, 0, 0
            
        # Calculate cup depth
        max_price = max(cup_prices)
        min_price = min(cup_prices)
        cup_depth = abs(max_price - min_price) / max_price
        
        if cup_depth < min_cup_depth:
            return False, 0, 0
            
        # Check cup symmetry
        mid_point = len(cup_prices) // 2
        left_side = cup_prices[:mid_point]
        right_side = cup_prices[mid_point:]
        
        if len(left_side) != len(right_side):
            right_side = right_side[:len(left_side)]
            
        symmetry_diff = np.mean(np.abs(left_side - right_side[::-1])) / max_price
        if symmetry_diff > symmetry_tolerance:
            return False, 0, 0
            
        # Look for handle formation
        handle_start = start_idx + min_cup_duration
        if len(prices) - handle_start < 5:  # Minimum handle length
            return False, 0, 0
            
        handle_prices = prices[handle_start:handle_start + max_handle_duration]
        handle_depth = abs(max(handle_prices) - min(handle_prices)) / max_price
        
        if handle_depth > max_handle_depth:
            return False, 0, 0
            
        return True, start_idx, handle_start + len(handle_prices)

    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        close = df['close'].values
        is_pattern = np.zeros_like(close)
        pattern_type = np.zeros_like(close)
        
        # Scan for regular Cup and Handle
        for i in range(len(close) - self.params['min_cup_duration']):
            is_valid, start, end = self._is_cup_and_handle(close, i)
            if is_valid:
                is_pattern[start:end] = 1
                pattern_type[start:end] = 1  # Bullish pattern
                
        # Scan for Inverted Cup and Handle
        for i in range(len(close) - self.params['min_cup_duration']):
            is_valid, start, end = self._is_cup_and_handle(close, i, is_inverted=True)
            if is_valid:
                is_pattern[start:end] = 1
                pattern_type[start:end] = -1  # Bearish pattern
        
        # Calculate pattern strength
        strength = np.zeros_like(close)
        for i in range(len(close)):
            if is_pattern[i]:
                # Calculate the height of the pattern relative to price
                pattern_height = abs(max(close[i:i+self.params['min_cup_duration']]) - 
                                  min(close[i:i+self.params['min_cup_duration']]))
                strength[i] = pattern_height / close[i]
        
        # Calculate trend context
        trend = np.full_like(close, -1, dtype=int)  # Default to -1
        for i in range(20, len(close)):  # Start from 20 to have enough data for SMA
            sma = np.mean(close[i-20:i])
            trend[i] = 1 if close[i] > sma else -1
        
        # Calculate pattern reliability
        reliability = np.full_like(close, -1, dtype=float)  # Default to -1 for no pattern
        future_window = 20
        
        for i in range(len(close) - future_window):
            if is_pattern[i]:
                future_returns = (df['close'].iloc[i+1:i+future_window+1].values - 
                                df['close'].iloc[i]) / df['close'].iloc[i]
                
                if pattern_type[i] > 0:  # Bullish pattern
                    max_return = np.max(future_returns)
                    reliability[i] = 1 if max_return > 0 else -1
                else:  # Bearish pattern
                    min_return = np.min(future_returns)
                    reliability[i] = 1 if min_return < 0 else -1
        
        return {
            'is_pattern': is_pattern.astype(int),
            'pattern_type': pattern_type,  # 1 for Cup and Handle, -1 for Inverted
            'strength': strength,
            'trend': trend,
            'reliability': reliability
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if not 0 < self.params['min_cup_depth'] < 1:
            raise ValueError("Minimum cup depth must be between 0 and 1")
        if not 0 < self.params['max_handle_depth'] < 1:
            raise ValueError("Maximum handle depth must be between 0 and 1")
        if self.params['min_cup_duration'] < 10:
            raise ValueError("Minimum cup duration must be at least 10 periods")
        if self.params['max_handle_duration'] < 5:
            raise ValueError("Maximum handle duration must be at least 5 periods")
        if not 0 < self.params['symmetry_tolerance'] < 1:
            raise ValueError("Symmetry tolerance must be between 0 and 1")
        return True 