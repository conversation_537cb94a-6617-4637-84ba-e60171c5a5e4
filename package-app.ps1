# PowerShell script to package the application with backend integration

# Set console colors for better readability
$host.UI.RawUI.ForegroundColor = "Cyan"
Write-Output "🚀 GarudaAlgo Packaging Script"
$host.UI.RawUI.ForegroundColor = "White"

# Kill any running processes that might lock files
Write-Output "📋 Stopping any running instances..."
Get-Process -Name "garuda_backend*" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Get-Process -Name "electron*" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue

# Clean up previous builds
if (Test-Path -Path release) {
    Write-Output "🧹 Cleaning previous builds..."
    Remove-Item -Path release -Recurse -Force -ErrorAction SilentlyContinue
}

# Verify backend executable exists
if (-not (Test-Path -Path "dist\garuda_backend.exe")) {
    $host.UI.RawUI.ForegroundColor = "Red"
    Write-Output "❌ ERROR: Backend executable not found at dist\garuda_backend.exe"
    Write-Output "   Please build it first with: pyinstaller garuda_backend.spec"
    $host.UI.RawUI.ForegroundColor = "White"
    exit 1
}

# Build React frontend
Write-Output "🔨 Building React frontend..."
npm run build

# Run electron-builder
Write-Output "📦 Packaging application with electron-builder..."
npx electron-builder --win --x64 --dir

Write-Output "Build process complete."
