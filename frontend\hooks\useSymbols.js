import { useState, useEffect } from 'react';

/**
 * useSymbols - Custom hook to fetch available trading symbols.
 * @returns {[symbols, loading, error]}
 */
export function useSymbols() {
  const [symbols, setSymbols] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    setLoading(true);
    setError(null);
    setSymbols([]);
    fetch('http://localhost:5001/api/symbols')
      .then(res => {
        if (!res.ok) throw new Error('Failed to fetch symbols');
        return res.json();
      })
      .then(setSymbols)
      .catch(setError)
      .finally(() => setLoading(false));
  }, []);

  return [symbols, loading, error];
}
