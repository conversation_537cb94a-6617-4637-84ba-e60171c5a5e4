<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GarudaAlgo MT5 Trader</title>
    <style>
        :root {
            --primary: #2563eb;
            --secondary: #4b5563;
            --background: #1a202c;
            --card: #2d3748;
            --text: #e2e8f0;
            --success: #10b981;
            --error: #ef4444;
            --warning: #f59e0b;
            --info: #3b82f6;
            --tableBg: #1f2937;
            --tableHeaderBg: #2d3748;
            --tableBorder: #374151;
            --inputBg: #1f2937;
            --inputBorder: #4b5563;
            --tabBg: #2d3748;
            --tabActiveBg: #3b82f6;
            --headerText: #ffffff;
            --modalBg: #2d3748;
            --modalOverlay: rgba(0, 0, 0, 0.7);
            --chartBg: #1f2937;
            --signalCardBg: #2d3748;
            --signalDetailBg: #1f2937;
            --dashboardCardBg: #2d3748;
            --dashboardCardText: #e2e8f0;
            --borderRadius: 8px;
        }

        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--background);
            color: var(--text);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background-color: var(--primary);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        header img {
            height: 40px;
            margin-right: 15px;
        }

        .connection-status {
            display: flex;
            align-items: center;
            margin-left: 20px;
            background-color: rgba(0, 0, 0, 0.2);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 14px;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .connected {
            background-color: var(--success);
            box-shadow: 0 0 8px var(--success);
        }

        .disconnected {
            background-color: var(--error);
            box-shadow: 0 0 8px var(--error);
        }

        .header-right {
            display: flex;
            align-items: center;
        }

        main {
            margin-top: 20px;
        }

        .card {
            background-color: var(--card);
            border-radius: var(--borderRadius);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 20px;
            margin-bottom: 20px;
        }

        button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            background-color: var(--primary);
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        button:hover {
            background-color: #1d4ed8;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        button.secondary {
            background-color: var(--secondary);
        }

        button.secondary:hover {
            background-color: #374151;
        }

        input, select {
            padding: 10px;
            border: 1px solid var(--inputBorder);
            border-radius: 6px;
            margin-bottom: 15px;
            width: 100%;
            background-color: var(--inputBg);
            color: var(--text);
            transition: border-color 0.2s;
        }

        input:focus, select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #a0aec0;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 100;
        }

        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: var(--modalBg);
            border-radius: var(--borderRadius);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            padding: 30px;
            min-width: 500px;
            max-width: 80%;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 25px;
        }

        .dashboard-card {
            background-color: var(--dashboardCardBg);
            border-radius: var(--borderRadius);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 20px;
            display: flex;
            flex-direction: column;
            transition: transform 0.2s;
        }

        .dashboard-card:hover {
            transform: translateY(-3px);
        }

        .dashboard-card h3 {
            margin-top: 0;
            color: #a0aec0;
            font-size: 16px;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .dashboard-card .value {
            font-size: 28px;
            font-weight: bold;
            margin: 10px 0;
        }

        .tabs {
            display: flex;
            background-color: var(--card);
            border-radius: var(--borderRadius) var(--borderRadius) 0 0;
            overflow: hidden;
            margin-bottom: 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .tab {
            padding: 14px 24px;
            cursor: pointer;
            background-color: transparent;
            border: none;
            border-bottom: 3px solid transparent;
            transition: all 0.2s;
            color: var(--text);
            font-weight: 500;
            border-radius: 0;
        }

        .tab:hover {
            background-color: rgba(59, 130, 246, 0.1);
            transform: none;
            box-shadow: none;
        }

        .tab.active {
            background-color: transparent;
            border-bottom: 3px solid var(--primary);
            color: var(--primary);
            font-weight: 600;
        }

        .tab-content {
            background-color: var(--card);
            border-radius: 0 0 var(--borderRadius) var(--borderRadius);
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .chart-container {
            width: 100%;
            height: 400px;
            margin-top: 20px;
            border: 1px solid var(--tableBorder);
            border-radius: var(--borderRadius);
            overflow: hidden;
            background-color: var(--chartBg);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            border-radius: var(--borderRadius);
            overflow: hidden;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
        }

        th {
            background-color: var(--tableHeaderBg);
            font-weight: 600;
            color: #a0aec0;
            border-bottom: 1px solid var(--tableBorder);
        }

        td {
            border-bottom: 1px solid var(--tableBorder);
        }

        tr:hover td {
            background-color: rgba(255,255,255,0.02);
        }

        .grid-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .signal-card {
            border-left: 4px solid var(--primary);
            padding: 20px;
            margin-bottom: 15px;
            background-color: var(--signalCardBg);
            border-radius: 0 var(--borderRadius) var(--borderRadius) 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: transform 0.2s;
        }

        .signal-card:hover {
            transform: translateY(-3px);
        }

        .signal-card.buy {
            border-left-color: var(--success);
        }

        .signal-card.sell {
            border-left-color: var(--error);
        }

        .signal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .signal-symbol {
            font-weight: bold;
            font-size: 20px;
        }

        .signal-type {
            padding: 6px 12px;
            border-radius: 20px;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .signal-type.buy {
            background-color: var(--success);
        }

        .signal-type.sell {
            background-color: var(--error);
        }

        .signal-details {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .signal-detail {
            background-color: var(--signalDetailBg);
            border-radius: var(--borderRadius);
            padding: 12px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .signal-detail-label {
            color: #a0aec0;
            font-size: 12px;
            margin-bottom: 5px;
        }

        .signal-detail-value {
            font-weight: 600;
            font-size: 16px;
        }

        .signal-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 10px;
        }

        h3 {
            margin-top: 0;
            font-size: 18px;
            margin-bottom: 20px;
            font-weight: 600;
            color: var(--text);
        }

        /* Light theme styles will be added by theme.js */
        .light-mode {
            --primary: #1e40af;
            --secondary: #6b7280;
            --background: #f8fafc;
            --card: #ffffff;
            --text: #334155;
            --tableBg: #ffffff;
            --tableHeaderBg: #f1f5f9;
            --tableBorder: #e2e8f0;
            --inputBg: #ffffff;
            --inputBorder: #d1d5db;
            --tabBg: #f1f5f9;
            --tabActiveBg: #ffffff;
            --chartBg: #ffffff;
            --signalCardBg: #ffffff;
            --signalDetailBg: #f8fafc;
            --dashboardCardBg: #ffffff;
            --dashboardCardText: #334155;
        }
    </style>
    <!-- Safe formatting utility -->
    <script src="./utils/safeFormatting.js"></script>
</head>
<body class="dark-mode">
    <header>
        <div class="header-left">
            <img src="./src/app_icon.png" alt="GarudaAlgo Logo">
            <h1>GarudaAlgo MT5 Trader</h1>
            <div class="connection-status">
                <div id="status-dot" class="status-dot disconnected"></div>
                <span id="connection-status">Disconnected</span>
            </div>
        </div>
        <div class="header-right">
            <button id="connect-button">Connect to MT5</button>
            <button id="theme-toggle" class="secondary" style="margin-left: 10px;">Light Mode</button>
        </div>
    </header>

    <div class="container">
        <!-- Main content - always visible -->
        <div class="dashboard">
            <div class="dashboard-card">
                <h3>Account Balance</h3>
                <div class="value" id="account-balance">$0.00</div>
                <div>Demo Account</div>
            </div>
            <div class="dashboard-card">
                <h3>Equity</h3>
                <div class="value" id="account-equity">$0.00</div>
                <div>Current equity</div>
            </div>
            <div class="dashboard-card">
                <h3>User & License</h3>
                <div style="font-size: 14px; margin-bottom: 5px;">
                    User: <span id="account-name-display" style="font-weight: bold;">N/A</span> 
                </div>
                <div style="font-size: 14px; margin-bottom: 5px;">
                    Plan: <span id="plan-type-display" style="font-weight: bold;">N/A</span>
                </div>
                <div style="font-size: 14px; margin-bottom: 5px;">
                    Expires: <span id="plan-end-date-display" style="font-weight: bold;">N/A</span>
                </div>
                <div style="font-size: 14px;">
                    License: <span id="license-status-display" style="font-weight: bold;">N/A</span>
                </div>
            </div>
            <!-- 
                The original dashboard had 4 cards. I've added a new one for license info.
                You might want to adjust the grid-template-columns for the .dashboard 
                or replace one of the existing cards if you prefer to keep it at 4.
                For example, change .dashboard grid-template-columns to repeat(5, 1fr) or repeat(auto-fit, minmax(250px, 1fr))
            -->
            <div class="dashboard-card">
                <h3>Profit/Loss</h3>
                <div class="value" id="account-pl">$0.00</div>
                <div>Daily P/L</div>
            </div>
            <div class="dashboard-card">
                <h3>Active Positions</h3>
                <div class="value" id="active-positions">0</div>
                <div>Open trades</div>
            </div>
        </div>

        <!-- Main tabs -->
        <div class="tabs">
            <button class="tab active" data-tab="market-watch">Market Watch</button>
            <button class="tab" data-tab="analysis">Analysis</button>
            <button class="tab" data-tab="signals">Trading Signals</button>
            <button class="tab" data-tab="positions">Positions</button>
            <button class="tab" data-tab="autonomous">AI Trading</button>
        </div>

        <div class="tab-content">
            <!-- Market Watch Tab -->
            <div id="market-watch-tab" class="tab-panel active">
                <div class="grid-container">
                    <div class="card">
                        <h3>Market Watch</h3>
                        <table>
                            <thead>
                                <tr>
                                    <th>Symbol</th>
                                    <th>Bid</th>
                                    <th>Ask</th>
                                    <th>Spread</th>
                                    <th>Change</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody id="market-watch-table">
                                <tr>
                                    <td>EURUSD</td>
                                    <td>1.0762</td>
                                    <td>1.0764</td>
                                    <td>2</td>
                                    <td>-0.12%</td>
                                    <td><button class="secondary">Chart</button></td>
                                </tr>
                                <tr>
                                    <td>GBPUSD</td>
                                    <td>1.2534</td>
                                    <td>1.2537</td>
                                    <td>3</td>
                                    <td>+0.08%</td>
                                    <td><button class="secondary">Chart</button></td>
                                </tr>
                                <tr>
                                    <td>USDJPY</td>
                                    <td>151.23</td>
                                    <td>151.26</td>
                                    <td>3</td>
                                    <td>+0.15%</td>
                                    <td><button class="secondary">Chart</button></td>
                                </tr>
                                <tr>
                                    <td>BTCUSD</td>
                                    <td>62145.75</td>
                                    <td>62165.25</td>
                                    <td>19.5</td>
                                    <td>+1.23%</td>
                                    <td><button class="secondary">Chart</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="card">
                        <h3>Price Chart</h3>
                        <div class="chart-container">
                            <div id="price-chart" style="height: 100%; display: flex; align-items: center; justify-content: center; color: #718096;">
                                Select a symbol to view chart
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analysis Tab -->
            <div id="analysis-tab" class="tab-panel">
                <div class="card">
                    <h3>Technical Analysis</h3>
                    <div class="grid-container">
                        <div>
                            <div style="margin-bottom: 15px;">
                                <label for="analysis-symbol">Symbol</label>
                                <select id="analysis-symbol">
                                    <option value="EURUSD">EURUSD</option>
                                    <option value="GBPUSD">GBPUSD</option>
                                    <option value="USDJPY">USDJPY</option>
                                    <option value="BTCUSD">BTCUSD</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label for="analysis-timeframe">Timeframe</label>
                                <select id="analysis-timeframe">
                                    <option value="M5">5 Minutes</option>
                                    <option value="M15">15 Minutes</option>
                                    <option value="M30">30 Minutes</option>
                                    <option value="H1" selected>1 Hour</option>
                                    <option value="H4">4 Hours</option>
                                    <option value="D1">Daily</option>
                                </select>
                            </div>
                            <button id="run-analysis">Run Analysis</button>
                        </div>
                        <div id="indicators-container">
                            <p style="color: #718096;">Select a symbol and timeframe, then run analysis</p>
                        </div>
                    </div>
                    <div class="chart-container">
                        <div id="analysis-chart" style="height: 100%; display: flex; align-items: center; justify-content: center; color: #718096;">
                            Analysis chart will be displayed here
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trading Signals Tab -->
            <div id="signals-tab" class="tab-panel">
                <div class="card">
                    <h3>AI Trade Recommendations</h3>
                    <div id="signals-container">
                        <div class="signal-card buy">
                            <div class="signal-header">
                                <div class="signal-symbol">EURUSD</div>
                                <div class="signal-type buy">BUY</div>
                            </div>
                            <div class="signal-details">
                                <div class="signal-detail">
                                    <div class="signal-detail-label">Entry Price</div>
                                    <div class="signal-detail-value">1.0765</div>
                                </div>
                                <div class="signal-detail">
                                    <div class="signal-detail-label">Stop Loss</div>
                                    <div class="signal-detail-value">1.0740</div>
                                </div>
                                <div class="signal-detail">
                                    <div class="signal-detail-label">Take Profit</div>
                                    <div class="signal-detail-value">1.0815</div>
                                </div>
                            </div>
                            <div class="signal-actions">
                                <button>Execute Trade</button>
                                <button class="secondary">Analyze</button>
                            </div>
                        </div>
                        <div class="signal-card sell">
                            <div class="signal-header">
                                <div class="signal-symbol">GBPUSD</div>
                                <div class="signal-type sell">SELL</div>
                            </div>
                            <div class="signal-details">
                                <div class="signal-detail">
                                    <div class="signal-detail-label">Entry Price</div>
                                    <div class="signal-detail-value">1.2530</div>
                                </div>
                                <div class="signal-detail">
                                    <div class="signal-detail-label">Stop Loss</div>
                                    <div class="signal-detail-value">1.2555</div>
                                </div>
                                <div class="signal-detail">
                                    <div class="signal-detail-label">Take Profit</div>
                                    <div class="signal-detail-value">1.2480</div>
                                </div>
                            </div>
                            <div class="signal-actions">
                                <button>Execute Trade</button>
                                <button class="secondary">Analyze</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Positions Tab -->
            <div id="positions-tab" class="tab-panel">
                <div class="card">
                    <h3>Open Positions</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Symbol</th>
                                <th>Type</th>
                                <th>Volume</th>
                                <th>Open Price</th>
                                <th>Current Price</th>
                                <th>Stop Loss</th>
                                <th>Take Profit</th>
                                <th>Profit</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="positions-table">
                            <tr>
                                <td colspan="9" style="text-align: center; color: #718096;">Connect to MT5 to view positions</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="card">
                    <h3>Recent Orders</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Symbol</th>
                                <th>Type</th>
                                <th>Volume</th>
                                <th>Price</th>
                                <th>Result</th>
                            </tr>
                        </thead>
                        <tbody id="orders-table">
                            <tr>
                                <td colspan="6" style="text-align: center; color: #718096;">Connect to MT5 to view order history</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Autonomous Trading Tab -->
            <div id="autonomous-tab" class="tab-panel">
                <div class="card">
                    <h3>AI Trading Strategy</h3>
                    <div class="grid-container">
                        <div>
                            <div style="margin-bottom: 15px;">
                                <label for="strategy-name">Strategy Name</label>
                                <input type="text" id="strategy-name" placeholder="My Strategy">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label for="strategy-symbols">Symbols</label>
                                <select id="strategy-symbols" multiple style="height: 100px;">
                                    <option value="EURUSD">EURUSD</option>
                                    <option value="GBPUSD">GBPUSD</option>
                                    <option value="USDJPY">USDJPY</option>
                                    <option value="BTCUSD">BTCUSD</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label for="strategy-timeframe">Timeframe</label>
                                <select id="strategy-timeframe">
                                    <option value="M15">15 Minutes</option>
                                    <option value="M30">30 Minutes</option>
                                    <option value="H1" selected>1 Hour</option>
                                    <option value="H4">4 Hours</option>
                                    <option value="D1">Daily</option>
                                </select>
                            </div>
                        </div>
                        <div>
                            <div style="margin-bottom: 15px;">
                                <label for="risk-per-trade">Risk Per Trade (%)</label>
                                <input type="number" id="risk-per-trade" min="0.1" max="10" step="0.1" value="1">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label for="max-trades">Max Concurrent Trades</label>
                                <input type="number" id="max-trades" min="1" max="10" value="3">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label for="strategy-enabled">Enable Strategy</label>
                                <input type="checkbox" id="strategy-enabled" style="width: auto;">
                            </div>
                            <button id="save-strategy" style="margin-top: 24px;">Save Strategy</button>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <h3>Strategy Performance</h3>
                    <div id="strategy-performance" style="color: #718096;">
                        <p>No strategy performance data available</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Connection Modal -->
    <div id="connection-modal" class="modal">
        <div class="modal-content">
            <h2 style="margin-top: 0; margin-bottom: 20px; font-size: 22px;">MT5 Connection Settings</h2>
            <form id="connection-form">
                <div>
                    <label for="mt5-path">MT5 Terminal Path</label>
                    <input type="text" id="mt5-path" placeholder="C:\Program Files\MetaTrader 5\terminal64.exe">
                </div>
                <div>
                    <label for="account-type">Account Type</label>
                    <select id="account-type">
                        <option value="demo">Demo Account</option>
                        <option value="real">Real Account</option>
                    </select>
                </div>
                <div>
                    <label for="login">Login</label>
                    <input type="text" id="login" placeholder="MT5 Login ID">
                </div>
                <div>
                    <label for="password">Password</label>
                    <input type="password" id="password" placeholder="MT5 Password">
                </div>
                <div>
                    <label for="server">Server</label>
                    <input type="text" id="server" placeholder="MT5 Server">
                </div>
                <div style="margin-top: 20px; display: flex; justify-content: flex-end; gap: 10px;">
                    <button type="button" class="secondary" id="cancel-button">Cancel</button>
                    <button type="submit">Connect</button>
                </div>
            </form>
        </div>
    </div>

    <script src="theme.js"></script>
    <script src="renderer.js"></script>
</body>
</html>