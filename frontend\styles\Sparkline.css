/* Sparkline Styles */
.sparkline-container {
  width: 100%;
  height: 40px;
  margin-top: 12px;
  position: relative;
  overflow: hidden;
}

.sparkline {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: flex-end;
  gap: 2px;
}

.sparkline-bar {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.1);
  min-width: 3px;
  border-radius: 2px 2px 0 0;
  transition: height 0.3s ease;
}

.sparkline-bar.up {
  background-color: rgba(0, 204, 0, 0.3);
}

.sparkline-bar.down {
  background-color: rgba(255, 77, 77, 0.3);
}

.sparkline-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  stroke: rgba(255, 255, 255, 0.5);
  stroke-width: 1.5;
  fill: none;
}

.sparkline-dot {
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: white;
  transform: translate(-50%, -50%);
}

.sparkline-dot.first {
  background-color: rgba(255, 255, 255, 0.5);
}

.sparkline-dot.last {
  background-color: white;
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
}

.sparkline-label {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 4px;
}

.sparkline-value {
  font-weight: 600;
}

.sparkline-value.up {
  color: #00cc00;
}

.sparkline-value.down {
  color: #ff4d4d;
}
