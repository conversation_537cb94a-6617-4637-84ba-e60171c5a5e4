/* Registration steps styles */
.registration-steps {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 2rem 0;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 0.5rem;
  position: relative;
  opacity: 0.5;
  transition: all 0.3s ease-out;
}

.step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 1.5rem;
  right: -1.5rem;
  width: 1rem;
  height: 2px;
  background: var(--text-secondary);
  opacity: 0.3;
}

.step-number {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: var(--bg-dark);
  border: 2px solid var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.step-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

/* Active step styles */
.step.active {
  opacity: 1;
  transform: scale(1.05);
}

.step.active .step-number {
  background: var(--primary);
  border-color: var(--primary-light);
  box-shadow: 0 0 15px var(--primary-light);
  color: white;
}

.step.active .step-text {
  color: var(--text-primary);
  font-weight: 500;
}

/* Completed step styles */
.step.completed .step-number {
  background: var(--success);
  border-color: var(--success-light);
  box-shadow: 0 0 15px var(--success-light);
}

.step.completed .step-text {
  color: var(--success);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .registration-steps {
    gap: 1rem;
  }
  
  .step-number {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 0.875rem;
  }
  
  .step-text {
    font-size: 0.75rem;
  }
  
  .step:not(:last-child)::after {
    right: -1rem;
    width: 0.5rem;
  }
}