<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GarudaAlgo | Login</title>
  <meta name="description" content="Login to your GarudaAlgo account - Navigate the markets with algorithmic intelligence & strategic precision.">
  <link rel="icon" href="app_icon.ico">
  <link rel="stylesheet" href="style.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    .login-section {
      padding: 80px 0;
      min-height: calc(100vh - 200px);
    }
    
    .login-container {
      max-width: 500px;
      margin: 0 auto;
      background: var(--bg-card);
      border-radius: 12px;
      padding: 40px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.08);
    }
    
    .login-header {
      text-align: center;
      margin-bottom: 30px;
    }
    
    .login-header h2 {
      font-size: 28px;
      margin-bottom: 10px;
      background: var(--text-gradient);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
    
    .login-form .form-group {
      margin-bottom: 20px;
    }
    
    .login-form label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
    }
    
    .login-form input {
      width: 100%;
      padding: 12px 16px;
      border-radius: 8px;
      background: var(--bg-dark);
      border: 1px solid rgba(255, 255, 255, 0.1);
      color: var(--text-primary);
      font-size: 16px;
      transition: all 0.3s ease;
    }
    
    .login-form input:focus {
      border-color: var(--primary-light);
      outline: none;
      box-shadow: 0 0 0 2px rgba(108, 99, 255, 0.2);
    }
    
    .login-form .btn-primary {
      width: 100%;
      margin-top: 10px;
    }
    
    .form-note {
      margin-top: 20px;
      font-size: 14px;
      color: var(--text-secondary);
      text-align: center;
    }
    
    .form-note a {
      color: var(--primary-light);
      text-decoration: none;
    }
    
    .form-note a:hover {
      text-decoration: underline;
    }
    
    .error-message {
      color: #ff5757;
      font-size: 14px;
      margin-top: 5px;
      display: none;
    }
    
    .login-error {
      background: rgba(255, 87, 87, 0.1);
      color: #ff5757;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
      display: none;
    }
    
    .verification-message {
      background: rgba(255, 193, 7, 0.1);
      color: #ffc107;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
      display: none;
    }
  </style>
</head>
<body>
  <header class="header">
    <div class="container">
      <nav class="navbar">
        <div class="logo">
          <img src="app_icon.png" alt="GarudaAlgo Logo">
          <h1>GarudaAlgo</h1>
        </div>
        <div class="nav-right">
          <div class="language-selector">
            <button id="lang-en" class="active">EN</button>
            <button id="lang-id">ID</button>
          </div>
          <a href="index.html" class="btn-secondary" data-lang-key="back-to-home">Back to Home</a>
        </div>
      </nav>
    </div>
  </header>

  <section class="login-section">
    <div class="container">
      <div class="login-container">
        <div class="login-header">
          <h2 data-lang-key="login-account">Login to Your Account</h2>
          <p data-lang-key="login-desc">Access your GarudaAlgo V2 dashboard</p>
        </div>
        
        <div class="login-error" id="login-error" data-lang-key="login-error">
          Invalid email or password. Please try again.
        </div>
        
        <div class="verification-message" id="verification-message" data-lang-key="verification-pending">
          Your account is pending verification. We'll notify you by email once it's approved.
        </div>
        
        <form class="login-form" id="login-form">
          <div class="form-group">
            <label for="email" data-lang-key="email">Email Address</label>
            <input type="email" id="email" name="email" required>
            <div class="error-message" id="email-error" data-lang-key="email-error">Please enter a valid email address</div>
          </div>
          
          <div class="form-group">
            <label for="password" data-lang-key="password">Password</label>
            <input type="password" id="password" name="password" required>
            <div class="error-message" id="password-error" data-lang-key="password-error">Please enter your password</div>
          </div>
          
          <button type="submit" class="btn-primary" data-lang-key="login">Login</button>
        </form>
        
        <p class="form-note" data-lang-key="not-registered">Don't have an account? <a href="register.html" data-lang-key="register-now">Register now</a></p>
        <p class="form-note" data-lang-key="forgot-password">Forgot your password? <a href="#" id="reset-password" data-lang-key="reset-password">Reset it here</a></p>
      </div>
    </div>
  </section>

  <footer class="footer">
    <div class="container">
      <div class="footer-grid">
        <div class="footer-col">
          <div class="footer-logo">
            <img src="app_icon.png" alt="GarudaAlgo Logo">
            <h2>GarudaAlgo</h2>
          </div>
          <p data-lang-key="footer-tagline">Navigate the markets with algorithmic intelligence & strategic precision.</p>
        </div>
        <div class="footer-col">
          <h3 data-lang-key="quick-links">Quick Links</h3>
          <ul>
            <li><a href="index.html#features" data-lang-key="features">Features</a></li>
            <li><a href="index.html#why-algo" data-lang-key="why-algo-trading-short">Why Algo & Autonomous</a></li>
            <li><a href="index.html#faq" data-lang-key="faq-link">FAQ</a></li>
            <li><a href="index.html#download" data-lang-key="download">Download</a></li>
            <li><a href="#" data-lang-key="support">Support</a></li>
          </ul>
        </div>
        <div class="footer-col">
          <h3 data-lang-key="legal">Legal</h3>
          <ul>
            <li><a href="#" data-lang-key="terms">Terms & Conditions</a></li>
            <li><a href="#" data-lang-key="privacy">Privacy Policy</a></li>
            <li><a href="#" data-lang-key="refund">Refund Policy</a></li>
          </ul>
        </div>
        <div class="footer-col">
          <h3 data-lang-key="contact">Contact</h3>
          <ul>
            <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
            <li class="social-icons">
              <a href="#"><i class="fab fa-twitter"></i></a>
              <a href="#"><i class="fab fa-facebook"></i></a>
              <a href="#"><i class="fab fa-instagram"></i></a>
              <a href="#"><i class="fab fa-youtube"></i></a>
            </li>
          </ul>
        </div>
      </div>
      <div class="copyright">
        <p>&copy; 2025 GarudaAlgo. <span data-lang-key="all-rights">All rights reserved.</span></p>
      </div>
    </div>
  </footer>

  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
  <script src="firebase-config.js"></script>
  <script src="locales.json"></script>
  <script src="script.js"></script>
  <script src="login.js"></script>
</body>
  <!-- Firebase & App Scripts -->
  <script src="firebase-config.js"></script>
  <script src="login.js"></script>
</body>
</html>
