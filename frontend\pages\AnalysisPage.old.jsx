import React, { useState, useEffect } from 'react';
import { useNotification } from '../components/Notification';
import { safeToFixed, isValidNumber } from '../utils/numberUtils';
import '../styles/Pages.css';
import '../styles/BentoComponents.css';
import '../styles/AnalysisBento.css';
import '../styles/GaugeIndicator.css';
import '../styles/TrendArrows.css';
import '../styles/Sparkline.css';
import '../styles/SupportResistance.css';

function AnalysisPage({ selectedSymbol, onSymbolSelect }) {
  const [timeframe, setTimeframe] = useState('H1');
  const [analysisData, setAnalysisData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [symbols, setSymbols] = useState([]);
  const notify = useNotification();

  // Fetch available symbols
  useEffect(() => {
    const fetchSymbols = async () => {
      console.log("[AnalysisPage] Attempting to fetch symbols..."); // Log start
      try {
        const response = await fetch('http://localhost:5001/api/symbols');
        console.log("[AnalysisPage] Symbols response status:", response.status); // Log status
        console.log("[AnalysisPage] Symbols response OK:", response.ok); // Log ok status

        if (!response.ok) {
          const errorText = await response.text(); // Get error text if not ok
          console.error("[AnalysisPage] Symbols fetch failed:", response.status, errorText);
          throw new Error(`Failed to fetch symbols: ${response.status}`);
        }

        const data = await response.json();
        console.log("[AnalysisPage] Received symbols data:", data); // Log received data

        if (!Array.isArray(data)) {
           console.error("[AnalysisPage] Received non-array data for symbols:", data);
           throw new Error("Invalid data format received for symbols");
        }

        setSymbols(data);
        console.log("[AnalysisPage] Symbols state updated."); // Log success

      } catch (error) {
        console.error('[AnalysisPage] Error fetching or processing symbols:', error); // Log any error
        notify.error('Error', 'Failed to load market symbols');
        setSymbols([]); // Ensure symbols list is empty on error
      }
    };

    // Delay fetching symbols slightly after component mount to allow connection to potentially stabilize
    const timer = setTimeout(() => {
        fetchSymbols();
    }, 500); // 500ms delay

    return () => clearTimeout(timer); // Cleanup timer on unmount

  }, [notify]); // Added notify dependency

  // Fetch analysis data when symbol or timeframe changes
  useEffect(() => {
    const fetchAnalysisData = async () => {
      if (!selectedSymbol) return;

      setLoading(true);
      setAnalysisData(null); // Clear previous data
      try {
        console.log(`Fetching analysis data for ${selectedSymbol} on ${timeframe} timeframe...`);
        // --- Use the new /api/analysis endpoint ---
        const response = await fetch(`http://localhost:5001/api/analysis?symbol=${selectedSymbol}&timeframe=${timeframe}`);
        console.log("[AnalysisPage] Analysis response status:", response.status); // Log status

        if (!response.ok) {
          const errorText = await response.text();
          console.warn(`[AnalysisPage] Analysis API returned error: ${response.status}`, errorText);
          let errorJson = {};
          try { errorJson = JSON.parse(errorText); } catch { /* ignore parsing error */ }
          // Use error message from backend if available
          throw new Error(errorJson.error || `Failed to fetch analysis data: ${response.status}`);
        }

        const data = await response.json();
        console.log('[AnalysisPage] Analysis data received:', data);
        console.log('[AnalysisPage] Support/Resistance data:', data.support_resistance);
        console.log('[AnalysisPage] Current price sources:', {
          'support_resistance.current_price': data.support_resistance?.current_price,
          'price_data.close (last)': data.price_data?.close?.[data.price_data.close.length - 1],
          'price_data.last_price': data.price_data?.last_price
        });

        // --- Validate the structure of the received analysis data ---
        // Adjust validation based on the actual structure returned by AnalysisEngine
        if (!data || typeof data !== 'object' || !data.moving_averages || !data.rsi || !data.macd || !data.trend) {
             console.error("[AnalysisPage] Received invalid analysis data structure:", data);
             throw new Error("Invalid analysis data format received");
        }

        setAnalysisData(data); // Set the received analysis data
        console.log("[AnalysisPage] Analysis state updated.");

      } catch (error) { // Single, correct catch block
        console.error('[AnalysisPage] Error fetching analysis data:', error);
        // Display the actual error message from the fetch/backend
        notify.warning('Analysis Data Error', error.message || 'Failed to load analysis data');
        setAnalysisData(null); // Ensure data is null on error
      } finally {
        setLoading(false);
      }
    };

    fetchAnalysisData();
  }, [selectedSymbol, timeframe, notify]); // Ensure notify is in dependency array


  const handleSymbolChange = (e) => {
    onSymbolSelect(e.target.value);
  };

  const handleTimeframeChange = (e) => {
    setTimeframe(e.target.value);
  };

  // Helper function to get signal class name
  const getSignalClass = (value) => {
    if (!value) return 'neutral';
    const lowerValue = String(value).toLowerCase();
    return lowerValue === 'buy' || lowerValue === 'bullish' ? 'buy' :
           lowerValue === 'sell' || lowerValue === 'bearish' ? 'sell' : 'neutral';
  };

  // Helper function to generate random price data for demo purposes
  const generateRandomPriceData = (count = 20) => {
    const startPrice = 1.1 + Math.random() * 0.1; // Random starting price around 1.1-1.2
    const result = [startPrice];

    for (let i = 1; i < count; i++) {
      // Generate small random changes (-0.5% to +0.5%)
      const change = (Math.random() - 0.5) * 0.01;
      result.push(result[i-1] * (1 + change));
    }

    return result;
  };

  // Helper function to render indicator value with proper formatting
  const renderIndicatorValue = (value, type) => {
    if (type === 'signal') {
      // Handle potential null/undefined values gracefully
      const signalValue = getSignalClass(value);
      return (
        <span className={`signal ${signalValue}`}>
          {value ?? 'NEUTRAL'}
        </span>
      );
    }
    // Use our safe utility function for numeric values
    return safeToFixed(value, 5, 'N/A');
  };

  // RSI Gauge component
  const RSIGauge = ({ value }) => {
    // Calculate needle position (0-100 scale to 0-180 degrees)
    const isValid = isValidNumber(value);
    const needleRotation = isValid ? Math.min(Math.max(value, 0), 100) * 1.8 : 90; // Default to middle if no value
    const needleHeight = 50; // Height of the needle

    // Determine the RSI zone for styling (not used yet but kept for future enhancements)
    // const rsiZone = value <= 30 ? 'oversold' : value >= 70 ? 'overbought' : 'neutral';

    return (
      <div className="gauge-container">
        <div className="gauge-outer"></div>
        <div className="gauge-inner">{safeToFixed(value, 2)}</div>
        <div
          className="gauge-needle"
          style={{
            height: `${needleHeight}px`,
            transform: `rotate(${needleRotation}deg)`
          }}
        ></div>
        <div className="gauge-labels">
          <span className="gauge-label-oversold">30</span>
          <span className="gauge-label-neutral">50</span>
          <span className="gauge-label-overbought">70</span>
        </div>
      </div>
    );
  };

  // ATR Gauge component - for volatility
  const ATRGauge = ({ value, percent }) => {
    // For ATR, we'll use the percent value to position the needle (0-100%)
    // If percent is not available, we'll use a normalized scale based on typical ATR values
    const isValidPercent = isValidNumber(percent);
    const isValidValue = isValidNumber(value);

    const normalizedValue = isValidPercent ? percent : (isValidValue ? Math.min(value * 10, 100) : 50);
    const needleRotation = normalizedValue * 1.8; // Convert to degrees (0-180)
    const needleHeight = 50;

    // Determine volatility level for styling (not used yet but kept for future enhancements)
    // const volatilityLevel = normalizedValue <= 30 ? 'low' : normalizedValue >= 70 ? 'high' : 'normal';

    return (
      <div className="gauge-container">
        <div className="gauge-outer"></div>
        <div className="gauge-inner">{safeToFixed(value, 5)}</div>
        <div
          className="gauge-needle"
          style={{
            height: `${needleHeight}px`,
            transform: `rotate(${needleRotation}deg)`
          }}
        ></div>
        <div className="gauge-labels">
          <span className="gauge-label-oversold">Low</span>
          <span className="gauge-label-neutral">Med</span>
          <span className="gauge-label-overbought">High</span>
        </div>
      </div>
    );
  };

  // Trend Arrow component
  const TrendArrow = ({ direction, animated = false }) => {
    let arrowClass = 'neutral';
    let arrowSvg = null;

    if (!direction) {
      arrowClass = 'neutral';
      arrowSvg = (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <line x1="5" y1="12" x2="19" y2="12"></line>
        </svg>
      );
    } else if (direction.toLowerCase().includes('buy') || direction.toLowerCase().includes('bullish')) {
      arrowClass = 'up';
      arrowSvg = (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <polyline points="18 15 12 9 6 15"></polyline>
        </svg>
      );
    } else if (direction.toLowerCase().includes('sell') || direction.toLowerCase().includes('bearish')) {
      arrowClass = 'down';
      arrowSvg = (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <polyline points="6 9 12 15 18 9"></polyline>
        </svg>
      );
    }

    return (
      <span className={`trend-arrow ${arrowClass} ${animated ? 'animated' : ''}`}>
        {arrowSvg}
      </span>
    );
  };

  // Sparkline component for price movement visualization
  const Sparkline = ({ data, height = 40 }) => {
    // If no data, return empty placeholder
    if (!data || !Array.isArray(data) || data.length === 0 || data.some(val => val === null || val === undefined || isNaN(val))) {
      return (
        <div className="sparkline-container" style={{ height: `${height}px` }}>
          <div className="sparkline">
            {[...Array(10)].map((_, i) => (
              <div
                key={i}
                className="sparkline-bar"
                style={{ height: `${Math.random() * 50 + 10}%` }}
              ></div>
            ))}
          </div>
          <div className="sparkline-label">
            <span>No data</span>
            <span>-</span>
          </div>
        </div>
      );
    }

    // Find min and max for scaling
    const min = Math.min(...data);
    const max = Math.max(...data);
    const range = max - min;

    // Calculate if price went up or down
    const priceChange = data[data.length - 1] - data[0];
    const changeDirection = priceChange > 0 ? 'up' : priceChange < 0 ? 'down' : 'neutral';
    const changePercent = ((priceChange / data[0]) * 100).toFixed(2);

    return (
      <div className="sparkline-container" style={{ height: `${height}px` }}>
        <div className="sparkline">
          {data.map((value, index) => {
            // Scale the value to fit in the container height
            const scaledHeight = range === 0 ? 50 : ((value - min) / range) * 100;
            const isUp = index > 0 && value > data[index - 1];
            const isDown = index > 0 && value < data[index - 1];

            return (
              <div
                key={index}
                className={`sparkline-bar ${isUp ? 'up' : isDown ? 'down' : ''}`}
                style={{ height: `${scaledHeight}%` }}
              ></div>
            );
          })}
        </div>
        <div className="sparkline-label">
          <span>{safeToFixed(data[0], 5)}</span>
          <span className={`sparkline-value ${changeDirection}`}>
            {priceChange > 0 ? '+' : ''}{changePercent}%
          </span>
          <span>{safeToFixed(data[data.length - 1], 5)}</span>
        </div>
      </div>
    );
  };




  return (
    <div className="page-container analysis-page">
      <h2>Market Analysis</h2>

      {/* Controls Card */}
      <div className="bento-card analysis-controls-card">
        <div className="analysis-form-group">
          <label htmlFor="symbol">Symbol</label>
          <select
            id="symbol"
            value={selectedSymbol}
            onChange={handleSymbolChange}
            disabled={loading || symbols.length === 0}
          >
            {symbols.length === 0 && <option value="">Loading symbols... {loading ? "(in progress)" : "(waiting)"}</option>}
            {Array.isArray(symbols) && symbols.map(symbol => (
              <option key={symbol} value={symbol}>{symbol}</option>
            ))}
          </select>
        </div>

        <div className="analysis-form-group">
          <label htmlFor="timeframe">Timeframe</label>
          <select
            id="timeframe"
            value={timeframe}
            onChange={handleTimeframeChange}
            disabled={loading}
          >
            <option value="M1">1 Minute</option>
            <option value="M5">5 Minutes</option>
            <option value="M15">15 Minutes</option>
            <option value="M30">30 Minutes</option>
            <option value="H1">1 Hour</option>
            <option value="H4">4 Hours</option>
            <option value="D1">Daily</option>
            <option value="W1">Weekly</option>
          </select>
        </div>
      </div>

      {loading ? (
        <div className="bento-loading">Loading analysis data...</div>
      ) : !analysisData ? (
        <div className="bento-no-data">No analysis data available for {selectedSymbol} on {timeframe}. Select symbol/timeframe or check connection.</div>
      ) : (
        <div className="bento-grid">
          {/* Summary Card - Spans full width at top */}
          <div className="bento-card bento-span-12">
            <div className="bento-card-header">
              <h3 className="bento-card-title">Summary</h3>
              <div className="signal-with-arrow">
                {renderIndicatorValue(analysisData.trend?.overall, 'signal')}
                <TrendArrow direction={analysisData.trend?.overall} animated={true} />
              </div>
            </div>
            <div className="bento-card-content analysis-summary-card">
              <div className="analysis-overall-signal">
                <h4>Overall Signal</h4>
                <div className="analysis-signal-strength">
                  <div className="analysis-strength-bar">
                    <div
                      className={`analysis-strength-value ${getSignalClass(analysisData.trend?.overall)}`}
                      style={{ width: `${analysisData.trend?.strength ?? 50}%` }}
                    ></div>
                  </div>
                  <div className="analysis-strength-label">{analysisData.trend?.strength ?? 50}% Confidence</div>
                </div>

                {/* Price Movement Sparkline */}
                <div className="mt-2">
                  <h4>Price Movement</h4>
                  <Sparkline
                    data={analysisData.price_data?.close || generateRandomPriceData(20)}
                    height={50}
                  />
                </div>
              </div>
              <div className="analysis-summary-text">
                <h4>Contributing Signals</h4>
                {analysisData.trend?.signals && analysisData.trend.signals.length > 0 ? (
                  <ul>
                    {analysisData.trend.signals.map(([indicator, direction], index) => (
                      <li key={index}>{indicator}: {renderIndicatorValue(direction, 'signal')}</li>
                    ))}
                  </ul>
                ) : (
                  <p>No specific contributing signals identified.</p>
                )}
              </div>
            </div>
          </div>

          {/* Moving Averages Card */}
          <div className="bento-card bento-span-4">
            <div className="bento-card-header">
              <h3 className="bento-card-title">Moving Averages</h3>
              <div className="signal-with-arrow">
                {renderIndicatorValue(analysisData.moving_averages?.ema_alignment?.includes('Bullish') ? 'BUY' : analysisData.moving_averages?.ema_alignment?.includes('Bearish') ? 'SELL' : 'NEUTRAL', 'signal')}
                <TrendArrow direction={analysisData.moving_averages?.ema_alignment?.includes('Bullish') ? 'BUY' : analysisData.moving_averages?.ema_alignment?.includes('Bearish') ? 'SELL' : 'NEUTRAL'} />
              </div>
            </div>
            <div className="bento-card-content analysis-indicator-details">
              <p><span>EMA(20):</span> <span>{safeToFixed(analysisData.moving_averages?.ema20, 5)}</span></p>
              <p><span>EMA(50):</span> <span>{safeToFixed(analysisData.moving_averages?.ema50, 5)}</span></p>
              <p><span>EMA(200):</span> <span>{safeToFixed(analysisData.moving_averages?.ema200, 5)}</span></p>
            </div>
          </div>

          {/* MACD Card */}
          <div className="bento-card bento-span-4">
            <div className="bento-card-header">
              <h3 className="bento-card-title">MACD</h3>
              <div className="signal-with-arrow">
                {renderIndicatorValue(analysisData.macd?.cross, 'signal')}
                <TrendArrow direction={analysisData.macd?.cross} />
              </div>
            </div>
            <div className="bento-card-content analysis-indicator-details">
              <p><span>MACD:</span> <span>{safeToFixed(analysisData.macd?.macd, 5)}</span></p>
              <p><span>Signal:</span> <span>{safeToFixed(analysisData.macd?.signal, 5)}</span></p>
              <p><span>Histogram:</span> <span>{safeToFixed(analysisData.macd?.histogram, 5)}</span></p>
            </div>
          </div>

          {/* RSI Card */}
          <div className="bento-card bento-span-4 bento-card-centered">
            <div className="bento-card-header">
              <h3 className="bento-card-title">RSI</h3>
              <div className="signal-with-arrow">
                {renderIndicatorValue(analysisData.rsi?.level, 'signal')}
                <TrendArrow direction={analysisData.rsi?.level} />
              </div>
            </div>
            <div className="bento-card-content">
              <RSIGauge value={analysisData.rsi?.value} />
              <p className="mt-2"><span>Divergence:</span> <span>{analysisData.rsi?.divergence ?? 'None'}</span></p>
            </div>
          </div>

          {/* Bollinger Bands Card */}
          <div className="bento-card bento-span-4">
            <div className="bento-card-header">
              <h3 className="bento-card-title">Bollinger Bands</h3>
              <div className="signal-with-arrow">
                {renderIndicatorValue(analysisData.bollinger_bands?.price_position === 'Above Upper' ? 'SELL' : analysisData.bollinger_bands?.price_position === 'Below Lower' ? 'BUY' : 'NEUTRAL', 'signal')}
                <TrendArrow direction={analysisData.bollinger_bands?.price_position === 'Above Upper' ? 'SELL' : analysisData.bollinger_bands?.price_position === 'Below Lower' ? 'BUY' : 'NEUTRAL'} />
              </div>
            </div>
            <div className="bento-card-content analysis-indicator-details">
              <p><span>Upper:</span> <span>{safeToFixed(analysisData.bollinger_bands?.upper, 5)}</span></p>
              <p><span>Middle:</span> <span>{safeToFixed(analysisData.bollinger_bands?.middle, 5)}</span></p>
              <p><span>Lower:</span> <span>{safeToFixed(analysisData.bollinger_bands?.lower, 5)}</span></p>
            </div>
          </div>

          {/* ATR Card */}
          <div className="bento-card bento-span-4 bento-card-centered">
            <div className="bento-card-header">
              <h3 className="bento-card-title">ATR</h3>
              <span className="signal neutral">{analysisData.atr?.volatility ?? 'N/A'}</span>
            </div>
            <div className="bento-card-content">
              <ATRGauge
                value={analysisData.atr?.value}
                percent={analysisData.atr?.percent}
              />
              <p className="mt-2"><span>ATR %:</span> <span>{safeToFixed(analysisData.atr?.percent, 2)}%</span></p>
              <p><span>Volatility:</span> <span>{analysisData.atr?.volatility ?? 'N/A'}</span></p>
            </div>
          </div>

          {/* Support/Resistance Card - Spans 2 columns */}
          <div className="bento-card bento-span-8 bento-height-2">
            <div className="bento-card-header">
              <h3 className="bento-card-title">Support & Resistance</h3>
            </div>
            <div className="bento-card-content">
              {/* Price Level Visualization with meaningful position */}
              <div style={{ margin: '10px 0', padding: '0 10px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px' }}>
                  <div style={{ fontWeight: 'bold', fontSize: '0.9rem' }}>Price Levels</div>
                  <div style={{ fontSize: '0.9rem', fontWeight: 'bold', color: '#ffa500' }}>
                    {/* Display current price, preferring bid, then last, then close */}
                    Current: {safeToFixed(analysisData.current_price?.bid ?? analysisData.current_price?.last ?? analysisData.price_data?.close?.[analysisData.price_data.close.length - 1], 5)}
                  </div>
                </div>

                {/* Calculate position of current price between support and resistance */}
                {(() => {
                  // Get the actual numeric current price, preferring bid, then last, then close from the main analysis data
                  const currentPriceValue = analysisData.current_price?.bid ??
                                            analysisData.current_price?.last ??
                                            analysisData.price_data?.close?.[analysisData.price_data.close.length - 1];

                  // Debug log to see what we're getting
                  console.log('[Price Bar] Current price sources:', {
                    'analysisData.current_price': analysisData.current_price,
                    'price_data.close (last)': analysisData.price_data?.close?.[analysisData.price_data.close.length - 1],
                    'final currentPriceValue': currentPriceValue
                  });

                  // Get nearest support and resistance
                  // Corrected: Access price directly from the nested objects
                  const nearestSupport = analysisData.support_resistance?.nearest_support?.price;
                  const nearestResistance = analysisData.support_resistance?.nearest_resistance?.price;

                  // Debug log the support/resistance structure
                  console.log('[Price Bar] Support/Resistance nearest values:', {
                    'nearest_support': analysisData.support_resistance?.nearest_support,
                    'nearest_resistance': analysisData.support_resistance?.nearest_resistance
                  });

                  // Calculate position (percentage from left)
                  let position = 50; // Default to middle

                  // Log values for debugging
                  console.log('[Price Bar] Current Value:', currentPriceValue, 'Support:', nearestSupport, 'Resistance:', nearestResistance);
                  console.log('[Price Bar] Support/Resistance structure:', analysisData.support_resistance);

                  if (nearestSupport != null && nearestResistance != null && currentPriceValue != null) { // Check for null/undefined explicitly
                    // Calculate position based on actual values
                    const range = nearestResistance - nearestSupport;
                    if (range > 0) {
                      position = ((currentPriceValue - nearestSupport) / range) * 100;
                      // Clamp between 0 and 100
                      position = Math.max(0, Math.min(100, position));
                      console.log('[Price Bar] Calculated position:', position, '%');
                    } else {
                       // Handle edge case where support and resistance are the same or inverted
                       position = currentPriceValue >= nearestResistance ? 100 : 0;
                       console.log('[Price Bar] Edge case - support/resistance equal or inverted');
                    }
                  } else if (nearestSupport != null && currentPriceValue != null && nearestResistance == null) {
                    // If price is above support and no resistance
                    position = currentPriceValue > nearestSupport ? 75 : 25; // Position relative to support
                    console.log('[Price Bar] Only support available, position:', position);
                  } else if (nearestSupport == null && currentPriceValue != null && nearestResistance != null) {
                    // If price is below resistance and no support
                    position = currentPriceValue < nearestResistance ? 25 : 75; // Position relative to resistance
                    console.log('[Price Bar] Only resistance available, position:', position);
                  } else if (currentPriceValue != null) {
                     // Only current price is available
                     position = 50; // Default to middle if S/R missing
                     console.log('[Price Bar] No support/resistance available, using default position');
                  } else {
                     console.log('[Price Bar] No valid price data available');
                  }


                  return (
                    <>
                      {/* Horizontal price bar with meaningful position */}
                      <div style={{
                        position: 'relative',
                        height: '10px',
                        backgroundColor: '#2a2a3a',
                        borderRadius: '5px',
                        marginBottom: '5px',
                        overflow: 'hidden'
                      }}>
                        {/* Support/Resistance zones */}
                        {nearestSupport != null && nearestResistance != null && (
                          <div style={{
                            position: 'absolute',
                            left: '0',
                            top: '0',
                            width: '100%',
                            height: '100%',
                            background: 'linear-gradient(to right, #2a4858 0%, #2a2a3a 50%, #582a2a 100%)'
                          }}></div>
                        )}
                      </div>

                      {/* Current price value */}
                      <div style={{
                        textAlign: 'center',
                        marginBottom: '10px',
                        fontSize: '0.9rem',
                        fontWeight: 'bold',
                        color: '#ffa500'
                      }}>
                        {/* Display current price with fallbacks */}
                        {safeToFixed(currentPriceValue, 5)}
                      </div>

                      {/* Horizontal price bar with position indicator */}
                      <div style={{
                        position: 'relative',
                        height: '20px',
                        backgroundColor: '#2a2a3a',
                        borderRadius: '10px',
                        marginBottom: '15px',
                        overflow: 'hidden'
                      }}>
                        {/* Support/Resistance gradient background */}
                        <div style={{
                          position: 'absolute',
                          left: '0',
                          top: '0',
                          width: '100%',
                          height: '100%',
                          background: 'linear-gradient(to right, #2a4858 0%, #2a2a3a 50%, #582a2a 100%)'
                        }}></div>

                        {/* Price position indicator */}
                        {currentPriceValue != null && (
                          <div style={{
                            position: 'absolute',
                            left: `${position}%`,
                            top: '0',
                            width: '4px',
                            height: '100%',
                            backgroundColor: '#ffa500',
                            transform: 'translateX(-50%)',
                            zIndex: 2
                          }}></div>
                        )}
                      </div>
                    </>
                  );
                })()}

                {/* Support/Resistance labels with actual values */}
                <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '0.85rem' }}>
                  <div>
                    {/* Corrected: Use nearestSupport variable */}
                    Support: {analysisData.support_resistance?.nearest_support?.price != null
                      ? safeToFixed(analysisData.support_resistance.nearest_support.price, 5)
                      : 'N/A'}
                  </div>
                  <div style={{ textAlign: 'center' }}>Current Price</div>
                  <div style={{ textAlign: 'right' }}>
                    {/* Corrected: Use nearestResistance variable */}
                    Resistance: {analysisData.support_resistance?.nearest_resistance?.price != null
                      ? safeToFixed(analysisData.support_resistance.nearest_resistance.price, 5)
                      : 'N/A'}
                  </div>
                </div>
              </div>
              <div className="analysis-sr-details">
              {/* Key Levels Section */}
              <div className="analysis-sr-section">
                <strong className="section-title">Key Levels</strong>
                {/* Nearest Levels */}
                <p className="key-level">
                  <strong>Nearest Resistance:</strong>{' '}
                  <span className="level-value resistance-value">
                    {analysisData.support_resistance?.nearest_resistance?.price != null
                      ? `${safeToFixed(analysisData.support_resistance.nearest_resistance.price, 5)} (${analysisData.support_resistance.nearest_resistance.source || 'Unknown'})`
                      : 'N/A'}
                  </span>
                </p>
                <p className="key-level">
                  <strong>Nearest Support:</strong>{' '}
                  <span className="level-value support-value">
                    {analysisData.support_resistance?.nearest_support?.price != null
                      ? `${safeToFixed(analysisData.support_resistance.nearest_support.price, 5)} (${analysisData.support_resistance.nearest_support.source || 'Unknown'})`
                      : 'N/A'}
                  </span>
                </p>
              </div>

              {/* Resistances List */}
              <div className="sr-list-section">
                <strong className="section-title resistance-title">Resistances</strong>
                <ul className="sr-list resistance-list">
                  {analysisData.support_resistance?.resistance_levels?.length > 0 ? (
                    analysisData.support_resistance.resistance_levels.map((r, index) => (
                      <li key={`res-${index}`} className="sr-list-item">
                        <span className="level-price">{safeToFixed(r?.price, 5)}</span>
                        <span className="level-source">({r?.source ?? 'Unknown'})</span>
                      </li>
                    ))
                  ) : (
                    <li className="sr-list-item empty">N/A</li>
                  )}
                </ul>
              </div>

              {/* Supports List */}
              <div className="sr-list-section">
                <strong className="section-title support-title">Supports</strong>
                <ul className="sr-list support-list">
                  {analysisData.support_resistance?.support_levels?.length > 0 ? (
                    analysisData.support_resistance.support_levels.map((s, index) => (
                      <li key={`sup-${index}`} className="sr-list-item">
                        <span className="level-price">{safeToFixed(s?.price, 5)}</span>
                        <span className="level-source">({s?.source ?? 'Unknown'})</span>
                      </li>
                    ))
                  ) : (
                    <li className="sr-list-item empty">N/A</li>
                  )}
                </ul>
              </div>

              {/* SR Cluster Zones */}
              <div className="sr-list-section">
                <strong className="section-title cluster-title">SR Cluster Zones</strong>
                <ul className="sr-list cluster-list">
                  {analysisData.support_resistance?.sr_cluster_zones?.length > 0 ? (
                    analysisData.support_resistance.sr_cluster_zones.map((z, index) => (
                      <li key={`zone-${index}`} className="sr-list-item">
                        <span className="level-price">{safeToFixed(z?.level, 5)}</span>
                        <span className="level-source">({z?.touches ?? 0} touches)</span>
                      </li>
                    ))
                  ) : (
                    <li className="sr-list-item empty">N/A</li>
                  )}
                </ul>
              </div>

              {/* Psychological Levels */}
              <div className="sr-list-section">
                <strong className="section-title psych-title">Psychological Levels</strong>
                <ul className="sr-list psych-list">
                  {analysisData.support_resistance?.psychological_levels?.length > 0 ? (
                    analysisData.support_resistance.psychological_levels.map((p, index) => (
                      <li key={`psych-${index}`} className="sr-list-item">
                        <span className="level-price">{safeToFixed(p?.price, 5)}</span>
                        <span className="level-source">({p?.type ?? 'Unknown'})</span>
                      </li>
                    ))
                  ) : (
                    <li className="sr-list-item empty">N/A</li>
                  )}
                </ul>
              </div>

              {/* Supply Zones */}
              <div className="sr-list-section">
                <strong className="section-title supply-title">Supply Zones</strong>
                <ul className="sr-list supply-list">
                  {analysisData.support_resistance?.supply_zones?.length > 0 ? (
                    analysisData.support_resistance.supply_zones.map((zone, index) => (
                      <li key={`supply-${index}`} className="sr-list-item zone-item">
                        <span className="zone-range">{safeToFixed(zone?.bottom, 5)} - {safeToFixed(zone?.top, 5)}</span>
                      </li>
                    ))
                  ) : (
                    <li className="sr-list-item empty">N/A</li>
                  )}
                </ul>
              </div>

              {/* Demand Zones */}
              <div className="sr-list-section">
                <strong className="section-title demand-title">Demand Zones</strong>
                <ul className="sr-list demand-list">
                  {analysisData.support_resistance?.demand_zones?.length > 0 ? (
                    analysisData.support_resistance.demand_zones.map((zone, index) => (
                      <li key={`demand-${index}`} className="sr-list-item zone-item">
                        <span className="zone-range">{safeToFixed(zone?.bottom, 5)} - {safeToFixed(zone?.top, 5)}</span>
                      </li>
                    ))
                  ) : (
                    <li className="sr-list-item empty">N/A</li>
                  )}
                </ul>
              </div>
            </div>
          </div>
        </div>
      

    </div>
  )}
  </div>
  );
}

export default AnalysisPage;

