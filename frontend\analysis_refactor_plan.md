# Frontend Market Analysis Refactor Plan

## Objective
Enhance the market analysis display for better usability, modularity, and extensibility.

---

## 1. Modularize the Analysis Page

### Proposed Components
- **CurrentPriceDisplay.jsx** — Prominently shows current price, price change, and trend direction.
- **TrendCard.jsx** — Summarizes overall trend and contributing signals.
- **IndicatorChart.jsx** — Displays technical indicators (RSI, MACD, etc.) on a chart.
- **SupportResistanceCard.jsx** — Lists support/resistance levels with visual cues.
- **PatternCard.jsx** — Shows detected chart patterns.
- **StatsCard.jsx** — Presents performance metrics (win rate, profit factor, etc.).
- **HistoricalSignalsTable.jsx** — Table of recent signals and their outcomes.
- **AnalysisChart.jsx** — Interactive chart with overlays for price, signals, and levels.
- **AnalysisControls.jsx** — Symbol/timeframe selectors and indicator toggles.

---

## 2. Improve Data Fetching and State Management
- Create custom hooks: `useAnalysisData`, `useSymbols`.
- Use React Context for shared state (selected symbol, timeframe, theme).

---

## 3. Enhance User Experience
- Use skeleton loaders or spinners in each card while loading.
- Display clear error messages in relevant cards.
- Use CSS Grid/Flexbox for responsive layout.

---

## 4. Add Interactivity
- Enable zoom, pan, and indicator toggling on the main chart.
- Allow cards to be expanded/collapsed.
- Add dark/light mode toggle.

---

## 5. Example Directory Structure

```
c:/Users/<USER>/Dev/garudaalgo/frontend/
  components/
    analysis/
      CurrentPriceDisplay.jsx
      TrendCard.jsx
      IndicatorChart.jsx
      SupportResistanceCard.jsx
      PatternCard.jsx
      StatsCard.jsx
      HistoricalSignalsTable.jsx
      AnalysisChart.jsx
      AnalysisControls.jsx
  pages/
    AnalysisPage.jsx
  hooks/
    useAnalysisData.js
    useSymbols.js
  styles/
    AnalysisPage.css
    AnalysisCard.css
```

---

## 6. Refactor Steps
1. Extract UI sections into their own components.
2. Refactor data fetching into custom hooks.
3. Compose new `AnalysisPage.jsx` using modular components.
4. Update CSS for clean, responsive visuals.
5. Add interactivity and user preferences.
6. Test all features and iterate based on feedback.

---

## 7. Optional Enhancements
- Integrate advanced charting (e.g., TradingView).
- Add real-time updates (polling/WebSocket).
- Allow users to customize visible cards/indicators.
