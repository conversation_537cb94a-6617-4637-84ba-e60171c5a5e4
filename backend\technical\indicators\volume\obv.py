from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class OBVIndicator(BaseIndicator):
    """On Balance Volume (OBV) indicator."""
    
    def __init__(self, ma_period: int = 20, ma_type: str = 'sma'):
        """
        Initialize OBV indicator.
        
        Args:
            ma_period: The period for the signal line
            ma_type: The type of moving average ('sma' or 'ema')
        """
        super().__init__({
            'ma_period': ma_period,
            'ma_type': ma_type
        })
    
    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate OBV values."""
        df = data.to_dataframe()
        if df.empty:
            return {}
        
        close = df['close'].values
        volume = df['volume'].values
        ma_period = self.params['ma_period']
        ma_type = self.params['ma_type'].lower()
        
        # Calculate price changes
        price_change = pd.Series(close).diff()
        
        # Calculate OBV
        obv = np.zeros_like(volume)
        obv[0] = volume[0]
        
        for i in range(1, len(volume)):
            if price_change[i] > 0:
                obv[i] = obv[i-1] + volume[i]
            elif price_change[i] < 0:
                obv[i] = obv[i-1] - volume[i]
            else:
                obv[i] = obv[i-1]
        
        # Calculate signal line
        if ma_type == 'sma':
            signal = pd.Series(obv).rolling(window=ma_period).mean()
        else:  # ema
            signal = pd.Series(obv).ewm(span=ma_period, adjust=False).mean()
        
        # Calculate divergences
        price_high = pd.Series(close).rolling(window=5).max()
        price_low = pd.Series(close).rolling(window=5).min()
        obv_high = pd.Series(obv).rolling(window=5).max()
        obv_low = pd.Series(obv).rolling(window=5).min()
        
        bullish_div = (price_low < price_low.shift(1)) & (obv_low > obv_low.shift(1))
        bearish_div = (price_high > price_high.shift(1)) & (obv_high < obv_high.shift(1))
        
        divergence = np.where(bullish_div, 1, np.where(bearish_div, -1, 0))
        
        # Calculate momentum
        momentum = obv - pd.Series(obv).shift(ma_period)
        
        # Calculate trend
        trend = np.zeros_like(obv)
        trend[obv > signal] = 1
        trend[obv < signal] = -1
        
        # Calculate crossovers
        signal_cross = np.where(
            (obv > signal) & (pd.Series(obv).shift(1) <= signal.shift(1)), 1,
            np.where((obv < signal) & (pd.Series(obv).shift(1) >= signal.shift(1)), -1, 0)
        )
        
        # Calculate volume trend
        volume_sma = pd.Series(volume).rolling(window=ma_period).mean()
        volume_trend = np.zeros_like(volume)
        volume_trend[volume > volume_sma] = 1
        volume_trend[volume < volume_sma] = -1
        
        # Calculate strength
        strength = np.zeros_like(obv)
        obv_std = pd.Series(obv).rolling(window=ma_period).std()
        obv_mean = pd.Series(obv).rolling(window=ma_period).mean()
        
        z_score = (obv - obv_mean) / obv_std
        
        strength[(z_score >= 1) & (z_score < 2)] = 1     # Strong
        strength[z_score >= 2] = 2                        # Very Strong
        strength[(z_score <= -1) & (z_score > -2)] = -1   # Weak
        strength[z_score <= -2] = -2                      # Very Weak
        
        # Calculate acceleration
        acceleration = momentum - pd.Series(momentum).shift(1)
        
        self._values = {
            'obv': obv,
            'signal': signal.values,
            'divergence': divergence,
            'momentum': momentum.values,
            'trend': trend,
            'signal_cross': signal_cross,
            'volume_trend': volume_trend,
            'strength': strength,
            'acceleration': acceleration.values,
            'z_score': z_score.values
        }
        return self._values
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_ma_types = ['sma', 'ema']
        if self.params['ma_type'].lower() not in valid_ma_types:
            raise ValueError(f"MA type must be one of {valid_ma_types}")
        if self.params['ma_period'] < 1:
            raise ValueError("MA period must be greater than 0")
        return True 