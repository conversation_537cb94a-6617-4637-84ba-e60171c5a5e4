# Dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock

# Environment files
.env
.env.local
.env.*.local

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
*.sublime-workspace
*.sublime-project

# Build output
dist/
build/
out/
.next/

# Release folders
release*/
release-*/
release_*/

# Operating System files
.DS_Store
Thumbs.db

# Logs
logs
*.log

# Testing
coverage/
.nyc_output/

# Temporary files
tmp/
temp/

# Debug
.debug/

# Misc
.cache/
.metadata
*.pyc
__pycache__/
*.pak
# Firebase service account key - DO NOT COMMIT
backend/garuda-algo-firebase-credentials.json
