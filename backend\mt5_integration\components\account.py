"""
Account component for MT5 integration.
"""

import MetaTrader5 as mt5
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

from ..types import AccountInfo

# Configure logging
logger = logging.getLogger("MT5Integration.Account")

class AccountComponent:
    """Component for handling MT5 account operations."""
    
    def __init__(self, connection_component):
        """
        Initialize the account component.
        
        Args:
            connection_component: The connection component to use for checking connection status
        """
        self._connection = connection_component
        self._account_info_cache: Optional[AccountInfo] = None
        self._cache_duration = timedelta(seconds=5)  # Default cache duration
        
    def get_account_info(self, use_cache: bool = True) -> Optional[AccountInfo]:
        """
        Get current account information, with optional caching.

        Args:
            use_cache: Whether to use cached account info (if available and not expired)

        Returns:
            AccountInfo tuple if successful, None if failed or not connected
        """
        try:
            # Check cache first if enabled
            if use_cache and self._account_info_cache:
                if datetime.now() - self._account_info_cache.last_updated < self._cache_duration:
                    logger.debug("Returning cached account info")
                    return self._account_info_cache

            # Get fresh account info
            if not self._connection.is_connected():
                logger.warning("Cannot get account info: Not connected")
                return None

            account_info = mt5.account_info()
            if not account_info:
                logger.error("Failed to retrieve account info from MT5")
                return None

            terminal_info = mt5.terminal_info()
            if not terminal_info:
                logger.error("Failed to retrieve terminal info from MT5")
                return None

            # Create new AccountInfo instance
            current_time = datetime.now()
            info = AccountInfo(
                balance=float(account_info.balance),
                equity=float(account_info.equity),
                name=account_info.name,
                leverage=account_info.leverage,
                server=account_info.server,
                last_updated=current_time
            )

            # Update cache
            self._account_info_cache = info
            return info
        except Exception as e:
            logger.error(f"Error retrieving account info: {e}")
            return None
            
    def get_enhanced_account_info(self) -> Dict[str, Any]:
        """
        Get enhanced account information including positions count and margin details.
        
        Returns:
            Dictionary with enhanced account information
        """
        try:
            account_info = self.get_account_info()
            if not account_info:
                return {"error": "Failed to retrieve account information"}
                
            # Get additional account details directly from MT5
            mt5_account_info = mt5.account_info()
            positions_count = 0

            # Get open positions count
            try:
                positions = mt5.positions_get()
                if positions is not None:
                    positions_count = len(positions)
            except Exception as e:
                logger.warning(f"Error getting positions count: {e}")

            # Create enhanced account info
            enhanced_info = {
                "balance": account_info.balance,
                "equity": account_info.equity,
                "name": account_info.name,
                "leverage": account_info.leverage,
                "server": account_info.server,
                "positions": positions_count,
                "margin": mt5_account_info.margin if mt5_account_info else 0,
                "margin_free": mt5_account_info.margin_free if mt5_account_info else 0,
                "margin_level": mt5_account_info.margin_level if mt5_account_info else 0,
                "currency": mt5_account_info.currency if mt5_account_info else "USD",
                "trade_mode": mt5_account_info.trade_mode if mt5_account_info else 0
            }
            
            return enhanced_info
        except Exception as e:
            logger.error(f"Error getting enhanced account info: {e}")
            return {"error": str(e)}
