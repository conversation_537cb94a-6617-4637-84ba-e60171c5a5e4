@echo off
echo Preparing build environment...

:: Clean up previous builds
if exist release rmdir /s /q release
echo Previous builds cleaned.

:: Copy the backend executable
echo Copying backend executable to project root...
copy dist\garuda_backend.exe .

:: Run the electron-builder tool
echo Running electron-builder...
npx electron-builder --win --x64 --dir

:: Clean up copied files
echo Cleaning up...
del garuda_backend.exe

echo Build process complete.
