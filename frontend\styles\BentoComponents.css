/* Bento Components - Reusable styles for bento layout components */

:root {
  --bento-gap: 16px;
  --bento-radius: 12px;
  --bento-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --bento-card-bg: var(--card, #1f2937);
  --bento-card-hover: var(--card-hover, #2d3748);
  --bento-header-bg: rgba(0, 0, 0, 0.1);
  --bento-border: 1px solid var(--border, rgba(255, 255, 255, 0.1));
  --bento-transition: all 0.3s ease;
}

/* Bento Grid Container */
.bento-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-auto-rows: minmax(100px, auto);
  gap: var(--bento-gap);
  width: 100%;
  margin-top: 20px;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

/* Bento Card Base Styles */
.bento-card {
  background-color: var(--bento-card-bg);
  border-radius: var(--bento-radius);
  border: var(--bento-border);
  box-shadow: var(--bento-shadow);
  padding: 16px;
  transition: var(--bento-transition);
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.bento-card:hover {
  background-color: var(--bento-card-hover);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  border-color: var(--border, rgba(255, 255, 255, 0.2));
}

/* Add subtle gradient overlay to cards */
.bento-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4a6bff, #2cceff);
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.bento-card:hover::after {
  opacity: 1;
}

/* Card Header */
.bento-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border, rgba(255, 255, 255, 0.1));
  position: relative;
}

.bento-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: var(--text, rgba(255, 255, 255, 0.9));
  letter-spacing: 0.5px;
}

/* Card Content */
.bento-card-content {
  height: calc(100% - 40px);
  overflow-y: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
  color: var(--text, rgba(255, 255, 255, 0.9));
}

/* Card with centered content */
.bento-card-centered .bento-card-content {
  justify-content: center;
  align-items: center;
  text-align: center;
}

/* Signal Indicator */
.bento-signal {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  text-align: center;
  min-width: 80px;
}

.bento-signal.buy {
  background-color: rgba(0, 255, 0, 0.15);
  color: var(--success, #10b981);
}

.bento-signal.sell {
  background-color: rgba(255, 0, 0, 0.15);
  color: var(--error, #ef4444);
}

.bento-signal.neutral {
  background-color: rgba(255, 255, 0, 0.15);
  color: var(--warning, #e6e600);
}

/* Card Sizes */
.bento-span-3 {
  grid-column: span 3;
}

.bento-span-4 {
  grid-column: span 4;
}

.bento-span-6 {
  grid-column: span 6;
}

.bento-span-8 {
  grid-column: span 8;
}

.bento-span-12 {
  grid-column: span 12;
}

/* Card Heights */
.bento-height-1 {
  grid-row: span 1;
}

.bento-height-2 {
  grid-row: span 2;
}

.bento-height-3 {
  grid-row: span 3;
}

/* Loading States */
.bento-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 100%;
  font-size: 1.1rem;
  color: var(--text-secondary, rgba(255, 255, 255, 0.6));
}

.bento-no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 100%;
  font-size: 1.1rem;
  color: var(--text-secondary, rgba(255, 255, 255, 0.6));
  text-align: center;
  padding: 0 20px;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
  .bento-span-3 {
    grid-column: span 4;
  }

  .bento-span-4 {
    grid-column: span 6;
  }

  .bento-span-6 {
    grid-column: span 6;
  }

  .bento-height-2 {
    grid-row: span 3; /* Increase height on smaller screens */
  }
}

@media (max-width: 992px) {
  .bento-span-3,
  .bento-span-4 {
    grid-column: span 6;
  }

  .bento-span-6,
  .bento-span-8 {
    grid-column: span 12;
  }

  .bento-height-2 {
    grid-row: span 3; /* Increase height on smaller screens */
  }

  .bento-card-content {
    max-height: 400px;
    overflow-y: auto;
  }
}

@media (max-width: 768px) {
  .bento-span-3,
  .bento-span-4,
  .bento-span-6,
  .bento-span-8 {
    grid-column: span 12;
  }

  .bento-height-2 {
    grid-row: span 4; /* Further increase height on mobile */
  }

  .bento-grid {
    gap: 12px; /* Reduce gap on mobile */
  }

  .bento-card {
    padding: 12px; /* Reduce padding on mobile */
  }
}
