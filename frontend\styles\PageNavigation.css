.page-navigation {
  margin-bottom: 24px;
  padding: 0 16px;
}

.page-navigation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.page-title-section {
  flex: 1;
  min-width: 300px;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: var(--text);
  letter-spacing: -0.02em;
  line-height: 1.2;
}

.page-description {
  color: var(--text-secondary);
  font-size: 0.95rem;
  max-width: 600px;
  line-height: 1.5;
}

.page-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: var(--radius);
  border: 1px solid var(--border);
  background-color: var(--card);
  color: var(--text);
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background-color: var(--card-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.next-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: var(--radius);
  border: none;
  background-color: var(--primary);
  color: white;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.next-button:hover {
  background-color: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.back-icon, .next-icon {
  font-size: 1.1rem;
  line-height: 1;
}

/* Progress steps */
.page-navigation-progress {
  margin: 24px 0;
  padding: 16px 0;
  border-top: 1px solid var(--border);
  border-bottom: 1px solid var(--border);
  background-color: rgba(0, 0, 0, 0.02);
}

.progress-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  position: relative;
}

.progress-steps::before {
  content: '';
  position: absolute;
  top: 24px;
  left: 60px;
  right: 60px;
  height: 2px;
  background-color: var(--border);
  z-index: 0;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
}

.step-indicator {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--card);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 10px;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
  border: 2px solid var(--border);
}

.progress-step.active .step-indicator {
  background-color: var(--primary);
  color: white;
  transform: scale(1.1);
  border-color: var(--primary);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15);
}

.progress-step.completed .step-indicator {
  background-color: var(--success);
  color: white;
  border-color: var(--success);
}

.step-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
  transition: color 0.3s;
  text-align: center;
  max-width: 100px;
  line-height: 1.3;
}

.progress-step.active .step-label {
  color: var(--text);
  font-weight: 500;
}

.progress-connector {
  flex-grow: 1;
  height: 2px;
  background-color: var(--border);
  margin: 0 5px;
  position: relative;
  top: -15px;
  z-index: 0;
}

/* Page transition animations */
.page-container {
  position: relative;
  min-height: 400px;
}

.page-transition {
  opacity: 0;
  transform: translateX(20px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Responsive styles */
@media (max-width: 992px) {
  .page-navigation-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .page-actions {
    width: 100%;
    justify-content: space-between;
  }

  .progress-steps::before {
    left: 40px;
    right: 40px;
  }
}

@media (max-width: 768px) {
  .page-navigation {
    padding: 0 12px;
  }

  .step-indicator {
    width: 40px;
    height: 40px;
    font-size: 0.9rem;
  }

  .step-label {
    font-size: 0.75rem;
    max-width: 80px;
  }

  .progress-steps::before {
    top: 20px;
  }
}

@media (max-width: 576px) {
  .progress-steps {
    padding: 0 8px;
  }

  .step-indicator {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }

  .step-label {
    font-size: 0.65rem;
    max-width: 60px;
  }

  .progress-steps::before {
    left: 20px;
    right: 20px;
    top: 16px;
  }

  .back-button, .next-button {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}
