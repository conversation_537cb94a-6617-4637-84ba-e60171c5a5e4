import React, { useState, useEffect } from 'react';
import '../styles/SimpleModal.css';

const SimpleConnectionModal = ({ isOpen, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    mt5Path: '',
    accountType: 'demo',
    account: '',
    password: '',
    server: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    // Load saved settings if available
    const loadSettings = async () => {
      try {
        // Try to load from localStorage first
        const savedSettings = localStorage.getItem('mt5Settings');
        if (savedSettings) {
          const parsedSettings = JSON.parse(savedSettings);
          setFormData({
            mt5Path: parsedSettings.path || '',
            accountType: parsedSettings.accountType || 'demo',
            account: parsedSettings.login || '',
            password: parsedSettings.password || '',
            server: parsedSettings.server || ''
          });
        }
      } catch (error) {
        console.error('Failed to load settings:', error);
      }
    };

    if (isOpen) {
      loadSettings();
      setIsSubmitting(false);
    }
  }, [isOpen]);

  const handleChange = (e) => {
    const { id, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [id]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log('Form submitted');

    if (isSubmitting) {
      console.log('Already submitting, ignoring');
      return;
    }

    setIsSubmitting(true);
    console.log('Setting isSubmitting to true');

    try {
      // Submit connection data
      const connectionData = {
        login: formData.account,
        password: formData.password,
        server: formData.server,
        path: formData.mt5Path,
        accountType: formData.accountType
      };

      console.log('Connection data prepared:', {
        login: connectionData.login,
        server: connectionData.server,
        path: connectionData.path,
        accountType: connectionData.accountType
      });

      // Save connection settings first to ensure it happens
      try {
        const settingsToSave = {
          path: connectionData.path,
          login: connectionData.login,
          server: connectionData.server,
          accountType: connectionData.accountType,
          password: connectionData.password
        };
        localStorage.setItem('mt5Settings', JSON.stringify(settingsToSave));
        console.log('Settings saved to localStorage');
      } catch (saveError) {
        console.error('Failed to save settings:', saveError);
      }

      // Now submit the connection data
      console.log('Calling onSubmit with connection data');
      await onSubmit(connectionData);
      console.log('onSubmit completed');
    } catch (error) {
      console.error('Connection error:', error);
      alert('Connection error: ' + (error.message || 'Unknown error'));
    } finally {
      console.log('Setting isSubmitting to false');
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="simple-modal-backdrop">
      <div className="simple-modal">
        <div className="simple-modal-header">
          <h2>Connect to MT5</h2>
          <button className="simple-modal-close" onClick={onClose}>&times;</button>
        </div>
        <div className="simple-modal-body">
          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="mt5Path">MT5 Terminal Path</label>
              <input
                type="text"
                id="mt5Path"
                value={formData.mt5Path}
                onChange={handleChange}
                placeholder="C:\\Program Files\\MetaTrader 5\\terminal64.exe"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="accountType">Account Type</label>
              <select
                id="accountType"
                value={formData.accountType}
                onChange={handleChange}
              >
                <option value="demo">Demo Account</option>
                <option value="real">Real Account</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="account">Login ID</label>
              <input
                type="text"
                id="account"
                value={formData.account}
                onChange={handleChange}
                placeholder="MT5 Login ID"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="server">Server</label>
              <input
                type="text"
                id="server"
                value={formData.server}
                onChange={handleChange}
                placeholder="MT5 Server"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="password">Password</label>
              <input
                type="password"
                id="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="MT5 Password"
                required
              />
            </div>

            <div className="simple-modal-footer">
              <button
                type="button"
                className="simple-button secondary"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="simple-button primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Connecting...' : 'Connect'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default SimpleConnectionModal;
