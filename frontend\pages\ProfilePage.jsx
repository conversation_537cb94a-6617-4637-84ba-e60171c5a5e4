import React, { useEffect } from 'react';
import '../styles/Pages.css'; // Assuming a generic Page.css for basic styling

const ProfilePage = ({ licenseInfo }) => {
  useEffect(() => {
    console.log("ProfilePage received licenseInfo:", licenseInfo);
  }, [licenseInfo]);

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    } catch (e) {
      console.error("Error formatting date:", dateString, e);
      return 'Invalid Date';
    }
  };

  return (
    <div className="page profile-page"> {/* Added .profile-page class for specific styling */}
      <h2 className="page-title">User Profile</h2>
      <div className="page-content">
        {licenseInfo && (licenseInfo.userName || licenseInfo.planType || licenseInfo.planEndDate || licenseInfo.status) ? (
          <div className="profile-details-card content-card"> {/* Added .content-card for consistent styling */}
            <h3>Account License Information</h3>
            <div className="profile-detail-item">
              <span className="detail-label">Username:</span>
              <span className="detail-value">{licenseInfo.userName || 'N/A'}</span>
            </div>
            {/* Email is not currently in licenseInfo, would need backend update if desired */}
            {/* <div className="profile-detail-item">
              <span className="detail-label">Email:</span>
              <span className="detail-value">{licenseInfo.email || 'N/A'}</span>
            </div> */}
            <div className="profile-detail-item">
              <span className="detail-label">Plan Type:</span>
              <span className="detail-value">{licenseInfo.planType || 'N/A'}</span>
            </div>
            <div className="profile-detail-item">
              <span className="detail-label">Plan End Date:</span>
              <span className="detail-value">{formatDate(licenseInfo.planEndDate)}</span>
            </div>
            <div className="profile-detail-item">
              <span className="detail-label">License Status:</span>
              <span className={`detail-value status-${(licenseInfo.status || 'unknown').toLowerCase()}`}>
                {licenseInfo.status ? licenseInfo.status.charAt(0).toUpperCase() + licenseInfo.status.slice(1) : 'N/A'}
              </span>
            </div>
          </div>
        ) : (
          <div className="content-card">
            <p>No license information available. Please connect to your MT5 account to view your license details. If you have connected and still see this message, please ensure your license is active.</p>
          </div>
        )}

        {/* Placeholder for future actions */}
        {/* <div className="profile-actions content-card" style={{ marginTop: '20px' }}>
          <h3>Actions</h3>
          <button className="button" disabled>Edit Profile (Coming Soon)</button>
          <button className="button" disabled>Change Password (Coming Soon)</button>
        </div> */}
      </div>
    </div>
  );
};

export default ProfilePage;
