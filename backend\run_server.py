import sys
from pathlib import Path

# Add project root to Python path
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from backend.app import app

if __name__ == "__main__":
    # Run the Flask development server
    # debug=True enables auto-reloading and provides detailed error pages
    # host='0.0.0.0' makes it accessible from the network (use '127.0.0.1' for local only)
    # port=5001 is used to avoid potential conflicts with port 5000
    app.run(host='127.0.0.1', port=5001, debug=True)
