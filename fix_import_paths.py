#!/usr/bin/env python
# A script to fix all import paths in the technical indicators
import os
import glob

def fix_imports():
    """Fix imports in all Python files under backend directory"""
    # Find all Python files
    python_files = glob.glob('backend/**/*.py', recursive=True)
    
    print(f"Found {len(python_files)} Python files.")
    
    # Count of files changed
    files_changed = 0
    
    for filepath in python_files:
        with open(filepath, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # Look for incorrect imports
        if 'from technical.' in content:
            # Store old content to check if we made changes
            old_content = content
            
            # Fix the import paths
            content = content.replace('from technical.', 'from backend.technical.')
            
            # Don't write if no changes were made
            if content != old_content:
                with open(filepath, 'w', encoding='utf-8') as file:
                    file.write(content)
                print(f"✓ Fixed imports in {filepath}")
                files_changed += 1
    
    return files_changed

if __name__ == "__main__":
    print("Starting to fix import paths...")
    count = fix_imports()
    print(f"Finished fixing imports in {count} files.")
