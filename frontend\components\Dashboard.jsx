import React, { useState, useEffect } from 'react';
import '../styles/Dashboard.css';
import { useNotification } from './Notification';

const Dashboard = ({ accountInfo = {}, currentPage }) => {
  const [contextActions, setContextActions] = useState([]);
  const [accountCurrency, setAccountCurrency] = useState('USD');
  const notify = useNotification();

  // Currency symbol mapping
  const getCurrencySymbol = (currency) => {
    const currencySymbols = {
      'USD': '$',
      'EUR': '€',
      'GBP': '£',
      'JPY': '¥',
      'CHF': 'CHF',
      'CAD': 'C$',
      'AUD': 'A$',
      'NZD': 'NZ$',
      'SEK': 'kr',
      'NOK': 'kr',
      'DKK': 'kr',
      'PLN': 'zł',
      'CZK': 'Kč',
      'HUF': 'Ft',
      'RUB': '₽',
      'CNY': '¥',
      'INR': '₹',
      'BRL': 'R$',
      'MXN': '$',
      'ZAR': 'R',
      'SGD': 'S$',
      'HKD': 'HK$',
      'THB': '฿',
      'TRY': '₺',
      'KRW': '₩',
      // ASEAN Countries
      'IDR': 'Rp',     // Indonesia
      'MYR': 'RM',     // Malaysia
      'PHP': '₱',      // Philippines
      'VND': '₫',      // Vietnam
      'LAK': '₭',      // Laos
      'KHR': '៛',      // Cambodia
      'MMK': 'K',      // Myanmar
      'BND': 'B$'      // Brunei
      // Note: Singapore (SGD) and Thailand (THB) already included above
    };
    return currencySymbols[currency] || currency;
  };

  // Define context-specific quick actions based on current page
  useEffect(() => {
    const pageActions = {
      'analysis': [
        { label: 'Save Analysis', icon: 'save', action: () => notify.success('Action', 'Analysis saved') }
      ],
      'recommendation': [
        { label: 'Execute Top Signal', icon: 'execute', action: () => notify.success('Action', 'Preparing to execute top signal') }
      ],
      'execution': [
        { label: 'Market Order', icon: 'market', action: () => notify.info('Action', 'Creating market order...') },
        { label: 'Limit Order', icon: 'limit', action: () => notify.info('Action', 'Creating limit order...') }
      ],
      'autonomous': [
        { label: 'Start Bot', icon: 'start', action: () => notify.success('Action', 'Trading bot started') },
        { label: 'Stop Bot', icon: 'stop', action: () => notify.warning('Action', 'Trading bot stopped') }
      ],
      'history': [
        { label: 'Export Data', icon: 'export', action: () => notify.info('Action', 'Exporting trading history...') },
        { label: 'Generate Report', icon: 'report', action: () => notify.info('Action', 'Generating performance report...') }
      ]
    };

    setContextActions(pageActions[currentPage] || []);
  }, [currentPage, notify]);

  // Fetch account currency
  useEffect(() => {
    console.log('Dashboard - Currency fetch useEffect triggered'); // Debug log
    const fetchAccountCurrency = async () => {
      try {
        console.log('Dashboard - Starting currency fetch...'); // Debug log
        const accountResponse = await fetch('http://localhost:5001/api/market/account');
        console.log('Dashboard - Account response status:', accountResponse.status); // Debug log
        if (accountResponse.ok) {
          const accountData = await accountResponse.json();
          console.log('Dashboard - Account data received:', accountData); // Debug log
          if (accountData.currency) {
            console.log('Dashboard - Setting account currency to:', accountData.currency); // Debug log
            setAccountCurrency(accountData.currency);
          } else {
            console.warn('Dashboard - No currency field in account data:', accountData);
          }
        } else {
          console.error('Dashboard - Failed to fetch account data, status:', accountResponse.status);
          const errorText = await accountResponse.text();
          console.error('Dashboard - Error response:', errorText);
        }
      } catch (error) {
        console.error('Dashboard - Error fetching account currency:', error);
      }
    };

    fetchAccountCurrency();
  }, []);

  const { balance = 0, equity = 0, profit = 0, positions = 0, accountType = 'Demo' } = accountInfo;

  const refreshAccountData = () => {
    notify.info('Refreshing', 'Updating account information...');
    // Trigger a custom event that App.jsx can listen for
    window.dispatchEvent(new CustomEvent('dashboard:refreshData'));
  };

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <h2 className="dashboard-title">Trading Dashboard</h2>
        <div className="context-actions">
          <button
            className="context-action-button"
            onClick={refreshAccountData}
          >
            <span className="context-action-icon refresh-icon"></span>
            <span className="context-action-label">Refresh Data</span>
          </button>
          {contextActions.length > 0 && contextActions.map((action, index) => (
            <button
              key={index}
              className="context-action-button"
              onClick={action.action}
            >
              <span className={`context-action-icon ${action.icon}-icon`}></span>
              <span className="context-action-label">{action.label}</span>
            </button>
          ))}
        </div>
      </div>

      <div className="dashboard">
        <div className="dashboard-card balance-card">
          <div className="card-icon balance-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
              <path d="M21 18v1c0 1.1-.9 2-2 2H5c-1.11 0-2-.9-2-2V5c0-1.1.89-2 2-2h14c1.1 0 2 .9 2 2v1h-9c-1.11 0-2 .9-2 2v8c0 1.1.89 2 2 2h9zm-9-2h10V8H12v8zm4-2.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z" />
            </svg>
          </div>
          <div className="card-content">
            <h3>Account Balance</h3>
            <div className="value">{getCurrencySymbol(accountCurrency)}{balance.toFixed(2)}</div>
            <div className="label">
              <span>{accountType} Account</span>
              <svg className="label-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-4h2v2h-2zm0-10h2v8h-2z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="dashboard-card equity-card">
          <div className="card-icon equity-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
              <path d="M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z" />
            </svg>
          </div>
          <div className="card-content">
            <h3>Equity</h3>
            <div className="value">{getCurrencySymbol(accountCurrency)}{equity.toFixed(2)}</div>
            <div className="label">Current equity</div>
          </div>
        </div>

        <div className="dashboard-card profit-card">
          <div className="card-icon profit-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
              {profit >= 0 ? (
                <path d="M7 14l5-5 5 5H7z" />
              ) : (
                <path d="M7 10l5 5 5-5H7z" />
              )}
            </svg>
          </div>
          <div className="card-content">
            <h3>Profit/Loss</h3>
            <div className={`value ${profit >= 0 ? 'positive' : 'negative'}`}>
              {profit >= 0 ? '+' : '-'}{getCurrencySymbol(accountCurrency)}{Math.abs(profit).toFixed(2)}
            </div>
            <div className="label">Daily P/L</div>
            <div className="trend-indicator">
              <div className={`trend-line ${profit >= 0 ? 'up' : 'down'}`}></div>
            </div>
          </div>
        </div>

        <div className="dashboard-card positions-card">
          <div className="card-icon positions-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14h-2V9h-2V7h4v10z" />
            </svg>
          </div>
          <div className="card-content">
            <h3>Active Positions</h3>
            <div className="value">{positions}</div>
            <div className="label">Open trades</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;