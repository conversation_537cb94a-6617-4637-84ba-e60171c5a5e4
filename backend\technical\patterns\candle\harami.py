from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class HaramiPatternIndicator(BaseIndicator):
    """Harami candlestick pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - body_ratio: Parameter description (default: 0.3)
        """
        default_params = {
            "body_ratio": 0.3,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)

    
    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        open_price = df['open'].values
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        body_ratio = self.params['body_ratio']
        
        # Calculate body sizes
        body_size = np.abs(close - open_price)
        
        # Calculate previous candle's data
        prev_open = np.zeros_like(open_price)
        prev_open[1:] = open_price[:-1]
        prev_close = np.zeros_like(close)
        prev_close[1:] = close[:-1]
        prev_high = np.zeros_like(high)
        prev_high[1:] = high[:-1]
        prev_low = np.zeros_like(low)
        prev_low[1:] = low[:-1]
        prev_body_size = np.abs(prev_close - prev_open)
        
        # Identify bullish harami patterns
        is_bullish_harami = (
            (prev_close < prev_open) &  # First candle is bearish
            (close > open_price) &  # Second candle is bullish
            (open_price > prev_close) &  # Second open above first close
            (close < prev_open) &  # Second close below first open
            (body_size <= (prev_body_size * body_ratio))  # Second body smaller than first
        )
        
        # Identify bearish harami patterns
        is_bearish_harami = (
            (prev_close > prev_open) &  # First candle is bullish
            (close < open_price) &  # Second candle is bearish
            (open_price < prev_close) &  # Second open below first close
            (close > prev_open) &  # Second close above first open
            (body_size <= (prev_body_size * body_ratio))  # Second body smaller than first
        )
        
        # Classify pattern types
        pattern_type = np.zeros_like(close)
        pattern_type[is_bullish_harami] = 1
        pattern_type[is_bearish_harami] = -1
        
        # Calculate pattern strength
        strength = np.zeros_like(close)
        for i in range(1, len(close)):
            if is_bullish_harami[i] or is_bearish_harami[i]:
                # Calculate the overlap between the two candles
                overlap = min(close[i], prev_close[i]) - max(open_price[i], prev_open[i])
                strength[i] = overlap / prev_body_size[i]  # Normalize by first candle's body
        
        # Calculate trend context
        trend = np.zeros_like(close)
        for i in range(1, len(close)):
            if i >= 20:  # Use 20-period SMA for trend
                sma = np.mean(close[i-20:i])
                trend[i] = 1 if close[i] > sma else -1
        
        # Calculate pattern reliability
        reliability = np.zeros_like(close)
        for i in range(1, len(close)):
            if is_bullish_harami[i] or is_bearish_harami[i]:
                # Check if price moved in the expected direction
                if i < len(close)-1:
                    future_return = (close[i+1] - close[i]) / close[i]
                    if is_bullish_harami[i]:
                        reliability[i] = 1 if future_return > 0 else -1
                    else:  # Bearish harami
                        reliability[i] = 1 if future_return < 0 else -1
        
        return {
            'is_bullish_harami': is_bullish_harami.astype(int),
            'is_bearish_harami': is_bearish_harami.astype(int),
            'pattern_type': pattern_type,
            'strength': strength,
            'trend': trend,
            'reliability': reliability,
            'body_size': body_size,
            'prev_body_size': prev_body_size
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if not 0 < self.params['body_ratio'] < 1:
            raise ValueError("Body ratio must be between 0 and 1")
        return True 