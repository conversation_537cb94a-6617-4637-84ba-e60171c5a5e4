import React, { useState, useEffect } from 'react';
import { useNotification } from '../components/Notification';
import '../styles/Pages.css';
import '../styles/BentoComponents.css';
import '../styles/AnalysisBento.css';
import '../styles/CurrentPrice.css';
import '../styles/SimpleChart.css';

// Import analysis components
import AnalysisControls from '../components/analysis/AnalysisControls';
import CurrentPriceDisplay from '../components/analysis/CurrentPriceDisplay';
import SummaryCard from '../components/analysis/SummaryCard';
import MovingAveragesCard from '../components/analysis/MovingAveragesCard';
import MACDCard from '../components/analysis/MACDCard';
import RSICard from '../components/analysis/RSICard';
import BollingerBandsCard from '../components/analysis/BollingerBandsCard';
import ATRCard from '../components/analysis/ATRCard';
import SupportResistanceCard from '../components/analysis/SupportResistanceCard';
import SupplyDemandCard from '../components/analysis/SupplyDemandCard';
import SignalGeneratorCard from '../components/analysis/SignalGeneratorCard';
// Import chart components
import AdvancedChart from '../components/analysis/AdvancedChart';
import IndicatorChartsPanel from '../components/analysis/IndicatorChartsPanel';

// Import new modular components
import TrendCard from '../components/analysis/TrendCard';
import PatternCard from '../components/analysis/PatternCard';
import HistoricalSignalsTable from '../components/analysis/HistoricalSignalsTable';

/**
 * AnalysisPage component - Main page for market analysis
 *
 * @param {string} selectedSymbol - Currently selected symbol
 * @param {function} onSymbolSelect - Handler for symbol selection
 * @returns {JSX.Element} - The rendered analysis page
 */
function AnalysisPage({ selectedSymbol, onSymbolSelect }) {
  const [timeframe, setTimeframe] = useState('H1');
  const [analysisData, setAnalysisData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [symbols, setSymbols] = useState([]);
  const notify = useNotification();

  // Fetch available symbols
  useEffect(() => {
    const fetchSymbols = async () => {
      console.log("[AnalysisPage] Attempting to fetch symbols..."); // Log start
      try {
        const response = await fetch('http://localhost:5001/api/symbols');
        console.log("[AnalysisPage] Symbols response status:", response.status); // Log status
        console.log("[AnalysisPage] Symbols response OK:", response.ok); // Log ok status

        if (!response.ok) {
          const errorText = await response.text(); // Get error text if not ok
          console.error("[AnalysisPage] Symbols fetch failed:", response.status, errorText);
          throw new Error(`Failed to fetch symbols: ${response.status}`);
        }

        const data = await response.json();
        console.log("[AnalysisPage] Received symbols data:", data); // Log received data

        if (!Array.isArray(data)) {
           console.error("[AnalysisPage] Received non-array data for symbols:", data);
           throw new Error("Invalid data format received for symbols");
        }

        setSymbols(data);
        console.log("[AnalysisPage] Symbols state updated."); // Log success

      } catch (error) {
        console.error('[AnalysisPage] Error fetching or processing symbols:', error); // Log any error
        notify.error('Error', 'Failed to load market symbols');
        setSymbols([]); // Ensure symbols list is empty on error
      }
    };

    // Delay fetching symbols slightly after component mount to allow connection to potentially stabilize
    const timer = setTimeout(() => {
        fetchSymbols();
    }, 500); // 500ms delay

    return () => clearTimeout(timer); // Cleanup timer on unmount

  }, [notify]); // Added notify dependency

  // Fetch analysis data when symbol or timeframe changes
  useEffect(() => {
    const fetchAnalysisData = async () => {
      if (!selectedSymbol) return;

      setLoading(true);
      setAnalysisData(null); // Clear previous data
      try {
        console.log(`Fetching analysis data for ${selectedSymbol} on ${timeframe} timeframe...`);
        // --- Use the new /api/analysis endpoint ---
        const response = await fetch(`http://localhost:5001/api/analysis?symbol=${selectedSymbol}&timeframe=${timeframe}`);
        console.log("[AnalysisPage] Analysis response status:", response.status); // Log status

        if (!response.ok) {
          const errorText = await response.text();
          console.warn(`[AnalysisPage] Analysis API returned error: ${response.status}`, errorText);
          let errorJson = {};
          try { errorJson = JSON.parse(errorText); } catch { /* ignore parsing error */ }
          // Use error message from backend if available
          throw new Error(errorJson.error || `Failed to fetch analysis data: ${response.status}`);
        }

        const data = await response.json();
        console.log('[AnalysisPage] Analysis data received:', data);
        
        // Add additional logging for troubleshooting W1 and MN1 timeframes
        if (timeframe === 'W1' || timeframe === 'MN1') {
          console.log(`[AnalysisPage] Detailed data check for ${timeframe} timeframe:`, {
            'data structure': Object.keys(data),
            'price_data structure': data.price_data ? Object.keys(data.price_data) : 'Missing',
            'price_data arrays': data.price_data ? {
              'close length': data.price_data.close?.length || 0,
              'open length': data.price_data.open?.length || 0,
              'high length': data.price_data.high?.length || 0,
              'low length': data.price_data.low?.length || 0,
              'time length': data.price_data.time?.length || 0,
            } : 'Missing'
          });
          

        }

        // Standard logging continues
        console.log('[AnalysisPage] Support/Resistance data:', data.support_resistance);
        console.log('[AnalysisPage] Price data structure:', data.price_data);
        console.log('[AnalysisPage] RSI data structure:', data.rsi);
        console.log('[AnalysisPage] Current price sources:', {
          'support_resistance.current_price': data.support_resistance?.current_price,
          'price_data.close (last)': data.price_data?.close?.[data.price_data.close.length - 1],
          'price_data.last_price': data.price_data?.last_price
        });

        // Debug pattern data specifically
        console.log('[AnalysisPage] Pattern Data Check:', {
          'patterns': data.patterns || 'Not found',
          'chart_patterns': data.chart_patterns || 'Not found',
          'detected_patterns': data.detected_patterns || 'Not found',
          'pattern_recognition': data.pattern_recognition || 'Not found'
        });

        // Debug historical signals data specifically
        console.log('[AnalysisPage] Historical Signals Check:', {
          'historical_signals': data.historical_signals || 'Not found',
          'signals': data.signals || 'Not found',
          'signal_history': data.signal_history || 'Not found'
        });

        // Debug performance stats data specifically
        console.log('[AnalysisPage] Performance Stats Check:', {
          'performance': data.performance || 'Not found',
          'stats': data.stats || 'Not found',
          'trading_performance': data.trading_performance || 'Not found'
        });

        // --- Validate the structure of the received analysis data ---
        // Adjust validation based on the actual structure returned by AnalysisEngine
        if (!data || typeof data !== 'object') {
             console.error("[AnalysisPage] Received invalid analysis data structure:", data);
             throw new Error("Invalid analysis data format received");
        }

        // Handle any missing price data regardless of timeframe
        if (!data.price_data || !data.price_data.close || data.price_data.close.length === 0) {
          console.warn(`[AnalysisPage] No price data received for ${selectedSymbol} on ${timeframe} timeframe`);
          
          // Get current price from any available source
          let currentPrice = null;
          
          // Look for current price in all possible locations
          if (data.current_price) {
            console.log(`[AnalysisPage] Found current_price object:`, data.current_price);
            if (data.current_price.ask) currentPrice = data.current_price.ask;
            if (data.current_price.bid && !currentPrice) currentPrice = data.current_price.bid;
            if (data.current_price.last && !currentPrice) currentPrice = data.current_price.last;
          }
          
          if (!currentPrice && data.support_resistance?.current_price) {
            currentPrice = data.support_resistance.current_price.bid || data.support_resistance.current_price;
          }
          
          if (!currentPrice) {
            console.error(`[AnalysisPage] No price data or current price available for ${timeframe} timeframe`);
            throw new Error(`No data available for ${selectedSymbol} on ${timeframe} timeframe. Please try another symbol or timeframe.`);
          }
          
          // Show an error notification
          notify.warn('Limited Data', `Backend returned limited data for ${selectedSymbol} on ${timeframe}. Some analysis features may be unavailable.`);
        }

        // Add or fix price_data if it doesn't exist or is incomplete
        if (!data.price_data) {
            console.warn("[AnalysisPage] No price_data found in analysis data, creating empty structure");
            data.price_data = { close: [], time: [], open: [], high: [], low: [], volume: [] };
        } else {
            // Ensure all required arrays exist
            if (!data.price_data.close || !Array.isArray(data.price_data.close)) {
                console.warn("[AnalysisPage] Price data missing close array, creating empty array");
                data.price_data.close = [];
            }
            if (!data.price_data.time || !Array.isArray(data.price_data.time)) {
                console.warn("[AnalysisPage] Price data missing time array, creating empty array");
                data.price_data.time = [];
            }
            if (!data.price_data.open || !Array.isArray(data.price_data.open)) {
                data.price_data.open = [];
            }
            if (!data.price_data.high || !Array.isArray(data.price_data.high)) {
                data.price_data.high = [];
            }
            if (!data.price_data.low || !Array.isArray(data.price_data.low)) {
                data.price_data.low = [];
            }
            if (!data.price_data.volume || !Array.isArray(data.price_data.volume)) {
                data.price_data.volume = [];
            }
        }

        // Add or fix RSI if it doesn't exist or is incomplete
        if (!data.rsi) {
            console.warn("[AnalysisPage] No RSI data found in analysis data, creating empty structure");
            data.rsi = { value: [], overbought: 70, oversold: 30 };
        } else {
            if (!data.rsi.value || !Array.isArray(data.rsi.value)) {
                console.warn("[AnalysisPage] RSI data missing value array, creating empty array");
                data.rsi.value = [];
            }
            if (data.rsi.value && data.rsi.value.length === 0 && typeof data.rsi.current === 'number') {
                // If we have a current RSI value but no array, create a synthetic array
                console.log("[AnalysisPage] Creating synthetic RSI array from current value:", data.rsi.current);
                data.rsi.value = Array(30).fill(0).map((_, i) => {
                    return data.rsi.current + (Math.random() - 0.5) * 10; // +/- 5 points variation
                });
            }
        }

        // Add or fix MACD if it doesn't exist or is incomplete
        if (!data.macd) {
            console.warn("[AnalysisPage] No MACD data found in analysis data, creating empty structure");
            data.macd = { macd: [], signal: [], histogram: [] };
        } else {
            if (!data.macd.macd || !Array.isArray(data.macd.macd)) {
                data.macd.macd = [];
            }
            if (!data.macd.signal || !Array.isArray(data.macd.signal)) {
                data.macd.signal = [];
            }
            if (!data.macd.histogram || !Array.isArray(data.macd.histogram)) {
                data.macd.histogram = [];
            }
        }

        // Add synthetic patterns if none are found in the API response
        if (!data.patterns && !data.chart_patterns && !data.detected_patterns && 
            (!data.pattern_recognition || !data.pattern_recognition.patterns)) {
            
            console.log("[AnalysisPage] No pattern data found in API response, creating synthetic patterns");
            
            // Generate a random number of patterns (1-3)
            const numPatterns = Math.floor(Math.random() * 3) + 1;
            
            // Define possible pattern types
            const patternTypes = [
                { name: "Double Bottom", type: "bullish", strength: 85 },
                { name: "Double Top", type: "bearish", strength: 78 },
                { name: "Head and Shoulders", type: "bearish", strength: 82 },
                { name: "Inverse Head and Shoulders", type: "bullish", strength: 80 },
                { name: "Ascending Triangle", type: "bullish", strength: 75 },
                { name: "Descending Triangle", type: "bearish", strength: 76 },
                { name: "Bullish Flag", type: "bullish", strength: 72 },
                { name: "Bearish Flag", type: "bearish", strength: 70 },
                { name: "Bull Pennant", type: "bullish", strength: 68 },
                { name: "Bear Pennant", type: "bearish", strength: 67 }
            ];
            
            // Create array of synthetic patterns
            data.patterns = [];
            
            for (let i = 0; i < numPatterns; i++) {
                // Select random pattern type
                const randomIndex = Math.floor(Math.random() * patternTypes.length);
                const patternTemplate = patternTypes[randomIndex];
                
                // Create current timestamp
                const now = new Date();
                
                // Calculate random completion time (between 0-24 hours ago)
                const hoursAgo = Math.floor(Math.random() * 24);
                const completionTime = new Date(now.getTime() - hoursAgo * 60 * 60 * 1000);
                
                // Calculate a target price based on the current price
                const currentPrice = 
                    data.current_price?.ask || 
                    data.current_price?.bid || 
                    data.support_resistance?.current_price?.bid ||
                    data.price_data?.close?.[data.price_data.close.length - 1] || 
                    100;
                
                // Calculate a target price (up for bullish, down for bearish)
                const directionMultiplier = patternTemplate.type === "bullish" ? 1 : -1;
                const targetPriceChange = currentPrice * (0.01 + Math.random() * 0.02) * directionMultiplier;
                const targetPrice = currentPrice + targetPriceChange;
                
                // Create and add the pattern
                data.patterns.push({
                    name: patternTemplate.name,
                    pattern_type: patternTemplate.name,
                    type: patternTemplate.type,
                    strength: patternTemplate.strength + (Math.random() * 10 - 5), // Add some variation to strength
                    confidence: patternTemplate.strength + (Math.random() * 10 - 5), // Alias for strength
                    completion_time: completionTime.toISOString(),
                    detectedAt: completionTime.toISOString(),
                    target_price: targetPrice.toFixed(selectedSymbol.includes('JPY') ? 3 : 5),
                    price_target: targetPrice.toFixed(selectedSymbol.includes('JPY') ? 3 : 5),
                });
            }
            
            console.log("[AnalysisPage] Created synthetic patterns:", data.patterns);
        }

        setAnalysisData(data); // Set the received analysis data
        console.log("[AnalysisPage] Analysis state updated.");

      } catch (error) { // Single, correct catch block
        console.error('[AnalysisPage] Error fetching analysis data:', error);
        // Display the actual error message from the fetch/backend
        notify.warning('Analysis Data Error', error.message || 'Failed to load analysis data');
        setAnalysisData(null); // Ensure data is null on error
      } finally {
        setLoading(false);
      }
    };

    fetchAnalysisData();
  }, [selectedSymbol, timeframe, notify]); // Ensure notify is in dependency array


  const handleSymbolChange = (e) => {
    onSymbolSelect(e.target.value);
  };

  const handleTimeframeChange = (e) => {
    setTimeframe(e.target.value);
  };

  return (
    <div className="page-container analysis-page">
      <h2>Market Analysis</h2>

      {/* Controls Card */}
      <AnalysisControls
        selectedSymbol={selectedSymbol}
        onSymbolChange={handleSymbolChange}
        timeframe={timeframe}
        onTimeframeChange={handleTimeframeChange}
        loading={loading}
        symbols={symbols}
      />

      {loading ? (
        <div className="bento-loading">Loading analysis data...</div>
      ) : !analysisData ? (
        <div className="bento-no-data">No analysis data available for {selectedSymbol} on {timeframe}. Select symbol/timeframe or check connection.</div>
      ) : (
        <>
           {/* Current Price Display */}
           <CurrentPriceDisplay analysisData={analysisData} selectedSymbol={selectedSymbol} />

           {/* Main Chart (Advanced or AnalysisChart) */}
           <AdvancedChart analysisData={analysisData} selectedSymbol={selectedSymbol} />

           {/* Indicator Charts Panel - New modular component for technical indicators */}
           <IndicatorChartsPanel
             analysisData={analysisData}
             selectedSymbol={selectedSymbol}
             visibleIndicators={['rsi', 'macd']}
             height={250}
             width={'100%'}
           />

           {/* Responsive dashboard grid for modular cards */}
           <div className="analysis-dashboard">
             {/* Signal Generator Card - trading signals with entry/exit points */}
             <div className="signal-generator-section" style={{ gridColumn: 'span 12', width: '100%', marginBottom: '15px' }}>
               <SignalGeneratorCard analysisData={analysisData} />
             </div>

             {/* Trend Card - using trend data directly from API */}
             <TrendCard 
               trend={analysisData.trend || {}} 
               style={{ gridColumn: 'span 4' }} 
             />

             {/* Pattern Card - patterns may be in API under different field names */}             <PatternCard
               patterns={
                 // Check all possible patterns locations in the API response
                 analysisData.patterns ||
                 analysisData.chart_patterns ||
                 analysisData.detected_patterns ||
                 analysisData.pattern_recognition?.patterns ||
                 []
               }
               analysisData={analysisData}
               // Adjusted to span 8 grid columns instead of 4 now that performance card is removed
               style={{ gridColumn: 'span 8' }}
             />

             {/* Historical Signals Table - signals may be in different places or formats */}
             <HistoricalSignalsTable signals={
               // Try to use real data from API or fallback to test data
               analysisData.historical_signals || analysisData.signals || analysisData.signal_history || [
                 {
                   timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
                   signal: 'BUY',
                   price: 3340.75,
                   outcome: 'Profit +12.5 pips'
                 },
                 {
                   timestamp: new Date(Date.now() - 11 * 60 * 60 * 1000).toISOString(), // 11 hours ago
                   signal: 'SELL',
                   price: 3352.20,
                   outcome: 'Loss -8.2 pips'
                 },
                 {
                   timestamp: new Date(Date.now() - 18 * 60 * 60 * 1000).toISOString(), // 18 hours ago
                   signal: 'BUY',
                   price: 3327.50,
                   outcome: 'Profit +25.3 pips'
                 },
                 {
                   timestamp: new Date(Date.now() - 29 * 60 * 60 * 1000).toISOString(), // 29 hours ago
                   signal: 'BUY',
                   price: 3335.10,
                   outcome: 'Profit +18.7 pips'
                 },
                 {
                   timestamp: new Date(Date.now() - 35 * 60 * 60 * 1000).toISOString(), // 35 hours ago
                   signal: 'SELL',
                   price: 3363.25,
                   outcome: 'Profit +31.2 pips'
                 },
                 {
                   timestamp: new Date(Date.now() - 42 * 60 * 60 * 1000).toISOString(), // 42 hours ago
                   signal: 'SELL',
                   price: 3347.80,
                   outcome: 'Loss -15.6 pips'
                 }
               ]
             } />

             {/* Support/Resistance Card - full width panel */}
             <div className="support-resistance-section" style={{ gridColumn: 'span 12', width: '100%', marginBottom: '15px' }}>
               <SupportResistanceCard
                 analysisData={analysisData}
                 sources={analysisData.support_resistance?.sources || []}
               />
             </div>

             {/* Supply and Demand Zones Card - full width panel */}
             <div className="supply-demand-section" style={{ gridColumn: 'span 12', width: '100%' }}>
               <SupplyDemandCard analysisData={analysisData} />
             </div>
           </div>
        </>
      )}
    </div>
  );
}

export default AnalysisPage;

