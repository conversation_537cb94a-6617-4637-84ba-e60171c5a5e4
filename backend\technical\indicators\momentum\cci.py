from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class CCIIndicator(BaseIndicator):
    """Commodity Channel Index indicator."""
    
    def __init__(self, period: int = 20, constant: float = 0.015):
        """
        Initialize CCI indicator.
        
        Args:
            period: The period for calculating CCI
            constant: The scaling constant (typically 0.015)
        """
        super().__init__({
            'period': period,
            'constant': constant
        })
    
    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate CCI values."""
        df = data.to_dataframe()
        if df.empty:
            return {}
        
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        period = self.params['period']
        constant = self.params['constant']
        
        # Calculate typical price
        typical_price = (high + low + close) / 3
        
        # Calculate SMA of typical price
        tp_sma = pd.Series(typical_price).rolling(window=period).mean()
        
        # Calculate Mean Deviation
        mean_dev = pd.Series(typical_price).rolling(window=period).apply(
            lambda x: np.mean(np.abs(x - np.mean(x)))
        )
        
        # Calculate CCI
        cci = (typical_price - tp_sma) / (constant * mean_dev)
        
        # Calculate overbought/oversold conditions
        overbought = np.zeros_like(cci)
        overbought[cci >= 100] = 1
        
        oversold = np.zeros_like(cci)
        oversold[cci <= -100] = 1
        
        # Calculate extreme conditions
        extreme_overbought = np.zeros_like(cci)
        extreme_overbought[cci >= 200] = 1
        
        extreme_oversold = np.zeros_like(cci)
        extreme_oversold[cci <= -200] = 1
        
        # Calculate divergences
        price_high = pd.Series(typical_price).rolling(window=5).max()
        price_low = pd.Series(typical_price).rolling(window=5).min()
        cci_high = pd.Series(cci).rolling(window=5).max()
        cci_low = pd.Series(cci).rolling(window=5).min()
        
        bullish_div = (price_low < price_low.shift(1)) & (cci_low > cci_low.shift(1))
        bearish_div = (price_high > price_high.shift(1)) & (cci_high < cci_high.shift(1))
        
        divergence = np.where(bullish_div, 1, np.where(bearish_div, -1, 0))
        
        # Calculate momentum
        momentum = cci - pd.Series(cci).shift(period)
        
        # Calculate trend
        trend = np.zeros_like(cci)
        trend[cci > 0] = 1
        trend[cci < 0] = -1
        
        # Calculate crossovers
        zero_cross = np.where(
            (cci > 0) & (pd.Series(cci).shift(1) <= 0), 1,
            np.where((cci < 0) & (pd.Series(cci).shift(1) >= 0), -1, 0)
        )
        
        # Calculate strength zones
        strength = np.zeros_like(cci)
        strength[(cci >= 100) & (cci < 200)] = 1     # Strong
        strength[cci >= 200] = 2                      # Very Strong
        strength[(cci <= -100) & (cci > -200)] = -1   # Weak
        strength[cci <= -200] = -2                    # Very Weak
        
        # Calculate volatility
        volatility = pd.Series(cci).rolling(window=period).std()
        
        self._values = {
            'cci': cci.values,
            'overbought': overbought,
            'oversold': oversold,
            'extreme_overbought': extreme_overbought,
            'extreme_oversold': extreme_oversold,
            'divergence': divergence,
            'momentum': momentum.values,
            'trend': trend,
            'zero_cross': zero_cross,
            'strength': strength,
            'volatility': volatility.values,
            'typical_price': typical_price
        }
        return self._values
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        if self.params['constant'] <= 0:
            raise ValueError("Constant must be greater than 0")
        return True 