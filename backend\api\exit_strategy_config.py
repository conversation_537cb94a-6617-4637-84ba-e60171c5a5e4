"""Configuration for automatic exit strategies in autonomous trading."""

# Define which exit strategies to apply for each trading style
DEFAULT_EXIT_STRATEGIES = {
    # Key is the trading style (lowercase), value is a list of exit strategies to apply
    "scalping": [
        {
            "strategy_name": "volatility_breakout",
            "params": {
                "volatility_factor": 1.2,  # More sensitive for scalping
                "timeframe": "M1"  # Use M1 for quick reactions
            },
            "monitoring_interval": 10  # Check frequently for scalping
        },
        {
            "strategy_name": "ma_crossover",
            "params": {
                "fast_ma": 5,  # Short MAs for scalping
                "slow_ma": 13,
                "timeframe": "M1"
            },
            "monitoring_interval": 10
        }
    ],
    
    "short-term": [
        {
            "strategy_name": "volatility_breakout",
            "params": {
                "volatility_factor": 1.3,
                "timeframe": "M5"  # Slightly longer timeframe
            },
            "monitoring_interval": 20
        },
        {
            "strategy_name": "trend_reversal",
            "params": {
                "reversal_strength": 6,  # More sensitive than default
                "timeframe": "M15"
            },
            "monitoring_interval": 30
        }
    ],
    
    "swing": [
        {
            "strategy_name": "support_resistance",
            "params": {
                "confirmation_candles": 2,
                "timeframe": "H1"  # Longer timeframe for swing trading
            },
            "monitoring_interval": 60
        },
        {
            "strategy_name": "trend_reversal",
            "params": {
                "reversal_strength": 7,
                "timeframe": "H4"  # Use a higher timeframe
            },
            "monitoring_interval": 120
        }
    ],
    
    "position": [
        {
            "strategy_name": "trend_reversal",
            "params": {
                "reversal_strength": 8,  # Less sensitive for long-term
                "timeframe": "D1"  # Daily timeframe
            },
            "monitoring_interval": 300
        },
        {
            "strategy_name": "rsi_overbought_oversold",
            "params": {
                "rsi_period": 14,
                "overbought": 75,  # More extreme levels for position trading
                "oversold": 25,
                "timeframe": "D1"
            },
            "monitoring_interval": 300
        }
    ]
}

# Global settings
EXIT_STRATEGY_SETTINGS = {
    "enabled": True,  # Master switch for exit strategies
    "log_level": "INFO",
    "max_strategies_per_position": 2  # Maximum number of exit strategies to apply to any single position
}
