from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class SpinningTopPatternIndicator(BaseIndicator):
    """Spinning Top candlestick pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - body_ratio: Parameter description (default: 0.3)
                - shadow_ratio: Parameter description (default: 0.4)
        """
        default_params = {
            "body_ratio": 0.3,
            "shadow_ratio": 0.4,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)

    
    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        open_price = df['open'].values
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        body_ratio = self.params['body_ratio']
        shadow_ratio = self.params['shadow_ratio']
        
        # Calculate body size and total range
        body_size = np.abs(close - open_price)
        total_range = high - low
        
        # Calculate shadows
        upper_shadow = high - np.maximum(open_price, close)
        lower_shadow = np.minimum(open_price, close) - low
        
        # Calculate total shadow size
        total_shadow = upper_shadow + lower_shadow
        
        # Identify spinning top patterns
        is_spinning_top = (
            (body_size <= (total_range * body_ratio)) &  # Small body
            (total_shadow >= (total_range * shadow_ratio)) &  # Long shadows
            (upper_shadow >= (total_range * 0.2)) &  # Significant upper shadow
            (lower_shadow >= (total_range * 0.2))  # Significant lower shadow
        )
        
        # Classify spinning top types
        spinning_top_type = np.zeros_like(close)
        spinning_top_type[is_spinning_top & (close > open_price)] = 1  # Bullish spinning top
        spinning_top_type[is_spinning_top & (close < open_price)] = -1  # Bearish spinning top
        
        # Calculate pattern strength
        strength = np.zeros_like(close)
        strength[is_spinning_top] = total_shadow[is_spinning_top] / total_range[is_spinning_top]
        
        # Calculate trend context
        trend = np.zeros_like(close)
        for i in range(1, len(close)):
            if i >= 20:  # Use 20-period SMA for trend
                sma = np.mean(close[i-20:i])
                trend[i] = 1 if close[i] > sma else -1
        
        # Calculate pattern reliability
        reliability = np.zeros_like(close)
        for i in range(1, len(close)):
            if is_spinning_top[i]:
                # Check if price moved in the expected direction
                if i < len(close)-1:
                    future_return = (close[i+1] - close[i]) / close[i]
                    if close[i] > open_price[i]:  # Bullish spinning top
                        reliability[i] = 1 if future_return > 0 else -1
                    else:  # Bearish spinning top
                        reliability[i] = 1 if future_return < 0 else -1
        
        # Calculate shadow balance
        shadow_balance = np.zeros_like(close)
        shadow_balance[is_spinning_top] = (upper_shadow[is_spinning_top] - lower_shadow[is_spinning_top]) / total_shadow[is_spinning_top]
        
        return {
            'is_spinning_top': is_spinning_top.astype(int),
            'spinning_top_type': spinning_top_type,
            'strength': strength,
            'trend': trend,
            'reliability': reliability,
            'shadow_balance': shadow_balance,
            'body_size': body_size,
            'upper_shadow': upper_shadow,
            'lower_shadow': lower_shadow,
            'total_shadow': total_shadow,
            'total_range': total_range
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if not 0 < self.params['body_ratio'] < 1:
            raise ValueError("Body ratio must be between 0 and 1")
        if not 0 < self.params['shadow_ratio'] < 1:
            raise ValueError("Shadow ratio must be between 0 and 1")
        return True 