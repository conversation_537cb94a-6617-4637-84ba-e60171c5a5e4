/* Bento Layout Styles */
.bento-container .page-intro {
  text-align: center;
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 30px;
}

.bento-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px; /* Gap between cards */
  padding: 10px; /* Padding around the grid */
}

.bento-card {
  background-color: var(--card);
  border-radius: 12px; /* More rounded corners */
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* Softer, more modern shadow */
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.bento-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.bento-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.bento-card-icon { /* Placeholder style for text icons */
  font-size: 1.5rem;
  margin-right: 12px;
  color: var(--primary); /* Use primary color for icons */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  /* background-color: rgba(var(--primary-rgb), 0.1); Optional background */
  /* border-radius: 50%; */
}

.bento-card-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--text);
}

.bento-card p,
.bento-card li {
  font-size: 0.9rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

.bento-card ul {
  list-style-type: none; /* Remove default bullets */
  padding-left: 0;
}

.bento-card ul li {
  margin-bottom: 8px;
  padding-left: 20px; /* Space for custom bullet/icon */
  position: relative;
}

.bento-card ul li::before { /* Custom bullet */
  content: '›'; /* Or use a unicode character, or SVG */
  position: absolute;
  left: 0;
  color: var(--primary);
  font-weight: bold;
}

/* Specific card types for grid spanning (example) */
.large-card {
  grid-column: span 2; /* Example: make this card wider */
}

@media (max-width: 900px) { /* Adjust breakpoint for when large-card should stack */
  .large-card {
    grid-column: span 1; /* Stack on smaller screens */
  }
}

.feature-icon {
  margin-right: 8px;
  font-size: 1rem; /* Adjust as needed */
}

/* FAQ specific styles within bento card */
.faq-card .faq-item-bento {
  margin-bottom: 10px;
  border-bottom: 1px solid var(--border-light, #374151);
  padding-bottom: 10px;
}
.faq-card .faq-item-bento:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.faq-question {
  background: none;
  border: none;
  color: var(--text);
  font-weight: 600;
  font-size: 1rem;
  padding: 10px 0;
  width: 100%;
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}
.faq-question:hover {
  color: var(--primary);
}

.faq-answer {
  font-size: 0.85rem;
  padding-left: 10px; /* Indent answer slightly */
  margin-top: 5px;
  color: var(--text-secondary);
  max-height: 0; /* For accordion effect */
  overflow: hidden;
  transition: max-height 0.3s ease-in-out, padding 0.3s ease-in-out; /* Smooth transition */
}

.faq-item-bento button.faq-question + .faq-answer { /* Style for open answer */
  /* This selector might need adjustment based on how openFaq state is applied */
  /* If openFaq state adds a class to the parent or answer, target that */
}

/* A more direct way if HelpPage.jsx adds an 'open' class to faq-answer when openFaq matches index */
.faq-answer.open {
  max-height: 200px; /* Adjust as needed, or use 'auto' if transitions are not critical */
  padding-top: 5px;
  padding-bottom: 10px;
}


.contact-card a {
  color: var(--primary);
  text-decoration: none;
}
.contact-card a:hover {
  text-decoration: underline;
}

/* Adjustments for better visual hierarchy */
.help-page .page-title {
  text-align: center;
  margin-bottom: 10px;
}
