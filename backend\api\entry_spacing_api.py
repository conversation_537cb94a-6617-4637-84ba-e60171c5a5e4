from flask import Blueprint, request, jsonify
import json
import os
from datetime import datetime
import logging
from backend.entry_spacing_manager import EntrySpacingManager

# Create blueprint for entry spacing API
entry_spacing_bp = Blueprint('entry_spacing', __name__)

# Initialize logger
logger = logging.getLogger(__name__)

# Global entry spacing manager instance
entry_spacing_manager = None

def get_entry_spacing_manager():
    """Get or create the entry spacing manager instance"""
    global entry_spacing_manager
    if entry_spacing_manager is None:
        try:
            config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'autonomous_config.json')
            entry_spacing_manager = EntrySpacingManager(config_path)
        except Exception as e:
            logger.error(f"Failed to initialize EntrySpacingManager: {e}")
            # Create with default config if file doesn't exist
            entry_spacing_manager = EntrySpacingManager()
    return entry_spacing_manager

@entry_spacing_bp.route('/api/entry_spacing/settings', methods=['GET'])
def get_entry_spacing_settings():
    """Get current entry spacing settings"""
    try:
        manager = get_entry_spacing_manager()
        settings = {
            'enabled': manager.enabled,
            'atr_period': manager.atr_period,
            'atr_multiplier': manager.atr_multiplier,
            'min_spacing_points': manager.min_spacing_points,
            'max_spacing_points': manager.max_spacing_points,
            'progressive_spacing_multipliers': manager.progressive_spacing_multipliers,
            'progressive_volume_multipliers': manager.progressive_volume_multipliers,
            'time_based_spacing': {
                'enabled': manager.time_based_spacing.get('enabled', True),
                'base_minutes': manager.time_based_spacing.get('base_minutes', 5),
                'loss_cooldown_multipliers': manager.time_based_spacing.get('loss_cooldown_multipliers', [1, 2, 3, 4, 5, 6])
            },
            'max_consecutive_losses': manager.max_consecutive_losses
        }
        
        return jsonify(settings), 200
        
    except Exception as e:
        logger.error(f"Error getting entry spacing settings: {e}")
        return jsonify({'error': str(e)}), 500

@entry_spacing_bp.route('/api/entry_spacing/settings', methods=['POST'])
def update_entry_spacing_settings():
    """Update entry spacing settings"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
            
        manager = get_entry_spacing_manager()
        
        # Update settings
        if 'enabled' in data:
            manager.enabled = bool(data['enabled'])
        if 'atr_period' in data:
            manager.atr_period = int(data['atr_period'])
        if 'atr_multiplier' in data:
            manager.atr_multiplier = float(data['atr_multiplier'])
        if 'min_spacing_points' in data:
            manager.min_spacing_points = int(data['min_spacing_points'])
        if 'max_spacing_points' in data:
            manager.max_spacing_points = int(data['max_spacing_points'])
        if 'progressive_spacing_multipliers' in data:
            manager.progressive_spacing_multipliers = [float(x) for x in data['progressive_spacing_multipliers']]
        if 'progressive_volume_multipliers' in data:
            manager.progressive_volume_multipliers = [float(x) for x in data['progressive_volume_multipliers']]
        if 'max_consecutive_losses' in data:
            manager.max_consecutive_losses = int(data['max_consecutive_losses'])
        if 'time_based_spacing' in data:
            time_settings = data['time_based_spacing']
            manager.time_based_spacing.update({
                'enabled': bool(time_settings.get('enabled', True)),
                'base_minutes': int(time_settings.get('base_minutes', 5)),
                'loss_cooldown_multipliers': [int(x) for x in time_settings.get('loss_cooldown_multipliers', [1, 2, 3, 4, 5, 6])]
            })
        
        # Save settings to config file
        try:
            config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'autonomous_config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
            else:
                config = {}
            
            # Update entry spacing section
            config['entry_spacing'] = {
                'enabled': manager.enabled,
                'atr_period': manager.atr_period,
                'atr_multiplier': manager.atr_multiplier,
                'min_spacing_points': manager.min_spacing_points,
                'max_spacing_points': manager.max_spacing_points,
                'progressive_spacing_multipliers': manager.progressive_spacing_multipliers,
                'progressive_volume_multipliers': manager.progressive_volume_multipliers,
                'time_based_spacing': manager.time_based_spacing,
                'max_consecutive_losses': manager.max_consecutive_losses
            }
            
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2)
                
        except Exception as e:
            logger.warning(f"Failed to save config to file: {e}")
        
        return jsonify({'message': 'Settings updated successfully'}), 200
        
    except Exception as e:
        logger.error(f"Error updating entry spacing settings: {e}")
        return jsonify({'error': str(e)}), 500

@entry_spacing_bp.route('/api/entry_spacing/statistics', methods=['GET'])
def get_entry_spacing_statistics():
    """Get entry spacing statistics and current status"""
    try:
        manager = get_entry_spacing_manager()
        
        # Load trade history
        history_file = manager.history_file
        if os.path.exists(history_file):
            with open(history_file, 'r') as f:
                history_data = json.load(f)
        else:
            history_data = {'trades': [], 'symbol_stats': {}}
        
        # Calculate overall statistics
        trades = history_data.get('trades', [])
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t.get('profit', 0) > 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        total_profit = sum(t.get('profit', 0) for t in trades)
        
        # Get symbol-specific statistics
        symbol_stats = {}
        for symbol, stats in history_data.get('symbol_stats', {}).items():
            # Calculate current multipliers based on consecutive losses
            consecutive_losses = stats.get('consecutive_losses', 0)
            spacing_idx = min(consecutive_losses, len(manager.progressive_spacing_multipliers) - 1)
            volume_idx = min(consecutive_losses, len(manager.progressive_volume_multipliers) - 1)
            
            current_spacing_multiplier = manager.progressive_spacing_multipliers[spacing_idx]
            current_volume_multiplier = manager.progressive_volume_multipliers[volume_idx]
            
            # Calculate next entry allowed time
            next_entry_allowed = None
            if manager.time_based_spacing.get('enabled', False) and stats.get('last_entry_time'):
                try:
                    last_entry = datetime.fromisoformat(stats['last_entry_time'])
                    cooldown_multiplier_idx = min(consecutive_losses, len(manager.time_based_spacing.get('loss_cooldown_multipliers', [1])) - 1)
                    cooldown_multiplier = manager.time_based_spacing.get('loss_cooldown_multipliers', [1])[cooldown_multiplier_idx]
                    delay_minutes = manager.time_based_spacing.get('base_minutes', 5) * cooldown_multiplier
                    
                    from datetime import timedelta
                    next_allowed = last_entry + timedelta(minutes=delay_minutes)
                    if next_allowed > datetime.now():
                        next_entry_allowed = next_allowed.isoformat()
                except:
                    pass
            
            symbol_stats[symbol] = {
                'consecutive_losses': consecutive_losses,
                'current_spacing_multiplier': current_spacing_multiplier,
                'current_volume_multiplier': current_volume_multiplier,
                'last_entry_time': stats.get('last_entry_time'),
                'next_entry_allowed': next_entry_allowed,
                'total_trades': stats.get('total_trades', 0),
                'winning_trades': stats.get('winning_trades', 0),
                'total_profit': stats.get('total_profit', 0)
            }
        
        # Get ATR analysis for symbols that have been traded
        atr_analysis = {}
        if hasattr(manager, 'get_atr') and symbol_stats:
            for symbol in symbol_stats.keys():
                try:
                    atr_value = manager.get_atr(symbol, manager.atr_period)
                    if atr_value:
                        atr_analysis[symbol] = atr_value
                except:
                    pass
        
        statistics = {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_profit': total_profit,
            'symbol_stats': symbol_stats,
            'atr_analysis': atr_analysis,
            'settings_summary': {
                'enabled': manager.enabled,
                'atr_period': manager.atr_period,
                'atr_multiplier': manager.atr_multiplier,
                'max_consecutive_losses': manager.max_consecutive_losses,
                'time_based_enabled': manager.time_based_spacing.get('enabled', False)
            }
        }
        
        return jsonify(statistics), 200
        
    except Exception as e:
        logger.error(f"Error getting entry spacing statistics: {e}")
        return jsonify({'error': str(e)}), 500

@entry_spacing_bp.route('/api/entry_spacing/reset', methods=['POST'])
def reset_entry_spacing_data():
    """Reset all entry spacing data (history and statistics)"""
    try:
        manager = get_entry_spacing_manager()
        
        # Reset the trade history file
        history_file = manager.history_file
        reset_data = {
            'trades': [],
            'symbol_stats': {},
            'reset_timestamp': datetime.now().isoformat()
        }
        
        with open(history_file, 'w') as f:
            json.dump(reset_data, f, indent=2)
        
        logger.info("Entry spacing data reset successfully")
        return jsonify({'message': 'Entry spacing data reset successfully'}), 200
        
    except Exception as e:
        logger.error(f"Error resetting entry spacing data: {e}")
        return jsonify({'error': str(e)}), 500

@entry_spacing_bp.route('/api/entry_spacing/check_entry_allowed', methods=['POST'])
def check_entry_allowed():
    """Check if entry is allowed for a specific symbol"""
    try:
        data = request.get_json()
        if not data or 'symbol' not in data:
            return jsonify({'error': 'Symbol is required'}), 400
            
        symbol = data['symbol']
        manager = get_entry_spacing_manager()
        
        if not manager.enabled:
            return jsonify({
                'allowed': True,
                'reason': 'Entry spacing disabled',
                'spacing_info': None
            }), 200
        
        # Check if entry is allowed
        allowed, reason = manager.is_entry_allowed(symbol)
        
        # Get current spacing info
        spacing_info = manager.get_current_spacing_info(symbol)
        
        return jsonify({
            'allowed': allowed,
            'reason': reason,
            'spacing_info': spacing_info
        }), 200
        
    except Exception as e:
        logger.error(f"Error checking entry allowed: {e}")
        return jsonify({'error': str(e)}), 500

@entry_spacing_bp.route('/api/entry_spacing/record_trade', methods=['POST'])
def record_trade():
    """Record a trade result for entry spacing analysis"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
            
        required_fields = ['symbol', 'profit', 'entry_time']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'{field} is required'}), 400
        
        symbol = data['symbol']
        profit = float(data['profit'])
        entry_time = data['entry_time']
        volume = data.get('volume', 1.0)
        
        manager = get_entry_spacing_manager()
        
        # Record the trade
        manager.record_trade_result(symbol, profit, entry_time, volume)
        
        return jsonify({'message': 'Trade recorded successfully'}), 200
        
    except Exception as e:
        logger.error(f"Error recording trade: {e}")
        return jsonify({'error': str(e)}), 500

# Error handlers
@entry_spacing_bp.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@entry_spacing_bp.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500
