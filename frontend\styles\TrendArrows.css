/* Trend Arrow Styles */
.trend-arrow {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-left: 8px;
  transition: all 0.3s ease;
}

.trend-arrow.up {
  background-color: rgba(0, 204, 0, 0.15);
  color: var(--success);
}

.trend-arrow.down {
  background-color: rgba(255, 77, 77, 0.15);
  color: var(--error);
}

.trend-arrow.neutral {
  background-color: rgba(230, 230, 0, 0.15);
  color: var(--warning);
}

.trend-arrow svg {
  width: 16px;
  height: 16px;
}

/* Animated trend arrows */
.trend-arrow.animated svg {
  animation-duration: 1s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
}

.trend-arrow.up.animated svg {
  animation-name: pulse-up;
}

.trend-arrow.down.animated svg {
  animation-name: pulse-down;
}

@keyframes pulse-up {
  0% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
  100% { transform: translateY(0); }
}

@keyframes pulse-down {
  0% { transform: translateY(0); }
  50% { transform: translateY(3px); }
  100% { transform: translateY(0); }
}

/* Trend Arrows Component Styles */
.trend-arrow {
  display: inline-flex;
  align-items: center;
  font-weight: 500;
  font-size: 14px;
  gap: 3px;
}

.trend-arrow i {
  font-size: 16px;
}

/* Up trend (green) */
.trend-arrow.up {
  background-color: rgba(0, 204, 0, 0.15);
  color: var(--success);
}

/* Down trend (red) */
.trend-arrow.down {
  background-color: rgba(255, 77, 77, 0.15);
  color: var(--error);
}

/* Sideways/neutral trend (yellow) */
.trend-arrow.neutral {
  background-color: rgba(230, 230, 0, 0.15);
  color: var(--warning);
}

/* Small variant */
.trend-arrow.small {
  font-size: 12px;
}

/* Large variant */
.trend-arrow.large {
  font-size: 16px;
  font-weight: 600;
}

/* Trend arrow icon only */
.trend-arrow.icon-only {
  font-size: 0;
}

.trend-arrow.icon-only i {
  font-size: 14px;
}

/* Bold variant */
.trend-arrow.bold {
  font-weight: 700;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}
