/* Analysis Page Bento Layout Specific Styles */

/* Controls Card */
.analysis-controls-card {
  grid-column: span 12;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  margin-bottom: 16px;
}

.analysis-form-group {
  flex: 1;
  min-width: 200px;
}

.analysis-form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text, #f5f5f5);
}

.analysis-form-group select {
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  background-color: var(--input, rgba(0, 0, 0, 0.3));
  border: 1px solid var(--border, rgba(255, 255, 255, 0.2));
  color: var(--text, white);
  font-size: 1rem;
  font-weight: 500;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
}

.analysis-form-group select:focus {
  outline: none;
  border-color: var(--primary, rgba(255, 255, 255, 0.5));
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.analysis-form-group select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Style for dropdown options */
.analysis-form-group select option {
  background-color: var(--input, #1e2430);
  color: var(--text, white);
  padding: 8px;
}

/* Style for the selected option in the dropdown */
.analysis-form-group select option:checked {
  background-color: var(--card-hover, #2c3e50);
  color: var(--text, white);
}

/* Analysis Dashboard Layout */
.analysis-dashboard {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 16px;
  margin-top: 16px;
  width: 100%;
}

/* Card sizing in dashboard */
.analysis-dashboard > * {
  background-color: var(--card, rgba(16, 24, 38, 0.7));
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

/* Component-specific sizing */
.analysis-dashboard .trend-card {
  grid-column: span 4;
}

.analysis-dashboard .pattern-card {
  grid-column: span 8; /* Changed from 4 to 8 since we removed the performance card */
}

.analysis-dashboard .historical-signals-table {
  grid-column: span 12;
}

.analysis-dashboard .support-resistance-card {
  grid-column: span 12;
}

/* Table styles for HistoricalSignalsTable */
.historical-signals-table table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 12px;
  color: var(--text, #f5f5f5);
}

.historical-signals-table th,
.historical-signals-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid var(--border, rgba(255, 255, 255, 0.1));
}

.historical-signals-table th {
  background-color: var(--card-hover, rgba(0, 0, 0, 0.2));
  font-weight: 600;
  color: var(--text, #ffffff);
}

.historical-signals-table tr:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.05);
}

.historical-signals-table tr:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .analysis-dashboard .trend-card,
  .analysis-dashboard .pattern-card {
    grid-column: span 12; /* Now spans full width on smaller screens since we removed the performance card */
  }
  
  .historical-signals-table {
    overflow-x: auto;
  }
}

@media (max-width: 768px) {
  .analysis-dashboard {
    grid-template-columns: 1fr;
  }
  .analysis-dashboard > * {
    grid-column: span 1 !important;
  }
  
  .historical-signals-table table {
    font-size: 14px;
  }
  
  .historical-signals-table th,
  .historical-signals-table td {
    padding: 8px 6px;
  }
}

/* Summary Card */
.analysis-summary-card {
  display: flex;
  flex-direction: column;
}

.analysis-overall-signal {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.analysis-signal-strength {
  margin-bottom: 16px;
}

.analysis-strength-bar {
  height: 12px;
  background-color: var(--card-hover, rgba(255, 255, 255, 0.1));
  border-radius: 6px;
  overflow: hidden;
  margin-top: 8px;
  position: relative;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.analysis-strength-value {
  height: 100%;
  border-radius: 6px;
  transition: width 0.5s ease-out;
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3));
  position: relative;
}

.analysis-strength-value::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 4px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 0 6px 6px 0;
}

.analysis-strength-value.buy {
  background-color: var(--success, #00cc00);
  box-shadow: 0 0 10px rgba(0, 204, 0, 0.5);
}

.analysis-strength-value.sell {
  background-color: var(--error, #ff4d4d);
  box-shadow: 0 0 10px rgba(255, 77, 77, 0.5);
}

.analysis-strength-value.neutral {
  background-color: var(--warning, #e6e600);
  box-shadow: 0 0 10px rgba(230, 230, 0, 0.5);
}

.analysis-strength-label {
  text-align: right;
  font-size: 1rem;
  margin-top: 8px;
  font-weight: 600;
  color: var(--text, rgba(255, 255, 255, 0.9));
}

.analysis-summary-text {
  margin-top: 16px;
  color: var(--text, rgba(255, 255, 255, 0.9));
}

.analysis-summary-text ul {
  margin-top: 8px;
  padding-left: 20px;
}

.analysis-summary-text li {
  margin-bottom: 4px;
}

/* Support/Resistance Card */
.analysis-sr-details {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text, #f5f5f5);
}

/* SR List Section Styling */
.sr-list-section {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.sr-list-section:hover {
  background-color: rgba(0, 0, 0, 0.3);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Section Title Styling */
.section-title {
  display: block;
  margin-bottom: 10px;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1rem;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.resistance-title {
  color: #ff4d4d;
}

.support-title {
  color: #00cc00;
}

.cluster-title {
  color: #e6e600;
}

.psych-title {
  color: #00bfff;
}

.supply-title {
  color: #ff6b6b;
}

.demand-title {
  color: #4ecdc4;
}

/* SR List Item Styling */
.sr-list {
  margin-top: 8px;
  padding-left: 10px;
  max-height: 120px;
  overflow-y: auto;
  list-style-type: none;
}

.sr-list-item {
  margin-bottom: 6px;
  font-size: 0.9rem;
  padding: 4px 6px;
  border-radius: 4px;
  border-bottom: 1px dotted rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sr-list-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.sr-list-item.empty {
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

.level-price {
  font-weight: 500;
  color: #ffffff;
}

.level-source {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
}

.zone-range {
  width: 100%;
  text-align: center;
  font-weight: 500;
}

/* Key Level Styling */
.key-level {
  margin: 8px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.resistance-value {
  color: #ff4d4d;
  font-weight: 500;
}

.support-value {
  color: #00cc00;
  font-weight: 500;
}

.analysis-sr-details ul {
  margin-top: 8px;
  padding-left: 20px;
  max-height: 120px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) rgba(0, 0, 0, 0.2);
}

.analysis-sr-details ul::-webkit-scrollbar {
  width: 6px;
}

.analysis-sr-details ul::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.analysis-sr-details ul::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.analysis-sr-details li {
  margin-bottom: 6px;
  font-size: 0.9rem;
  padding: 2px 0;
  border-bottom: 1px dotted rgba(255, 255, 255, 0.1);
}

.analysis-sr-details li:last-child {
  border-bottom: none;
}

.analysis-sr-details strong {
  display: block;
  margin-bottom: 8px;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1rem;
  padding-bottom: 4px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.analysis-sr-details p {
  margin: 4px 0;
}

.analysis-sr-section {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
}

.analysis-sr-details > div {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.analysis-sr-details > div:hover {
  background-color: rgba(0, 0, 0, 0.3);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Indicator Details */
.analysis-indicator-details {
  margin-top: 12px;
}

.analysis-indicator-details p {
  margin: 4px 0;
  display: flex;
  justify-content: space-between;
}

.analysis-indicator-value {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 10px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Signal styling */
.signal {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
  text-align: center;
}

.signal.buy {
  background-color: rgba(0, 255, 0, 0.15);
  color: var(--success, #10b981);
}

.signal.sell {
  background-color: rgba(255, 0, 0, 0.15);
  color: var(--error, #ef4444);
}

.signal.neutral {
  background-color: rgba(255, 255, 0, 0.15);
  color: var(--warning, #e6e600);
}

/* Signal with arrow */
.signal-with-arrow {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Utility classes */
.mt-2 {
  margin-top: 12px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .analysis-controls-card {
    flex-direction: column;
    align-items: stretch;
  }

  .analysis-form-group {
    width: 100%;
  }

  .analysis-sr-details {
    grid-template-columns: 1fr;
  }
}

/* Pattern Card specific styles */
.pattern-card .pattern-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.pattern-card .pattern-item {
  margin-bottom: 12px;
  padding: 10px;
  border-radius: 6px;
  background-color: var(--background-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.pattern-card .pattern-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px var(--shadow);
}

.pattern-card .pattern-item.expanded {
  box-shadow: 0 2px 8px var(--shadow);
  border: 1px solid var(--border);
}

.pattern-card .pattern-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pattern-card .pattern-icon {
  margin-right: 8px;
  font-size: 1.4em;
}

.pattern-card .pattern-details {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid var(--border);
  font-size: 0.9em;
}

.pattern-card .visualization {
  margin-top: 10px;
  text-align: center;
}

.pattern-card .detail-row {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.pattern-card .detail-item {
  text-align: center;
}

.pattern-card .detail-label {
  color: var(--text-secondary);
  margin-bottom: 5px;
  font-size: 0.9em;
}

.pattern-card .detail-value {
  font-weight: 600;
}

.pattern-card .bullish {
  color: var(--success);
}

.pattern-card .bearish {
  color: var(--error);
}

.pattern-card .neutral {
  color: var(--text-secondary);
}
