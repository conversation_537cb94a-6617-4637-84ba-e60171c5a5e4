from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class WilliamsRIndicator(BaseIndicator):
    """Williams %R indicator."""

    def __init__(self, period: int = 14):
        """
        Initialize Williams %R indicator.

        Args:
            period: The lookback period for calculating highest high and lowest low.
        """
        super().__init__({'period': period})

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Williams %R values."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period']:
             return {'williams_r': np.array([])}

        period = self.params['period']

        high = df['high']
        low = df['low']
        close = df['close']

        # Calculate Highest High and Lowest Low over the period
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()

        # Calculate Williams %R
        # Avoid division by zero
        price_range = (highest_high - lowest_low).replace(0, np.nan)
        williams_r = -100 * (highest_high - close) / price_range
        williams_r = williams_r.fillna(0) # Fill NaNs (e.g., at the start or if range is 0)

        # Clip values to the standard -100 to 0 range if desired, although raw values can exceed this
        # williams_r = williams_r.clip(-100, 0)

        self._values = {
            'williams_r': williams_r.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        return True