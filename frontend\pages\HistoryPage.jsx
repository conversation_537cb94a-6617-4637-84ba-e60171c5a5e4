import React, { useState, useEffect } from 'react';
import { useNotification } from '../components/Notification';
import '../styles/Pages.css';

const HistoryPage = () => {
  const [historyData, setHistoryData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState('week');
  const [symbols, setSymbols] = useState([]);
  const [selectedSymbol, setSelectedSymbol] = useState('all');
  const [stats, setStats] = useState(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Account currency state
  const [accountCurrency, setAccountCurrency] = useState('USD');

  const notify = useNotification();

  // Currency symbol mapping
  const getCurrencySymbol = (currency) => {
    const currencySymbols = {
      'USD': '$',
      'EUR': '€',
      'GBP': '£',
      'JPY': '¥',
      'CHF': 'CHF',
      'CAD': 'C$',
      'AUD': 'A$',
      'NZD': 'NZ$',
      'SEK': 'kr',
      'NOK': 'kr',
      'DKK': 'kr',
      'PLN': 'zł',
      'CZK': 'Kč',
      'HUF': 'Ft',
      'RUB': '₽',
      'CNY': '¥',
      'INR': '₹',
      'BRL': 'R$',
      'MXN': '$',
      'ZAR': 'R',
      'SGD': 'S$',
      'HKD': 'HK$',
      'THB': '฿',
      'TRY': '₺',
      'KRW': '₩',
      // ASEAN Countries
      'IDR': 'Rp',     // Indonesia
      'MYR': 'RM',     // Malaysia
      'PHP': '₱',      // Philippines
      'VND': '₫',      // Vietnam
      'LAK': '₭',      // Laos
      'KHR': '៛',      // Cambodia
      'MMK': 'K',      // Myanmar
      'BND': 'B$'      // Brunei
      // Note: Singapore (SGD) and Thailand (THB) already included above
    };
    return currencySymbols[currency] || currency;
  };

  // Fetch available symbols
  useEffect(() => {
    const fetchSymbols = async () => {
      try {
        const response = await fetch('http://localhost:5001/api/symbols');
        if (response.ok) {
          const data = await response.json();
          setSymbols(['all', ...data]);
        }
      } catch (error) {
        console.error('Failed to fetch symbols:', error);
      }
    };

    fetchSymbols();
  }, []);

  // Fetch history data when filters change
  useEffect(() => {
    const fetchHistoryData = async () => {
      setLoading(true);
      // Reset to page 1 when filters change
      setCurrentPage(1);

      try {
        const symbolParam = selectedSymbol === 'all' ? '' : `&symbol=${selectedSymbol}`;
        const response = await fetch(`http://localhost:5001/api/trade/history?period=${dateRange}${symbolParam}`);
        if (response.ok) {
          const data = await response.json();
          setHistoryData(data.history || []);
          setStats(data.stats || null);
        } else {
          const errorData = await response.json();
          notify.error('History Error', errorData.message || 'Failed to fetch history data');
        }
      } catch (error) {
        console.error('Failed to fetch history data:', error);
        notify.error('Error', 'Failed to fetch trading history');
      } finally {
        setLoading(false);
      }
    };

    fetchHistoryData();
  }, [dateRange, selectedSymbol]);

  // Fetch account currency
  useEffect(() => {
    const fetchAccountCurrency = async () => {
      try {
        const accountResponse = await fetch('http://localhost:5001/api/market/account');
        if (accountResponse.ok) {
          const accountData = await accountResponse.json();
          if (accountData.currency) {
            setAccountCurrency(accountData.currency);
          }
        }
      } catch (error) {
        console.warn('Could not fetch account currency for history page, using default USD:', error);
      }
    };

    fetchAccountCurrency();
  }, []);

  const handleDateRangeChange = (e) => {
    setDateRange(e.target.value);
  };

  const handleSymbolChange = (e) => {
    setSelectedSymbol(e.target.value);
  };

  const handleRowsPerPageChange = (e) => {
    setRowsPerPage(parseInt(e.target.value));
    setCurrentPage(1); // Reset to first page when changing rows per page
  };

  const formatDate = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  const formatDuration = (minutes) => {
    if (minutes < 60) {
      return `${minutes} min`;
    } else if (minutes < 1440) {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return `${hours}h ${mins}m`;
    } else {
      const days = Math.floor(minutes / 1440);
      const hours = Math.floor((minutes % 1440) / 60);
      return `${days}d ${hours}h`;
    }
  };

  // Calculate pagination values
  const totalPages = Math.ceil(historyData.length / rowsPerPage);
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = Math.min(startIndex + rowsPerPage, historyData.length);
  const paginatedData = historyData.slice(startIndex, endIndex);

  // Navigation functions
  const goToPage = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // Generate page buttons
  const generatePageButtons = () => {
    const buttons = [];
    const maxButtons = 5; // Maximum number of buttons to show

    let startPage = Math.max(1, currentPage - Math.floor(maxButtons / 2));
    let endPage = Math.min(totalPages, startPage + maxButtons - 1);

    // Adjust start page if we can't fit maxButtons from the current page
    if (endPage - startPage + 1 < maxButtons) {
      startPage = Math.max(1, endPage - maxButtons + 1);
    }

    // Add first page button if not included in range
    if (startPage > 1) {
      buttons.push(
        <button
          key="first"
          className={`pagination-button ${1 === currentPage ? 'active' : ''}`}
          onClick={() => goToPage(1)}
        >
          1
        </button>
      );

      // Add ellipsis if there's a gap
      if (startPage > 2) {
        buttons.push(<span key="ellipsis1" className="pagination-ellipsis">...</span>);
      }
    }

    // Add page buttons in the calculated range
    for (let i = startPage; i <= endPage; i++) {
      buttons.push(
        <button
          key={i}
          className={`pagination-button ${i === currentPage ? 'active' : ''}`}
          onClick={() => goToPage(i)}
        >
          {i}
        </button>
      );
    }

    // Add last page button if not included in range
    if (endPage < totalPages) {
      // Add ellipsis if there's a gap
      if (endPage < totalPages - 1) {
        buttons.push(<span key="ellipsis2" className="pagination-ellipsis">...</span>);
      }

      buttons.push(
        <button
          key="last"
          className={`pagination-button ${totalPages === currentPage ? 'active' : ''}`}
          onClick={() => goToPage(totalPages)}
        >
          {totalPages}
        </button>
      );
    }

    return buttons;
  };

  return (
    <div className="page-container history-page">
      <h2>Trading History</h2>

      <div className="controls">
        <div className="form-group">
          <label htmlFor="dateRange">Period</label>
          <select
            id="dateRange"
            value={dateRange}
            onChange={handleDateRangeChange}
            disabled={loading}
          >
            <option value="day">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">Last 3 Months</option>
            <option value="year">This Year</option>
            <option value="all">All Time</option>
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="symbol">Symbol</label>
          <select
            id="symbol"
            value={selectedSymbol}
            onChange={handleSymbolChange}
            disabled={loading}
          >
            {symbols.map(symbol => (
              <option key={symbol} value={symbol}>{symbol === 'all' ? 'All Symbols' : symbol}</option>
            ))}
          </select>
        </div>
      </div>

      {loading ? (
        <div className="loading">Loading history data...</div>
      ) : (
        <div className="history-content">
          {stats && (
            <div className="performance-summary-card">
              <h3>Performance Summary</h3>
              <div className="performance-metrics">
                <div className="performance-metric">
                  <span className="metric-label">Total Trades</span>
                  <span className="metric-value">{stats.total_trades}</span>
                </div>
                <div className="performance-metric">
                  <span className="metric-label">Win Rate</span>
                  <span className="metric-value">{(stats.win_rate * 100).toFixed(1)}%</span>
                </div>
                <div className="performance-metric">
                  <span className="metric-label">Profit Factor</span>
                  <span className="metric-value">{stats.profit_factor === null ? '∞' : stats.profit_factor.toFixed(2)}</span>
                </div>
                <div className="performance-metric">
                  <span className="metric-label">Net Profit</span>
                  <span className={`metric-value ${stats.net_profit >= 0 ? 'positive' : 'negative'}`}>
                    {getCurrencySymbol(accountCurrency)}{Math.abs(stats.net_profit).toFixed(2)}
                  </span>
                </div>
                <div className="performance-metric">
                  <span className="metric-label">Avg Profit</span>
                  <span className={`metric-value ${stats.avg_profit >= 0 ? 'positive' : 'negative'}`}>
                    {getCurrencySymbol(accountCurrency)}{Math.abs(stats.avg_profit).toFixed(2)}
                  </span>
                </div>
                <div className="performance-metric">
                  <span className="metric-label">Avg Loss</span>
                  <span className="metric-value negative">
                    {getCurrencySymbol(accountCurrency)}{Math.abs(stats.avg_loss).toFixed(2)}
                  </span>
                </div>
                <div className="performance-metric">
                  <span className="metric-label">Largest Win</span>
                  <span className="metric-value positive">
                    {getCurrencySymbol(accountCurrency)}{stats.largest_win.toFixed(2)}
                  </span>
                </div>
                <div className="performance-metric">
                  <span className="metric-label">Largest Loss</span>
                  <span className="metric-value negative">
                    {getCurrencySymbol(accountCurrency)}{Math.abs(stats.largest_loss).toFixed(2)}
                  </span>
                </div>
                <div className="performance-metric">
                  <span className="metric-label">Avg Hold Time</span>
                  <span className="metric-value">
                    {formatDuration(stats.avg_hold_time_minutes)}
                  </span>
                </div>
              </div>
            </div>
          )}

          <div className="history-table-container">
            <h3>Closed Trades</h3>
            {historyData.length === 0 ? (
              <p className="no-data">No trading history found for the selected period</p>
            ) : (
              <>
                <table className="history-table">
                  <thead>
                    <tr>
                      <th>Ticket</th>
                      <th>Symbol</th>
                      <th>Type</th>
                      <th>Volume</th>
                      <th>Open Time</th>
                      <th>Close Time</th>
                      <th>Duration</th>
                      <th>Open Price</th>
                      <th>Close Price</th>
                      <th>SL</th>
                      <th>TP</th>
                      <th>Profit</th>
                      <th>Pips</th>
                      <th>Comment</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedData.map(trade => (
                      <React.Fragment key={trade.ticket}>
                        <tr className={`${trade.profit >= 0 ? 'profit-row' : 'loss-row'} ${trade.is_zero_profit ? 'zero-profit-row' : ''}`}>
                          <td>{trade.ticket}</td>
                          <td>{trade.symbol}</td>
                          <td className={trade.type.toLowerCase()}>{trade.type}</td>
                          <td>{trade.volume.toFixed(2)}</td>
                          <td>{formatDate(trade.open_time)}</td>
                          <td>{formatDate(trade.close_time)}</td>
                          <td>{formatDuration(trade.duration_minutes)}</td>
                          <td>{trade.open_price.toFixed(5)}</td>
                          <td>{trade.close_price.toFixed(5)}</td>
                          <td>{trade.sl ? trade.sl.toFixed(5) : '-'}</td>
                          <td>{trade.tp ? trade.tp.toFixed(5) : '-'}</td>
                          <td className={trade.profit >= 0 ? 'positive' : 'negative'}>
                            {getCurrencySymbol(accountCurrency)}{Math.abs(trade.profit).toFixed(2)}
                            {trade.is_zero_profit && <span className="zero-profit-tag">?</span>}
                          </td>
                          <td className={trade.pips >= 0 ? 'positive' : 'negative'}>
                            {Math.abs(trade.pips).toFixed(1)}
                          </td>
                          <td>{trade.comment || '-'}</td>
                        </tr>
                        {trade.is_zero_profit && (
                          <tr className="zero-profit-details-row">
                            <td colSpan="14">
                              <div className="zero-profit-details">
                                <strong>Zero Profit Trade Details:</strong>
                                <ul>
                                  <li><strong>Ticket:</strong> {trade.ticket}</li>
                                  <li><strong>Comment:</strong> {trade.comment || 'None'}</li>
                                  <li><strong>Entry Type:</strong> {trade.entry}</li>
                                  <li><strong>Reason Code:</strong> {trade.reason}</li>
                                </ul>
                              </div>
                            </td>
                          </tr>
                        )}
                      </React.Fragment>
                    ))}
                  </tbody>
                </table>

                <div className="pagination-controls">
                  <div className="pagination-info">
                    Showing {startIndex + 1} to {endIndex} of {historyData.length} trades
                  </div>

                  <div className="pagination-buttons">
                    <button
                      className="pagination-button"
                      onClick={goToPreviousPage}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </button>

                    {generatePageButtons()}

                    <button
                      className="pagination-button"
                      onClick={goToNextPage}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </button>
                  </div>

                  <div className="rows-per-page">
                    <label htmlFor="rowsPerPage">Rows per page:</label>
                    <select
                      id="rowsPerPage"
                      value={rowsPerPage}
                      onChange={handleRowsPerPageChange}
                    >
                      <option value="5">5</option>
                      <option value="10">10</option>
                      <option value="20">20</option>
                      <option value="50">50</option>
                      <option value="100">100</option>
                    </select>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default HistoryPage;
