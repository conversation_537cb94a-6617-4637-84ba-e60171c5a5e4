from typing import Dict, Any, List
import numpy as np
import pandas as pd
from scipy.signal import find_peaks

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class TrianglePatternIndicator(BaseIndicator):
    """Triangle chart pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - min_peak_distance: Parameter description (default: 10)
                - min_peak_height: Parameter description (default: 0.02)
                - price_tolerance: Parameter description (default: 0.02)
                - min_points: Parameter description (default: 3)
        """
        default_params = {
            "min_peak_distance": 10,
            "min_peak_height": 0.02,
            "price_tolerance": 0.02,
            "min_points": 3,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)

    
    def _find_peaks_and_troughs(self, prices: np.ndarray, min_distance: int,
                              min_height: float) -> tuple:
        """Find peaks and troughs in price data."""
        # Find peaks
        peaks, _ = find_peaks(prices, distance=min_distance,
                            height=np.mean(prices) * min_height)
        
        # Find troughs (inverse of peaks)
        troughs, _ = find_peaks(-prices, distance=min_distance,
                              height=np.mean(prices) * min_height)
        
        return peaks, troughs
    
    def _is_ascending_triangle(self, prices: np.ndarray, peaks: np.ndarray,
                             troughs: np.ndarray, tolerance: float) -> tuple:
        """Identify Ascending Triangle patterns."""
        is_pattern = np.zeros_like(prices)
        pattern_type = np.zeros_like(prices)
        
        if len(peaks) >= self.params['min_points'] and len(troughs) >= self.params['min_points']:
            # Get the most recent peaks and troughs
            recent_peaks = peaks[-self.params['min_points']:]
            recent_troughs = troughs[-self.params['min_points']:]
            
            # Check if peaks are at similar levels
            peak_prices = prices[recent_peaks]
            peak_diff = np.max(peak_prices) - np.min(peak_prices)
            peak_ratio = peak_diff / np.mean(peak_prices)
            
            # Check if troughs are rising
            trough_prices = prices[recent_troughs]
            trough_slope = np.polyfit(range(len(trough_prices)), trough_prices, 1)[0]
            
            if (peak_ratio <= tolerance and trough_slope > 0):
                # Mark the pattern
                start_idx = min(recent_peaks[0], recent_troughs[0])
                end_idx = max(recent_peaks[-1], recent_troughs[-1])
                is_pattern[start_idx:end_idx+1] = 1
                pattern_type[start_idx:end_idx+1] = 1  # Bullish pattern
        
        return is_pattern, pattern_type
    
    def _is_descending_triangle(self, prices: np.ndarray, peaks: np.ndarray,
                              troughs: np.ndarray, tolerance: float) -> tuple:
        """Identify Descending Triangle patterns."""
        is_pattern = np.zeros_like(prices)
        pattern_type = np.zeros_like(prices)
        
        if len(peaks) >= self.params['min_points'] and len(troughs) >= self.params['min_points']:
            # Get the most recent peaks and troughs
            recent_peaks = peaks[-self.params['min_points']:]
            recent_troughs = troughs[-self.params['min_points']:]
            
            # Check if troughs are at similar levels
            trough_prices = prices[recent_troughs]
            trough_diff = np.max(trough_prices) - np.min(trough_prices)
            trough_ratio = trough_diff / np.mean(trough_prices)
            
            # Check if peaks are declining
            peak_prices = prices[recent_peaks]
            peak_slope = np.polyfit(range(len(peak_prices)), peak_prices, 1)[0]
            
            if (trough_ratio <= tolerance and peak_slope < 0):
                # Mark the pattern
                start_idx = min(recent_peaks[0], recent_troughs[0])
                end_idx = max(recent_peaks[-1], recent_troughs[-1])
                is_pattern[start_idx:end_idx+1] = 1
                pattern_type[start_idx:end_idx+1] = -1  # Bearish pattern
        
        return is_pattern, pattern_type
    
    def _is_symmetric_triangle(self, prices: np.ndarray, peaks: np.ndarray,
                             troughs: np.ndarray, tolerance: float) -> tuple:
        """Identify Symmetric Triangle patterns."""
        is_pattern = np.zeros_like(prices)
        pattern_type = np.zeros_like(prices)
        
        if len(peaks) >= self.params['min_points'] and len(troughs) >= self.params['min_points']:
            # Get the most recent peaks and troughs
            recent_peaks = peaks[-self.params['min_points']:]
            recent_troughs = troughs[-self.params['min_points']:]
            
            # Calculate slopes
            peak_prices = prices[recent_peaks]
            trough_prices = prices[recent_troughs]
            peak_slope = np.polyfit(range(len(peak_prices)), peak_prices, 1)[0]
            trough_slope = np.polyfit(range(len(trough_prices)), trough_prices, 1)[0]
            
            # Check if slopes are opposite and similar in magnitude
            if (peak_slope < 0 and trough_slope > 0 and
                abs(peak_slope + trough_slope) <= tolerance):
                # Mark the pattern
                start_idx = min(recent_peaks[0], recent_troughs[0])
                end_idx = max(recent_peaks[-1], recent_troughs[-1])
                is_pattern[start_idx:end_idx+1] = 1
                # Determine pattern type based on price position
                if prices[end_idx] > prices[start_idx]:
                    pattern_type[start_idx:end_idx+1] = 1  # Bullish pattern
                else:
                    pattern_type[start_idx:end_idx+1] = -1  # Bearish pattern
        
        return is_pattern, pattern_type
    
    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        close = df['close'].values
        min_distance = self.params['min_peak_distance']
        min_height = self.params['min_peak_height']
        tolerance = self.params['price_tolerance']
        
        # Find peaks and troughs
        peaks, troughs = self._find_peaks_and_troughs(close, min_distance, min_height)
        
        # Identify patterns
        is_at, at_type = self._is_ascending_triangle(close, peaks, troughs, tolerance)
        is_dt, dt_type = self._is_descending_triangle(close, peaks, troughs, tolerance)
        is_st, st_type = self._is_symmetric_triangle(close, peaks, troughs, tolerance)
        
        # Combine patterns using logical operations
        is_pattern = np.logical_or(np.logical_or(is_at, is_dt), is_st).astype(bool)
        pattern_type = np.where(is_at, at_type, np.where(is_dt, dt_type, st_type))
        
        # Calculate pattern strength
        strength = np.zeros_like(close)
        for i in range(len(close)):
            if is_pattern[i]:
                # Calculate the height of the pattern relative to price
                if pattern_type[i] > 0:  # Bullish pattern
                    height = close[peaks[peaks > i][0]] - close[troughs[troughs > i][0]]
                else:  # Bearish pattern
                    height = close[peaks[peaks > i][0]] - close[troughs[troughs > i][0]]
                strength[i] = height / close[i]
        
        # Calculate trend context
        trend = np.full_like(close, -1, dtype=int)  # Default to -1
        for i in range(20, len(close)):  # Start from 20 to have enough data for SMA
            sma = np.mean(close[i-20:i])
            trend[i] = 1 if close[i] > sma else -1
        
        # Calculate pattern reliability
        reliability = np.full_like(close, -1, dtype=float)  # Default to -1 for no pattern
        future_window = 20
        
        for i in range(len(close) - future_window):
            if is_pattern[i]:
                future_returns = (df['close'].iloc[i+1:i+future_window+1].values - 
                                df['close'].iloc[i]) / df['close'].iloc[i]
                
                if pattern_type[i] > 0:  # Bullish pattern
                    max_return = np.max(future_returns)
                    reliability[i] = 1 if max_return > 0 else -1
                else:  # Bearish pattern
                    min_return = np.min(future_returns)
                    reliability[i] = 1 if min_return < 0 else -1
        
        # Calculate pattern type name
        pattern_type_name = np.zeros_like(close, dtype=int)
        pattern_type_name[is_at.astype(bool)] = 1  # Ascending Triangle
        pattern_type_name[is_dt.astype(bool)] = 2  # Descending Triangle
        pattern_type_name[is_st.astype(bool)] = 3  # Symmetric Triangle
        
        return {
            'is_pattern': is_pattern.astype(int),
            'pattern_type': pattern_type,
            'pattern_type_name': pattern_type_name,
            'strength': strength,
            'trend': trend,
            'reliability': reliability
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['min_peak_distance'] < 2:
            raise ValueError("Minimum peak distance must be at least 2")
        if not 0 < self.params['min_peak_height'] < 1:
            raise ValueError("Minimum peak height must be between 0 and 1")
        if not 0 < self.params['price_tolerance'] < 1:
            raise ValueError("Price tolerance must be between 0 and 1")
        if self.params['min_points'] < 3:
            raise ValueError("Minimum points must be at least 3")
        return True 