# Phase 5: Multi-Timeframe Signal Confirmation

## Overview

This phase implements multi-timeframe signal confirmation to improve trade entry quality. By requiring confirmation from multiple timeframes, the system can filter out false signals and only execute trades with higher probability of success.

## Current Implementation

Currently, the Garuda-Algo autonomous trading system primarily uses signals from a single timeframe for trading decisions, which can lead to false signals and premature entries. There is limited consideration of higher timeframe trends or confirmation across multiple timeframes.

## Implementation Details

### 1. Multi-Timeframe Analysis Framework

Create a new file `backend/multi_timeframe.py` for multi-timeframe analysis:

```python
import logging
from typing import Dict, List, Any

logger = logging.getLogger('multi_timeframe')

class MultiTimeframeAnalyzer:
    """Class for multi-timeframe analysis and signal confirmation."""
    
    def __init__(self, mt5_connector, analysis_engine, config):
        self.mt5 = mt5_connector
        self.analysis = analysis_engine
        self.config = config
        self.mtf_config = config.get("multi_timeframe", {})
        
    def analyze_timeframes(self, symbol, timeframes=None):
        """Analyze multiple timeframes for a symbol.
        
        Args:
            symbol: Trading symbol
            timeframes: List of timeframes to analyze, or None to use config
            
        Returns:
            dict: Analysis results for each timeframe
        """
        if timeframes is None:
            # Use timeframes from configuration
            timeframes = self.mtf_config.get("confirmation_timeframes", ["M15", "H1", "H4"])
            
        results = {}
        
        try:
            # Get current price
            price_data = self.mt5.get_current_price(symbol)
            if "error" in price_data:
                return {
                    "success": False,
                    "message": f"Failed to get price for {symbol}: {price_data['error']}"
                }
            
            # Analyze each timeframe
            for tf in timeframes:
                try:
                    # Perform analysis for timeframe
                    analysis_result = self.analysis.get_analysis_for_timeframe(symbol, tf)
                    
                    if not analysis_result.get("success", False):
                        logger.warning(f"Analysis failed for {symbol}/{tf}: {analysis_result.get('message', 'Unknown error')}")
                        continue
                        
                    # Extract analysis data
                    analysis_data = analysis_result.get("analysis", analysis_result.get("data", {}))
                    
                    # Ensure analysis data includes current price
                    analysis_data["current_price"] = price_data
                    
                    # Generate signals for this timeframe
                    from backend.api.signals import generate_signals_from_analysis
                    signals_result = generate_signals_from_analysis(symbol, tf, analysis_data, price_data)
                    
                    # Store results for this timeframe
                    results[tf] = {
                        "analysis": analysis_data,
                        "signals": signals_result.get("signals", []),
                        "indicators": analysis_data.get("indicators", {})
                    }
                    
                except Exception as e:
                    logger.exception(f"Error analyzing timeframe {tf} for {symbol}: {str(e)}")
                    results[tf] = {
                        "error": str(e)
                    }
            
            return {
                "success": True,
                "results": results,
                "price_data": price_data
            }
            
        except Exception as e:
            logger.exception(f"Error in multi-timeframe analysis for {symbol}: {str(e)}")
            return {
                "success": False,
                "message": f"Error in multi-timeframe analysis: {str(e)}"
            }
    
    def get_consensus_signal(self, symbol, primary_timeframe, analysis_results=None):
        """Get consensus signal across multiple timeframes.
        
        Args:
            symbol: Trading symbol
            primary_timeframe: Primary timeframe for entry signals
            analysis_results: Optional pre-computed analysis results
            
        Returns:
            dict: Consensus signal with confirmation details
        """
        try:
            # If no analysis results provided, perform analysis
            if analysis_results is None or not analysis_results.get("success", False):
                analysis_results = self.analyze_timeframes(symbol)
                
                if not analysis_results.get("success", False):
                    return analysis_results  # Return error
            
            results = analysis_results.get("results", {})
            if not results or primary_timeframe not in results:
                return {
                    "success": False,
                    "message": f"No analysis results for {symbol}/{primary_timeframe}"
                }
            
            # Get configuration parameters
            min_timeframes = self.mtf_config.get("min_timeframes_agreement", 2)
            higher_tf_weight = self.mtf_config.get("higher_timeframe_weight", 1.5)
            
            # Get all timeframes in ascending order (smallest first)
            timeframes = sorted(results.keys(), key=self.timeframe_to_minutes)
            
            # Assign weights to timeframes (higher timeframes get higher weight)
            timeframe_weights = {}
            base_weight = 1.0
            for i, tf in enumerate(timeframes):
                # Higher timeframes get progressively higher weights
                if i > 0:  # Not the lowest timeframe
                    weight = base_weight * (higher_tf_weight ** i)
                else:
                    weight = base_weight
                timeframe_weights[tf] = weight
            
            # Extract signals from each timeframe
            buy_signals = []
            sell_signals = []
            
            for tf, tf_results in results.items():
                if "signals" not in tf_results:
                    continue
                
                tf_signals = tf_results["signals"]
                for signal in tf_signals:
                    signal_type = signal.get("signalType", "").lower()
                    confidence = signal.get("confidence", 0)
                    
                    # Store signal with timeframe and weight info
                    signal_info = {
                        "timeframe": tf,
                        "confidence": confidence,
                        "weight": timeframe_weights[tf],
                        "strategy": signal.get("strategy", ""),
                        "weighted_confidence": confidence * timeframe_weights[tf]
                    }
                    
                    if signal_type == "buy":
                        buy_signals.append(signal_info)
                    elif signal_type == "sell":
                        sell_signals.append(signal_info)
            
            # Sort signals by weighted confidence
            buy_signals.sort(key=lambda x: x["weighted_confidence"], reverse=True)
            sell_signals.sort(key=lambda x: x["weighted_confidence"], reverse=True)
            
            # Count unique timeframes with buy/sell signals
            buy_timeframes = len(set(s["timeframe"] for s in buy_signals))
            sell_timeframes = len(set(s["timeframe"] for s in sell_signals))
            
            # Calculate weighted average confidence
            buy_confidence = 0
            buy_weight_sum = 0
            for signal in buy_signals:
                buy_confidence += signal["confidence"] * signal["weight"]
                buy_weight_sum += signal["weight"]
            
            sell_confidence = 0
            sell_weight_sum = 0
            for signal in sell_signals:
                sell_confidence += signal["confidence"] * signal["weight"]
                sell_weight_sum += signal["weight"]
            
            # Normalize confidence scores
            if buy_weight_sum > 0:
                buy_confidence /= buy_weight_sum
            if sell_weight_sum > 0:
                sell_confidence /= sell_weight_sum
            
            # Determine consensus
            consensus = "none"
            consensus_confidence = 0
            consensus_timeframes = 0
            
            if buy_timeframes >= min_timeframes and buy_confidence > sell_confidence:
                consensus = "buy"
                consensus_confidence = buy_confidence
                consensus_timeframes = buy_timeframes
            elif sell_timeframes >= min_timeframes and sell_confidence > buy_confidence:
                consensus = "sell"
                consensus_confidence = sell_confidence
                consensus_timeframes = sell_timeframes
            
            # Get entry signal from primary timeframe
            entry_signal = None
            if primary_timeframe in results and "signals" in results[primary_timeframe]:
                primary_signals = results[primary_timeframe]["signals"]
                
                # Find highest confidence signal matching consensus
                for signal in primary_signals:
                    signal_type = signal.get("signalType", "").lower()
                    if signal_type == consensus:
                        if entry_signal is None or signal.get("confidence", 0) > entry_signal.get("confidence", 0):
                            entry_signal = signal
            
            return {
                "success": True,
                "consensus": consensus,
                "consensus_confidence": consensus_confidence,
                "consensus_timeframes": consensus_timeframes,
                "min_timeframes_required": min_timeframes,
                "entry_signal": entry_signal,
                "buy_signals": buy_signals,
                "sell_signals": sell_signals,
                "price_data": analysis_results.get("price_data")
            }
            
        except Exception as e:
            logger.exception(f"Error determining consensus signal for {symbol}: {str(e)}")
            return {
                "success": False,
                "message": f"Error determining consensus signal: {str(e)}"
            }
    
    def timeframe_to_minutes(self, timeframe):
        """Convert timeframe string to minutes for comparison."""
        # Map of timeframe to minutes
        tf_map = {
            "M1": 1,
            "M5": 5,
            "M15": 15,
            "M30": 30,
            "H1": 60,
            "H4": 240,
            "D1": 1440,
            "W1": 10080,
            "MN1": 43200
        }
        
        return tf_map.get(timeframe, 0)
```

### 2. Integration with Trading Loop

Modify `backend/api/autonomous.py` to use multi-timeframe confirmation in the trading loop:

```python
from backend.multi_timeframe import MultiTimeframeAnalyzer

def _trading_loop(app):
    """Main autonomous trading loop."""
    global AUTONOMOUS_RUNNING
    
    # ... existing code ...
    
    # Create analysis engine
    analysis_engine = AnalysisEngine(mt5_instance)
    
    # Create multi-timeframe analyzer
    mtf_analyzer = MultiTimeframeAnalyzer(mt5_instance, analysis_engine, AUTONOMOUS_CONFIG)
    
    while AUTONOMOUS_RUNNING:
        try:
            # ... existing connection, time filter, and position management checks ...
            
            # Process each symbol
            for symbol in AUTONOMOUS_CONFIG["symbols"]:
                # ... existing symbol validation ...
                
                # Get current strategy and configuration
                timeframe = AUTONOMOUS_CONFIG["timeframes"][0]  # Primary timeframe
                
                # Check if multi-timeframe confirmation is enabled
                if AUTONOMOUS_CONFIG["multi_timeframe"].get("enabled", False):
                    # Use multi-timeframe analysis instead of single timeframe
                    mtf_result = mtf_analyzer.get_consensus_signal(symbol, timeframe)
                    
                    if not mtf_result.get("success", False):
                        logger.error(f"Multi-timeframe analysis failed for {symbol}: {mtf_result.get('message', 'Unknown error')}")
                        continue
                    
                    consensus = mtf_result["consensus"]
                    if consensus == "none":
                        logger.info(f"No consensus signal for {symbol} across timeframes. Skipping.")
                        continue
                    
                    # Use entry signal from primary timeframe if available
                    if mtf_result["entry_signal"] is None:
                        logger.info(f"Consensus found ({consensus}) but no entry signal in primary timeframe. Skipping.")
                        continue
                    
                    # Use the signal from multi-timeframe analysis
                    signal = consensus
                    best_signal = mtf_result["entry_signal"]
                    
                    logger.info(f"Multi-timeframe consensus: {consensus} signal for {symbol} with "
                              f"{mtf_result['consensus_confidence']:.1f}% confidence, "
                              f"confirmed across {mtf_result['consensus_timeframes']} timeframes")
                else:
                    # Use existing single timeframe analysis
                    # ... existing signal generation ...
                
                # ... rest of trading logic with entry signal ...
```

### 3. Timeframe Alignment Check

Implement hierarchical timeframe alignment checking:

```python
def check_timeframe_alignment(self, symbol, entry_timeframe, trade_direction):
    """Check if higher timeframes align with the entry timeframe direction.
    
    Args:
        symbol: Trading symbol
        entry_timeframe: Entry timeframe
        trade_direction: Proposed trade direction ('buy' or 'sell')
        
    Returns:
        dict: Result with alignment status and details
    """
    try:
        # Get all timeframes higher than entry_timeframe
        all_timeframes = sorted(
            self.mtf_config.get("confirmation_timeframes", ["M15", "H1", "H4"]),
            key=self.timeframe_to_minutes
        )
        
        # Find entry timeframe position
        try:
            entry_tf_index = all_timeframes.index(entry_timeframe)
        except ValueError:
            logger.warning(f"Entry timeframe {entry_timeframe} not in confirmation timeframes list")
            entry_tf_index = -1
        
        higher_timeframes = all_timeframes[entry_tf_index+1:] if entry_tf_index >= 0 else all_timeframes
        if not higher_timeframes:
            # No higher timeframes to check, consider aligned
            return {
                "success": True,
                "aligned": True,
                "message": f"No higher timeframes to check above {entry_timeframe}"
            }
        
        # Analyze higher timeframes
        aligned_timeframes = 0
        misaligned_timeframes = 0
        timeframe_results = {}
        
        # Get analysis for higher timeframes
        analysis_results = self.analyze_timeframes(symbol, higher_timeframes)
        if not analysis_results.get("success", False):
            return {
                "success": False,
                "message": f"Failed to analyze higher timeframes: {analysis_results.get('message', 'Unknown error')}"
            }
        
        results = analysis_results.get("results", {})
        
        # Check alignment for each timeframe
        for tf in higher_timeframes:
            if tf not in results or "signals" not in results[tf]:
                continue
            
            tf_signals = results[tf]["signals"]
            
            # Find highest confidence signal in this timeframe
            highest_confidence = 0
            highest_signal_type = "none"
            
            for signal in tf_signals:
                signal_type = signal.get("signalType", "").lower()
                confidence = signal.get("confidence", 0)
                
                if confidence > highest_confidence:
                    highest_confidence = confidence
                    highest_signal_type = signal_type
            
            # Check if aligned with proposed direction
            is_aligned = highest_signal_type == trade_direction
            
            # Store result
            timeframe_results[tf] = {
                "signal": highest_signal_type,
                "confidence": highest_confidence,
                "aligned": is_aligned
            }
            
            if is_aligned:
                aligned_timeframes += 1
            else:
                misaligned_timeframes += 1
        
        # Determine overall alignment
        # Consider aligned if more timeframes align than misalign
        is_aligned = aligned_timeframes > misaligned_timeframes
        
        return {
            "success": True,
            "aligned": is_aligned,
            "aligned_timeframes": aligned_timeframes,
            "misaligned_timeframes": misaligned_timeframes,
            "timeframe_results": timeframe_results,
            "message": f"{aligned_timeframes} higher timeframes aligned with {trade_direction}, {misaligned_timeframes} misaligned"
        }
        
    except Exception as e:
        logger.exception(f"Error checking timeframe alignment for {symbol}: {str(e)}")
        return {
            "success": False,
            "message": f"Error checking timeframe alignment: {str(e)}"
        }
```

### 4. Position Sizing Based on Confirmation Strength

Implement adaptive position sizing based on confirmation strength:

```python
def calculate_position_size_multiplier(self, mtf_result):
    """Calculate position size multiplier based on multi-timeframe confirmation strength.
    
    Args:
        mtf_result: Result from get_consensus_signal
        
    Returns:
        float: Position size multiplier (0.5 to 1.5)
    """
    if not mtf_result.get("success", True):
        return 1.0  # Default multiplier on error
    
    # Base factors
    confidence_factor = 0.5  # 50% from confidence
    timeframe_factor = 0.5   # 50% from timeframe count
    
    # Get values from result
    consensus_confidence = mtf_result.get("consensus_confidence", 0)
    consensus_timeframes = mtf_result.get("consensus_timeframes", 0)
    min_timeframes = mtf_result.get("min_timeframes_required", 2)
    
    # Calculate confidence component (0 to 0.5)
    # Map 60-100% confidence to 0-0.5 factor
    if consensus_confidence < 60:
        confidence_component = 0
    elif consensus_confidence > 100:
        confidence_component = 0.5
    else:
        confidence_component = (consensus_confidence - 60) / 80 * 0.5
    
    # Calculate timeframe component (0 to 0.5)
    # More confirming timeframes = higher factor
    # Start with min_timeframes as baseline (0.25) and increase from there
    if consensus_timeframes <= min_timeframes:
        timeframe_component = 0.25
    else:
        # Each additional timeframe adds up to 0.25 more
        additional = consensus_timeframes - min_timeframes
        timeframe_component = 0.25 + min(0.25, additional * 0.1)
    
    # Combine components to get multiplier (0.5 to 1.5)
    multiplier = 1.0 + confidence_component + timeframe_component - 0.5
    
    return max(0.5, min(1.5, multiplier))  # Limit to 0.5-1.5 range
```

## Files to Modify/Create

1. **backend/multi_timeframe.py** (new):
   - Implement MultiTimeframeAnalyzer class
   - Add consensus determination functions
   - Add timeframe alignment checks
   - Add position sizing calculation

2. **backend/api/autonomous.py**:
   - Integrate multi-timeframe confirmation into trading loop
   - Adapt position sizing based on confirmation strength
   - Update configuration with multi-timeframe settings

## Testing Plan

1. **Multi-Timeframe Analysis**:
   - Test analysis across multiple timeframes
   - Verify proper extraction of signals
   - Test with different combinations of timeframes

2. **Consensus Determination**:
   - Test signal weighting across timeframes
   - Verify correct identification of consensus
   - Test with conflicting signals across timeframes

3. **Timeframe Alignment**:
   - Test alignment checking with higher timeframes
   - Verify handling of mixed signals
   - Test proper counting of aligned/misaligned timeframes

4. **Position Sizing**:
   - Test calculation of size multipliers
   - Verify scaling based on confirmation strength
   - Test with various signal scenarios

5. **Integration Tests**:
   - Verify end-to-end flow with multi-timeframe confirmation
   - Test with real market data
   - Verify logging of consensus information

## Acceptance Criteria

- System uses signals from multiple timeframes for trade decisions
- Higher timeframe confirmation is required for trade entry
- Lower timeframes are used for precise entry timing
- Position sizing is adjusted based on confirmation strength
- All multi-timeframe confirmation details are properly logged
