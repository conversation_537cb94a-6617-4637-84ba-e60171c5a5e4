import React from 'react';
import SignalCard from './SignalCard';

/**
 * SignalMatrix component - Displays all available signals in a grid layout
 */
function SignalMatrix({ 
  signalData, 
  strategyFilter, 
  minimumConfidence, 
  onSignalSelect,
  selectedSignals 
}) {
  // Filter signals based on strategy and confidence
  const filteredSignals = signalData?.signals?.filter(signal => {
    // Apply strategy filter
    if (strategyFilter !== 'all' && signal.strategy !== strategyFilter) {
      return false;
    }
    
    // Apply confidence filter
    if (signal.confidence < minimumConfidence) {
      return false;
    }
    
    return true;
  }) || [];
  
  // Group signals by strategy for easier visualization
  const groupedSignals = {
    scalping: filteredSignals.filter(s => s.strategy === 'scalping'),
    intraday: filteredSignals.filter(s => s.strategy === 'intraday'),
    swing: filteredSignals.filter(s => s.strategy === 'swing'),
    position: filteredSignals.filter(s => s.strategy === 'position')
  };
  
  // Count active signals
  const totalSignals = filteredSignals.length;
  
  return (
    <div className="signal-matrix">
      <div className="matrix-header">
        <h3>Available Signals {totalSignals > 0 && `(${totalSignals})`}</h3>
      </div>
      
      {totalSignals === 0 ? (
        <div className="no-signals-message">
          <p>
            {strategyFilter === 'all' 
              ? 'No signals available matching your confidence criteria.'
              : `No ${strategyFilter} signals available matching your confidence criteria.`
            }
          </p>
          <p>Try adjusting your filters or select a different symbol/timeframe.</p>
        </div>
      ) : (
        <div className="matrix-content">
          {/* Scalping Signals */}
          {(strategyFilter === 'all' || strategyFilter === 'scalping') && groupedSignals.scalping.length > 0 && (
            <div className="signal-group">
              <h4 className="strategy-heading">Scalping Signals</h4>
              <div className="signals-row">
                {groupedSignals.scalping.map(signal => (
                  <SignalCard 
                    key={signal.id}
                    signal={signal}
                    onSelect={onSignalSelect}
                    isSelected={selectedSignals.includes(signal.id)}
                  />
                ))}
              </div>
            </div>
          )}
          
          {/* Intraday Signals */}
          {(strategyFilter === 'all' || strategyFilter === 'intraday') && groupedSignals.intraday.length > 0 && (
            <div className="signal-group">
              <h4 className="strategy-heading">Intraday Signals</h4>
              <div className="signals-row">
                {groupedSignals.intraday.map(signal => (
                  <SignalCard 
                    key={signal.id}
                    signal={signal}
                    onSelect={onSignalSelect}
                    isSelected={selectedSignals.includes(signal.id)}
                  />
                ))}
              </div>
            </div>
          )}
          
          {/* Swing Signals */}
          {(strategyFilter === 'all' || strategyFilter === 'swing') && groupedSignals.swing.length > 0 && (
            <div className="signal-group">
              <h4 className="strategy-heading">Swing Signals</h4>
              <div className="signals-row">
                {groupedSignals.swing.map(signal => (
                  <SignalCard 
                    key={signal.id}
                    signal={signal}
                    onSelect={onSignalSelect}
                    isSelected={selectedSignals.includes(signal.id)}
                  />
                ))}
              </div>
            </div>
          )}
          
          {/* Position Signals */}
          {(strategyFilter === 'all' || strategyFilter === 'position') && groupedSignals.position.length > 0 && (
            <div className="signal-group">
              <h4 className="strategy-heading">Position Signals</h4>
              <div className="signals-row">
                {groupedSignals.position.map(signal => (
                  <SignalCard 
                    key={signal.id}
                    signal={signal}
                    onSelect={onSignalSelect}
                    isSelected={selectedSignals.includes(signal.id)}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default SignalMatrix;
