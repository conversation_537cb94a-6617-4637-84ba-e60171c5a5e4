from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class VWAPIndicator(BaseIndicator):
    """Volume Weighted Average Price (VWAP) indicator."""
    
    def __init__(self, period: int = 0, num_stdev: float = 2.0,
                 anchor: str = 'daily'):
        """
        Initialize VWAP indicator.
        
        Args:
            period: The period for calculating VWAP (0 for session-based)
            num_stdev: Number of standard deviations for bands
            anchor: VWAP anchor point ('daily', 'weekly', 'monthly')
        """
        super().__init__({
            'period': period,
            'num_stdev': num_stdev,
            'anchor': anchor
        })
    
    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate VWAP values."""
        df = data.to_dataframe()
        if df.empty:
            return {}
        
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        volume = df['volume'].values
        period = self.params['period']
        num_stdev = self.params['num_stdev']
        anchor = self.params['anchor'].lower()
        
        # Calculate typical price
        typical_price = (high + low + close) / 3
        
        # Calculate cumulative values
        if period > 0:
            # Rolling window VWAP
            price_vol = pd.Series(typical_price * volume)
            cum_vol = pd.Series(volume).rolling(window=period).sum()
            cum_pv = price_vol.rolling(window=period).sum()
            vwap = cum_pv / cum_vol
        else:
            # Session-based VWAP
            dates = pd.to_datetime(df.index)
            if anchor == 'weekly':
                reset_mask = dates.weekday == 0
            elif anchor == 'monthly':
                reset_mask = dates.day == 1
            else:  # daily
                reset_mask = dates.time == dates.time.min()
            
            cum_vol = np.zeros_like(volume, dtype=float)
            cum_pv = np.zeros_like(volume, dtype=float)
            
            for i in range(len(volume)):
                if i == 0 or reset_mask[i]:
                    cum_vol[i] = volume[i]
                    cum_pv[i] = typical_price[i] * volume[i]
                else:
                    cum_vol[i] = cum_vol[i-1] + volume[i]
                    cum_pv[i] = cum_pv[i-1] + (typical_price[i] * volume[i])
            
            vwap = cum_pv / cum_vol
        
        # Calculate standard deviation
        squared_diff = (typical_price - vwap) ** 2
        if period > 0:
            variance = pd.Series(squared_diff * volume).rolling(window=period).sum() / cum_vol
        else:
            variance = np.zeros_like(volume, dtype=float)
            for i in range(len(volume)):
                if i == 0 or reset_mask[i]:
                    variance[i] = 0
                else:
                    variance[i] = (squared_diff[i] * volume[i] + 
                                 variance[i-1] * cum_vol[i-1]) / cum_vol[i]
        
        std_dev = np.sqrt(variance)
        
        # Calculate VWAP bands
        upper_band = vwap + (num_stdev * std_dev)
        lower_band = vwap - (num_stdev * std_dev)
        
        # Calculate trend
        trend = np.zeros_like(close)
        trend[close > vwap] = 1
        trend[close < vwap] = -1
        
        # Calculate distance from VWAP
        distance = ((close - vwap) / vwap) * 100
        
        # Calculate crossovers
        crossover = np.where(
            (close > vwap) & (pd.Series(close).shift(1) <= pd.Series(vwap).shift(1)), 1,
            np.where((close < vwap) & (pd.Series(close).shift(1) >= pd.Series(vwap).shift(1)), -1, 0)
        )
        
        # Calculate strength zones
        strength = np.zeros_like(close)
        strength[(distance >= 1) & (distance < 2)] = 1     # Strong
        strength[distance >= 2] = 2                        # Very Strong
        strength[(distance <= -1) & (distance > -2)] = -1   # Weak
        strength[distance <= -2] = -2                      # Very Weak
        
        # Calculate volume profile
        volume_above = np.where(close > vwap, volume, 0)
        volume_below = np.where(close < vwap, volume, 0)
        
        if period > 0:
            volume_ratio = (pd.Series(volume_above).rolling(window=period).sum() /
                          pd.Series(volume_below).rolling(window=period).sum())
        else:
            cum_vol_above = np.zeros_like(volume, dtype=float)
            cum_vol_below = np.zeros_like(volume, dtype=float)
            
            for i in range(len(volume)):
                if i == 0 or reset_mask[i]:
                    cum_vol_above[i] = volume_above[i]
                    cum_vol_below[i] = volume_below[i]
                else:
                    cum_vol_above[i] = cum_vol_above[i-1] + volume_above[i]
                    cum_vol_below[i] = cum_vol_below[i-1] + volume_below[i]
            
            volume_ratio = cum_vol_above / cum_vol_below
        
        self._values = {
            'vwap': vwap,
            'upper_band': upper_band,
            'lower_band': lower_band,
            'std_dev': std_dev,
            'trend': trend,
            'distance': distance,
            'crossover': crossover,
            'strength': strength,
            'volume_ratio': volume_ratio,
            'cum_vol': cum_vol,
            'cum_pv': cum_pv,
            'typical_price': typical_price
        }
        return self._values
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_anchors = ['daily', 'weekly', 'monthly']
        if self.params['anchor'].lower() not in valid_anchors:
            raise ValueError(f"Anchor must be one of {valid_anchors}")
        if self.params['period'] < 0:
            raise ValueError("Period must be greater than or equal to 0")
        if self.params['num_stdev'] <= 0:
            raise ValueError("Number of standard deviations must be greater than 0")
        return True 