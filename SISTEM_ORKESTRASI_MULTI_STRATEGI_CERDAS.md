# Sistem Orkestrasi Multi-Strategi Cerdas

## 📋 Daftar Isi
- [<PERSON><PERSON><PERSON><PERSON>](#gambaran-umum)
- [Cara Kerja Multiple Strategi](#cara-kerja-multiple-strategi)
- [Algoritma Prioritas Cerdas](#algoritma-prioritas-cerdas)
- [<PERSON><PERSON><PERSON>](#analisis-kondisi-pasar)
- [Sistem Resolusi Konflik](#sistem-resolusi-konflik)
- [Rekomendasi Timeframe](#rekomendasi-timeframe)
- [Pelacakan Performa](#pelacakan-performa)
- [<PERSON>toh Praktis](#contoh-praktis)
- [API Endpoints](#api-endpoints)
- [Praktik Terbaik](#praktik-terbaik)

## 🎯 Gambaran Umum

Sistem orkestrasi multi-strategi cerdas Garuda Algo V2 memungkinkan beberapa strategi trading otomatis ber<PERSON><PERSON> bersamaan dengan koordinasi yang pintar. Berbeda dengan sistem sederhana yang memproses strategi secara berurutan, sistem kami menggunakan algoritma canggih untuk menentukan strategi mana yang harus dieksekusi berdasarkan kondisi pasar saat ini, kualitas sinyal, dan metrik performa.

### Fitur Utama
- **Eksekusi Strategi Bersamaan**: Jalankan strategi scalping, intraday, swing, dan position bersamaan
- **Prioritas Cerdas**: Algoritma 5-faktor menentukan eksekusi strategi optimal
- **Kesadaran Kondisi Pasar**: Mencocokkan strategi dengan lingkungan pasar optimal
- **Resolusi Konflik**: Menangani sinyal berlawanan secara cerdas
- **Adaptasi Performa**: Belajar dari performa strategi untuk meningkatkan keputusan
- **Koordinasi Risiko**: Memastikan semua strategi menghormati batas risiko bersama

## 🔄 Cara Kerja Multiple Strategi

### Independensi Strategi
Setiap strategi beroperasi secara independen dengan:
- **Simbol & Timeframe**: Pair trading dan timeframe chart yang berbeda
- **Pengaturan Risiko**: Risiko individual per trading dan ambang batas confidence
- **Entry Spacing**: Aturan spacing independen dan periode cooldown
- **Generasi Sinyal**: Analisis dan pembuatan sinyal terpisah

### Komponen Bersama
Semua strategi berbagi:
- **Trading Loop**: Thread eksekusi tunggal memproses semua strategi
- **Batas Risiko**: Max total risk global berlaku untuk semua strategi gabungan
- **Koneksi MT5**: Semua strategi menggunakan koneksi MetaTrader 5 yang sama
- **Pelacakan Performa**: Monitoring terpusat dari semua hasil strategi

### Proses Koordinasi
1. **Pengumpulan Sinyal**: Kumpulkan sinyal dari semua strategi aktif
2. **Analisis Pasar**: Tentukan kondisi pasar saat ini untuk setiap simbol
3. **Kalkulasi Prioritas**: Skor setiap sinyal menggunakan algoritma 5-faktor
4. **Resolusi Konflik**: Tangani sinyal berlawanan secara cerdas
5. **Keputusan Eksekusi**: Eksekusi sinyal prioritas tertinggi dalam batas risiko

## 🧠 Algoritma Prioritas Cerdas

### Sistem Skor Prioritas 5-Faktor

Sistem menghitung skor prioritas (0.0 hingga 1.0) untuk setiap sinyal strategi menggunakan lima faktor berbobot:

#### 1. Kecocokan Kondisi Pasar (35% bobot)
**Tujuan**: Prioritaskan strategi yang cocok dengan kondisi pasar saat ini

**Afinitas Strategi-Pasar**:
- **Scalping**: Optimal di pasar ranging/volatile
- **Intraday**: Optimal di pasar trending (lemah/kuat)
- **Swing**: Optimal di pasar reversal/breakout
- **Position**: Optimal di pasar trending kuat/breakout

**Penilaian**:
- Cocok sempurna: 1.0
- Cocok sebagian: 0.7
- Cocok buruk: 0.3

#### 2. Confidence Sinyal (25% bobot)
**Tujuan**: Sinyal confidence tinggi mendapat prioritas

**Kalkulasi**: `skor_confidence = confidence_sinyal / 100`

**Contoh**: 85% confidence = 0.85 skor

#### 3. Performa Terkini (20% bobot)
**Tujuan**: Strategi dengan performa terkini lebih baik mendapat prioritas

**Metrik**:
- Win rate dari 5 trading terakhir (60% bobot)
- Rata-rata profit trading terkini (40% bobot)

**Kalkulasi**: `skor_performa = (win_rate * 0.6) + (profit_ternormalisasi * 0.4)`

#### 4. Optimasi Risiko (15% bobot)
**Tujuan**: Seimbangkan penggunaan risiko antar strategi

**Kalkulasi**: `skor_risiko = max(0, 1 - (risiko_sinyal / sisa_budget_risiko))`

**Manfaat**: Mencegah satu strategi menghabiskan seluruh budget risiko

#### 5. Prioritas Timeframe (5% bobot)
**Tujuan**: Preferensi sedikit untuk timeframe eksekusi lebih cepat

**Skor Timeframe**:
- M1: 0.9, M5: 0.8, M15: 0.7, M30: 0.6
- H1: 0.5, H4: 0.4, D1: 0.3, W1: 0.2, MN1: 0.1

### Formula Skor Prioritas
```
Prioritas = (cocok_pasar × 0.35) + (confidence × 0.25) + (performa × 0.20) + (opt_risiko × 0.15) + (timeframe × 0.05)
```

## 📊 Analisis Kondisi Pasar

### Jenis Kondisi Pasar

#### 1. Trending Kuat
**Karakteristik**:
- Nilai histogram MACD tinggi (> 0.6)
- Pergerakan arah yang jelas
- Momentum berkelanjutan

**Strategi Optimal**: Intraday Trend, Position Trading

#### 2. Trending Lemah
**Karakteristik**:
- Histogram MACD moderat (0.3 - 0.6)
- Beberapa bias arah
- Momentum moderat

**Strategi Optimal**: Intraday Trend

#### 3. Ranging
**Karakteristik**:
- Kekuatan trend rendah (< 0.3)
- Harga berosilasi antar level
- Volatilitas normal

**Strategi Optimal**: Scalping Momentum

#### 4. Volatile
**Karakteristik**:
- Nilai ATR tinggi
- Pergerakan harga cepat
- Kekuatan trend tinggi dengan volatilitas

**Strategi Optimal**: Scalping Momentum

#### 5. Breakout
**Karakteristik**:
- Volatilitas tinggi + RSI ekstrem (> 70 atau < 30)
- Momentum kuat
- Harga menembus level kunci

**Strategi Optimal**: Swing Reversal, Position Trading

#### 6. Reversal
**Karakteristik**:
- Level RSI ekstrem
- Kekuatan trend rendah
- Potensi perubahan arah

**Strategi Optimal**: Swing Reversal

### Proses Analisis Pasar
1. **Pengumpulan Indikator**: Kumpulkan ATR, RSI, MACD, Bollinger Bands
2. **Penilaian Volatilitas**: Bandingkan ATR saat ini dengan rata-rata
3. **Analisis Trend**: Evaluasi histogram dan arah MACD
4. **Deteksi Ekstrem**: Periksa kondisi overbought/oversold
5. **Klasifikasi Kondisi**: Tetapkan kondisi pasar berdasarkan kriteria
6. **Cache Hasil**: Simpan analisis selama 5 menit untuk meningkatkan performa

## ⚔️ Sistem Resolusi Konflik

### Deteksi Konflik
**Konflik terjadi ketika**:
- Multiple strategi menghasilkan sinyal berlawanan (BUY vs SELL) untuk simbol sama
- Strategi bersaing untuk budget risiko terbatas
- Konflik timeframe pada simbol sama

### Proses Resolusi

#### 1. Pengelompokan Sinyal
Kelompokkan semua sinyal berdasarkan simbol untuk mengidentifikasi konflik

#### 2. Deteksi Oposisi
Identifikasi konflik BUY vs SELL untuk setiap simbol

#### 3. Perbandingan Prioritas
Hitung skor prioritas untuk semua sinyal yang berkonflik

#### 4. Pemilihan Pemenang
Pilih sinyal dengan skor prioritas tertinggi

#### 5. Logging & Transparansi
Log alasan keputusan untuk audit trail

### Contoh Resolusi Konflik
```
Konflik EURUSD Terdeteksi:
- Strategi Scalping: SELL
  - Confidence: 75%
  - Cocok Pasar: Buruk (0.3)
  - Skor Prioritas: 0.68
  
- Strategi Swing: BUY
  - Confidence: 80%
  - Cocok Pasar: Optimal (1.0)
  - Skor Prioritas: 0.91

Pemenang: Strategi Swing BUY
Alasan: Prioritas lebih tinggi karena kecocokan kondisi pasar optimal
```

## ⏰ Rekomendasi Timeframe

### Rekomendasi Spesifik Strategi

#### Scalping Momentum
- **⭐ Optimal**: M1, M5 (Entry dan exit cepat)
- **✅ Bagus**: M15 (Dapat diterima untuk scalping)
- **❌ Hindari**: H1, H4, D1, W1, MN1 (Terlalu lambat untuk scalping)

**Alasan**: Scalping memerlukan pergerakan harga cepat dan pengambilan keputusan cepat

#### Intraday Trend Following
- **⭐ Optimal**: M15, M30, H1 (Tangkap trend intraday)
- **✅ Bagus**: M5, H4 (Identifikasi trend dapat diterima)
- **❌ Hindari**: M1, D1, W1, MN1 (Terlalu cepat/lambat untuk intraday)

**Alasan**: Strategi intraday memerlukan timeframe menengah untuk mengidentifikasi trend dalam hari trading

#### Swing Reversal
- **⭐ Optimal**: H1, H4 (Identifikasi pola multi-hari)
- **✅ Bagus**: M30, D1 (Dapat diterima untuk analisis swing)
- **❌ Hindari**: M1, M5, W1, MN1 (Timeframe salah untuk pola swing)

**Alasan**: Swing trading memerlukan timeframe tinggi untuk mengidentifikasi pola reversal

#### Position Trading
- **⭐ Optimal**: D1, W1 (Analisis trend jangka panjang)
- **✅ Bagus**: H4, MN1 (Dapat diterima untuk analisis posisi)
- **❌ Hindari**: M1, M5, M15, M30 (Terlalu pendek untuk position trading)

**Alasan**: Position trading fokus pada trend utama yang memerlukan analisis harian/mingguan

### Indikator Visual
- **⭐ Bintang**: Pilihan timeframe optimal
- **✅ Centang**: Pilihan timeframe bagus
- **❌ Tanda X**: Timeframe tidak direkomendasikan
- **Kode Warna**: Hijau (optimal), Biru (bagus), Merah (hindari)

## 📈 Pelacakan Performa

### Metrik Performa Strategi

#### Metrik Dasar
- **Total Trading**: Jumlah trading yang diselesaikan
- **Trading Menang**: Jumlah trading profitable
- **Win Rate**: Persentase trading profitable
- **Total Profit**: Profit/loss kumulatif
- **Rata-rata Profit**: Rata-rata profit per trading

#### Metrik Lanjutan
- **Performa Terkini**: Skor performa berdasarkan 5 trading terakhir
- **Trend Performa**: Indikator performa meningkat/menurun
- **Risk-Adjusted Returns**: Profit relatif terhadap risiko yang diambil
- **Performa Kondisi Pasar**: Performa berdasarkan jenis pasar

### Kalkulasi Performa
```python
# Skor Performa Terkini
trading_terkini = 5_trading_terakhir
komponen_win_rate = (trading_menang / total_trading) * 0.6
komponen_profit = (rata_rata_profit / 100) * 0.4
performa_terkini = komponen_win_rate + komponen_profit
```

### Dampak Performa pada Prioritas
- **Performa Tinggi** (> 0.7): Tingkatkan prioritas hingga 20%
- **Performa Rata-rata** (0.3 - 0.7): Dampak netral
- **Performa Buruk** (< 0.3): Kurangi prioritas hingga 20%

## 💡 Contoh Praktis

### Contoh 1: Skenario Pasar Trending
**Kondisi Pasar**: EURUSD - Trending Kuat
**Strategi Aktif**:
1. Scalping (M5): Confidence 80%, Prioritas 0.52 (cocok pasar buruk)
2. Intraday (M30): Confidence 75%, Prioritas 0.87 (cocok pasar optimal)
3. Swing (H4): Confidence 85%, Prioritas 0.71 (cocok pasar sebagian)

**Keputusan**: Strategi Intraday dieksekusi karena kecocokan kondisi pasar optimal

### Contoh 2: Resolusi Konflik
**Skenario**: GBPUSD sinyal berlawanan
- **Scalping SELL**: Confidence 85%, Cocok pasar buruk, Prioritas 0.68
- **Swing BUY**: Confidence 75%, Cocok pasar optimal, Prioritas 0.82

**Resolusi**: Swing BUY dieksekusi (prioritas lebih tinggi meski confidence lebih rendah)

### Contoh 3: Manajemen Budget Risiko
**Situasi**: Budget risiko terbatas tersisa
- **Strategi A**: Memerlukan risiko 2%, Prioritas 0.85
- **Strategi B**: Memerlukan risiko 0.5%, Prioritas 0.80
- **Budget Tersisa**: 1%

**Keputusan**: Strategi B dieksekusi (sesuai dengan budget risiko)

## 🔌 API Endpoints

### GET /autonomous/strategy-recommendations
**Tujuan**: Dapatkan rekomendasi strategi optimal untuk kondisi pasar saat ini

**Response**:
```json
{
  "success": true,
  "recommendations": {
    "EURUSD": {
      "market_condition": "trending_strong",
      "optimal_strategies": ["intraday_trend", "position_trading"],
      "confidence": "high"
    }
  },
  "timestamp": 1703123456
}
```

### GET /autonomous/strategy-performance
**Tujuan**: Dapatkan metrik performa untuk semua strategi

**Response**:
```json
{
  "success": true,
  "performance": {
    "scalping_momentum": {
      "total_trades": 45,
      "winning_trades": 28,
      "win_rate": 0.62,
      "total_profit": 1250.50,
      "avg_profit": 27.79,
      "recent_performance": 0.75
    }
  },
  "timestamp": 1703123456
}
```

## 📚 Praktik Terbaik

### Konfigurasi Strategi
1. **Diversifikasi Timeframe**: Gunakan timeframe berbeda untuk strategi berbeda
2. **Cocokkan Kondisi Pasar**: Pilih strategi yang sesuai untuk kondisi pasar yang diharapkan
3. **Seimbangkan Risiko**: Pastikan risiko gabungan tidak melebihi tingkat kenyamanan
4. **Monitor Performa**: Tinjau metrik performa strategi secara rutin

### Manajemen Risiko
1. **Tetapkan Batas Konservatif**: Mulai dengan persentase risiko lebih rendah
2. **Gunakan Batas Bersama**: Manfaatkan max total risk untuk perlindungan portfolio
3. **Monitor Korelasi**: Hindari terlalu banyak strategi berkorelasi
4. **Review Rutin**: Sesuaikan pengaturan berdasarkan performa

### Tips Optimasi
1. **Mulai Sederhana**: Mulai dengan maksimum 2-3 strategi
2. **Test Menyeluruh**: Gunakan akun demo untuk testing awal
3. **Scaling Bertahap**: Tingkatkan kompleksitas seiring bertambahnya pengalaman
4. **Analisis Performa**: Gunakan data performa untuk mengoptimalkan pengaturan

---

**Terakhir Diupdate**: Desember 2024
**Versi**: Garuda Algo V2
**Penulis**: Tim Sistem Trading Garuda

**Disclaimer**: Trading melibatkan risiko kerugian yang substansial. Sistem orkestrasi cerdas dirancang untuk mengoptimalkan koordinasi strategi tetapi tidak dapat menjamin hasil yang menguntungkan. Selalu trading dengan uang yang Anda mampu untuk kehilangan.
