import MetaTrader5 as mt5
import sys
import time

def test_mt5_connection_with_credentials():
    print("Testing MT5 connection with credentials...")
    
    # MT5 terminal path
    mt5_path = "C:\\Program Files\\MetaTrader 5\\terminal64.exe"
    
    # Account credentials
    login = *********  # Replace with your actual login
    password = "{2Rfj>0D"  # Replace with your actual password
    server = "FBS-Demo"  # Replace with your actual server
    
    print(f"Using MT5 path: {mt5_path}")
    print(f"Login: {login}")
    print(f"Server: {server}")
    
    # Initialize MT5
    print("\nInitializing MT5...")
    if not mt5.initialize(path=mt5_path):
        print(f"Failed to initialize MT5: {mt5.last_error()}")
        return False
    
    print("MT5 initialized successfully.")
    
    # Check if MT5 is connected
    terminal_info = mt5.terminal_info()
    if terminal_info is None:
        print("Failed to get terminal info.")
        mt5.shutdown()
        return False
    
    print(f"Terminal connected: {terminal_info.connected}")
    
    # Try to login
    print("\nAttempting to login...")
    login_result = mt5.login(login=login, password=password, server=server)
    
    if not login_result:
        print(f"Failed to login: {mt5.last_error()}")
        mt5.shutdown()
        return False
    
    print("Login successful!")
    
    # Get account info
    print("\nGetting account info...")
    account_info = mt5.account_info()
    if account_info is None:
        print("Failed to get account info.")
        mt5.shutdown()
        return False
    
    print(f"Account name: {account_info.name}")
    print(f"Account balance: {account_info.balance}")
    print(f"Account equity: {account_info.equity}")
    print(f"Account leverage: {account_info.leverage}")
    print(f"Account server: {account_info.server}")
    
    # Get positions
    print("\nGetting positions...")
    positions = mt5.positions_get()
    if positions is None:
        print(f"No positions or error: {mt5.last_error()}")
    else:
        print(f"Total positions: {len(positions)}")
    
    # Get symbols
    print("\nGetting symbols...")
    symbols = mt5.symbols_get()
    if symbols is None:
        print(f"Failed to get symbols: {mt5.last_error()}")
    else:
        print(f"Total symbols: {len(symbols)}")
        print(f"First 5 symbols: {[s.name for s in symbols[:5]]}")
    
    # Shutdown MT5
    print("\nShutting down MT5...")
    mt5.shutdown()
    print("MT5 shutdown successfully.")
    
    return True

if __name__ == "__main__":
    print("MT5 Connection Test With Credentials")
    print("===================================")
    
    start_time = time.time()
    success = test_mt5_connection_with_credentials()
    end_time = time.time()
    
    print(f"\nTest completed in {end_time - start_time:.2f} seconds.")
    print(f"Result: {'SUCCESS' if success else 'FAILURE'}")
    
    input("\nPress Enter to exit...")
