import React, { useState } from 'react';
// Removed: import { useNavigate } from 'react-router-dom';
import '../styles/Header.css';
import { useNotification } from './Notification';
import { useConnection } from '../context/ConnectionContext';
import appLogo from '../src/app_icon.png';

const Header = ({ isConnected, onConnect, onDisconnect, currentPage, navigateToPage, accountInfo = {} }) => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const notify = useNotification();
  // Removed: const navigate = useNavigate(); 
  const { reconnect, disconnect, isReconnecting } = useConnection();

  // Handle disconnect with both context and prop methods
  const handleDisconnect = () => {
    // Call the context disconnect method first
    disconnect();

    // Then call the prop method if available
    if (typeof onDisconnect === 'function') {
      onDisconnect();
    }

    notify.success('Disconnected', 'Successfully disconnected from MT5');
  };

  const handleNavClick = (page) => {
    navigateToPage(page); // This is for the main hamburger menu, ensure it's still working
    setMenuOpen(false);
  };

  const toggleMenu = () => {
    setMenuOpen(prev => !prev);
  };

  const toggleUserMenu = () => {
    setUserMenuOpen(prev => !prev);
  };

  const handleUserAction = (action) => {
    setUserMenuOpen(false); // Close menu first
    switch(action) {
      case 'settings':
        navigateToPage('settings'); // Use existing navigation system
        break;
      case 'profile':
        navigateToPage('profile'); // Use existing navigation system
        break;
      case 'help':
        navigateToPage('help');    // Use existing navigation system
        break;
      default:
        break;
    }
  };

  return (
    <header className="header">
      <div className="header-left">
        <img src={appLogo} alt="GarudaAlgo Logo" className="logo" />
        <h1>GarudaAlgo MT5 Trader</h1>
      </div>
      <div className="header-right">
        <div className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>
          <div className="status-dot"></div>
          <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
          {isConnected && accountInfo.accountName && (
            <span className="account-info">{accountInfo.accountName}</span>
          )}
          {isConnected ? (
            <button
              className="reconnect-button"
              onClick={() => {
                reconnect();
                notify.info('Reconnecting', 'Attempting to reconnect to MT5...');
              }}
              disabled={isReconnecting}
              title="Force reconnection to MT5"
            >
              {isReconnecting ? '...' : '⟳'}
            </button>
          ) : (
            <button
              className="reconnect-button connect"
              onClick={async () => {
                try {
                  notify.info('Connecting', 'Attempting to connect to MT5...');
                  const response = await fetch('http://localhost:5001/api/connection/reload_settings', {
                    method: 'POST'
                  });

                  if (response.ok) {
                    const data = await response.json();
                    if (data.status === 'connected' || data.status === 'success') {
                      notify.success('Connected', 'Successfully connected to MT5');
                      window.location.reload();
                    } else {
                      notify.error('Connection Failed', data.message || 'Failed to connect');
                    }
                  } else {
                    notify.error('Connection Failed', `Server error: ${response.status}`);
                  }
                } catch (error) {
                  notify.error('Connection Error', error.message || 'Failed to connect');
                }
              }}
              disabled={isReconnecting}
              title="Connect to MT5"
            >
              {isReconnecting ? '...' : '⟳'}
            </button>
          )}
        </div>

        {isConnected ? (
          <button
            className="connect-button"
            onClick={handleDisconnect}
          >
            Disconnect
          </button>
        ) : (
          <div className="connection-buttons">
            <button
              className="connect-button"
              onClick={() => {
                console.log('Configure MT5 button clicked');
                if (typeof onConnect === 'function') {
                  onConnect();
                } else {
                  console.error('onConnect is not a function:', onConnect);
                }
              }}
              title="Open connection wizard to configure and connect to MT5"
            >
              <span className="button-icon settings-icon"></span>
              Configure MT5
            </button>
            <button
              className="connect-button direct"
              onClick={async () => {
                try {
                  // Use saved user settings instead of hardcoded credentials
                  console.log('Attempting quick connection with saved user settings');
                  notify.info('Connecting', 'Attempting to connect with saved settings...');

                  // Call the backend API directly for more reliable connection
                  const response = await fetch('http://localhost:5001/api/connection/reload_settings', {
                    method: 'POST'
                  });

                  if (response.ok) {
                    const data = await response.json();
                    console.log('Quick connect response:', data);

                    if (data.status === 'connected' || data.status === 'success') {
                      notify.success('Connected', 'Successfully connected to MT5');

                      // Refresh the page to ensure all components update correctly
                      window.location.reload();
                    } else {
                      notify.error('Connection Failed', data.message || 'Failed to connect to MT5');
                    }
                  } else {
                    notify.error('Connection Failed', `Server returned ${response.status}`);
                  }
                } catch (error) {
                  console.error('Quick connect error:', error);
                  notify.error('Connection Error', error.message || 'Failed to connect to MT5');
                }
              }}
              disabled={isReconnecting}
              title="Connect using saved account settings"
            >
              <span className="button-icon connect-icon"></span>
              {isReconnecting ? 'Connecting...' : 'Quick Connect'}
            </button>
          </div>
        )}

        <div className="user-menu-container">
          <button className="user-menu-button" onClick={toggleUserMenu}>
            <span className="user-avatar">
              {isConnected ? 'MT' : 'G'}
            </span>
            <span className="user-name">
              {isConnected ? 'MT5 User' : 'Guest'}
            </span>
            <span className="dropdown-arrow"></span>
          </button>

          {userMenuOpen && (
            <div className="user-dropdown">
              <div className="user-dropdown-header">
                <div className="user-info">
                  <span className="user-avatar large">
                    {isConnected ? 'MT' : 'G'}
                  </span>
                  <div className="user-details">
                    <div className="user-name">{isConnected ? (accountInfo.accountName || 'MT5 User') : 'Guest'}</div>
                    {isConnected && (
                      <div className="user-account">
                        {accountInfo.server || 'MT5 Server'}
                        {accountInfo.accountType && ` (${accountInfo.accountType})`}
                      </div>
                    )}
                  </div>
                </div>
                {isConnected && (
                  <div className="user-balance">
                    <div className="balance-label">Balance</div>
                    <div className="balance-value">${accountInfo.balance?.toFixed(2) || '0.00'}</div>
                  </div>
                )}
              </div>
              <div className="user-dropdown-menu">
                <button onClick={() => handleUserAction('profile')} className="user-menu-item">
                  <span className="menu-icon profile-icon"></span>
                  <span>Profile</span>
                </button>
                <button onClick={() => handleUserAction('settings')} className="user-menu-item">
                  <span className="menu-icon settings-icon"></span>
                  <span>Settings</span>
                </button>
                <button onClick={() => handleUserAction('help')} className="user-menu-item">
                  <span className="menu-icon help-icon"></span>
                  <span>Help</span>
                </button>
              </div>
            </div>
          )}
        </div>

        <button className="menu-toggle" onClick={toggleMenu}>
          <span className="menu-icon"></span>
        </button>
      </div>

      {userMenuOpen && <div className="menu-backdrop" onClick={toggleUserMenu}></div>}
      {menuOpen && <div className="menu-backdrop" onClick={toggleMenu}></div>}
    </header>
  );
};

export default Header;
