from typing import Dict, Any
import numpy as np
import pandas as pd
from scipy import stats

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class LinearRegressionAngleIndicator(BaseIndicator):
    """Linear Regression Angle indicator."""

    def __init__(self, period: int = 14, source: str = 'close'):
        """
        Initialize Linear Regression Angle indicator.

        Args:
            period: The lookback period for linear regression calculation.
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
        """
        super().__init__({
            'period': period,
            'source': source
        })

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate rolling linear regression angle in degrees."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period']:
             return {'angle': np.array([])}

        period = self.params['period']
        source_col = self.params['source'].lower()

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Initialize result array
        n = len(source_data)
        angles = np.full(n, np.nan)
        x = np.arange(period)

        for i in range(period - 1, n):
            y_window = source_data.iloc[i - period + 1 : i + 1].values
            if len(y_window) != period or np.isnan(y_window).any():
                continue
            try:
                slope, _, _, _, _ = stats.linregress(x, y_window)
                # Calculate angle in degrees
                angle = np.degrees(np.arctan(slope))
                angles[i] = angle
            except ValueError:
                continue

        self._values = {
            'angle': angles
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 2: # Linregress needs at least 2 points
            raise ValueError("Period must be at least 2 for Linear Regression Angle")
        return True