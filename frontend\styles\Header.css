.header {
  background-color: var(--card);
  padding: 0 20px;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo {
  height: 32px;
  width: auto;
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text);
  margin: 0;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.75rem;
  padding: 4px 10px;
  border-radius: 20px;
  background-color: rgba(0, 0, 0, 0.1);
  position: relative;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--error);
}

.connection-status.connected .status-dot {
  background-color: var(--success);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
  height: 100%;
}

.connection-buttons {
  display: flex;
  gap: 10px;
}

.connect-button {
  padding: 6px 12px;
  border-radius: 4px;
  background-color: var(--primary);
  color: white;
  border: none;
  font-weight: 500;
  font-size: 0.85rem;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.2s;
}

.connect-button:hover {
  background-color: var(--primary-hover);
  transform: translateY(-2px);
}

.connect-button.direct {
  background-color: var(--success, #10b981);
}

.connect-button.direct:hover {
  background-color: var(--success-hover, #059669);
}

.button-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 6px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  vertical-align: text-bottom;
}

.settings-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z'/%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M15 12a3 3 0 11-6 0 3 3 0 016 0z'/%3E%3C/svg%3E");
}

.connect-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M13 10V3L4 14h7v7l9-11h-7z'/%3E%3C/svg%3E");
}

.menu-toggle {
  display: none;
  background: none;
  border: none;
  width: 40px;
  height: 40px;
  position: relative;
  cursor: pointer;
}

.menu-icon,
.menu-icon:before,
.menu-icon:after {
  width: 24px;
  height: 2px;
  background-color: var(--text);
  position: absolute;
  transition: all 0.3s ease;
}

.menu-icon {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.menu-icon:before,
.menu-icon:after {
  content: '';
  left: 0;
}

.menu-icon:before {
  top: -8px;
}

.menu-icon:after {
  top: 8px;
}

/* User Menu Styles */
.user-menu-container {
  position: relative;
}

.user-menu-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  padding: 5px 10px;
  border-radius: 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: var(--text);
}

.user-menu-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.user-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: var(--primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
}

.user-avatar.large {
  width: 40px;
  height: 40px;
  font-size: 1rem;
}

.user-name {
  font-size: 0.85rem;
  font-weight: 500;
}

.dropdown-arrow {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid var(--text-secondary);
  margin-left: 5px;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 280px;
  background-color: var(--card);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  z-index: 100;
  animation: slideDown 0.2s ease-out;
}

.user-dropdown-header {
  padding: 15px;
  border-bottom: 1px solid var(--border);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 10px;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-account {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: 2px;
}

.user-balance {
  background-color: rgba(59, 130, 246, 0.1);
  border-radius: 6px;
  padding: 8px 12px;
  margin-top: 10px;
}

.balance-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-bottom: 2px;
}

.balance-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary);
}

.user-dropdown-menu {
  padding: 8px 0;
}

.user-menu-item {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  padding: 10px 15px;
  background: none;
  border: none;
  text-align: left;
  color: var(--text);
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-menu-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.menu-icon {
  width: 18px;
  height: 18px;
  opacity: 0.7;
}

.nav-icon {
  width: 20px;
  height: 20px;
  display: inline-block;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.7;
}

.main-nav li.active .nav-icon {
  opacity: 1;
}

/* Icon placeholders - replace with actual icons */
.analysis-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E");
}

.recommendation-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14h-2V9h-2V7h4v10z'/%3E%3C/svg%3E");
}

.execution-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E");
}

.autonomous-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M15 9H9v6h6V9zm-2 4h-2v-2h2v2zm8-2V9h-2V7c0-1.1-.9-2-2-2h-2V3h-2v2h-2V3H9v2H7c-1.1 0-2 .9-2 2v2H3v2h2v2H3v2h2v2c0 1.1.9 2 2 2h2v2h2v-2h2v2h2v-2h2c1.1 0 2-.9 2-2v-2h2v-2h-2v-2h2zm-4 6H7V7h10v10z'/%3E%3C/svg%3E");
}

.history-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z'/%3E%3C/svg%3E");
}

.menu-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 90;
  display: none;
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive styles */
@media (max-width: 992px) {
  .main-nav ul {
    flex-direction: column;
    gap: 0;
  }

  .main-nav button {
    padding: 12px 15px;
    width: 100%;
    text-align: left;
    justify-content: flex-start;
  }

  .user-dropdown {
    width: 250px;
  }
}

@media (max-width: 768px) {
  .header h1 {
    font-size: 1.2rem;
  }

  .menu-toggle {
    display: block;
  }

  .main-nav {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--card);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    z-index: 95;
  }

  .main-nav.open {
    display: block;
  }

  .menu-backdrop {
    display: block;
  }
}

.account-info {
  font-weight: 600;
  margin-left: 4px;
}

.reconnect-button {
  background: none;
  border: none;
  color: var(--text);
  font-size: 14px;
  cursor: pointer;
  padding: 2px 6px;
  margin-left: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.reconnect-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--primary);
}

.reconnect-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.reconnect-button.connect {
  background-color: rgba(0, 255, 0, 0.1);
}

.reconnect-button.connect:hover {
  background-color: rgba(0, 255, 0, 0.2);
  color: var(--success);
}
