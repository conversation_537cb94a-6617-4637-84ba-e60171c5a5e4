from typing import Dict, Any
import numpy as np
import pandas as pd
from scipy import stats

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class HurstExponentIndicator(BaseIndicator):
    """Rolling Hurst Exponent indicator."""

    def __init__(self, period: int = 100, lags: int = 20, source: str = 'close'):
        """
        Initialize Rolling Hurst Exponent indicator.

        Args:
            period: The rolling window size for Hurst calculation.
            lags: Number of lags to use in the Rescaled Range (R/S) analysis.
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
        """
        super().__init__({
            'period': period,
            'lags': lags,
            'source': source
        })

    def _calculate_hurst_for_window(self, prices: np.ndarray, lags: int) -> float:
        """Calculate Hurst exponent for a single window of prices."""
        if len(prices) < lags + 1: # Need enough data for lags
            return np.nan

        # Calculate log returns
        returns = np.log(prices[1:] / prices[:-1])
        if len(returns) < lags:
             return np.nan

        lag_values = np.arange(2, lags + 1)
        rs_values = []

        for lag in lag_values:
            # Ensure we have enough data for at least one full chunk
            if len(returns) < lag:
                continue

            # Split returns into chunks of size 'lag'
            num_chunks = len(returns) // lag
            if num_chunks == 0:
                continue
            chunks = np.array_split(returns[:num_chunks * lag], num_chunks)

            # Calculate mean-adjusted series and cumulative deviation for each chunk
            mean_adjusted = [chunk - np.mean(chunk) for chunk in chunks]
            cumulative_dev = [np.cumsum(chunk) for chunk in mean_adjusted]

            # Calculate range (R) and standard deviation (S) for each chunk
            ranges = [np.ptp(chunk) for chunk in cumulative_dev] # ptp = max - min
            stds = [np.std(chunk) for chunk in chunks]

            # Calculate R/S values, avoiding division by zero
            rs_chunk_values = [r / s if s > 1e-8 else np.nan for r, s in zip(ranges, stds)]
            rs_chunk_values = [val for val in rs_chunk_values if not np.isnan(val)] # Filter out NaNs

            if not rs_chunk_values: # If all stds were zero for this lag
                continue

            rs_values.append(np.mean(rs_chunk_values))

        if len(rs_values) < 2: # Need at least two points for regression
            return np.nan

        # Estimate Hurst as the slope of log(R/S) vs log(lag)
        valid_lags = lag_values[:len(rs_values)] # Match lags to calculated rs_values
        x = np.log(valid_lags)
        y = np.log(rs_values)

        try:
            slope, _, _, _, _ = stats.linregress(x, y)
            return slope
        except ValueError:
            return np.nan


    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate rolling Hurst Exponent."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period']:
             return {'hurst': np.array([])}

        period = self.params['period']
        lags = self.params['lags']
        source_col = self.params['source'].lower()

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Calculate rolling Hurst exponent
        # Using apply is generally slow, but necessary for complex window functions like Hurst
        hurst_values = source_data.rolling(window=period).apply(
            lambda x: self._calculate_hurst_for_window(x.values, lags),
            raw=False # Pass Series to lambda
        )

        self._values = {
            'hurst': hurst_values.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        # DEBUG: Validating Hurst params
        if self.params['period'] < self.params['lags'] + 1:
            raise ValueError("Period must be greater than lags + 1")
        if self.params['lags'] < 2:
            raise ValueError("Lags must be at least 2")
        return True