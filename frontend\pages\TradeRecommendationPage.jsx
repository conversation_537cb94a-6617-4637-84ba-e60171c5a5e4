import React, { useState, useEffect } from 'react';
import { useNotification } from '../components/Notification';
import '../styles/Pages.css';

const TradeRecommendationPage = ({ selectedSymbol }) => {
  const [timeframes, setTimeframes] = useState(['H1', 'H4', 'D1']);
  const [recommendations, setRecommendations] = useState(null);
  const [loading, setLoading] = useState(false);
  const [symbols, setSymbols] = useState([]);
  const [localSymbol, setLocalSymbol] = useState(selectedSymbol || 'EURUSD');
  const notify = useNotification();

  // Fetch available symbols
  useEffect(() => {
    const fetchSymbols = async () => {
      try {
        const response = await fetch('http://localhost:5001/api/symbols');
        if (response.ok) {
          const data = await response.json();
          setSymbols(data);
        }
      } catch (error) {
        console.error('Failed to fetch symbols:', error);
      }
    };

    fetchSymbols();
  }, []);

  // Update local symbol when prop changes
  useEffect(() => {
    if (selectedSymbol) {
      setLocalSymbol(selectedSymbol);
    }
  }, [selectedSymbol]);

  // Fetch recommendations when symbol changes
  useEffect(() => {
    const fetchRecommendations = async () => {
      if (!localSymbol) return;
      
      setLoading(true);
      try {
        const response = await fetch(`http://localhost:5001/api/recommendations?symbol=${localSymbol}&timeframes=${timeframes.join(',')}`);
        if (response.ok) {
          const data = await response.json();
          setRecommendations(data);
        } else {
          const errorData = await response.json();
          notify.error('Recommendation Error', errorData.message || 'Failed to fetch recommendations');
        }
      } catch (error) {
        console.error('Failed to fetch recommendations:', error);
        notify.error('Error', 'Failed to fetch trade recommendations');
      } finally {
        setLoading(false);
      }
    };

    fetchRecommendations();
  }, [localSymbol, timeframes]);

  const handleSymbolChange = (e) => {
    setLocalSymbol(e.target.value);
  };

  const handleTimeframeToggle = (tf) => {
    setTimeframes(prev => {
      if (prev.includes(tf)) {
        // Don't remove if it's the last timeframe
        if (prev.length === 1) return prev;
        return prev.filter(t => t !== tf);
      } else {
        return [...prev, tf];
      }
    });
  };

  const renderSignalBadge = (signal) => {
    return (
      <span className={`signal-badge ${signal.toLowerCase()}`}>
        {signal}
      </span>
    );
  };

  return (
    <div className="page-container recommendation-page">
      <h2>Trade Recommendations</h2>
      
      <div className="controls">
        <div className="form-group">
          <label htmlFor="symbol">Symbol</label>
          <select 
            id="symbol" 
            value={localSymbol} 
            onChange={handleSymbolChange}
            disabled={loading}
          >
            {symbols.map(symbol => (
              <option key={symbol} value={symbol}>{symbol}</option>
            ))}
          </select>
        </div>
        
        <div className="timeframe-toggles">
          <span>Timeframes:</span>
          <div className="toggle-buttons">
            <button 
              className={`toggle-button ${timeframes.includes('M15') ? 'active' : ''}`}
              onClick={() => handleTimeframeToggle('M15')}
              disabled={loading}
            >
              M15
            </button>
            <button 
              className={`toggle-button ${timeframes.includes('M30') ? 'active' : ''}`}
              onClick={() => handleTimeframeToggle('M30')}
              disabled={loading}
            >
              M30
            </button>
            <button 
              className={`toggle-button ${timeframes.includes('H1') ? 'active' : ''}`}
              onClick={() => handleTimeframeToggle('H1')}
              disabled={loading}
            >
              H1
            </button>
            <button 
              className={`toggle-button ${timeframes.includes('H4') ? 'active' : ''}`}
              onClick={() => handleTimeframeToggle('H4')}
              disabled={loading}
            >
              H4
            </button>
            <button 
              className={`toggle-button ${timeframes.includes('D1') ? 'active' : ''}`}
              onClick={() => handleTimeframeToggle('D1')}
              disabled={loading}
            >
              D1
            </button>
          </div>
        </div>
      </div>
      
      {loading ? (
        <div className="loading">Loading recommendations...</div>
      ) : !recommendations ? (
        <div className="no-data">No recommendations available</div>
      ) : (
        <div className="recommendations-content">
          <div className="summary-card">
            <h3>Multi-Timeframe Summary</h3>
            <div className="summary-signal">
              <span className="label">Overall Signal:</span>
              {renderSignalBadge(recommendations.summary.overall_signal)}
            </div>
            <div className="summary-strength">
              <span className="label">Confidence:</span>
              <div className="strength-bar">
                <div 
                  className={`strength-value ${recommendations.summary.overall_signal.toLowerCase()}`}
                  style={{ width: `${recommendations.summary.confidence}%` }}
                ></div>
              </div>
              <span className="strength-label">{recommendations.summary.confidence}%</span>
            </div>
            <div className="summary-text">
              <p>{recommendations.summary.recommendation_text}</p>
            </div>
          </div>
          
          <div className="timeframe-recommendations">
            {Object.entries(recommendations.timeframes).map(([tf, data]) => (
              <div key={tf} className="recommendation-card">
                <div className="card-header">
                  <h3>{tf} Timeframe</h3>
                  {renderSignalBadge(data.signal)}
                </div>
                
                <div className="recommendation-details">
                  <div className="entry-exit">
                    <div className="entry">
                      <h4>Entry Zone</h4>
                      <p className="price">{data.entry_zone.lower.toFixed(5)} - {data.entry_zone.upper.toFixed(5)}</p>
                      <p className="note">{data.entry_zone.note}</p>
                    </div>
                    
                    <div className="targets">
                      <h4>Take Profit Targets</h4>
                      <ul>
                        {data.take_profit.map((tp, index) => (
                          <li key={index}>
                            <span className="target-label">TP{index + 1}:</span>
                            <span className="target-price">{tp.price.toFixed(5)}</span>
                            <span className="target-pips">({tp.pips} pips)</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div className="stop-loss">
                      <h4>Stop Loss</h4>
                      <p className="price">{data.stop_loss.price.toFixed(5)}</p>
                      <p className="pips">({data.stop_loss.pips} pips)</p>
                    </div>
                  </div>
                  
                  <div className="risk-reward">
                    <h4>Risk/Reward Ratio</h4>
                    <p className="ratio">1:{data.risk_reward_ratio.toFixed(2)}</p>
                  </div>
                  
                  <div className="indicators">
                    <h4>Key Indicators</h4>
                    <ul>
                      {data.key_indicators.map((indicator, index) => (
                        <li key={index}>
                          <span className="indicator-name">{indicator.name}:</span>
                          <span className={`indicator-signal ${indicator.signal.toLowerCase()}`}>
                            {indicator.signal}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="recommendation-text">
                    <h4>Analysis</h4>
                    <p>{data.analysis_text}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="risk-management">
            <h3>Risk Management</h3>
            <div className="risk-card">
              <div className="position-sizing">
                <h4>Suggested Position Sizing</h4>
                <table>
                  <thead>
                    <tr>
                      <th>Risk %</th>
                      <th>Lot Size</th>
                      <th>Risk Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    {recommendations.risk_management.position_sizing.map((size, index) => (
                      <tr key={index}>
                        <td>{size.risk_percent}%</td>
                        <td>{size.lot_size.toFixed(2)}</td>
                        <td>${size.risk_amount.toFixed(2)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              <div className="risk-notes">
                <h4>Risk Notes</h4>
                <ul>
                  {recommendations.risk_management.notes.map((note, index) => (
                    <li key={index}>{note}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TradeRecommendationPage;