/* Autonomous Trading Page Styles */

.autonomous-page {
  /* Inheriting theme variables */
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.bot-status {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 20px;
  background-color: var(--card);
  border: 1px solid var(--border);
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.bot-status.running .status-indicator {
  background-color: var(--success);
  box-shadow: 0 0 8px var(--success);
}

.bot-status.stopped .status-indicator {
  background-color: var(--error);
  box-shadow: 0 0 8px var(--error);
}

/* Info Panel */
.autonomous-info-panel {
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.autonomous-info-panel h3 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 1.1rem;
  color: var(--text);
}

.autonomous-info-panel p {
  margin: 0;
  line-height: 1.5;
  color: var(--text-secondary);
}

/* Page Content */
.page-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

/* Config Panel */
.config-panel {
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 20px;
}

.config-panel h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.1rem;
  color: var(--text);
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text);
}

.form-group select,
.form-group input {
  width: 100%;
  padding: 10px 12px;
  border-radius: 6px;
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
  color: white;
  font-size: 0.9rem;
}

.form-group select:focus,
.form-group input:focus {
  outline: none;
  border-color: #3f51b5;
}

.strategy-description {
  margin-top: 8px;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.form-row {
  display: flex;
  gap: 16px;
}

.form-group.half {
  flex: 1;
}

.input-hint {
  margin-top: 4px;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

.form-actions {
  margin-top: 24px;
}

/* Mode Toggle Switch Styles */
.mode-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin: 12px 0;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.mode-toggle span {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
  transition: color 0.3s ease;
}

.mode-toggle span.active-mode {
  color: #3498db;
  font-weight: 500;
}

/* Toggle Switch */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #3498db;
  transition: 0.3s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #e74c3c;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.form-help {
  display: block;
  margin-top: 4px;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.3;
}

/* Confidence Slider */
.slider-container {
  width: 100%;
  margin: 10px 0;
}

.confidence-slider {
  -webkit-appearance: none;
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(to right, #f44336, #ff9800, #4caf50);
  outline: none;
}

.confidence-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #fff;
  cursor: pointer;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.confidence-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #fff;
  cursor: pointer;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Active Strategies Panel */
.active-strategies-panel {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 8px;
  padding: 20px;
}

.active-strategies-panel h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.1rem;
}

.loading-indicator,
.no-strategies {
  padding: 20px;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
}

.strategies-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.strategy-card {
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
}

.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.strategy-header h4 {
  margin: 0;
  font-size: 1rem;
}

.status {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status.active {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.status.inactive {
  background-color: rgba(244, 67, 54, 0.2);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.strategy-details {
  margin-bottom: 16px;
}

.detail-row {
  display: flex;
  margin-bottom: 8px;
}

.detail-item {
  flex: 1;
  margin-bottom: 8px;
}

.detail-item.full {
  flex: 0 0 100%;
}

.detail-label {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  margin-right: 6px;
}

.detail-value {
  font-size: 0.9rem;
  font-weight: 500;
}

.detail-value.positive {
  color: var(--success-color);
}

.detail-value.negative {
  color: var(--error-color);
}

.strategy-actions {
  display: flex;
  justify-content: flex-end;
}

/* Responsive styles */
@media (max-width: 992px) {
  .page-content {
    grid-template-columns: 1fr;
  }

  .config-panel,
  .active-strategies-panel {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .bot-status {
    margin-top: 12px;
  }

  .form-row {
    flex-direction: column;
    gap: 16px;
  }
}
