from typing import Dict, Any
import numpy as np
import pandas as pd

from backend.technical.base_indicator import BaseIndicator

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

class FractalsIndicator(BaseIndicator):
    """<PERSON>' Fractals indicator."""

    def __init__(self, period: int = 5):
        """
        Initialize Fractals indicator.

        Args:
            period: The number of bars required for a fractal formation (typically 5).
                    A period of 5 means checking 2 bars before and 2 bars after the center bar.
        """
        if period % 2 == 0 or period < 3:
            raise ValueError("Period must be an odd number greater than or equal to 3")
        super().__init__({'period': period})

    def calculate(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """Calculate Fractals."""
        df = data.copy()
        if df.empty or len(df) < self.params['period']:
             return {
                'fractal_high_signal': np.array([]),
                'fractal_low_signal': np.array([]),
                'fractal_high_level': np.array([]),
                'fractal_low_level': np.array([])
             }

        period = self.params['period']
        n_side = period // 2 # Number of bars on each side of the center

        high = df['high']
        low = df['low']

        n = len(df)
        fractal_high_signal = np.zeros(n) # 1 if a high fractal is confirmed
        fractal_low_signal = np.zeros(n)  # -1 if a low fractal is confirmed
        fractal_high_level = np.full(n, np.nan) # Price level of confirmed high fractal
        fractal_low_level = np.full(n, np.nan)  # Price level of confirmed low fractal

        for i in range(n_side, n - n_side):
            # Check for High Fractal at index i
            is_high_fractal = True
            center_high = high.iloc[i]
            for j in range(1, n_side + 1):
                if high.iloc[i-j] >= center_high or high.iloc[i+j] >= center_high:
                    is_high_fractal = False
                    break
            if is_high_fractal:
                fractal_high_signal[i] = 1
                fractal_high_level[i] = center_high

            # Check for Low Fractal at index i
            is_low_fractal = True
            center_low = low.iloc[i]
            for j in range(1, n_side + 1):
                 if low.iloc[i-j] <= center_low or low.iloc[i+j] <= center_low:
                    is_low_fractal = False
                    break
            if is_low_fractal:
                fractal_low_signal[i] = -1
                fractal_low_level[i] = center_low


        self._values = {
            'fractal_high_signal': fractal_high_signal, # Signal at the fractal bar
            'fractal_low_signal': fractal_low_signal,   # Signal at the fractal bar
            'fractal_high_level': fractal_high_level, # Price level of the high fractal
            'fractal_low_level': fractal_low_level    # Price level of the low fractal
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        period = self.params['period']
        if not isinstance(period, int) or period < 3 or period % 2 == 0:
             raise ValueError("Period must be an odd integer greater than or equal to 3")
        return True
