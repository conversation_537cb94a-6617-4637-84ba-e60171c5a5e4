/**
 * Safely formats a number with a fixed number of decimal places.
 * Returns 'N/A' for null, undefined, NaN, or non-number values.
 * 
 * @param {any} value - The value to format
 * @param {number} decimals - Number of decimal places (default: 2)
 * @param {string} fallback - Fallback string for invalid values (default: 'N/A')
 * @returns {string} Formatted number or fallback string
 */
export const safeToFixed = (value, decimals = 2, fallback = 'N/A') => {
  // Check if value is a valid number
  if (value === null || value === undefined || isNaN(value) || typeof value !== 'number') {
    return fallback;
  }
  
  try {
    return value.toFixed(decimals);
  } catch (error) {
    console.warn(`Error formatting value: ${value}`, error);
    return fallback;
  }
};

/**
 * Checks if a value is a valid number (not null, undefined, or NaN)
 * 
 * @param {any} value - The value to check
 * @returns {boolean} True if value is a valid number
 */
export const isValidNumber = (value) => {
  return value !== null && value !== undefined && typeof value === 'number' && !isNaN(value);
};
