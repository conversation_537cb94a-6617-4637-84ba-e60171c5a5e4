"""
Exceptions for MT5 integration.
"""

class MT5Error(Exception):
    """Base class for MT5 integration errors."""
    pass

class MT5ConnectionError(MT5Error):
    """Raised when connection to MT5 terminal fails."""
    pass

class MT5AuthenticationError(MT5Error):
    """Raised when authentication with MT5 server fails."""
    pass

class MT5TimeoutError(MT5Error):
    """Raised when an MT5 operation times out."""
    pass

class MT5SettingsError(MT5Error):
    """Raised when there are issues with settings loading or validation."""
    pass

class MT5SymbolError(MT5Error):
    """Raised when there are issues with symbol operations."""
    pass
