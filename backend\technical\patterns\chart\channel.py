from typing import Dict, Any, List
import numpy as np
import pandas as pd
from scipy.signal import find_peaks

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class ChannelPatternIndicator(BaseIndicator):
    """Channel pattern indicator for identifying price channels."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - min_points: Parameter description (default: 4)
                - min_width: Parameter description (default: 20)
                - price_tolerance: Parameter description (default: 0.02)
                - slope_tolerance: Parameter description (default: 0.05)
                - parallel_tolerance: Parameter description (default: 0.1)
        """
        default_params = {
            "min_points": 4,
            "min_width": 20,
            "price_tolerance": 0.02,
            "slope_tolerance": 0.05,
            "parallel_tolerance": 0.1,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)


    def _is_channel(self, prices: np.ndarray, peaks: np.ndarray,
                   troughs: np.ndarray) -> tuple:
        """Identify Channel patterns and their type."""
        if len(peaks) < self.params['min_points'] or len(troughs) < self.params['min_points']:
            return False, 0, 0, 0
            
        # Calculate trend lines
        peak_x = peaks
        peak_y = prices[peaks]
        peak_slope, peak_intercept = np.polyfit(peak_x, peak_y, 1)
        
        trough_x = troughs
        trough_y = prices[troughs]
        trough_slope, trough_intercept = np.polyfit(trough_x, trough_y, 1)
        
        # Check if lines are roughly parallel
        slope_diff = abs(peak_slope - trough_slope)
        avg_slope = (abs(peak_slope) + abs(trough_slope)) / 2
        if slope_diff / (avg_slope + 1e-6) > self.params['parallel_tolerance']:
            return False, 0, 0, 0
            
        # Find pattern boundaries
        start_idx = min(peaks[0], troughs[0])
        end_idx = max(peaks[-1], troughs[-1])
        
        # Check minimum width
        if end_idx - start_idx < self.params['min_width']:
            return False, 0, 0, 0
            
        # Check price points fit within channel
        for i in range(start_idx, end_idx+1):
            upper_bound = peak_slope * i + peak_intercept
            lower_bound = trough_slope * i + trough_intercept
            
            if prices[i] > upper_bound + self.params['price_tolerance'] * prices[i]:
                return False, 0, 0, 0
            if prices[i] < lower_bound - self.params['price_tolerance'] * prices[i]:
                return False, 0, 0, 0
        
        # Determine channel type
        if abs(avg_slope) <= self.params['slope_tolerance']:
            channel_type = 0  # Horizontal channel (sideways)
        elif avg_slope > 0:
            channel_type = 1  # Ascending channel
        else:
            channel_type = -1  # Descending channel
            
        return True, start_idx, end_idx, channel_type

    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        
        is_pattern = np.zeros_like(close)
        pattern_type = np.zeros_like(close)  # 0=Horizontal, 1=Ascending, -1=Descending
        
        # Find peaks and troughs using both high and low prices
        peaks, _ = find_peaks(high, distance=5)
        troughs, _ = find_peaks(-low, distance=5)
        
        # Scan for channels
        for i in range(len(close)):
            valid_peaks = peaks[peaks >= i]
            valid_troughs = troughs[troughs >= i]
            
            if len(valid_peaks) >= self.params['min_points'] and len(valid_troughs) >= self.params['min_points']:
                is_valid, start, end, ch_type = self._is_channel(close, valid_peaks, valid_troughs)
                if is_valid:
                    is_pattern[start:end+1] = 1
                    pattern_type[start:end+1] = ch_type
        
        # Calculate channel characteristics
        strength = np.zeros_like(close)
        volatility = np.zeros_like(close)
        
        for i in range(len(close)):
            if is_pattern[i]:
                # Calculate channel height relative to price
                window = slice(i, min(i + self.params['min_width'], len(close)))
                channel_height = max(high[window]) - min(low[window])
                strength[i] = channel_height / close[i]
                
                # Calculate price volatility within channel
                price_std = np.std(close[window])
                volatility[i] = price_std / close[i]
        
        # Calculate sideways probability
        sideways_prob = np.zeros_like(close)
        for i in range(len(close)):
            if is_pattern[i] and pattern_type[i] == 0:  # Horizontal channel
                # Higher probability if:
                # 1. Low volatility relative to channel height
                # 2. Price near middle of channel
                # 3. Consistent volume (if available)
                vol_ratio = volatility[i] / (strength[i] + 1e-6)
                sideways_prob[i] = max(0, 1 - vol_ratio)
        
        # Calculate trend context
        trend = np.zeros_like(close)
        for i in range(20, len(close)):
            sma = np.mean(close[i-20:i])
            trend[i] = 1 if close[i] > sma else -1
        
        # Calculate pattern reliability
        reliability = np.zeros_like(close)
        for i in range(len(close)-1):
            if is_pattern[i]:
                # For horizontal channels, check if price stays within channel
                if pattern_type[i] == 0 and i < len(close)-1:
                    next_close = close[i+1]
                    expected_range = strength[i] * close[i]
                    if abs(next_close - close[i]) <= expected_range/2:
                        reliability[i] = 1
                    else:
                        reliability[i] = -1
                # For trending channels, check if price follows the trend
                else:
                    future_return = (close[i+1] - close[i]) / close[i]
                    if pattern_type[i] > 0:  # Ascending
                        reliability[i] = 1 if future_return > 0 else -1
                    elif pattern_type[i] < 0:  # Descending
                        reliability[i] = 1 if future_return < 0 else -1
        
        return {
            'is_pattern': is_pattern.astype(int),
            'pattern_type': pattern_type,  # 0=Horizontal, 1=Ascending, -1=Descending
            'strength': strength,
            'volatility': volatility,
            'sideways_prob': sideways_prob,  # Probability of continued sideways movement
            'trend': trend,
            'reliability': reliability
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['min_points'] < 3:
            raise ValueError("Minimum points must be at least 3")
        if self.params['min_width'] < 10:
            raise ValueError("Minimum width must be at least 10 periods")
        if not 0 < self.params['price_tolerance'] < 1:
            raise ValueError("Price tolerance must be between 0 and 1")
        if not 0 < self.params['slope_tolerance'] < 1:
            raise ValueError("Slope tolerance must be between 0 and 1")
        if not 0 < self.params['parallel_tolerance'] < 1:
            raise ValueError("Parallel tolerance must be between 0 and 1")
        return True 