# Story FE-05: Core Redirection Functionality

## User Story
**As a** user  
**I want to** be automatically redirected after successful login  
**So that** I can access the appropriate view (dashboard or analysis)

## Acceptance Criteria
1. [x] System checks for redirect parameter in URL after successful connection
2. [x] When no redirect parameter exists, system navigates to dashboard view
3. [x] When redirect=analysis parameter exists, system navigates to analysis view
4. [x] URL parameters are preserved during redirection
5. [x] Redirection happens within 500ms of connection success
6. [x] Error state does not trigger redirection
7. [x] Connection status is properly updated in UI during redirection

## Technical Implementation Notes

### Changes Required
1. **ConnectionTab.js**:
```javascript
// Modify setupConnectionManager() to handle redirection
this.connectionManager.onStatusChange = (status) => {
    this.updateStatus(status);
    this.setConnected(status === ConnectionStates.CONNECTED);
    
    if (status === ConnectionStates.CONNECTED) {
        const urlParams = new URLSearchParams(window.location.search);
        const redirectTo = urlParams.get('redirect') || 'dashboard';
        const preserveParams = ['symbol', 'timeframe']; // Parameters to preserve
        
        // Build new URL params
        const newParams = new URLSearchParams();
        preserveParams.forEach(param => {
            if (urlParams.has(param)) {
                newParams.set(param, urlParams.get(param));
            }
        });
        
        window.electronAPI.navigateToTab(`${redirectTo}?${newParams.toString()}`);
    }
};
```

2. **preload.js** (ensure API is exposed):
```javascript
contextBridge.exposeInMainWorld('electronAPI', {
    navigateToTab: (tabId) => ipcRenderer.send('navigate-to-tab', tabId)
});
```

### Dependencies
1. Electron API must expose navigateToTab functionality
2. Dashboard and Analysis views must be registered in main navigation
3. URL parameter handling must be implemented in target views

## Test Scenarios

### Happy Path
1. **Default Redirection**:
   - Navigate to app with no URL params
   - Connect successfully
   - Verify redirected to dashboard

2. **Analysis Redirection**:
   - Navigate to app with ?redirect=analysis&symbol=EURUSD
   - Connect successfully
   - Verify redirected to analysis view with symbol preserved

### Edge Cases
1. **Invalid Redirect Target**:
   - Navigate to app with ?redirect=invalid
   - Connect successfully
   - Verify redirected to dashboard (default)

2. **Connection Error**:
   - Navigate to app with ?redirect=analysis
   - Enter invalid credentials
   - Verify no redirection occurs

3. **Missing Parameters**:
   - Navigate to app with ?redirect=analysis
   - Connect successfully
   - Verify redirected to analysis view without symbol/timeframe

## Dependencies
1. FE-401 (Redux store setup) should be completed first
2. Backend API must be available (/api/positions, /api/ohlc)
3. Dashboard and Analysis components must be implemented