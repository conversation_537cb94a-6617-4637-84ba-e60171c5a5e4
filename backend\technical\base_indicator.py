from typing import Dict, Any
import pandas as pd
import numpy as np

class BaseIndicator:
    """
    Base class for technical indicators.
    Provides common structure and parameter handling.
    """
    def __init__(self, params: Dict[str, Any]):
        """
        Initialize the base indicator with parameters.

        Args:
            params: A dictionary of parameters for the indicator.
        """
        self.params = params
        self._values: Dict[str, np.ndarray] = {} # To store calculated values
        self.name = self.__class__.__name__ # Default name to class name
        self.validate_params() # Validate on initialization

    def calculate(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """
        Calculate the indicator values.
        This method should be implemented by subclasses.

        Args:
            data: Input data (pandas DataFrame).

        Returns:
            A dictionary where keys are value names (e.g., 'sma', 'rsi')
            and values are numpy arrays of the calculated indicator values.
        """
        raise NotImplementedError("Subclasses must implement the calculate method.")

    def validate_params(self) -> bool:
        """
        Validate the parameters passed during initialization.
        Subclasses should override this to implement specific checks.

        Returns:
            True if parameters are valid.

        Raises:
            ValueError: If any parameter is invalid.
        """
        # Base implementation does no validation, subclasses should override
        return True

    @property
    def values(self) -> Dict[str, np.ndarray]:
        """Return the calculated indicator values."""
        return self._values

    def get_value(self, key: str, index: int = -1) -> Any:
        """
        Get a specific calculated value at a given index (default is latest).

        Args:
            key: The key of the value to retrieve (e.g., 'sma', 'rsi').
            index: The index of the value to retrieve (-1 for latest).

        Returns:
            The indicator value at the specified index, or None if not found.
        """
        if key in self._values and len(self._values[key]) > abs(index):
            return self._values[key][index]
        return None
