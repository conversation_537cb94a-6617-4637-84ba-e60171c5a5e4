"""API endpoints for algorithmic exit strategies."""

import logging
from flask import Blueprint, jsonify, request, current_app
import MetaTrader5 as mt5
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

exit_strategies_bp = Blueprint('exit_strategies', __name__, url_prefix='/api/exit-strategies')


@exit_strategies_bp.route('/apply/<int:position_id>', methods=['POST'])
def apply_exit_strategy(position_id):
    """Apply an algorithmic exit strategy to a position."""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get parameters from request
        data = request.get_json(silent=True) or {}
        
        # Required parameters
        strategy_name = data.get('strategy_name')
        if not strategy_name:
            return jsonify({'error': 'Strategy name is required'}), 400
        
        # Optional parameters with defaults
        params = data.get('params', {})
        monitoring_interval = data.get('monitoring_interval', 60)  # Default 60 seconds
        
        # Apply the exit strategy
        result = mt5_conn.exit_strategies.apply_exit_strategy(
            position_id=position_id,
            strategy_name=strategy_name,
            params=params,
            monitoring_interval=monitoring_interval
        )
        
        if not result['success']:
            return jsonify({'error': result['message']}), 400
        
        return jsonify(result)
    
    except Exception as e:
        logger.exception(f"Error applying exit strategy: {str(e)}")
        return jsonify({'error': str(e)}), 500


@exit_strategies_bp.route('/stop/<int:position_id>', methods=['POST'])
def stop_exit_strategy(position_id):
    """Stop monitoring a position for exit conditions."""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Stop the exit strategy
        result = mt5_conn.exit_strategies.stop_monitoring(position_id)
        
        if not result['success']:
            return jsonify({'error': result['message']}), 400
        
        return jsonify(result)
    
    except Exception as e:
        logger.exception(f"Error stopping exit strategy: {str(e)}")
        return jsonify({'error': str(e)}), 500


@exit_strategies_bp.route('/active', methods=['GET'])
def list_active_strategies():
    """List all active exit strategies."""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get active strategies
        result = mt5_conn.exit_strategies.list_active_strategies()
        
        return jsonify(result)
    
    except Exception as e:
        logger.exception(f"Error listing active strategies: {str(e)}")
        return jsonify({'error': str(e)}), 500


@exit_strategies_bp.route('/available', methods=['GET'])
def get_available_strategies():
    """Get a list of available exit strategies."""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # List of available strategies with descriptions
        strategies = {
            "trend_reversal": {
                "name": "Trend Reversal",
                "description": "Exits the position when the analysis engine detects a trend reversal",
                "params": {
                    "symbol": "Trading symbol (optional, defaults to position symbol)",
                    "timeframe": "Timeframe for analysis (default: H1)",
                    "reversal_strength": "Minimum strength to trigger exit (1-10, default: 7)"
                }
            },
            "volatility_breakout": {
                "name": "Volatility Breakout",
                "description": "Exits when volatility significantly increases against the position direction",
                "params": {
                    "symbol": "Trading symbol (optional, defaults to position symbol)",
                    "timeframe": "Timeframe for analysis (default: H1)",
                    "volatility_factor": "Multiple of average volatility to trigger exit (default: 1.5)"
                }
            },
            "support_resistance": {
                "name": "Support/Resistance Break",
                "description": "Exits when price breaks key support/resistance levels against the position",
                "params": {
                    "symbol": "Trading symbol (optional, defaults to position symbol)",
                    "timeframe": "Timeframe for analysis (default: H1)",
                    "confirmation_candles": "Number of candles to confirm a break (default: 2)"
                }
            },
            "ma_crossover": {
                "name": "Moving Average Crossover",
                "description": "Exits on bearish crossover for longs or bullish crossover for shorts",
                "params": {
                    "symbol": "Trading symbol (optional, defaults to position symbol)",
                    "timeframe": "Timeframe for analysis (default: H1)",
                    "fast_ma": "Fast moving average period (default: 9)",
                    "slow_ma": "Slow moving average period (default: 21)"
                }
            },
            "rsi_overbought_oversold": {
                "name": "RSI Overbought/Oversold",
                "description": "Exits when RSI enters overbought territory for shorts or oversold for longs",
                "params": {
                    "symbol": "Trading symbol (optional, defaults to position symbol)",
                    "timeframe": "Timeframe for analysis (default: H1)",
                    "rsi_period": "RSI period (default: 14)",
                    "overbought": "Overbought threshold (default: 70)",
                    "oversold": "Oversold threshold (default: 30)"
                }
            }
        }
        
        return jsonify({
            "success": True,
            "available_strategies": strategies
        })
    
    except Exception as e:
        logger.exception(f"Error getting available strategies: {str(e)}")
        return jsonify({'error': str(e)}), 500
