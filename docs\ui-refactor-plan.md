# UI Refactor Plan: Connection Modal

## 1. Goal

Refactor the initial connection UI from the existing tab-based approach to a modal dialog. This aims to improve the user experience by creating a distinct connection step before accessing the main application features.

## 2. Approved Approach: Connection Modal

-   **Initial Load:** The application loads the main interface (tab navigation structure). Tabs like Trading, Analysis, Settings might be visible but disabled or indicate a disconnected state.
-   **Connection Modal Trigger:** If the application detects it's not connected to the MT5 account upon startup, it will immediately display the Connection settings form within a modal dialog that overlays the main interface.
-   **User Interaction:** While the modal is active, the user interacts *only* with the modal to enter credentials and establish a connection. The underlying main UI tabs are not interactive.
-   **On Success:** Once a connection is successfully established:
    -   The modal closes automatically.
    -   The main application tabs (Trading, Analysis, etc.) become fully active.
    -   The user is directed to the default main view (e.g., Dashboard or Trading tab).
-   **Reconnection/Status:** A persistent status indicator (e.g., in a header or status bar) will show the current connection status. A "Connect/Disconnect" button or similar mechanism will allow users to manage the connection or re-open the modal if needed after the initial setup.

## 3. Workflow Diagram

```mermaid
sequenceDiagram
    participant User
    participant App (Renderer)
    participant ConnectionModal
    participant MainUI (Tabs)
    participant BackendAPI

    App->>App: Check Connection Status on Startup
    alt Not Connected
        App->>ConnectionModal: Show Modal
        User->>ConnectionModal: Enter Credentials
        ConnectionModal->>BackendAPI: Attempt Connection(Credentials)
        BackendAPI-->>ConnectionModal: Connection Success/Failure
        alt Connection Success
            ConnectionModal->>App: Report Success
            App->>ConnectionModal: Hide Modal
            App->>MainUI: Enable Tabs & Navigate to Default View (e.g., Dashboard)
            User->>MainUI: Interact with App
        else Connection Failure
            ConnectionModal->>ConnectionModal: Show Error Message
            User->>ConnectionModal: Adjust Credentials/Retry
        end
    else Connected
        App->>MainUI: Enable Tabs & Navigate to Default View
        User->>MainUI: Interact with App
    end
```

## 4. Implementation Steps

1.  **Modify `renderer.js` / `main.js`:** Implement logic to check connection status on startup. Conditionally render/trigger the display of the `ConnectionModal` component if not connected. Manage the overall application state (e.g., `isConnected`).
2.  **Create/Adapt `ConnectionModal.js`:** Develop a reusable React component for the modal. This will likely involve adapting the existing form logic from `frontend/components/ConnectionTab.js`. The modal should handle user input, validation, communication with the backend API for connection attempts, and report success/failure back to the main application logic.
3.  **Update Main UI Components:** Modify the main application container and individual tab components (`Dashboard.js`, `Trading.js`, etc.) to be aware of the connection state. Tabs might be disabled or show placeholder content when disconnected. Ensure correct navigation to the default view upon successful connection via the modal.
4.  **Implement Status Indicator & Reconnect:** Add UI elements (e.g., in a shared header or footer) to display the connection status persistently. Implement functionality to allow users to disconnect or re-trigger the `ConnectionModal` if the connection is lost or needs to be changed.