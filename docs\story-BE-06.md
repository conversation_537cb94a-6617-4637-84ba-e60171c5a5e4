# Story BE-06: Implement Fetch Symbols Logic

## Description
Extend the MT5 integration module (`mt5_integration.py`) to implement functionality for retrieving the list of available trading symbols from the connected MT5 terminal. This feature will support the symbol selection dropdown in the Dashboard UI and is a prerequisite for market data operations.

## Technical Context
- Builds upon existing MT5 integration module (BE-02)
- Uses Python `MetaTrader5` library's symbol information functions
- Will be called via API endpoint to populate frontend symbol selector
- Must handle large symbol lists efficiently
- Requires active MT5 connection to function

## Dependencies
- BE-02 (Core MT5 Connection Logic) must be completed and stable
- MT5 terminal must be properly connected

## Acceptance Criteria

### 1. Symbol Fetching Implementation
- [ ] Add `get_symbols() -> List[str]` function to `mt5_integration.py`
  - Must verify active connection before attempting to fetch
  - Must return alphabetically sorted list of available symbols
  - Must filter out invalid or inactive symbols
  - Must cache results (valid for session) to improve performance
- [ ] Include metadata with symbol properties when available:
  - Trading allowed status
  - Digits (decimal places)
  - Trade mode (e.g., forex, futures)
  - Base currency information

### 2. Error Handling & Validation
- [ ] Implement proper error handling for common scenarios:
  - No active MT5 connection
  - Symbol retrieval failures
  - Empty or invalid symbol lists
- [ ] Add input validation for any parameters
- [ ] Create appropriate custom exceptions
- [ ] Log all errors with appropriate detail

### 3. Performance Optimization
- [ ] Implement symbol list caching mechanism
  - Cache duration: Valid for connection session
  - Cache invalidation: On disconnect or explicit refresh
- [ ] Fetch operation should complete within 500ms
- [ ] Handle large symbol lists (1000+ symbols) efficiently
- [ ] Include option to force refresh cache when needed

### 4. Testing Requirements
- [ ] Unit tests for symbol fetching functionality
- [ ] Integration tests with actual MT5 connection
- [ ] Cache mechanism tests
- [ ] Error scenario tests
- [ ] Performance tests for large symbol lists

### 5. Documentation
- [ ] Add comprehensive docstrings with type hints
- [ ] Document caching behavior and limitations
- [ ] Include example usage in module documentation
- [ ] Document error cases and handling

## Performance Requirements
- Initial symbol fetch: < 500ms
- Cached symbol fetch: < 50ms
- Memory efficient handling of large symbol lists
- Minimal impact on other MT5 operations

## Implementation Notes
1. Consider using MT5's `symbols_get()` function for efficient retrieval
2. Cache implementation should be thread-safe
3. Follow existing logging patterns from BE-02
4. Consider implementing background refresh for cache
5. Use dataclasses or named tuples for symbol metadata

## Example Usage
```python
# Get all available symbols
mt5_client = MT5Integration()
if mt5_client.is_connected():
    try:
        symbols = mt5_client.get_symbols()
        # Returns: ['EURUSD', 'GBPUSD', 'USDJPY', ...]
    except MT5SymbolError as e:
        logger.error(f"Failed to fetch symbols: {e}")

# Get symbols with metadata
symbols_with_meta = mt5_client.get_symbols(include_metadata=True)
# Returns: [
#   SymbolInfo(name='EURUSD', digits=5, trade_mode='forex', ...),
#   SymbolInfo(name='GBPUSD', digits=5, trade_mode='forex', ...),
#   ...
# ]
```

## Related Frontend Tasks
- FE-05 (Create Dashboard Tab & Symbol Selector UI) will consume this functionality
- Consider providing symbol metadata to enhance frontend display and filtering options