/* AnalysisPage.css - Layout and responsive styles for AnalysisPage */
.analysis-dashboard {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
  gap: 18px;
  margin: 20px 0;
}
@media (max-width: 900px) {
  .analysis-dashboard {
    grid-template-columns: 1fr;
  }
}
.analysis-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
.theme-toggle {
  margin-left: 16px;
  cursor: pointer;
}
