/* Connection Wizard Styles */
.wizard-container {
  max-width: 600px;
  width: 100%;
  background-color: var(--card, #1e293b); /* Use theme variable with fallback */
  color: var(--text, #f1f5f9); /* Use theme variable with fallback */
}

.wizard-progress {
  margin: 20px 0;
  padding: 0 20px;
}

.wizard-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.wizard-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
}

.step-number {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--secondary);
  color: var(--text);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 8px;
  transition: background-color 0.3s, color 0.3s;
}

.wizard-step.active .step-number {
  background-color: var(--primary);
  color: white;
}

.wizard-step.completed .step-number {
  background-color: var(--success);
  color: white;
}

.step-label {
  font-size: 14px;
  color: var(--text-secondary);
  transition: color 0.3s;
}

.wizard-step.active .step-label {
  color: var(--text);
  font-weight: 500;
}

.step-connector {
  flex-grow: 1;
  height: 2px;
  background-color: var(--border);
  margin: 0 10px;
  position: relative;
  top: -18px;
  z-index: 0;
}

.wizard-content {
  padding: 20px;
  min-height: 300px;
}

.wizard-step-content {
  animation: fadeIn 0.3s ease-in-out;
}

.wizard-actions {
  display: flex;
  justify-content: space-between;
  padding: 20px;
  border-top: 1px solid var(--border);
}

.step-description {
  color: var(--text-secondary);
  margin-bottom: 20px;
}

.input-with-button {
  display: flex;
  gap: 10px;
}

.input-with-button input {
  flex-grow: 1;
}

.connection-status-message {
  margin-top: 15px;
  padding: 12px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 10px;
  animation: fadeIn 0.3s ease-in-out;
}

.connection-status-message.testing {
  background-color: rgba(59, 130, 246, 0.1);
  border: 1px solid var(--info);
}

.connection-status-message.success {
  background-color: rgba(16, 185, 129, 0.1);
  border: 1px solid var(--success);
}

.connection-status-message.error {
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid var(--error);
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  border-top-color: var(--info);
  animation: spin 1s linear infinite;
}

.success-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--success);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.error-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--error);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.connection-summary {
  margin-top: 20px;
  padding: 15px;
  background-color: var(--card);
  border-radius: 6px;
  border: 1px solid var(--border);
}

.connection-summary h4 {
  margin-bottom: 10px;
  color: var(--text);
}

.summary-item {
  display: flex;
  margin-bottom: 8px;
}

.summary-label {
  width: 120px;
  color: var(--text-secondary);
  font-weight: 500;
}

.summary-value {
  color: var(--text);
  word-break: break-all;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
