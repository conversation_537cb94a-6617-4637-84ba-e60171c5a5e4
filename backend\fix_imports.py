import os
import re

def fix_imports_in_file(file_path):
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Replace backend.technical.base_indicator with technical.base_indicator
    content = content.replace('from backend.technical.base_indicator import', 'from technical.base_indicator import')
    
    # Replace backend.technical.indicators with technical.indicators
    content = content.replace('from backend.technical.indicators', 'from technical.indicators')
    
    # Add sys.path.append if not already present
    if 'sys.path.append' not in content and 'from technical.' in content:
        import_lines = content.split('\n')
        new_lines = []
        import_section_end = 0
        
        # Find the end of the import section
        for i, line in enumerate(import_lines):
            if line.startswith('import ') or line.startswith('from '):
                import_section_end = i
            elif line.strip() and import_section_end > 0:
                break
        
        # Insert the sys.path.append after the imports
        for i, line in enumerate(import_lines):
            new_lines.append(line)
            if i == import_section_end:
                new_lines.append('')
                new_lines.append('import sys')
                new_lines.append('import os')
                new_lines.append('sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))')
        
        content = '\n'.join(new_lines)
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"Fixed imports in {file_path}")

def fix_imports_in_directory(directory):
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                fix_imports_in_file(file_path)

if __name__ == '__main__':
    # Fix imports in the technical directory
    fix_imports_in_directory('technical')
    print("Done fixing imports!")
