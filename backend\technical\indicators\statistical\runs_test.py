from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class RunsTestIndicator(BaseIndicator):
    """Runs Test for Randomness indicator."""

    def __init__(self, period: int = 20, source: str = 'close'):
        """
        Initialize Runs Test indicator.

        Args:
            period: The rolling window size for the test.
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
        """
        super().__init__({
            'period': period,
            'source': source
        })

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate rolling Runs Test Z-statistic."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period']:
             return {'z_statistic': np.array([])}

        period = self.params['period']
        source_col = self.params['source'].lower()

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Calculate price changes
        price_change = source_data.diff().fillna(0)

        # Initialize result array
        n = len(source_data)
        z_stats = np.full(n, np.nan)

        for i in range(period -1, n):
            window_changes = price_change.iloc[i - period + 1 : i + 1].values
            # Remove zero changes as they don't contribute to runs up or down
            window_changes = window_changes[window_changes != 0]

            if len(window_changes) < 2: # Need at least two non-zero changes
                continue

            pos = np.sum(window_changes > 0)
            neg = np.sum(window_changes < 0)
            n_runs = pos + neg

            # Skip if all changes are in the same direction
            if pos == 0 or neg == 0:
                continue

            # Count runs
            signs = np.sign(window_changes)
            # A run ends when the sign changes
            runs = np.sum(signs[:-1] != signs[1:]) + 1

            # Calculate expected runs and standard deviation
            exp_runs = (2 * pos * neg) / n_runs + 1
            # Handle potential division by zero in variance calculation if n_runs is 1
            if n_runs > 1:
                 variance = (2 * pos * neg * (2 * pos * neg - n_runs)) / (n_runs**2 * (n_runs - 1))
                 # Avoid division by zero if variance is zero or negative (shouldn't happen with pos>0 and neg>0)
                 if variance > 1e-10:
                     std_runs = np.sqrt(variance)
                     z = (runs - exp_runs) / std_runs
                     z_stats[i] = z
                 else:
                     z_stats[i] = 0 # Or NaN, if variance is zero, z is undefined or zero
            else:
                 z_stats[i] = np.nan # Cannot calculate for only one run


        self._values = {
            'z_statistic': z_stats
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 2:
            raise ValueError("Period must be at least 2 for Runs Test")
        return True