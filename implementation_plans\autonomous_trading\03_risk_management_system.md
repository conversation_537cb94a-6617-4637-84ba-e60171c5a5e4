# Phase 3: Enhanced Risk Management System

## Overview

This phase focuses on implementing an enhanced risk management system to prevent excessive position accumulation and improve risk control. The system will track overall risk exposure, detect potential market reversals, and implement advanced position management techniques.

## Current Implementation

Currently, the Garuda-Algo autonomous trading system has basic risk management with:
- Risk percentage per trade
- Maximum open trades limit
- Simple stop-loss and take-profit placement

Key limitations include:
- Continues adding positions without calculating overall risk exposure
- No consideration for potential market reversals
- No dynamic position management or early exit strategy
- No daily risk limits or drawdown protection

## Implementation Details

### 1. Risk Tracking and Calculation

Create a new file `backend/risk_manager.py` for risk management functionality:

```python
import datetime
import logging
from typing import Dict, List, Optional, Tuple, Union, Any

logger = logging.getLogger('risk_manager')

class RiskManager:
    """Class to manage and track trading risk."""
    
    def __init__(self, config, mt5_connector):
        """Initialize with risk management configuration."""
        self.config = config
        self.risk_config = config.get("risk_management", {})
        self.mt5 = mt5_connector
        
        # Risk tracking
        self.daily_risk_used = 0.0
        self.symbol_risk = {}  # Risk per symbol
        self.consecutive_losses = 0
        self.last_reset_day = datetime.datetime.now().day
        
        # Initialize
        self.reset_daily_risk_if_needed()
    
    def reset_daily_risk_if_needed(self):
        """Reset daily risk if it's a new day."""
        current_day = datetime.datetime.now().day
        if current_day != self.last_reset_day:
            logger.info(f"Resetting daily risk tracking for new day")
            self.daily_risk_used = 0.0
            self.last_reset_day = current_day
    
    def calculate_position_risk(self, symbol, volume, entry_price, stop_loss):
        """Calculate the risk for a position in monetary and percentage terms.
        
        Args:
            symbol: The trading symbol
            volume: The position volume (lot size)
            entry_price: The entry price
            stop_loss: The stop loss price
            
        Returns:
            dict: Risk information including monetary and percentage risk
        """
        # Get account info
        account_info_result = self.mt5.get_account_info()
        if not account_info_result["success"]:
            logger.error(f"Failed to get account info: {account_info_result['message']}")
            return {
                "success": False,
                "message": "Failed to get account info"
            }
        
        account_info = account_info_result["account_info"]
        balance = account_info.get("balance", 0)
        if balance <= 0:
            return {
                "success": False,
                "message": "Invalid account balance"
            }
        
        # Calculate pip value
        symbol_info = self.mt5.get_symbol_info(symbol)
        if not symbol_info["success"]:
            return {
                "success": False,
                "message": f"Failed to get symbol info for {symbol}"
            }
        
        # Calculate risk in monetary terms
        point = symbol_info["point"]
        price_per_pip = symbol_info["trade_tick_value"] / symbol_info["trade_tick_size"]
        stop_distance = abs(entry_price - stop_loss) / point
        monetary_risk = stop_distance * price_per_pip * volume
        percentage_risk = (monetary_risk / balance) * 100
        
        return {
            "success": True,
            "monetary_risk": monetary_risk,
            "percentage_risk": percentage_risk,
            "volume": volume,
            "symbol": symbol
        }
    
    def is_trade_allowed(self, symbol, volume, entry_price, stop_loss):
        """Check if a trade is allowed based on risk management rules.
        
        Args:
            symbol: The trading symbol
            volume: The position volume (lot size)
            entry_price: The entry price
            stop_loss: The stop loss price
            
        Returns:
            dict: Result with allowed status and reason if not allowed
        """
        # Reset daily risk if needed
        self.reset_daily_risk_if_needed()
        
        # Calculate position risk
        risk_result = self.calculate_position_risk(symbol, volume, entry_price, stop_loss)
        if not risk_result["success"]:
            return {
                "allowed": False,
                "reason": risk_result["message"]
            }
        
        percentage_risk = risk_result["percentage_risk"]
        monetary_risk = risk_result["monetary_risk"]
        
        # Get risk limits
        max_daily_risk = self.risk_config.get("max_daily_risk", 5.0)
        max_risk_per_symbol = self.risk_config.get("max_risk_per_symbol", 2.0)
        
        # Apply risk reduction if configured and needed
        if self.risk_config.get("reduce_risk_after_loss", True):
            max_consecutive_losses = self.risk_config.get("max_consecutive_losses", 3)
            risk_reduction_factor = self.risk_config.get("risk_reduction_factor", 0.5)
            
            if self.consecutive_losses >= max_consecutive_losses:
                # Reduce risk limits
                reduced_factor = risk_reduction_factor ** (self.consecutive_losses - max_consecutive_losses + 1)
                max_daily_risk *= reduced_factor
                max_risk_per_symbol *= reduced_factor
                logger.info(f"Reducing risk limits due to {self.consecutive_losses} consecutive losses. Factor: {reduced_factor}")
        
        # Check against daily risk limit
        if self.daily_risk_used + percentage_risk > max_daily_risk:
            return {
                "allowed": False,
                "reason": f"Daily risk limit exceeded. Adding {percentage_risk:.2f}% would exceed the limit of {max_daily_risk:.2f}%",
                "risk_info": risk_result
            }
        
        # Check against per-symbol risk limit
        current_symbol_risk = self.symbol_risk.get(symbol, 0.0)
        if current_symbol_risk + percentage_risk > max_risk_per_symbol:
            return {
                "allowed": False,
                "reason": f"Symbol risk limit exceeded for {symbol}. Adding {percentage_risk:.2f}% would exceed the limit of {max_risk_per_symbol:.2f}%",
                "risk_info": risk_result
            }
        
        # Check risk-reward ratio if configured
        min_risk_reward = self.risk_config.get("risk_reward_min_ratio", 1.5)
        if min_risk_reward > 0 and "take_profit" in locals():
            take_profit = locals()["take_profit"]  # This would need to be passed in
            reward = abs(take_profit - entry_price)
            risk = abs(entry_price - stop_loss)
            if risk > 0:
                risk_reward_ratio = reward / risk
                if risk_reward_ratio < min_risk_reward:
                    return {
                        "allowed": False,
                        "reason": f"Risk-reward ratio {risk_reward_ratio:.2f} is below minimum {min_risk_reward}",
                        "risk_info": risk_result
                    }
        
        # All checks passed
        return {
            "allowed": True,
            "risk_info": risk_result
        }
    
    def register_trade(self, trade_result):
        """Register a new trade and update risk tracking.
        
        Args:
            trade_result: Result from trade execution including risk info
        """
        if trade_result["success"] and "risk_info" in trade_result:
            risk_info = trade_result["risk_info"]
            symbol = risk_info["symbol"]
            percentage_risk = risk_info["percentage_risk"]
            
            # Update risk tracking
            self.daily_risk_used += percentage_risk
            self.symbol_risk[symbol] = self.symbol_risk.get(symbol, 0.0) + percentage_risk
            
            logger.info(f"Registered new trade for {symbol}. Daily risk now {self.daily_risk_used:.2f}%, symbol risk {self.symbol_risk[symbol]:.2f}%")
    
    def register_trade_result(self, trade_result):
        """Register the result of a closed trade.
        
        Args:
            trade_result: Result from trade close
        """
        if "profit" in trade_result:
            profit = trade_result["profit"]
            symbol = trade_result.get("symbol", "unknown")
            
            # Update consecutive wins/losses counter
            if profit > 0:
                self.consecutive_losses = 0
                logger.info(f"Trade result: Profit {profit} on {symbol}. Reset consecutive losses counter.")
            else:
                self.consecutive_losses += 1
                logger.info(f"Trade result: Loss {profit} on {symbol}. Consecutive losses now {self.consecutive_losses}")
            
            # Update symbol risk (if trade is closed, reduce allocated risk)
            if symbol in self.symbol_risk and "percentage_risk" in trade_result:
                self.symbol_risk[symbol] -= trade_result["percentage_risk"]
                self.symbol_risk[symbol] = max(0, self.symbol_risk[symbol])  # Ensure not negative
```

### 2. Market Reversal Detection

Implement market reversal detection to close trades when market conditions change:

```python
def detect_reversal(self, symbol, timeframe, current_direction, confidence_threshold=70):
    """Detect if market is showing signs of reversal against current position.
    
    Args:
        symbol: Trading symbol
        timeframe: Timeframe to analyze
        current_direction: Current trade direction ('buy' or 'sell')
        confidence_threshold: Minimum confidence to confirm reversal
        
    Returns:
        dict: Result with reversal detected flag and confidence
    """
    try:
        # Create analysis engine instance
        from analysis import AnalysisEngine
        analyzer = AnalysisEngine(self.mt5)
        
        # Get current price for symbol
        price_data = self.mt5.get_current_price(symbol)
        if "error" in price_data:
            return {
                "success": False,
                "message": f"Failed to get price for {symbol}: {price_data['error']}"
            }
            
        # Add current price to analyzer
        analyzer.current_price = price_data
        
        # Perform analysis
        analysis_result = analyzer.get_analysis_for_timeframe(symbol, timeframe)
        if not analysis_result.get("success"):
            return {
                "success": False,
                "message": f"Analysis failed for {symbol}/{timeframe}: {analysis_result.get('message', 'Unknown error')}"
            }
            
        # Extract analysis data
        analysis_data = analysis_result.get("analysis", analysis_result.get("data", {}))
        
        # Ensure analysis data includes current price
        analysis_data["current_price"] = price_data
        
        # Generate signals
        from backend.api.signals import generate_signals_from_analysis
        signals_result = generate_signals_from_analysis(symbol, timeframe, analysis_data, price_data)
        
        # Check for reversal signals
        all_signals = signals_result.get('signals', [])
        
        # Look for signals that contradict current position
        opposing_signals = []
        
        for signal in all_signals:
            signal_type = signal.get('signalType', '').lower()
            confidence = signal.get('confidence', 0)
            
            # Check if signal contradicts current position
            if (current_direction == 'buy' and signal_type == 'sell') or \
               (current_direction == 'sell' and signal_type == 'buy'):
                opposing_signals.append({
                    'type': signal_type,
                    'confidence': confidence,
                    'strategy': signal.get('strategy', '')
                })
        
        # Sort by confidence
        opposing_signals.sort(key=lambda x: x['confidence'], reverse=True)
        
        # Check if any opposing signal meets threshold
        if opposing_signals and opposing_signals[0]['confidence'] >= confidence_threshold:
            return {
                'success': True,
                'reversal_detected': True,
                'confidence': opposing_signals[0]['confidence'],
                'signal': opposing_signals[0]
            }
            
        return {
            'success': True,
            'reversal_detected': False
        }
            
    except Exception as e:
        logger.exception(f"Error detecting reversal: {str(e)}")
        return {
            'success': False,
            'message': f"Error detecting reversal: {str(e)}"
        }
```

### 3. Position Management and Early Exit

Implement position management to close trades early when conditions change:

```python
def manage_open_positions(self):
    """Manage open positions based on risk management rules.
    
    This includes:
    - Detecting reversals and closing positions
    - Implementing trailing stops
    - Hedging when market conditions change
    
    Returns:
        dict: Result with actions taken
    """
    actions_taken = []
    
    try:
        # Get current open positions
        positions_result = self.mt5.get_open_positions()
        if not positions_result.get("success", False):
            return {
                "success": False,
                "message": f"Failed to get open positions: {positions_result.get('message', 'Unknown error')}"
            }
            
        open_positions = positions_result.get("positions", [])
        if not open_positions:
            return {
                "success": True,
                "message": "No open positions to manage",
                "actions": []
            }
            
        # Check each position for potential management actions
        for position in open_positions:
            position_id = position.get("ticket")
            symbol = position.get("symbol")
            direction = "buy" if position.get("type") == 0 else "sell"
            profit = position.get("profit", 0)
            volume = position.get("volume", 0)
            open_price = position.get("price_open", 0)
            current_price = position.get("price_current", 0)
            
            # 1. Check for reversals if enabled
            if self.risk_config.get("close_trades_on_reversal", True):
                timeframe = "H1"  # Use hourly for reversal detection or get from config
                confidence_level = self.risk_config.get("reversal_confirmation_level", 70)
                
                reversal_result = self.detect_reversal(symbol, timeframe, direction, confidence_level)
                
                if reversal_result.get("success", False) and reversal_result.get("reversal_detected", False):
                    # Close position due to reversal signal
                    close_result = self.mt5.close_position(position_id)
                    
                    if close_result.get("success", False):
                        logger.info(f"Closed position {position_id} ({symbol} {direction}) due to reversal signal with "
                                  f"{reversal_result.get('confidence')}% confidence. Profit: {profit}")
                        
                        actions_taken.append({
                            "action": "close_reversal",
                            "position_id": position_id,
                            "symbol": symbol,
                            "direction": direction,
                            "confidence": reversal_result.get('confidence'),
                            "profit": profit
                        })
                        
                        # Register the trade result
                        self.register_trade_result({
                            "profit": profit,
                            "symbol": symbol,
                            "percentage_risk": self.symbol_risk.get(symbol, 0) / (open_positions.count(lambda p: p.get("symbol") == symbol))
                        })
                    else:
                        logger.error(f"Failed to close position {position_id} despite reversal signal: "
                                   f"{close_result.get('message', 'Unknown error')}")
            
            # 2. Implement trailing stops if enabled
            if self.risk_config.get("trailing_stop_enabled", False):
                # Calculate profit in percentage for activation comparison
                risk_info = self.calculate_position_risk(symbol, volume, open_price, position.get("sl", 0))
                risk_amount = risk_info.get("monetary_risk", 0) if risk_info.get("success", False) else 0
                
                # Only apply trailing stop if we have valid risk calculation and positive profit
                if risk_amount > 0 and profit > 0:
                    profit_ratio = profit / risk_amount
                    activation_level = self.risk_config.get("trailing_stop_activation", 0.5)
                    
                    if profit_ratio >= activation_level:
                        # Calculate new stop loss level
                        current_sl = position.get("sl", 0)
                        
                        if direction == "buy":
                            # For buy positions, move stop loss up
                            min_distance = (current_price - open_price) * 0.3  # Minimum 30% of gained distance
                            new_sl = max(current_sl, open_price + min_distance)
                        else:  # sell position
                            # For sell positions, move stop loss down
                            min_distance = (open_price - current_price) * 0.3  # Minimum 30% of gained distance
                            new_sl = min(current_sl, open_price - min_distance)
                        
                        # Only update if the new stop loss would be more favorable
                        if (direction == "buy" and new_sl > current_sl) or \
                           (direction == "sell" and new_sl < current_sl):
                            modify_result = self.mt5.modify_position(position_id, sl=new_sl)
                            
                            if modify_result.get("success", False):
                                logger.info(f"Updated trailing stop for position {position_id} ({symbol} {direction}). "
                                          f"New SL: {new_sl}, Old SL: {current_sl}")
                                          
                                actions_taken.append({
                                    "action": "trailing_stop",
                                    "position_id": position_id,
                                    "symbol": symbol,
                                    "new_sl": new_sl,
                                    "old_sl": current_sl,
                                    "profit_ratio": profit_ratio
                                })
            
            # 3. Implement hedging if enabled
            if self.risk_config.get("hedging_enabled", False):
                # Only consider hedging if position is in profit and timeframe trend has changed significantly
                # This is more complex logic that would need to be implemented based on strategy
                pass
        
        return {
            "success": True,
            "message": f"Managed {len(open_positions)} positions with {len(actions_taken)} actions",
            "actions": actions_taken
        }
        
    except Exception as e:
        logger.exception(f"Error in position management: {str(e)}")
        return {
            "success": False,
            "message": f"Error in position management: {str(e)}",
            "actions": actions_taken
        }
```

### 4. Integration with Trading Loop

Modify `backend/api/autonomous.py` to use the risk manager in the trading loop:

```python
from backend.risk_manager import RiskManager

def _trading_loop(app):
    """Main autonomous trading loop."""
    global AUTONOMOUS_RUNNING
    
    # ... existing code ...
    
    # Create risk manager
    risk_manager = RiskManager(AUTONOMOUS_CONFIG, mt5_instance)
    
    while AUTONOMOUS_RUNNING:
        try:
            # ... existing connection checks ...
            
            # Manage existing positions first
            management_result = risk_manager.manage_open_positions()
            if management_result.get("success", False) and management_result.get("actions", []):
                logger.info(f"Position management performed {len(management_result['actions'])} actions")
            
            # ... existing position counting code ...
            
            # Process each symbol
            for symbol in AUTONOMOUS_CONFIG["symbols"]:
                # ... existing symbol validation ...
                
                # ... existing signal generation ...
                
                # Get the highest confidence signal
                best_signal = valid_signals[0]
                signal_type = best_signal['signalType'].lower()
                
                # ... convert signal to internal format ...
                
                # Use the stop loss from the signal
                sl_level = best_signal['stopLoss']
                tp_level = best_signal['takeProfit']
                
                # Calculate lot size
                lot_size_result = mt5_instance.calculate_lot_size(symbol, AUTONOMOUS_CONFIG["risk_percent"], sl_points)
                
                if not lot_size_result.get("success", False):
                    logger.error(f"Failed to calculate lot size: {lot_size_result.get('message', 'Unknown error')}")
                    continue
                    
                volume = lot_size_result["lot_size"]
                
                # Check if trade is allowed by risk manager
                risk_check = risk_manager.is_trade_allowed(
                    symbol, 
                    volume, 
                    price_data["ask"] if signal == "buy" else price_data["bid"],
                    sl_level
                )
                
                if not risk_check["allowed"]:
                    logger.info(f"Trade not allowed by risk manager: {risk_check['reason']}")
                    continue
                
                # Execute the trade
                # ... existing code for trade execution ...
                
                # Register trade with risk manager
                if trade_result["success"]:
                    trade_result["risk_info"] = risk_check["risk_info"]
                    risk_manager.register_trade(trade_result)
```

## Files to Modify/Create

1. **backend/risk_manager.py** (new):
   - Implement RiskManager class
   - Add risk calculation functions
   - Add reversal detection
   - Add position management

2. **backend/api/autonomous.py**:
   - Integrate risk management into trading loop
   - Add position management before new trades
   - Update configuration with risk management settings

3. **backend/autonomous_trader.py**:
   - Update to use risk manager for trading decisions

## Testing Plan

1. **Risk Calculation**:
   - Test position risk calculation with different symbols and position sizes
   - Verify daily risk accumulation
   - Test risk limits enforcement

2. **Reversal Detection**:
   - Test with different market conditions
   - Verify correct identification of reversal signals
   - Test with different confirmation thresholds

3. **Position Management**:
   - Test automatic closing of positions on reversal
   - Verify trailing stop functionality
   - Test handling of multiple positions

4. **Integration Tests**:
   - Verify risk manager prevents excessive position accumulation
   - Test risk reduction after consecutive losses
   - Verify trade execution with risk checks

## Acceptance Criteria

- System prevents excessive position accumulation
- Daily risk limits are enforced
- Positions are closed when reversal signals appear
- Trailing stops protect profits on winning trades
- Risk is automatically reduced after consecutive losses
- All risk management actions are properly logged
