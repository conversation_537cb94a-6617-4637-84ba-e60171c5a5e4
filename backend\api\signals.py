from flask import Blueprint, request, jsonify, current_app
import os
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from mt5_integration import MT5Integration
from analysis import AnalysisEngine
from datetime import datetime, timedelta
import logging
import time

signals_bp = Blueprint('signals', __name__)
logger = logging.getLogger(__name__)

@signals_bp.route('/trade_signals', methods=['GET'])
def get_trade_signals():
    """
    Generate trade signals for different strategies based on technical analysis.
    Returns signals categorized by strategy type (scalping, intraday, swing, position).
    """
    symbol = request.args.get('symbol')
    timeframe = request.args.get('timeframe')
    
    if not symbol or not timeframe:
        return jsonify({'error': 'Symbol and timeframe parameters required'}), 400
        
    try:
        start_time = time.time()
        logger.info(f"[API Signals] Generating signals for {symbol} on {timeframe} timeframe...")
        
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503
            
        # Create analysis engine
        analyzer = AnalysisEngine(mt5_conn)
        
        # Get current price
        current_price = mt5_conn.get_current_price(symbol)
        if not current_price:
            return jsonify({'error': f'Could not get current price for {symbol}'}), 500
            
        # Add current price to analyzer
        analyzer.current_price = current_price
        
        # Perform analysis
        analysis_result = analyzer.get_analysis_for_timeframe(symbol, timeframe)
        
        if not analysis_result.get("success"):
            error_msg = analysis_result.get("message", "Analysis failed")
            logger.error(f"Analysis failed for {symbol}/{timeframe}: {error_msg}")
            return jsonify({'error': error_msg}), 500
        
        # Extract analysis data
        if "analysis" in analysis_result:
            analysis_data = analysis_result["analysis"]
        elif "data" in analysis_result:
            analysis_data = analysis_result["data"]
        else:
            analysis_data = analysis_result
            
        # Ensure we have current price in analysis data
        analysis_data["current_price"] = current_price
        
        # Generate signals for each strategy
        signals = generate_signals_from_analysis(symbol, timeframe, analysis_data, current_price)
        
        # Add processing time for debugging
        processing_time = time.time() - start_time
        signals['processing_time'] = processing_time
        
        logger.info(f"[API Signals] Generated signals for {symbol}/{timeframe} in {processing_time:.2f} seconds")
        return jsonify(signals)
        
    except Exception as e:
        logger.exception(f"Error generating signals for {symbol}/{timeframe}: {str(e)}")
        return jsonify({'error': str(e)}), 500
        

def generate_signals_from_analysis(symbol, timeframe, analysis_data, current_price_data):
    """
    Process analysis data to generate signals for different strategy types.
    
    Args:
        symbol (str): Trading symbol
        timeframe (str): Timeframe for analysis
        analysis_data (dict): Technical analysis data
        current_price_data (dict): Current price data
        
    Returns:
        dict: Signals categorized by strategy type
    """
    # Initialize result structure
    result = {
        'signals': [],
        'strategySignals': {
            'scalping': [],
            'intraday': [],
            'swing': [],
            'position': []
        },
        'baseData': analysis_data,
        'currentPrice': 0
    }
    
    # Extract current price
    current_price = None
    if current_price_data:
        if 'bid' in current_price_data:
            current_price = current_price_data['bid']
        elif 'last' in current_price_data:
            current_price = current_price_data['last']
        elif 'close' in current_price_data:
            current_price = current_price_data['close']
            
    # If we couldn't find a current price, try other sources
    if not current_price and 'price_data' in analysis_data and analysis_data['price_data']:
        price_data = analysis_data['price_data']
        if 'close' in price_data and price_data['close'] and len(price_data['close']) > 0:
            current_price = price_data['close'][-1]
            
    # If still no current price, return empty signals
    if not current_price:
        result['currentPrice'] = 0
        logger.warning(f"Could not find current price for {symbol}")
        return result
        
    result['currentPrice'] = current_price
    
    # Extract signal recommendation if available
    base_signal = {}
    if 'signal_recommendation' in analysis_data:
        base_signal = analysis_data['signal_recommendation']
    else:
        # Create a signal recommendation based on indicators
        signal_type = 'NEUTRAL'
        confidence = 50
        indicators = {}
        
        # Check RSI for signal
        if 'rsi' in analysis_data:
            rsi_value = analysis_data['rsi'].get('value')
            if rsi_value:
                indicators['rsi'] = 'NEUTRAL'
                if rsi_value < 30:
                    indicators['rsi'] = 'OVERSOLD'
                    signal_type = 'BUY'
                    confidence = max(confidence, 60)
                elif rsi_value < 40:
                    indicators['rsi'] = 'APPROACHING_OVERSOLD'
                    if signal_type == 'NEUTRAL':
                        signal_type = 'BUY'
                        confidence = max(confidence, 55)
                elif rsi_value > 70:
                    indicators['rsi'] = 'OVERBOUGHT'
                    signal_type = 'SELL'
                    confidence = max(confidence, 60)
                elif rsi_value > 60:
                    indicators['rsi'] = 'APPROACHING_OVERBOUGHT'
                    if signal_type == 'NEUTRAL':
                        signal_type = 'SELL'
                        confidence = max(confidence, 55)
                        
        # Check MACD for signal
        if 'macd' in analysis_data:
            macd_data = analysis_data['macd']
            macd_value = macd_data.get('macd')
            signal_value = macd_data.get('signal')
            histogram = macd_data.get('histogram')
            
            if macd_value is not None and signal_value is not None:
                indicators['macd'] = 'NEUTRAL'
                
                # MACD crossover (bullish)
                if macd_value > signal_value and histogram > 0:
                    indicators['macd'] = 'BULLISH_CROSSOVER'
                    if signal_type == 'NEUTRAL' or signal_type == 'BUY':
                        signal_type = 'BUY'
                        confidence = max(confidence, 65)
                    if signal_type == 'STRONG_BUY':
                        confidence = max(confidence, 75)
                        
                # MACD crossover (bearish)
                elif macd_value < signal_value and histogram < 0:
                    indicators['macd'] = 'BEARISH_CROSSOVER'
                    if signal_type == 'NEUTRAL' or signal_type == 'SELL':
                        signal_type = 'SELL'
                        confidence = max(confidence, 65)
                    if signal_type == 'STRONG_SELL':
                        confidence = max(confidence, 75)
        
        # Check trend for signal
        if 'trend' in analysis_data:
            trend_data = analysis_data['trend']
            trend_direction = trend_data.get('overall', 'NEUTRAL')
            strength = trend_data.get('strength', 50)
            
            indicators['trend'] = trend_direction.upper()
            
            if 'bullish' in trend_direction.lower() and strength > 60:
                if signal_type == 'BUY':
                    signal_type = 'STRONG_BUY'
                    confidence = max(confidence, 70)
                elif signal_type == 'NEUTRAL':
                    signal_type = 'BUY'
                    confidence = max(confidence, 60)
                    
            elif 'bearish' in trend_direction.lower() and strength > 60:
                if signal_type == 'SELL':
                    signal_type = 'STRONG_SELL'
                    confidence = max(confidence, 70)
                elif signal_type == 'NEUTRAL':
                    signal_type = 'SELL'
                    confidence = max(confidence, 60)
        
        # Create base signal
        base_signal = {
            'signal': signal_type,
            'confidence': confidence,
            'indicators': indicators
        }
    
    # Generate signals for each strategy type
    scalping_signals = generate_scalping_signals(symbol, timeframe, analysis_data, base_signal, current_price)
    intraday_signals = generate_intraday_signals(symbol, timeframe, analysis_data, base_signal, current_price)
    swing_signals = generate_swing_signals(symbol, timeframe, analysis_data, base_signal, current_price)
    position_signals = generate_position_signals(symbol, timeframe, analysis_data, base_signal, current_price)
    
    # Update result
    result['strategySignals']['scalping'] = scalping_signals
    result['strategySignals']['intraday'] = intraday_signals
    result['strategySignals']['swing'] = swing_signals
    result['strategySignals']['position'] = position_signals
    
    # Combine all signals
    all_signals = []
    all_signals.extend(scalping_signals)
    all_signals.extend(intraday_signals)
    all_signals.extend(swing_signals)
    all_signals.extend(position_signals)
    
    result['signals'] = all_signals
    
    return result

def generate_scalping_signals(symbol, timeframe, analysis_data, base_signal, current_price):
    """Generate signals for scalping strategy"""
    if not base_signal or not base_signal.get('signal') or base_signal.get('signal') == 'NEUTRAL':
        return []
    
    # Use the main signal but adjust for scalping (tighter stops, smaller targets)
    direction = 'BUY' if 'BUY' in base_signal.get('signal', '') else 'SELL'
    is_strong = 'STRONG' in base_signal.get('signal', '')
    
    # Get ATR value if available or use percentage of price
    atr_value = analysis_data.get('atr', {}).get('value', current_price * 0.001)  # Default to 0.1%
    
    # Calculate stop loss and take profit for scalping (tighter ranges)
    scalping_sl = current_price - (atr_value * 1.0) if direction == 'BUY' else current_price + (atr_value * 1.0)
    scalping_tp = current_price + (atr_value * 2.0) if direction == 'BUY' else current_price - (atr_value * 2.0)
    
    # Calculate risk-reward ratio
    risk = abs(current_price - scalping_sl)
    reward = abs(current_price - scalping_tp)
    risk_reward = round(reward / risk, 2) if risk > 0 else 0
    
    # Execution window for scalping (shorter)
    now = datetime.now()
    execution_start = now
    execution_end = now + timedelta(hours=2)  # 2 hours
    expiry = execution_end
    
    # Get indicators used for the signal
    indicators = base_signal.get('indicators', {})
    
    # Create signal with unique ID
    signal_id = f"{symbol}-{timeframe}-scalping-{direction}-{int(time.time())}".lower()
    
    return [{
        'id': signal_id,
        'symbol': symbol,
        'timeframe': timeframe,
        'strategy': 'scalping',
        'signalType': direction,
        'confidence': is_strong and base_signal.get('confidence', 50) or max(40, base_signal.get('confidence', 50) - 10),
        'entry': {
            'price': current_price,
            'type': 'MARKET',
            'time': now.isoformat()
        },
        'stopLoss': round(scalping_sl, 5),
        'takeProfit': round(scalping_tp, 5),
        'riskReward': risk_reward,
        'timeWindow': {
            'start': execution_start.isoformat(),
            'end': execution_end.isoformat(),
            'expires': expiry.isoformat()
        },
        'source': {
            **(indicators or {}),
            'primaryIndicator': next(iter(indicators or {}), 'price')
        },
        'explanation': f"Scalping opportunity based on {direction} signal with {is_strong and 'strong' or 'moderate'} momentum. Tight stop loss for quick in-and-out trade."
    }]

def generate_intraday_signals(symbol, timeframe, analysis_data, base_signal, current_price):
    """Generate signals for intraday trading strategy"""
    if not base_signal or not base_signal.get('signal') or base_signal.get('signal') == 'NEUTRAL':
        return []
    
    # Use the main signal but adjust for intraday trading
    direction = 'BUY' if 'BUY' in base_signal.get('signal', '') else 'SELL'
    is_strong = 'STRONG' in base_signal.get('signal', '')
    
    # Get ATR value if available or use percentage of price
    atr_value = analysis_data.get('atr', {}).get('value', current_price * 0.002)  # Default to 0.2%
    
    # Calculate stop loss and take profit for intraday (medium ranges)
    intraday_sl = current_price - (atr_value * 1.5) if direction == 'BUY' else current_price + (atr_value * 1.5)
    intraday_tp = current_price + (atr_value * 3.0) if direction == 'BUY' else current_price - (atr_value * 3.0)
    
    # Calculate risk-reward ratio
    risk = abs(current_price - intraday_sl)
    reward = abs(current_price - intraday_tp)
    risk_reward = round(reward / risk, 2) if risk > 0 else 0
    
    # Execution window for intraday (current session)
    now = datetime.now()
    execution_start = now
    execution_end = now + timedelta(hours=8)  # 8 hours
    expiry = execution_end
    
    # Get indicators used for the signal
    indicators = base_signal.get('indicators', {})
    
    # Create signal with unique ID
    signal_id = f"{symbol}-{timeframe}-intraday-{direction}-{int(time.time())}".lower()
    
    return [{
        'id': signal_id,
        'symbol': symbol,
        'timeframe': timeframe,
        'strategy': 'intraday',
        'signalType': direction,
        'confidence': is_strong and base_signal.get('confidence', 50) or max(40, base_signal.get('confidence', 50) - 5),
        'entry': {
            'price': current_price,
            'type': 'MARKET',
            'time': now.isoformat()
        },
        'stopLoss': round(intraday_sl, 5),
        'takeProfit': round(intraday_tp, 5),
        'riskReward': risk_reward,
        'timeWindow': {
            'start': execution_start.isoformat(),
            'end': execution_end.isoformat(),
            'expires': expiry.isoformat()
        },
        'source': {
            **(indicators or {}),
            'primaryIndicator': next(iter(indicators or {}), 'price')
        },
        'explanation': f"Intraday opportunity based on {direction} signal with {is_strong and 'strong' or 'moderate'} conviction. Trade has potential for completion within the current trading session."
    }]

def generate_swing_signals(symbol, timeframe, analysis_data, base_signal, current_price):
    """Generate signals for swing trading strategy"""
    # Only generate swing signals on H4 or higher timeframes
    if not base_signal or not base_signal.get('signal') or base_signal.get('signal') == 'NEUTRAL' or \
            timeframe not in ['H4', 'D1', 'W1']:
        return []
    
    # Use the main signal for swing trading
    direction = 'BUY' if 'BUY' in base_signal.get('signal', '') else 'SELL'
    is_strong = 'STRONG' in base_signal.get('signal', '')
    
    # Find key support/resistance for swing trading
    sr_data = analysis_data.get('support_resistance', {})
    key_level = None
    
    if direction == 'BUY' and 'support_levels' in sr_data:
        # For buy signals, find a strong support level below price
        support_levels = [level for level in sr_data['support_levels'] 
                          if level.get('price', 0) < current_price and level.get('strength', 0) >= 6]
        if support_levels:
            support_levels.sort(key=lambda x: x.get('price', 0), reverse=True)  # Highest support first
            key_level = support_levels[0].get('price') if support_levels else None
        
    elif direction == 'SELL' and 'resistance_levels' in sr_data:
        # For sell signals, find a strong resistance level above price
        resistance_levels = [level for level in sr_data['resistance_levels'] 
                            if level.get('price', 0) > current_price and level.get('strength', 0) >= 6]
        if resistance_levels:
            resistance_levels.sort(key=lambda x: x.get('price', 0))  # Lowest resistance first
            key_level = resistance_levels[0].get('price') if resistance_levels else None
    
    # Get ATR value if available or use percentage of price
    atr_value = analysis_data.get('atr', {}).get('value', current_price * 0.005)  # Default to 0.5%
    
    # Use key levels if available, otherwise use ATR
    swing_sl = 0
    if direction == 'BUY':
        swing_sl = key_level * 0.999 if key_level else current_price - (atr_value * 2.5)
    else:  # SELL
        swing_sl = key_level * 1.001 if key_level else current_price + (atr_value * 2.5)
    
    # Calculate take profit using risk-reward ratio
    risk = abs(current_price - swing_sl)
    swing_tp = current_price + (risk * 2.5) if direction == 'BUY' else current_price - (risk * 2.5)
    
    # Calculate risk-reward ratio
    reward = abs(current_price - swing_tp)
    risk_reward = round(reward / risk, 2) if risk > 0 else 0
    
    # Execution window for swing trades (longer)
    now = datetime.now()
    execution_start = now
    execution_end = now + timedelta(days=2)  # 2 days
    expiry = now + timedelta(days=14)  # 14 days
    
    # Get indicators used for the signal
    indicators = base_signal.get('indicators', {})
    if key_level:
        indicators['keyLevel'] = round(key_level, 5)
    
    # Create signal with unique ID
    signal_id = f"{symbol}-{timeframe}-swing-{direction}-{int(time.time())}".lower()
    
    return [{
        'id': signal_id,
        'symbol': symbol,
        'timeframe': timeframe,
        'strategy': 'swing',
        'signalType': direction,
        'confidence': min(100, is_strong and base_signal.get('confidence', 50) + 5 or base_signal.get('confidence', 50)),
        'entry': {
            'price': current_price,
            'type': 'MARKET',
            'time': now.isoformat()
        },
        'stopLoss': round(swing_sl, 5),
        'takeProfit': round(swing_tp, 5),
        'riskReward': risk_reward,
        'timeWindow': {
            'start': execution_start.isoformat(),
            'end': execution_end.isoformat(),
            'expires': expiry.isoformat()
        },
        'source': {
            **(indicators or {}),
            'primaryIndicator': next(iter(indicators or {}), 'price'),
            'keyLevel': key_level and round(key_level, 5) or None
        },
        'explanation': f"Swing trading opportunity based on {direction} signal with {key_level and 'key support/resistance level' or 'technical indicators'}. Target duration: 1-2 weeks."
    }]

def generate_position_signals(symbol, timeframe, analysis_data, base_signal, current_price):
    """Generate signals for position trading strategy"""
    # Only generate position signals on D1 or higher timeframes with strong signals
    if not base_signal or not base_signal.get('signal') or base_signal.get('signal') == 'NEUTRAL' or \
            timeframe not in ['D1', 'W1', 'MN1'] or \
            'STRONG' not in base_signal.get('signal', ''):
        return []
        
    # For position trading we only want strong signals
    direction = 'BUY' if 'BUY' in base_signal.get('signal', '') else 'SELL'
    
    # Find key support/resistance levels
    sr_data = analysis_data.get('support_resistance', {})
    major_level = None
    
    if direction == 'BUY' and 'support_levels' in sr_data:
        # For buy signals, find strongest support level below price
        support_levels = [level for level in sr_data['support_levels'] 
                          if level.get('price', 0) < current_price and level.get('strength', 0) >= 8]
        if support_levels:
            support_levels.sort(key=lambda x: x.get('strength', 0), reverse=True)  # Strongest support first
            major_level = support_levels[0].get('price') if support_levels else None
        
    elif direction == 'SELL' and 'resistance_levels' in sr_data:
        # For sell signals, find strongest resistance level above price
        resistance_levels = [level for level in sr_data['resistance_levels'] 
                            if level.get('price', 0) > current_price and level.get('strength', 0) >= 8]
        if resistance_levels:
            resistance_levels.sort(key=lambda x: x.get('strength', 0), reverse=True)  # Strongest resistance first
            major_level = resistance_levels[0].get('price') if resistance_levels else None
    
    # Get ATR value if available or use percentage of price
    atr_value = analysis_data.get('atr', {}).get('value', current_price * 0.01)  # Default to 1%
    
    # Calculate stop loss - use major level if available, otherwise use larger ATR multiple
    position_sl = 0
    if direction == 'BUY':
        position_sl = major_level * 0.995 if major_level else current_price - (atr_value * 5.0)
    else:  # SELL
        position_sl = major_level * 1.005 if major_level else current_price + (atr_value * 5.0)
    
    # Calculate take profit with larger risk-reward for position trading
    risk = abs(current_price - position_sl)
    position_tp = current_price + (risk * 3.0) if direction == 'BUY' else current_price - (risk * 3.0)
    
    # Calculate risk-reward ratio
    reward = abs(current_price - position_tp)
    risk_reward = round(reward / risk, 2) if risk > 0 else 0
    
    # Execution window for position trades (much longer)
    now = datetime.now()
    execution_start = now
    execution_end = now + timedelta(days=5)  # 5 days to establish position
    expiry = now + timedelta(days=30)  # 30 days horizon
    
    # Get indicators used for the signal
    indicators = base_signal.get('indicators', {})
    if major_level:
        indicators['majorLevel'] = round(major_level, 5)
    
    # Create signal with unique ID
    signal_id = f"{symbol}-{timeframe}-position-{direction}-{int(time.time())}".lower()
    
    return [{
        'id': signal_id,
        'symbol': symbol,
        'timeframe': timeframe,
        'strategy': 'position',
        'signalType': direction,
        'confidence': min(100, base_signal.get('confidence', 70) + 10),  # Higher confidence for position trades
        'entry': {
            'price': current_price,
            'type': 'MARKET',
            'time': now.isoformat()
        },
        'stopLoss': round(position_sl, 5),
        'takeProfit': round(position_tp, 5),
        'riskReward': risk_reward,
        'timeWindow': {
            'start': execution_start.isoformat(),
            'end': execution_end.isoformat(),
            'expires': expiry.isoformat()
        },
        'source': {
            **(indicators or {}),
            'primaryIndicator': 'trend',  # Position trading mainly based on trend
            'majorLevel': major_level and round(major_level, 5) or None
        },
        'explanation': f"Position trading opportunity based on {direction} signal. Strong trend established with {major_level and 'major support/resistance zones' or 'favorable technical setup'}. Target holding period: 2-4 weeks minimum."
    }]
