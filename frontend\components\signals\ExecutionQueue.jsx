import React, { useState } from 'react';

/**
 * ExecutionQueue component - Displays selected signals ready for execution
 */
function ExecutionQueue({ selectedSignals, onRemoveSignal }) {
  const [positionSize, setPositionSize] = useState(0.1); // Default position size in lots
  const [accountRisk, setAccountRisk] = useState(2); // Default risk percentage per trade
  
  // Function to calculate appropriate lot size based on account risk
  const calculatePositionSize = (signal, riskPercent, accountBalance = 10000) => {
    // This is a simplified calculation for demonstration
    // In production, you would use more sophisticated risk management
    
    const { entry, stopLoss, symbol } = signal;
    const entryPrice = entry.price;
    const slPrice = stopLoss;
    
    // Calculate pip value based on symbol (simplified)
    const pipValue = symbol.includes('JPY') ? 0.01 : 0.0001;
    
    // Calculate risk in pips
    const pips = Math.abs(entryPrice - slPrice) / pipValue;
    
    // Calculate risk amount in account currency
    const riskAmount = (accountBalance * (riskPercent / 100));
    
    // Calculate lot size based on risk (simplified)
    // This assumes $10 per pip for 1 standard lot, scaled down to micro lots
    const recommendedLots = (riskAmount / (pips * 10)) * 100;
    
    return parseFloat(recommendedLots.toFixed(2));
  };
  
  // Function to handle execution of all selected signals
  const handleExecuteAll = () => {
    // Here you would integrate with MT5 to place the orders
    // For now, we'll just simulate with a confirmation
    
    const signalCount = selectedSignals.length;
    if (signalCount === 0) return;
    
    if (window.confirm(`Execute ${signalCount} selected signals with position size ${positionSize} lots?`)) {
      console.log('Executing signals:', selectedSignals);
      
      // Execute each signal in sequence
      executeSignalsSequentially(selectedSignals, positionSize);
    }
  };
  
  // Function to execute signals one by one in sequence
  const executeSignalsSequentially = async (signals, lotSize) => {
    let successCount = 0;
    let failedCount = 0;
    
    // Create a queue copy to track progress
    const signalQueue = [...signals];
    
    for (const signal of signalQueue) {
      try {
        // Get current market price if using market execution
        const useMarketPrice = signal.entry.type === 'Market' || !signal.entry.price;
        const orderPrice = useMarketPrice ? 0 : signal.entry.price;
        
        // Prepare order data for the API
        const apiOrderData = {
          symbol: signal.symbol,
          order_type: signal.signalType === 'BUY' ? 'BUY' : 'SELL', 
          volume: lotSize,
          price: orderPrice,
          sl: signal.stopLoss,
          tp: signal.takeProfit,
          // Add required MT5 fields
          type_time: 'GTC', // Good Till Cancelled
          type_filling: 'FOK', // Fill or Kill
          deviation: 20, // Allow small price deviation
          magic: 12345, // Magic number for identifying
          comment: `GarudaAlgo ${signal.strategy || 'signal'}`
        };
        
        console.log(`Executing signal for ${signal.symbol}: ${signal.signalType}`, apiOrderData);
        
        // Send to API
        const response = await fetch('http://localhost:5001/api/trade/place_order', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(apiOrderData),
        });
        
        if (!response.ok) {
          // Check if response is JSON
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Error ${response.status}: ${response.statusText}`);
          } else {
            throw new Error(`Error ${response.status}: ${response.statusText}`);
          }
        }
        
        const data = await response.json();
        
        if (data.error) {
          throw new Error(data.error);
        }
        
        console.log(`Order executed successfully for ${signal.symbol}:`, data);
        successCount++;
      } catch (error) {
        console.error(`Error executing trade for ${signal.symbol}:`, error);
        failedCount++;
      }
      
      // Brief pause between orders to prevent flooding
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Final summary notification
    if (successCount > 0) {
      alert(`Successfully executed ${successCount} signals.${failedCount > 0 ? ` Failed: ${failedCount}` : ''}`);
    } else if (failedCount > 0) {
      alert(`Failed to execute all ${failedCount} signals. Check console for details.`);
    }
  };
  
  // Function to sort signals by priority or other criteria
  const prioritizeSignals = (signals) => {
    // Sort by confidence (highest first) and then by risk/reward ratio
    return [...signals].sort((a, b) => {
      if (b.confidence !== a.confidence) {
        return b.confidence - a.confidence; // Higher confidence first
      }
      return b.riskReward - a.riskReward; // Better risk/reward first
    });
  };
  
  const prioritizedSignals = prioritizeSignals(selectedSignals);
  
  return (
    <div className="execution-queue">
      <div className="queue-header">
        <h3>Execution Queue {selectedSignals.length > 0 && `(${selectedSignals.length})`}</h3>
      </div>
      
      {selectedSignals.length === 0 ? (
        <div className="empty-queue-message">
          <p>No signals selected for execution.</p>
          <p>Check the signals above and select ones to execute.</p>
        </div>
      ) : (
        <>
          <div className="queue-controls">
            <div className="control-group">
              <label>Position Size (lots)</label>
              <input 
                type="number" 
                min="0.01" 
                step="0.01" 
                value={positionSize}
                onChange={(e) => setPositionSize(parseFloat(e.target.value))}
              />
            </div>
            
            <div className="control-group">
              <label>Account Risk (%)</label>
              <input 
                type="number" 
                min="0.1" 
                max="10" 
                step="0.1" 
                value={accountRisk}
                onChange={(e) => setAccountRisk(parseFloat(e.target.value))}
              />
            </div>
            
            <button 
              className="execute-all-btn"
              onClick={handleExecuteAll}
              disabled={selectedSignals.length === 0}
            >
              Execute All
            </button>
          </div>
          
          <div className="queue-list">
            {prioritizedSignals.map((signal) => {
              // Calculate recommended lot size based on risk parameters
              const recommendedSize = calculatePositionSize(signal, accountRisk);
              
              return (
                <div key={signal.id} className="queue-item">
                  <div className="queue-item-header">
                    <div className="queue-item-symbol">
                      {signal.symbol} {signal.timeframe}
                    </div>
                    <div className={`queue-item-type ${signal.signalType === 'BUY' ? 'buy' : 'sell'}`}>
                      {signal.signalType}
                    </div>
                    <div className="queue-item-strategy">
                      {signal.strategy}
                    </div>
                    <div className="queue-item-remove">
                      <button 
                        className="remove-btn" 
                        onClick={() => onRemoveSignal(signal.id)}
                        title="Remove from queue"
                      >
                        ✕
                      </button>
                    </div>
                  </div>
                  
                  <div className="queue-item-details">
                    <div className="queue-item-levels">
                      <div className="level">Entry: {signal.entry && signal.entry.price ? signal.entry.price.toFixed(5) : '-'}</div>
                      <div className="level sl">SL: {signal.stopLoss ? signal.stopLoss.toFixed(5) : '-'}</div>
                      <div className="level tp">TP: {signal.takeProfit ? signal.takeProfit.toFixed(5) : '-'}</div>
                    </div>
                    
                    <div className="queue-item-sizes">
                      <div className="size-info">
                        <span className="size-label">Manual:</span>
                        <span className="size-value">{positionSize} lots</span>
                      </div>
                      <div className="size-info recommended">
                        <span className="size-label">Recommended:</span>
                        <span className="size-value">{recommendedSize} lots</span>
                      </div>
                      <div className="size-info rr">
                        <span className="size-label">R:R:</span>
                        <span className="size-value">{signal.riskReward}:1</span>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </>
      )}
    </div>
  );
}

export default ExecutionQueue;
