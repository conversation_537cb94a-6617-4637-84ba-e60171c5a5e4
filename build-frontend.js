const { build } = require('vite');
const { resolve } = require('path');

async function buildFrontend() {
  console.log('Building frontend...');
  
  try {
    await build({
      configFile: resolve(__dirname, 'frontend', 'vite.config.js'),
      mode: 'production'
    });
    
    console.log('Frontend build successful!');
  } catch (error) {
    console.error('Frontend build failed:', error);
    process.exit(1);
  }
}

buildFrontend();
