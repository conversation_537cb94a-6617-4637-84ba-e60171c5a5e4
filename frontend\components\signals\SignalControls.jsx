import React from 'react';

/**
 * SignalControls component for managing trade signal filters and settings
 */
function SignalControls({
  selectedSymbol,
  onSymbolChange,
  timeframe,
  onTimeframeChange,
  strategyFilter,
  onStrategyFilterChange,
  minimumConfidence,
  onConfidenceChange,
  loading,
  symbols
}) {
  // Define timeframes with their display names
  const timeframeOptions = [
    { value: 'M1', label: '1 Minute' },
    { value: 'M5', label: '5 Minutes' },
    { value: 'M15', label: '15 Minutes' },
    { value: 'M30', label: '30 Minutes' },
    { value: 'H1', label: '1 Hour' },
    { value: 'H4', label: '4 Hours' },
    { value: 'D1', label: 'Daily' },
    { value: 'W1', label: 'Weekly' },
    { value: 'MN1', label: 'Monthly' }
  ];
  
  return (
    <div className="signal-controls">
      <div className="control-row">
        {/* Symbol selector */}
        <div className="control-group">
          <label>Symbol</label>
          <select 
            value={selectedSymbol || ''} 
            onChange={onSymbolChange}
            disabled={loading}
          >
            <option value="">Select Symbol</option>
            {symbols.map(symbol => (
              <option key={symbol} value={symbol}>{symbol}</option>
            ))}
          </select>
        </div>
        
        {/* Timeframe selector */}
        <div className="control-group">
          <label>Timeframe</label>
          <select 
            value={timeframe} 
            onChange={onTimeframeChange}
            disabled={loading}
          >
            {timeframeOptions.map(tf => (
              <option key={tf.value} value={tf.value}>{tf.label}</option>
            ))}
          </select>
        </div>
        
        {/* Strategy filter */}
        <div className="control-group strategy-filter">
          <label>Strategy</label>
          <div className="button-group">
            <button 
              className={strategyFilter === 'all' ? 'active' : ''}
              onClick={() => onStrategyFilterChange('all')}
              disabled={loading}
            >
              All
            </button>
            <button 
              className={strategyFilter === 'scalping' ? 'active' : ''}
              onClick={() => onStrategyFilterChange('scalping')}
              disabled={loading}
            >
              Scalping
            </button>
            <button 
              className={strategyFilter === 'intraday' ? 'active' : ''}
              onClick={() => onStrategyFilterChange('intraday')}
              disabled={loading}
            >
              Intraday
            </button>
            <button 
              className={strategyFilter === 'swing' ? 'active' : ''}
              onClick={() => onStrategyFilterChange('swing')}
              disabled={loading}
            >
              Swing
            </button>
            <button 
              className={strategyFilter === 'position' ? 'active' : ''}
              onClick={() => onStrategyFilterChange('position')}
              disabled={loading}
            >
              Position
            </button>
          </div>
        </div>
      </div>
      
      <div className="control-row">
        {/* Confidence slider */}
        <div className="control-group confidence-filter">
          <label>Minimum Confidence: {minimumConfidence}%</label>
          <input 
            type="range" 
            min="0" 
            max="100" 
            value={minimumConfidence} 
            onChange={(e) => onConfidenceChange(parseInt(e.target.value))}
            disabled={loading}
          />
        </div>
      </div>
    </div>
  );
}

export default SignalControls;
