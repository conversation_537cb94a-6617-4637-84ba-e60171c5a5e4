/* Price Level Indicator Styles */
.price-level-container {
  position: relative;
  width: 100%;
  margin: 16px 0;
  padding: 0 10px;
}

/* Enhanced Price Level Visualization */
.price-level-chart {
  position: relative;
  width: 100%;
  height: 120px;
  margin-bottom: 30px;
  display: flex;
  flex-direction: column;
}

/* Current Price Line */
.current-price-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background-color: white;
  z-index: 5;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
}

.current-price-marker {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: white;
  transform: translate(-50%, -50%);
  z-index: 6;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
}

.current-price-label {
  position: absolute;
  left: 0;
  padding: 4px 8px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
  transform: translateY(-50%);
  z-index: 7;
}

/* Level Zones */
.price-zones {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.price-zone {
  position: relative;
  width: 100%;
  flex: 1;
  display: flex;
  align-items: center;
}

.price-zone-bar {
  position: absolute;
  left: 0;
  right: 0;
  height: 100%;
  opacity: 0.15;
  z-index: 1;
}

.price-zone.resistance .price-zone-bar {
  background-color: #ff4d4d;
}

.price-zone.support .price-zone-bar {
  background-color: #00cc00;
}

.price-zone.neutral .price-zone-bar {
  background-color: #e6e600;
}

/* Level Markers */
.level-markers {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 2;
}

.level-marker {
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  display: flex;
  align-items: center;
}

.level-marker-line {
  flex: 1;
  height: 1px;
}

.level-marker.resistance .level-marker-line {
  background-color: rgba(255, 77, 77, 0.7);
}

.level-marker.support .level-marker-line {
  background-color: rgba(0, 204, 0, 0.7);
}

.level-marker-label {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.8rem;
  white-space: nowrap;
  margin-left: 8px;
}

.level-marker.resistance .level-marker-label {
  background-color: rgba(255, 77, 77, 0.2);
  color: rgba(255, 77, 77, 0.9);
}

.level-marker.support .level-marker-label {
  background-color: rgba(0, 204, 0, 0.2);
  color: rgba(0, 204, 0, 0.9);
}

/* Legend */
.price-level-legend {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  padding: 0 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 6px;
}

.legend-color.resistance {
  background-color: rgba(255, 77, 77, 0.7);
}

.legend-color.support {
  background-color: rgba(0, 204, 0, 0.7);
}

.legend-color.current {
  background-color: white;
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

/* Title */
.price-level-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-level-subtitle {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: normal;
}
