import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
import MetaTrader5 as mt5
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("recommendation_engine.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("RecommendationEngine")

class RecommendationEngine:
    """
    A class to generate trade recommendations based on multi-timeframe analysis results.
    """
    
    def __init__(self, mt5_module, analysis_engine):
        """
        Initialize the recommendation engine.
        
        Args:
            mt5_module: An instance of the MT5Integration class
            analysis_engine: An instance of the AnalysisEngine class
        """
        self.mt5 = mt5_module
        self.analysis = analysis_engine
    
    def generate_recommendations(self, symbol: str, timeframes: List[str], 
                               account_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate trade recommendations based on multi-timeframe analysis.
        
        Args:
            symbol: Symbol name
            timeframes: List of timeframe strings (e.g., ["M5", "H1", "D1"])
            account_info: Account details from MT5
            
        Returns:
            Dict with trade recommendations
        """
        try:
            # Check connection
            if not self.mt5.check_connection()["connected"]:
                return {
                    "success": False,
                    "message": "Not connected to MT5"
                }
            
            # Get symbol info
            symbol_info_result = self.mt5.get_symbol_info(symbol)
            if not symbol_info_result["success"]:
                return {
                    "success": False,
                    "message": symbol_info_result["message"]
                }
            
            symbol_info = symbol_info_result["symbol_info"]
            
            # Perform multi-timeframe analysis
            analysis_result = self.analysis.analyze_symbol(symbol, timeframes)
            if not analysis_result["success"]:
                return {
                    "success": False,
                    "message": analysis_result["message"]
                }
            
            # Generate recommendations based on analysis results
            recommendations = self._generate_trade_setups(
                symbol, 
                analysis_result["results"], 
                account_info, 
                symbol_info
            )
            
            return {
                "success": True,
                "symbol": symbol,
                "recommendations": recommendations
            }
        except Exception as e:
            logger.exception(f"Exception generating recommendations for {symbol}: {str(e)}")
            return {
                "success": False,
                "message": f"Exception generating recommendations for {symbol}: {str(e)}"
            }
    
    def _generate_trade_setups(self, symbol: str, analysis_results: Dict[str, Any], 
                             account_info: Dict[str, Any], symbol_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate trade setups based on analysis results.
        
        Args:
            symbol: Symbol name
            analysis_results: Results from multi-timeframe analysis
            account_info: Account details from MT5
            symbol_info: Symbol information from MT5
            
        Returns:
            List of trade recommendations
        """
        recommendations = []
        
        try:
            # Get current price
            symbol_tick = mt5.symbol_info_tick(symbol)
            if symbol_tick is None:
                logger.error(f"Failed to get symbol tick for {symbol}")
                return []
            
            current_bid = symbol_tick.bid
            current_ask = symbol_tick.ask
            
            # Define trading styles and their corresponding timeframes
            trading_styles = {
                "Scalping": ["M1", "M5", "M15"],
                "Short-term": ["M15", "M30", "H1"],
                "Swing": ["H1", "H4", "D1"],
                "Long-term": ["D1", "W1", "MN1"]
            }
            
            # Check for potential setups for each trading style
            for style, style_timeframes in trading_styles.items():
                # Filter timeframes that are available in analysis results
                available_timeframes = [tf for tf in style_timeframes if tf in analysis_results]
                
                if not available_timeframes:
                    continue
                
                # Check for buy setups
                buy_setup = self._check_buy_setup(symbol, available_timeframes, analysis_results)
                if buy_setup["valid"]:
                    # Calculate stop loss and take profit
                    sl_price, tp_price, sl_points = self._calculate_sl_tp(
                        symbol, "Buy", current_ask, buy_setup["confidence"], 
                        analysis_results, available_timeframes, symbol_info
                    )
                    
                    # Calculate lot size based on risk
                    risk_percent = self._determine_risk_percent(buy_setup["confidence"])
                    lot_size_result = self.mt5.calculate_lot_size(symbol, risk_percent, sl_points)
                    
                    if lot_size_result["success"]:
                        lot_size = lot_size_result["lot_size"]
                    else:
                        lot_size = 0.01  # Default minimum lot size
                    
                    # Create recommendation
                    recommendation = {
                        "symbol": symbol,
                        "direction": "Buy",
                        "style": style,
                        "confidence": buy_setup["confidence"],
                        "signals": buy_setup["signals"],
                        "risk_pct": risk_percent,
                        "entry_price": current_ask,
                        "sl_price": sl_price,
                        "tp_price": tp_price,
                        "sl_points": sl_points,
                        "calculated_lot": lot_size,
                        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
                    recommendations.append(recommendation)
                
                # Check for sell setups
                sell_setup = self._check_sell_setup(symbol, available_timeframes, analysis_results)
                if sell_setup["valid"]:
                    # Calculate stop loss and take profit
                    sl_price, tp_price, sl_points = self._calculate_sl_tp(
                        symbol, "Sell", current_bid, sell_setup["confidence"], 
                        analysis_results, available_timeframes, symbol_info
                    )
                    
                    # Calculate lot size based on risk
                    risk_percent = self._determine_risk_percent(sell_setup["confidence"])
                    lot_size_result = self.mt5.calculate_lot_size(symbol, risk_percent, sl_points)
                    
                    if lot_size_result["success"]:
                        lot_size = lot_size_result["lot_size"]
                    else:
                        lot_size = 0.01  # Default minimum lot size
                    
                    # Create recommendation
                    recommendation = {
                        "symbol": symbol,
                        "direction": "Sell",
                        "style": style,
                        "confidence": sell_setup["confidence"],
                        "signals": sell_setup["signals"],
                        "risk_pct": risk_percent,
                        "entry_price": current_bid,
                        "sl_price": sl_price,
                        "tp_price": tp_price,
                        "sl_points": sl_points,
                        "calculated_lot": lot_size,
                        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
                    recommendations.append(recommendation)
            
            # Sort recommendations by confidence (descending)
            recommendations.sort(key=lambda x: x["confidence"], reverse=True)
            
            return recommendations
        except Exception as e:
            logger.exception(f"Exception generating trade setups for {symbol}: {str(e)}")
            return []
    
    def _check_buy_setup(self, symbol: str, timeframes: List[str], 
                       analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Check for buy setup based on analysis results.
        
        Args:
            symbol: Symbol name
            timeframes: List of timeframe strings
            analysis_results: Results from multi-timeframe analysis
            
        Returns:
            Dict with buy setup validity and confidence
        """
        signals = []
        
        try:
            # Check trend alignment across timeframes
            bullish_trends = 0
            for tf in timeframes:
                if tf in analysis_results:
                    tf_analysis = analysis_results[tf]
                    
                    # Check overall trend
                    if "trend" in tf_analysis and "overall" in tf_analysis["trend"]:
                        if tf_analysis["trend"]["overall"] == "Bullish":
                            bullish_trends += 1
                            signals.append(f"{tf} Trend: Bullish")
            
            # Check for specific buy signals
            for tf in timeframes:
                if tf in analysis_results:
                    tf_analysis = analysis_results[tf]
                    
                    # Check moving averages
                    if "moving_averages" in tf_analysis:
                        ma = tf_analysis["moving_averages"]
                        
                        # EMA crossover
                        if "ema_20_50_cross" in ma and ma["ema_20_50_cross"] == "Bullish":
                            signals.append(f"{tf} EMA Cross: Bullish")
                        
                        # Price above EMAs
                        if "price_vs_ema20" in ma and ma["price_vs_ema20"] == "Above":
                            signals.append(f"{tf} Price > EMA20")
                        
                        if "price_vs_ema50" in ma and ma["price_vs_ema50"] == "Above":
                            signals.append(f"{tf} Price > EMA50")
                    
                    # Check RSI
                    if "rsi" in tf_analysis:
                        rsi = tf_analysis["rsi"]
                        
                        # Oversold condition
                        if "level" in rsi and rsi["level"] == "Oversold":
                            signals.append(f"{tf} RSI: Oversold")
                        
                        # Bullish divergence
                        if "divergence" in rsi and rsi["divergence"] == "Bullish":
                            signals.append(f"{tf} RSI Divergence: Bullish")
                    
                    # Check MACD
                    if "macd" in tf_analysis:
                        macd = tf_analysis["macd"]
                        
                        # Bullish crossover
                        if "cross" in macd and macd["cross"] == "Bullish":
                            signals.append(f"{tf} MACD Cross: Bullish")
                        
                        # Positive histogram
                        if "histogram_status" in macd and macd["histogram_status"] == "Positive":
                            signals.append(f"{tf} MACD Histogram: Positive")
                    
                    # Check Bollinger Bands
                    if "bollinger_bands" in tf_analysis:
                        bb = tf_analysis["bollinger_bands"]
                        
                        # Price below lower band (potential bounce)
                        if "price_position" in bb and bb["price_position"] == "Below Lower":
                            signals.append(f"{tf} BB: Price below lower band")
            
            # Calculate confidence based on signals
            confidence = min(100, len(signals) * 10)
            
            # Determine if setup is valid
            # Require at least 3 signals and trend alignment in majority of timeframes
            valid = len(signals) >= 3 and bullish_trends >= len(timeframes) / 2
            
            return {
                "valid": valid,
                "confidence": confidence,
                "signals": signals
            }
        except Exception as e:
            logger.exception(f"Exception checking buy setup for {symbol}: {str(e)}")
            return {
                "valid": False,
                "confidence": 0,
                "signals": []
            }
    
    def _check_sell_setup(self, symbol: str, timeframes: List[str], 
                        analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Check for sell setup based on analysis results.
        
        Args:
            symbol: Symbol name
            timeframes: List of timeframe strings
            analysis_results: Results from multi-timeframe analysis
            
        Returns:
            Dict with sell setup validity and confidence
        """
        signals = []
        
        try:
            # Check trend alignment across timeframes
            bearish_trends = 0
            for tf in timeframes:
                if tf in analysis_results:
                    tf_analysis = analysis_results[tf]
                    
                    # Check overall trend
                    if "trend" in tf_analysis and "overall" in tf_analysis["trend"]:
                        if tf_analysis["trend"]["overall"] == "Bearish":
                            bearish_trends += 1
                            signals.append(f"{tf} Trend: Bearish")
            
            # Check for specific sell signals
            for tf in timeframes:
                if tf in analysis_results:
                    tf_analysis = analysis_results[tf]
                    
                    # Check moving averages
                    if "moving_averages" in tf_analysis:
                        ma = tf_analysis["moving_averages"]
                        
                        # EMA crossover
                        if "ema_20_50_cross" in ma and ma["ema_20_50_cross"] == "Bearish":
                            signals.append(f"{tf} EMA Cross: Bearish")
                        
                        # Price below EMAs
                        if "price_vs_ema20" in ma and ma["price_vs_ema20"] == "Below":
                            signals.append(f"{tf} Price < EMA20")
                        
                        if "price_vs_ema50" in ma and ma["price_vs_ema50"] == "Below":
                            signals.append(f"{tf} Price < EMA50")
                    
                    # Check RSI
                    if "rsi" in tf_analysis:
                        rsi = tf_analysis["rsi"]
                        
                        # Overbought condition
                        if "level" in rsi and rsi["level"] == "Overbought":
                            signals.append(f"{tf} RSI: Overbought")
                        
                        # Bearish divergence
                        if "divergence" in rsi and rsi["divergence"] == "Bearish":
                            signals.append(f"{tf} RSI Divergence: Bearish")
                    
                    # Check MACD
                    if "macd" in tf_analysis:
                        macd = tf_analysis["macd"]
                        
                        # Bearish crossover
                        if "cross" in macd and macd["cross"] == "Bearish":
                            signals.append(f"{tf} MACD Cross: Bearish")
                        
                        # Negative histogram
                        if "histogram_status" in macd and macd["histogram_status"] == "Negative":
                            signals.append(f"{tf} MACD Histogram: Negative")
                    
                    # Check Bollinger Bands
                    if "bollinger_bands" in tf_analysis:
                        bb = tf_analysis["bollinger_bands"]
                        
                        # Price above upper band (potential reversal)
                        if "price_position" in bb and bb["price_position"] == "Above Upper":
                            signals.append(f"{tf} BB: Price above upper band")
            
            # Calculate confidence based on signals
            confidence = min(100, len(signals) * 10)
            
            # Determine if setup is valid
            # Require at least 3 signals and trend alignment in majority of timeframes
            valid = len(signals) >= 3 and bearish_trends >= len(timeframes) / 2
            
            return {
                "valid": valid,
                "confidence": confidence,
                "signals": signals
            }
        except Exception as e:
            logger.exception(f"Exception checking sell setup for {symbol}: {str(e)}")
            return {
                "valid": False,
                "confidence": 0,
                "signals": []
            }
    
    def _calculate_sl_tp(self, symbol: str, direction: str, entry_price: float, 
                       confidence: int, analysis_results: Dict[str, Any], 
                       timeframes: List[str], symbol_info: Dict[str, Any]) -> Tuple[float, float, int]:
        """
        Calculate stop loss and take profit levels.
        
        Args:
            symbol: Symbol name
            direction: Trade direction ("Buy" or "Sell")
            entry_price: Entry price
            confidence: Trade confidence
            analysis_results: Results from multi-timeframe analysis
            timeframes: List of timeframe strings
            symbol_info: Symbol information from MT5
            
        Returns:
            Tuple of (stop_loss_price, take_profit_price, sl_points)
        """
        try:
            # Get symbol point value
            point = symbol_info["point"]
            digits = symbol_info["digits"]
            
            # Get ATR for dynamic SL/TP calculation
            atr_value = None
            primary_tf = timeframes[1] if len(timeframes) > 1 else timeframes[0]  # Use middle timeframe if available
            
            if primary_tf in analysis_results and "atr" in analysis_results[primary_tf]:
                atr = analysis_results[primary_tf]["atr"]
                if "value" in atr:
                    atr_value = atr["value"]
            
            # If ATR is not available, use a fixed percentage
            if atr_value is None:
                # Use 0.5% of price as default
                atr_value = entry_price * 0.005
            
            # Calculate SL distance based on ATR and confidence
            # Higher confidence = tighter stop loss
            sl_multiplier = 2.0
            if confidence > 80:
                sl_multiplier = 1.5
            elif confidence < 50:
                sl_multiplier = 3.0
            
            sl_distance = atr_value * sl_multiplier
            
            # Calculate TP distance based on risk:reward ratio
            # Higher confidence = higher risk:reward
            rr_ratio = 1.5  # Default risk:reward ratio
            if confidence > 80:
                rr_ratio = 2.0
            elif confidence > 60:
                rr_ratio = 1.8
            elif confidence < 50:
                rr_ratio = 1.2
            
            tp_distance = sl_distance * rr_ratio
            
            # Calculate SL and TP prices based on direction
            if direction == "Buy":
                sl_price = entry_price - sl_distance
                tp_price = entry_price + tp_distance
            else:  # Sell
                sl_price = entry_price + sl_distance
                tp_price = entry_price - tp_distance
            
            # Round prices to symbol digits
            sl_price = round(sl_price, digits)
            tp_price = round(tp_price, digits)
            
            # Calculate SL in points
            sl_points = int(abs(entry_price - sl_price) / point)
            
            return sl_price, tp_price, sl_points
        except Exception as e:
            logger.exception(f"Exception calculating SL/TP for {symbol}: {str(e)}")
            
            # Fallback to basic calculation
            if direction == "Buy":
                sl_price = entry_price * 0.99  # 1% below entry
                tp_price = entry_price * 1.02  # 2% above entry
            else:  # Sell
                sl_price = entry_price * 1.01  # 1% above entry
                tp_price = entry_price * 0.98  # 2% below entry
            
            # Round prices to symbol digits
            sl_price = round(sl_price, symbol_info["digits"])
            tp_price = round(tp_price, symbol_info["digits"])
            
            # Calculate SL in points
            sl_points = int(abs(entry_price - sl_price) / symbol_info["point"])
            
            return sl_price, tp_price, sl_points
    
    def _determine_risk_percent(self, confidence: int) -> float:
        """
        Determine risk percentage based on confidence.
        
        Args:
            confidence: Trade confidence
            
        Returns:
            Risk percentage
        """
        # Base risk is 1%
        base_risk = 1.0
        
        # Adjust risk based on confidence
        if confidence > 80:
            return base_risk * 1.2  # 1.2%
        elif confidence > 60:
            return base_risk  # 1%
        elif confidence > 40:
            return base_risk * 0.8  # 0.8%
        else:
            return base_risk * 0.5  # 0.5%
