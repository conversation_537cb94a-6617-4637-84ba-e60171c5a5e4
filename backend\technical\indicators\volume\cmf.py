from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class ChaikinMoneyFlowIndicator(BaseIndicator):
    """Chaikin Money Flow (CMF) indicator."""

    def __init__(self, period: int = 20):
        """
        Initialize Chaikin Money Flow indicator.

        Args:
            period: The lookback period for calculating CMF.
        """
        super().__init__({'period': period})

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Chaikin Money Flow values."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period']:
             return {'cmf': np.array([])}

        period = self.params['period']

        high = df['high']
        low = df['low']
        close = df['close']
        volume = df['volume']

        # Calculate Money Flow Multiplier
        # Avoid division by zero if high == low
        mf_multiplier = ((close - low) - (high - close)) / (high - low).replace(0, np.nan)
        mf_multiplier = mf_multiplier.fillna(0) # Fill NaNs where high == low

        # Calculate Money Flow Volume
        mf_volume = mf_multiplier * volume

        # Calculate Chaikin Money Flow
        cmf = mf_volume.rolling(window=period).sum() / volume.rolling(window=period).sum()

        self._values = {
            'cmf': cmf.values,
            'mf_volume': mf_volume.values, # Optional intermediate value
            'mf_multiplier': mf_multiplier.values # Optional intermediate value
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        return True