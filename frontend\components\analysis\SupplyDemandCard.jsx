import React, { useState } from 'react';
import { safeToFixed } from '../../utils/numberUtils';

/**
 * SupplyDemandCard component for visualizing supply and demand zones from MT5 analysis
 * 
 * @param {Object} analysisData - The full analysis data object
 * @returns {JSX.Element} - The rendered supply and demand card
 */
const SupplyDemandCard = ({ analysisData }) => {
  const [activeTab, setActiveTab] = useState('all'); // 'all', 'supply', 'demand'
  const [expanded, setExpanded] = useState(null); // Track expanded zone for details

  // Extract supply and demand zones from analysis data
  const supplyZones = analysisData?.support_resistance?.supply_zones || [];
  const demandZones = analysisData?.support_resistance?.demand_zones || [];
  
  // Get current price for comparison
  const currentPrice = 
    analysisData?.current_price?.bid || 
    analysisData?.support_resistance?.current_price?.bid ||
    analysisData?.current_price?.last || 
    analysisData?.support_resistance?.current_price?.last || 0;

  // Helper to calculate if price is inside a zone
  const isPriceInZone = (zone) => {
    if (!zone || !currentPrice) return false;
    return currentPrice >= zone.bottom && currentPrice <= zone.top;
  };

  // Helper to format time
  const formatTime = (timeStr) => {
    if (!timeStr) return "";
    try {
      const date = new Date(timeStr);
      return date.toLocaleString('en-US', { 
        month: 'short', 
        day: 'numeric', 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } catch (e) {
      return timeStr;
    }
  };

  // Helper to get zone strength color
  const getStrengthColor = (strength) => {
    if (strength === undefined || strength === null) return 'var(--text-secondary)';
    if (strength > 1.5) return 'var(--success)'; // Strong - green
    if (strength > 1.0) return 'var(--warning)'; // Medium - yellow
    return 'var(--error)'; // Weak - red
  };

  // Helper to format strength as a percentage
  const formatStrength = (strength) => {
    if (strength === undefined || strength === null || typeof strength !== 'number' || isNaN(strength)) return 'N/A';
    try {
      // Use window.safeToFixed which is globally defined for extra safety
      return `${window.safeToFixed ? window.safeToFixed(strength * 50, 0, 'N/A') : safeToFixed(strength * 50, 0, 'N/A')}%`;
    } catch (e) {
      console.error('Error formatting strength:', e);
      return 'N/A';
    }
  };

  // Format numeric strength to descriptive text
  const getStrengthText = (strength) => {
    if (!strength) return 'Unknown';
    if (strength > 1.5) return 'Strong';
    if (strength > 1.0) return 'Medium';
    return 'Weak';
  };

  // Render a single zone card
  const renderZoneCard = (zone, type, index) => {
    const isSupply = type === 'supply';
    const baseColor = isSupply ? 'var(--error)' : 'var(--success)';
    const bgColor = isSupply ? 'rgba(255,65,54,0.08)' : 'rgba(46,204,64,0.08)';
    const isActive = expanded === `${type}-${index}`;
    const inZone = isPriceInZone(zone);

    return (
      <div key={`${type}-${index}`} 
           style={{ 
             marginBottom: '10px', 
             backgroundColor: bgColor,
             borderRadius: '6px',
             border: `1px solid ${inZone ? baseColor : 'var(--border)'}`,
             overflow: 'hidden'
           }}>
        {/* Zone header */}
        <div style={{ 
          padding: '8px 12px',
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          cursor: 'pointer',
          borderLeft: `4px solid ${getStrengthColor(zone.strength_score)}`
        }} onClick={() => setExpanded(isActive ? null : `${type}-${index}`)}>
          <div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
              <span style={{ 
                color: baseColor, 
                fontWeight: 'bold', 
                fontSize: '0.9rem' 
              }}>
                {isSupply ? 'Supply' : 'Demand'} Zone
              </span>
              {inZone && (
                <span style={{ 
                  fontSize: '0.7rem', 
                  backgroundColor: baseColor, 
                  color: 'var(--card)', 
                  padding: '1px 6px', 
                  borderRadius: '10px' 
                }}>
                  ACTIVE
                </span>
              )}
            </div>
            <div style={{ fontSize: '0.75rem', color: 'var(--text-secondary)', marginTop: '3px' }}>
              {formatTime(zone.time)}
            </div>
          </div>

          <div style={{ textAlign: 'right' }}>
            <div style={{ 
              fontWeight: 'bold', 
              fontSize: '0.9rem',
              color: 'var(--text)'
            }}>
              {safeToFixed(zone.proximal, 5)} - {safeToFixed(zone.distal, 5)}
            </div>
            <div style={{ 
              fontSize: '0.75rem', 
              color: getStrengthColor(zone.strength_score),
              marginTop: '3px'
            }}>
              {getStrengthText(zone.strength_score)} ({formatStrength(zone.strength_score)})
            </div>
          </div>
        </div>

        {/* Expanded details */}
        {isActive && (
          <div style={{ 
            padding: '10px 12px', 
            borderTop: '1px solid var(--border)',
            backgroundColor: 'var(--background-secondary)'
          }}>
            <table style={{ width: '100%', fontSize: '0.8rem', borderCollapse: 'collapse' }}>
              <tbody>
                <tr>
                  <td style={{ color: 'var(--text-secondary)', paddingRight: '8px', paddingBottom: '4px' }}>Proximal Level:</td>
                  <td style={{ fontWeight: 'bold', paddingBottom: '4px', color: 'var(--text)' }}>{safeToFixed(zone.proximal, 5)}</td>
                  <td style={{ color: 'var(--text-secondary)', paddingRight: '8px', paddingBottom: '4px' }}>Distal Level:</td>
                  <td style={{ fontWeight: 'bold', paddingBottom: '4px', color: 'var(--text)' }}>{safeToFixed(zone.distal, 5)}</td>
                </tr>
                <tr>
                  <td style={{ color: 'var(--text-secondary)', paddingRight: '8px', paddingBottom: '4px' }}>Base Quality:</td>
                  <td style={{ fontWeight: 'bold', paddingBottom: '4px', color: 'var(--text)' }}>
                    {safeToFixed(zone.strength?.base_quality ? zone.strength.base_quality * 100 : null, 1, 'N/A') + '%'}
                  </td>
                  <td style={{ color: 'var(--text-secondary)', paddingRight: '8px', paddingBottom: '4px' }}>Leg Strength:</td>
                  <td style={{ fontWeight: 'bold', paddingBottom: '4px', color: 'var(--text)' }}>
                    {safeToFixed(zone.strength?.leg_strength ? zone.strength.leg_strength * 100 : null, 1, 'N/A') + '%'}
                  </td>
                </tr>
                <tr>
                  <td style={{ color: 'var(--text-secondary)', paddingRight: '8px' }}>Freshness:</td>
                  <td style={{ fontWeight: 'bold', color: 'var(--text)' }}>
                    {safeToFixed(zone.strength?.freshness ? zone.strength.freshness * 100 : null, 1, 'N/A') + '%'}
                  </td>
                  <td style={{ color: 'var(--text-secondary)', paddingRight: '8px' }}>Overall:</td>
                  <td style={{ fontWeight: 'bold', color: getStrengthColor(zone.strength_score) }}>
                    {safeToFixed(zone.strength?.overall ? zone.strength.overall * 100 : null, 1, 'N/A') + '%'}
                  </td>
                </tr>
              </tbody>
            </table>
            
            {/* Zone thickness visualization */}
            <div style={{ marginTop: '10px' }}>
              <div style={{ fontSize: '0.8rem', color: 'var(--text-secondary)', marginBottom: '4px' }}>Zone Thickness</div>
              <div style={{ 
                height: '20px', 
                position: 'relative',
                backgroundColor: 'var(--background-tertiary, rgba(0,0,0,0.3))',
                borderRadius: '4px',
                overflow: 'hidden'
              }}>
                <div style={{ 
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  height: '100%',
                  width: `${Math.min(100, (Math.abs(zone.distal - zone.proximal) / 0.01) * 100)}%`,
                  background: `linear-gradient(to right, ${baseColor}80, ${baseColor}40)`,
                  borderRadius: '4px'
                }}></div>
                <div style={{ 
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                  fontSize: '0.75rem',
                  color: 'var(--text)'
                }}>
                  {safeToFixed(zone.distal != null && zone.proximal != null && zone.proximal !== 0 ? ((zone.distal - zone.proximal) / zone.proximal * 100) : null, 2, 'N/A') + '% range'}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Render the NoZones component
  const NoZones = ({ type }) => (
    <div style={{ 
      padding: '20px', 
      textAlign: 'center', 
      color: 'var(--text-secondary)',
      fontSize: '0.9rem',
      backgroundColor: 'var(--background-secondary)',
      borderRadius: '6px',
      margin: '10px 0'
    }}>
      No {type} zones detected.
    </div>
  );

  return (
    <div className="bento-card bento-span-8 bento-height-2 supply-demand-card" 
         style={{ background: 'var(--card)', border: '1px solid var(--border)', borderRadius: '8px', overflow: 'hidden' }}>
      <div className="bento-card-header" style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        padding: '10px 15px',
        borderBottom: '1px solid var(--border)'
      }}>
        <h3 className="bento-card-title" style={{ margin: 0, fontSize: '1.1rem', color: 'var(--text)' }}>
          Supply & Demand Zones
        </h3>
        
        {/* Zone count badges */}
        <div style={{ display: 'flex', gap: '8px' }}>
          <div style={{ 
            fontSize: '0.7rem', 
            backgroundColor: 'rgba(255,65,54,0.2)', 
            padding: '2px 8px', 
            borderRadius: '10px',
            color: 'var(--error)'
          }}>
            {supplyZones.length} Supply
          </div>
          <div style={{ 
            fontSize: '0.7rem', 
            backgroundColor: 'rgba(46,204,64,0.2)', 
            padding: '2px 8px', 
            borderRadius: '10px',
            color: 'var(--success)' 
          }}>
            {demandZones.length} Demand
          </div>
        </div>
      </div>

      {/* Tab navigation */}
      <div style={{ 
        display: 'flex', 
        borderBottom: '1px solid var(--border)', 
        backgroundColor: 'var(--background-secondary)'
      }}>
        <button 
          onClick={() => setActiveTab('all')}
          style={{ 
            flex: 1, 
            padding: '8px 0', 
            background: 'none', 
            border: 'none', 
            borderBottom: activeTab === 'all' ? '2px solid var(--warning)' : '2px solid transparent',
            color: activeTab === 'all' ? 'var(--text)' : 'var(--text-secondary)',
            cursor: 'pointer'
          }}
        >
          All Zones
        </button>
        <button 
          onClick={() => setActiveTab('supply')}
          style={{ 
            flex: 1, 
            padding: '8px 0', 
            background: 'none', 
            border: 'none', 
            borderBottom: activeTab === 'supply' ? '2px solid var(--error)' : '2px solid transparent',
            color: activeTab === 'supply' ? 'var(--text)' : 'var(--text-secondary)',
            cursor: 'pointer'
          }}
        >
          Supply ({supplyZones.length})
        </button>
        <button 
          onClick={() => setActiveTab('demand')}
          style={{ 
            flex: 1, 
            padding: '8px 0', 
            background: 'none', 
            border: 'none', 
            borderBottom: activeTab === 'demand' ? '2px solid var(--success)' : '2px solid transparent',
            color: activeTab === 'demand' ? 'var(--text)' : 'var(--text-secondary)',
            cursor: 'pointer'
          }}
        >
          Demand ({demandZones.length})
        </button>
      </div>

      {/* Zones content */}
      <div style={{ 
        padding: '10px', 
        maxHeight: '350px', 
        overflowY: 'auto'
      }}>
        {/* All Zones Tab Content */}
        {activeTab === 'all' && (
          <div className="all-zones-container">
            {/* Supply Zones Section */}
            <div style={{ marginBottom: '20px' }}>
              <div style={{ 
                marginBottom: '8px', 
                paddingBottom: '5px', 
                borderBottom: '1px solid var(--border)'
              }}>
                <strong style={{ 
                  display: 'block', 
                  color: 'var(--error)', 
                  fontSize: '0.95rem', 
                  marginBottom: '4px'
                }}>
                  Supply Zones
                </strong>
                <div style={{ fontSize: '0.8rem', color: 'var(--text-secondary)' }}>
                  Areas where sellers are likely to enter the market
                </div>
              </div>
              
              {supplyZones.length > 0 ? (
                <div>
                  {supplyZones.map((zone, idx) => (
                    <div key={`supply-all-${idx}`} style={{ marginBottom: '10px' }}>
                      {renderZoneCard(zone, 'supply', idx)}
                    </div>
                  ))}
                </div>
              ) : (
                <NoZones type="supply" />
              )}
            </div>
            
            {/* Demand Zones Section */}
            <div>
              <div style={{ 
                marginBottom: '8px', 
                paddingBottom: '5px', 
                borderBottom: '1px solid var(--border)'
              }}>
                <strong style={{ 
                  display: 'block', 
                  color: 'var(--success)', 
                  fontSize: '0.95rem', 
                  marginBottom: '4px'
                }}>
                  Demand Zones
                </strong>
                <div style={{ fontSize: '0.8rem', color: 'var(--text-secondary)' }}>
                  Areas where buyers are likely to enter the market
                </div>
              </div>
              
              {demandZones.length > 0 ? (
                <div>
                  {demandZones.map((zone, idx) => (
                    <div key={`demand-all-${idx}`} style={{ marginBottom: '10px' }}>
                      {renderZoneCard(zone, 'demand', idx)}
                    </div>
                  ))}
                </div>
              ) : (
                <NoZones type="demand" />
              )}
            </div>
          </div>
        )}
        
        {/* Supply Tab Content */}
        {activeTab === 'supply' && (
          <div style={{ width: '100%' }}>
            {supplyZones.length > 0 ? (
              <div style={{ 
                display: 'flex', 
                flexDirection: 'row',
                flexWrap: 'wrap',
                gap: '10px'
              }}>
                {supplyZones.map((zone, idx) => (
                  <div key={`supply-container-${idx}`} style={{ flex: '1 1 calc(50% - 5px)' }}>
                    {renderZoneCard(zone, 'supply', idx)}
                  </div>
                ))}
              </div>
            ) : (
              <NoZones type="supply" />
            )}
          </div>
        )}
        
        {/* Demand Tab Content */}
        {activeTab === 'demand' && (
          <div style={{ width: '100%' }}>
            {demandZones.length > 0 ? (
              <div style={{ 
                display: 'flex', 
                flexDirection: 'row',
                flexWrap: 'wrap',
                gap: '10px'
              }}>
                {demandZones.map((zone, idx) => (
                  <div key={`demand-container-${idx}`} style={{ flex: activeTab === 'all' ? '1' : '1 1 calc(50% - 5px)' }}>
                    {renderZoneCard(zone, 'demand', idx)}
                  </div>
                ))}
              </div>
            ) : (
              <NoZones type="demand" />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SupplyDemandCard;
