import React from 'react';

/**
 * TrendCard - Summarizes the overall trend and its contributing signals.
 * @param {Object} trend - { overall: 'Bullish'|'Bearish'|'Neutral', strength: number, signals: Array<string> }
 * @param {Object} style - Optional style overrides
 */
function TrendCard({ trend = {}, style = {} }) {
  // Extract data from API structure (backend uses 'overall' instead of 'direction')
  const { overall = '-', strength = null, signals = [], bullish_count = 0, bearish_count = 0 } = trend;
  
  // Determine color based on trend direction
  const color = overall === 'Bullish' ? 'var(--success, #2ecc40)' : 
                overall === 'Bearish' ? 'var(--error, #ff4136)' : 
                'var(--text-secondary, #888)';
  
  // Calculate confidence based on strength or count ratio if available
  // Ensure confidence is always a number or null, never undefined
  let confidence = null;
  if (strength !== null && strength !== undefined) {
    confidence = Number(strength);
  } else if (bullish_count + bearish_count > 0) {
    confidence = Math.round((overall === 'Bullish' ? bullish_count : bearish_count) / 
                 (bullish_count + bearish_count) * 100);
  }
    return (
    <div className="analysis-card trend-card" style={{ 
      padding: '15px', 
      borderRadius: '8px', 
      background: 'var(--card, #1e2130)', 
      border: '1px solid var(--border, #2a2f45)',
      ...style 
    }}>
      <h3 style={{ color: 'var(--text, #fff)' }}>Market Trend</h3>
      <div style={{ fontSize: '1.5em', color, fontWeight: 'bold' }}>{overall}</div>
      <div style={{ color: 'var(--text, #fff)' }}>Confidence: {confidence !== null ? `${confidence}%` : '-'}</div>
      
      {/* Show signal count summary */}
      {(bullish_count > 0 || bearish_count > 0) && (
        <div style={{ fontSize: '0.9em', marginTop: '5px', color: 'var(--text-secondary, #aaa)' }}>
          <span style={{ color: 'var(--success, #2ecc40)' }}>{bullish_count} bullish</span> / 
          <span style={{ color: 'var(--error, #ff4136)' }}>{bearish_count} bearish</span> signals
        </div>
      )}
      
      {/* Show individual signals if available */}
      {signals && signals.length > 0 && (
        <ul style={{ marginTop: 8, paddingLeft: '20px', color: 'var(--text, #fff)' }}>
          {signals.map((signal, idx) => (
            <li key={idx} style={{ marginBottom: '3px' }}>
              {signal}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

export default TrendCard;
