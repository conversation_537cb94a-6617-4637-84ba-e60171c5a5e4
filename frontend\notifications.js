/**
 * Notification System
 * A simple notification system that utilizes the theme variables
 */

// Store active notifications
let activeNotifications = [];
let notificationCounter = 0;

// Setup global keyboard shortcut for closing notifications
document.addEventListener('keydown', (event) => {
    if (event.key === 'Escape') {
        // Force close all notifications when Escape key is pressed
        forceRemoveNotification();
    }
});

// Create container if it doesn't exist
function ensureContainer() {
    let container = document.querySelector('.notification-container');
    if (!container) {
        console.log('Creating notification container');
        container = document.createElement('div');
        container.className = 'notification-container';
        document.body.appendChild(container);

        // Add CSS if not already present
        if (!document.querySelector('#notification-styles')) {
            console.log('Adding notification styles');
            const style = document.createElement('style');
            style.id = 'notification-styles';
            style.textContent = `
                .notification-container {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 9999;
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                    max-width: 350px;
                }
                .notification {
                    padding: 15px;
                    border-radius: 6px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    animation: notification-slide-in 0.3s ease;
                    background-color: var(--card, #1a1d2d);
                    border-left: 4px solid;
                }
                .notification.success { border-color: var(--success, #10b981); }
                .notification.error { border-color: var(--error, #ef4444); }
                .notification.warning { border-color: var(--warning, #f59e0b); }
                .notification.info { border-color: var(--info, #3b82f6); }

                .notification-content {
                    flex: 1;
                }
                .notification-title {
                    font-weight: 600;
                    margin-bottom: 5px;
                    color: var(--text-primary, #ffffff);
                }
                .notification.success .notification-title { color: var(--success, #10b981); }
                .notification.error .notification-title { color: var(--error, #ef4444); }
                .notification.warning .notification-title { color: var(--warning, #f59e0b); }
                .notification.info .notification-title { color: var(--info, #3b82f6); }

                .notification-message {
                    color: var(--text-secondary, #a0aec0);
                    font-size: 0.9rem;
                }
                .notification-close {
                    background: none;
                    border: none;
                    color: var(--text-secondary, #a0aec0);
                    cursor: pointer;
                    font-size: 1.2rem;
                    line-height: 1;
                    padding: 0 5px;
                    margin-left: 10px;
                }
                .notification-close:hover {
                    color: var(--text-primary, #ffffff);
                }
                .notification.hide {
                    animation: notification-slide-out 0.3s ease forwards;
                }
                @keyframes notification-slide-in {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes notification-slide-out {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }
    }
    return container;
}

/**
 * Show a notification
 * @param {Object} options - Notification options
 * @param {string} options.type - Notification type (success, error, warning, info)
 * @param {string} options.title - Notification title
 * @param {string} options.message - Notification message
 * @param {number} options.duration - How long to show notification (ms), default 5000
 * @returns {number} Notification ID
 */
function showNotification(options) {
    console.log(`Showing notification: ${options.type}`, options);

    const container = ensureContainer();
    const { type = 'info', title, message, duration = 5000 } = options;

    // Create notification element
    const notificationId = notificationCounter++;
    const notificationEl = document.createElement('div');
    notificationEl.className = `notification ${type}`;
    notificationEl.dataset.id = notificationId;

    // Create content
    const contentEl = document.createElement('div');
    contentEl.className = 'notification-content';

    if (title) {
        const titleEl = document.createElement('div');
        titleEl.className = 'notification-title';
        titleEl.textContent = title;
        contentEl.appendChild(titleEl);
    }

    if (message) {
        const messageEl = document.createElement('div');
        messageEl.className = 'notification-message';
        messageEl.textContent = message;
        contentEl.appendChild(messageEl);
    }

    // Create close button
    const closeBtn = document.createElement('button');
    closeBtn.className = 'notification-close';
    closeBtn.innerHTML = '&times;';
    closeBtn.addEventListener('click', () => {
        closeNotification(notificationId);
    });

    // Assemble notification
    notificationEl.appendChild(contentEl);
    notificationEl.appendChild(closeBtn);

    // Add to container
    container.appendChild(notificationEl);

    // Store reference
    activeNotifications.push({
        id: notificationId,
        element: notificationEl,
        timeout: setTimeout(() => {
            closeNotification(notificationId);
        }, duration)
    });

    return notificationId;
}

/**
 * Close a notification by ID
 * @param {number} id - Notification ID
 * @param {boolean} force - Force close without animation
 */
function closeNotification(id, force = false) {
    const notificationIndex = activeNotifications.findIndex(n => n.id === id);
    if (notificationIndex === -1) {
        // If notification not found in active list, attempt to find it in DOM by data-id
        const allNotifications = document.querySelectorAll('.notification');
        for (const el of allNotifications) {
            if (el.dataset.id === id.toString()) {
                console.log(`Found orphaned notification with ID ${id} in DOM, removing`);
                if (el.parentNode) {
                    el.parentNode.removeChild(el);
                    return true;
                }
            }
        }
        return false;
    }

    const notification = activeNotifications[notificationIndex];

    // Clear timeout if it exists
    if (notification.timeout) {
        clearTimeout(notification.timeout);
    }

    if (force) {
        // Immediately remove without animation
        if (notification.element && notification.element.parentNode) {
            notification.element.parentNode.removeChild(notification.element);
        }
        activeNotifications.splice(notificationIndex, 1);
    } else {
        // Add hide class to trigger animation
        notification.element.classList.add('hide');

        // Remove after animation completes
        setTimeout(() => {
            try {
                if (notification.element && notification.element.parentNode) {
                    notification.element.parentNode.removeChild(notification.element);
                }

                // Remove from array if it still exists
                const currentIndex = activeNotifications.findIndex(n => n.id === id);
                if (currentIndex !== -1) {
                    activeNotifications.splice(currentIndex, 1);
                }
            } catch (error) {
                console.error(`Error removing notification ${id}:`, error);
                // Attempt force remove if regular remove fails
                forceRemoveNotification(id);
            }

            // Remove container if no notifications left
            if (activeNotifications.length === 0) {
                const container = document.querySelector('.notification-container');
                if (container && container.childNodes.length === 0) {
                    container.parentNode.removeChild(container);
                }
            }
        }, 300); // Match the animation duration
    }
    
    return true;
}

/**
 * Force remove any notification by direct DOM manipulation
 * @param {number} id - Notification ID (optional)
 */
function forceRemoveNotification(id = null) {
    // If ID provided, try to find and remove that specific notification
    if (id !== null) {
        const el = document.querySelector(`.notification[data-id="${id}"]`);
        if (el && el.parentNode) {
            el.parentNode.removeChild(el);
            return;
        }
    }
    
    // Otherwise, find all notifications and remove them
    const notifications = document.querySelectorAll('.notification');
    for (const el of notifications) {
        if (el.parentNode) {
            el.parentNode.removeChild(el);
        }
    }
    
    // Clear our tracking array
    activeNotifications = [];
}

/**
 * Clear all notifications
 */
function clearAllNotifications() {
    [...activeNotifications].forEach(notification => {
        closeNotification(notification.id);
    });
}

/**
 * Convenience methods for different notification types
 */
const notifications = {
    success: (title, message, duration) => showNotification({ type: 'success', title, message, duration }),
    error: (title, message, duration) => showNotification({ type: 'error', title, message, duration }),
    warning: (title, message, duration) => showNotification({ type: 'warning', title, message, duration }),
    info: (title, message, duration) => showNotification({ type: 'info', title, message, duration }),
    clear: clearAllNotifications
};

// Export notification methods
export default notifications;