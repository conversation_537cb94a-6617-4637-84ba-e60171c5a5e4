# Phase 4: Market Condition Handling

## Overview

This phase implements market condition detection and adaptive trading strategies. The system will identify different market conditions (trending, ranging, volatile) and adapt trading parameters accordingly for improved performance.

## Current Implementation

Currently, the Garuda-Algo autonomous trading system applies the same strategy parameters regardless of market conditions, which can lead to poor performance when conditions change. It does not consider:

- Market volatility levels
- Trending vs ranging markets
- Correlation between instruments
- Spread widening during news events

## Implementation Details

### 1. Market Condition Detection

Create a new file `backend/market_condition.py` for market condition detection:

```python
import numpy as np
import logging
from typing import Dict, List, Any
from enum import Enum

logger = logging.getLogger('market_condition')

class MarketCondition(Enum):
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    RANGING = "ranging"
    VOLATILE = "volatile"
    NORMAL = "normal"

class MarketConditionDetector:
    """Class to detect and classify market conditions."""
    
    def __init__(self, mt5_connector, config):
        self.mt5 = mt5_connector
        self.config = config
        self.market_config = config.get("market_condition", {})
        
    def detect_condition(self, symbol, timeframe="H1"):
        """Detect the current market condition for a symbol.
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe to analyze
            
        Returns:
            dict: Result with detected market condition and metrics
        """
        try:
            # Get historical data for analysis
            candles_result = self.mt5.get_candles(symbol, timeframe, 100)
            if not candles_result.get("success", False):
                return {
                    "success": False,
                    "message": f"Failed to get candles for {symbol} on {timeframe}: {candles_result.get('message', 'Unknown error')}"
                }
                
            candles = candles_result.get("candles", [])
            if len(candles) < 50:  # Need enough data for reliable detection
                return {
                    "success": False,
                    "message": f"Insufficient historical data for {symbol} on {timeframe}: {len(candles)} candles"
                }
                
            # Convert to numpy arrays for efficient calculation
            closes = np.array([c["close"] for c in candles])
            highs = np.array([c["high"] for c in candles])
            lows = np.array([c["low"] for c in candles])
            
            # 1. Calculate volatility (ATR)
            atr = self.calculate_atr(highs, lows, closes)
            
            # 2. Calculate trend strength (ADX)
            adx = self.calculate_adx(highs, lows, closes)
            
            # 3. Calculate recent price direction
            direction = self.calculate_direction(closes)
            
            # 4. Identify market condition based on metrics
            condition = self.classify_condition(adx, atr, direction)
            
            return {
                "success": True,
                "condition": condition,
                "metrics": {
                    "atr": atr,
                    "atr_percent": atr / closes[-1] * 100,  # ATR as percentage of price
                    "adx": adx,
                    "direction": direction
                }
            }
            
        except Exception as e:
            logger.exception(f"Error detecting market condition: {str(e)}")
            return {
                "success": False,
                "message": f"Error detecting market condition: {str(e)}"
            }
    
    def calculate_atr(self, highs, lows, closes, period=14):
        """Calculate Average True Range."""
        # Truncate arrays to required length
        if len(highs) <= period:
            return 0
            
        tr1 = abs(highs[1:] - lows[1:])
        tr2 = abs(highs[1:] - closes[:-1])
        tr3 = abs(lows[1:] - closes[:-1])
        
        # True range is the max of the three
        tr = np.vstack([tr1, tr2, tr3]).max(axis=0)
        
        # Calculate ATR using simple moving average
        atr = np.mean(tr[-period:])
        return atr
    
    def calculate_adx(self, highs, lows, closes, period=14):
        """Calculate Average Directional Index."""
        # Implementation of ADX calculation
        # This is a simplified version, actual implementation would be more detailed
        if len(highs) <= period * 2:
            return 0
            
        # Calculate +DI and -DI (directional indicators)
        # Simplified implementation
        plus_dm = np.where(highs[1:] - highs[:-1] > lows[:-1] - lows[1:], 
                          highs[1:] - highs[:-1], 0)
        minus_dm = np.where(lows[:-1] - lows[1:] > highs[1:] - highs[:-1], 
                           lows[:-1] - lows[1:], 0)
        
        # Use ATR as the normalization factor (true range)
        atr = self.calculate_atr(highs, lows, closes, period)
        
        # Avoid division by zero
        if atr == 0:
            return 0
            
        # Calculate DI values
        plus_di = 100 * np.mean(plus_dm[-period:]) / atr
        minus_di = 100 * np.mean(minus_dm[-period:]) / atr
        
        # Calculate DX
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di) if (plus_di + minus_di) > 0 else 0
        
        # Simple ADX calculation (should be smoothed in actual implementation)
        adx = dx
        return adx
    
    def calculate_direction(self, closes, short_period=5, long_period=20):
        """Calculate recent price direction."""
        if len(closes) <= long_period:
            return 0
            
        # Simple moving averages
        short_ma = np.mean(closes[-short_period:])
        long_ma = np.mean(closes[-long_period:])
        
        # Price direction: 1 for up, -1 for down, 0 for neutral
        if short_ma > long_ma * 1.001:  # 0.1% threshold to reduce noise
            return 1  # Uptrend
        elif short_ma < long_ma * 0.999:  # 0.1% threshold
            return -1  # Downtrend
        else:
            return 0  # Neutral/Range
    
    def classify_condition(self, adx, atr, direction):
        """Classify market condition based on metrics."""
        # ADX interpretation: > 25 is trending, < 20 is ranging
        # This should be calibrated based on specific market characteristics
        
        # Threshold for volatility (should be customized per symbol)
        volatility_threshold = self.market_config.get("volatility_threshold", 2.0)
        standard_atr = 0.5  # This should be dynamically calculated per symbol
        
        if atr > standard_atr * volatility_threshold:
            return MarketCondition.VOLATILE.value
        elif adx > 25:  # Strong trend
            if direction > 0:
                return MarketCondition.TRENDING_UP.value
            elif direction < 0:
                return MarketCondition.TRENDING_DOWN.value
            else:
                return MarketCondition.NORMAL.value
        elif adx < 20:  # Weak trend/range
            return MarketCondition.RANGING.value
        else:
            return MarketCondition.NORMAL.value
```

### 2. Parameter Adaptation Based on Market Conditions

Implement strategy parameter adaptation based on detected market conditions:

```python
def adapt_strategy_parameters(self, strategy_id, market_condition, symbol=None):
    """Adapt strategy parameters based on detected market condition.
    
    Args:
        strategy_id: Strategy identifier
        market_condition: Detected market condition
        symbol: Optional symbol for symbol-specific adjustments
        
    Returns:
        dict: Adapted strategy parameters
    """
    from backend.strategy_defaults import STRATEGY_DEFAULTS, get_optimized_parameters
    
    # Get base parameters for strategy
    base_params = STRATEGY_DEFAULTS.get(strategy_id, {})
    if not base_params:
        logger.warning(f"No default parameters found for strategy {strategy_id}")
        return {}
    
    # Get optimized parameters for the market condition
    optimized_params = get_optimized_parameters(strategy_id, market_condition)
    
    # Start with base parameters
    adapted_params = base_params.copy()
    
    # Update with optimized parameters for the market condition
    adapted_params.update(optimized_params)
    
    # Apply market-specific adjustments
    if market_condition == MarketCondition.RANGING.value:
        # In ranging markets, use tighter stops and take profits
        adapted_params["sl_multiplier"] = adapted_params.get("sl_multiplier", 1.0) * 0.8
        adapted_params["tp_multiplier"] = adapted_params.get("tp_multiplier", 1.0) * 0.8
        
    elif market_condition == MarketCondition.VOLATILE.value:
        # In volatile markets, widen stops and take profits
        adapted_params["sl_multiplier"] = adapted_params.get("sl_multiplier", 1.0) * 1.5
        adapted_params["tp_multiplier"] = adapted_params.get("tp_multiplier", 1.0) * 1.5
        
    elif market_condition == MarketCondition.TRENDING_UP.value:
        # In uptrends, favor buy signals and widen take profits
        adapted_params["favor_direction"] = "buy"
        adapted_params["tp_multiplier"] = adapted_params.get("tp_multiplier", 1.0) * 1.2
        
    elif market_condition == MarketCondition.TRENDING_DOWN.value:
        # In downtrends, favor sell signals and widen take profits
        adapted_params["favor_direction"] = "sell"
        adapted_params["tp_multiplier"] = adapted_params.get("tp_multiplier", 1.0) * 1.2
    
    # Apply symbol-specific adjustments if needed
    if symbol and "symbol_adjustments" in base_params and symbol in base_params["symbol_adjustments"]:
        symbol_params = base_params["symbol_adjustments"][symbol]
        adapted_params.update(symbol_params)
    
    logger.info(f"Adapted parameters for {strategy_id} in {market_condition} condition")
    return adapted_params
```

### 3. Spread Management

Implement spread monitoring and trading adjustments:

```python
def check_spread_conditions(self, symbol, max_spread_points=None):
    """Check if spread conditions are acceptable for trading.
    
    Args:
        symbol: Trading symbol
        max_spread_points: Maximum acceptable spread in points
        
    Returns:
        dict: Result with spread status and information
    """
    try:
        # Get symbol info
        symbol_info = self.mt5.get_symbol_info(symbol)
        if not symbol_info.get("success", False):
            return {
                "success": False,
                "message": f"Failed to get symbol info: {symbol_info.get('message', 'Unknown error')}"
            }
            
        # Get current bid/ask prices
        price_data = self.mt5.get_current_price(symbol)
        if "error" in price_data:
            return {
                "success": False,
                "message": f"Failed to get price for {symbol}: {price_data['error']}"
            }
            
        # Calculate spread in points
        ask = price_data["ask"]
        bid = price_data["bid"]
        point = symbol_info["point"]
        
        spread_points = (ask - bid) / point
        
        # Get historical average spread if available
        avg_spread = 20  # Default fallback
        if hasattr(self, "spread_history") and symbol in self.spread_history:
            avg_spread = np.mean(self.spread_history[symbol])
        
        # If max_spread_points is not provided, use config or calculate
        if max_spread_points is None:
            # Dynamic limit based on average: 1.5x average or from config
            max_spread_points = self.market_config.get("max_spread_multiplier", 1.5) * avg_spread
        
        # Check if current spread exceeds maximum
        if spread_points > max_spread_points:
            return {
                "success": True,
                "acceptable": False,
                "spread_points": spread_points,
                "max_spread_points": max_spread_points,
                "message": f"Spread for {symbol} is {spread_points} points, exceeding maximum {max_spread_points}"
            }
        
        return {
            "success": True,
            "acceptable": True,
            "spread_points": spread_points,
            "max_spread_points": max_spread_points,
            "message": f"Spread for {symbol} is {spread_points} points, within acceptable limit"
        }
        
    except Exception as e:
        logger.exception(f"Error checking spread conditions: {str(e)}")
        return {
            "success": False,
            "message": f"Error checking spread conditions: {str(e)}"
        }
```

### 4. Integration with Trading Loop

Modify `backend/api/autonomous.py` to use the market condition detector in the trading loop:

```python
from backend.market_condition import MarketConditionDetector

def _trading_loop(app):
    """Main autonomous trading loop."""
    global AUTONOMOUS_RUNNING
    
    # ... existing code ...
    
    # Create market condition detector
    market_detector = MarketConditionDetector(mt5_instance, AUTONOMOUS_CONFIG)
    
    while AUTONOMOUS_RUNNING:
        try:
            # ... existing connection and time filter checks ...
            
            # ... existing position management ...
            
            # Process each symbol
            for symbol in AUTONOMOUS_CONFIG["symbols"]:
                # ... existing symbol validation ...
                
                # Check market conditions
                if AUTONOMOUS_CONFIG["market_condition"].get("volatility_filter_enabled", True):
                    condition_result = market_detector.detect_condition(symbol, "H1")
                    
                    if condition_result.get("success", False):
                        market_condition = condition_result["condition"]
                        logger.info(f"Detected {market_condition} condition for {symbol}")
                        
                        # Skip volatile markets if configured
                        if market_condition == "volatile" and not AUTONOMOUS_CONFIG["market_condition"].get("trade_in_volatile", False):
                            logger.info(f"Skipping {symbol} due to volatile market conditions")
                            continue
                            
                        # Check spread conditions
                        if AUTONOMOUS_CONFIG["market_condition"].get("adapt_to_spread", True):
                            spread_result = market_detector.check_spread_conditions(symbol)
                            if spread_result.get("success", False) and not spread_result.get("acceptable", True):
                                logger.info(f"Skipping {symbol}: {spread_result['message']}")
                                continue
                    else:
                        logger.warning(f"Failed to detect market condition for {symbol}: {condition_result.get('message', 'Unknown error')}")
                
                # ... existing signal generation ...
                
                # Get the active strategy
                strategy_id = active_strategy["strategy_id"]
                
                # Adapt strategy parameters based on market condition
                if AUTONOMOUS_CONFIG["market_condition"].get("ranging_market_filter", True) and market_condition:
                    adapted_params = market_detector.adapt_strategy_parameters(strategy_id, market_condition, symbol)
                    
                    # Apply adapted parameters to signal generation
                    # This would require modifying the signal generation to accept parameters
                    # For now, we can adjust the stop loss and take profit based on the adapted parameters
                    if adapted_params:
                        sl_multiplier = adapted_params.get("sl_multiplier", 1.0)
                        tp_multiplier = adapted_params.get("tp_multiplier", 1.0)
                        
                        # Adjust SL and TP values
                        sl_level = sl_level + (price_data["ask" if signal == "buy" else "bid"] - sl_level) * (1.0 - sl_multiplier)
                        tp_level = tp_level + (tp_level - price_data["ask" if signal == "buy" else "bid"]) * (tp_multiplier - 1.0)
                        
                        # Apply direction preference if any
                        favor_direction = adapted_params.get("favor_direction", None)
                        if favor_direction and signal != favor_direction:
                            logger.info(f"Skipping {signal} signal for {symbol} as {favor_direction} is favored in current market condition")
                            continue
                
                # ... existing risk checks and trade execution ...
```

## Files to Modify/Create

1. **backend/market_condition.py** (new):
   - Implement MarketConditionDetector class
   - Add market classification functions
   - Add parameter adaptation
   - Add spread monitoring

2. **backend/api/autonomous.py**:
   - Integrate market condition detection into trading loop
   - Add market condition checks before trading
   - Apply adapted parameters to trading decisions

3. **backend/strategy_defaults.py**:
   - Add market condition-specific parameters

## Testing Plan

1. **Market Condition Detection**:
   - Test with different historical data patterns
   - Verify correct classification of market conditions
   - Test robustness against edge cases

2. **Parameter Adaptation**:
   - Test parameter changes in different market conditions
   - Verify signal generation with adapted parameters
   - Test symbol-specific adjustments

3. **Spread Management**:
   - Test spread calculation
   - Verify trading decisions based on spread conditions
   - Test with different spread scenarios

4. **Integration Tests**:
   - Verify end-to-end flow with market condition detection
   - Test trading performance in different market conditions
   - Verify logging of market conditions and adaptations

## Acceptance Criteria

- System correctly detects and classifies market conditions
- Trading parameters are adapted based on market conditions
- Volatile market conditions are handled appropriately
- Spread conditions are monitored and trading is adjusted accordingly
- Market condition information is properly logged
