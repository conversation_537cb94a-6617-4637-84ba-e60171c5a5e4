/* Entry Spacing Settings Styles - Full Width Layout */

.entry-spacing-settings {
  width: 100%;
  padding: 2rem;
  background: var(--background);
  color: var(--text);
  box-sizing: border-box;
}

/* Header Styling - Full Width */
.settings-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 3rem;
  padding: 2rem;
  margin-bottom: 2rem;
  background: var(--card);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}

.header-content {
  flex: 1;
  min-width: 0;
}

.header-content h3 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  font-size: 1.8rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-description {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
  max-width: none;
}

.settings-actions {
  display: flex;
  gap: 1rem;
  flex-shrink: 0;
}

/* Main Content Layout - Full Width Single Column */
.settings-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.settings-panel {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-bottom: 2rem;
}

/* Enhanced Card Styling - Full Width */
.settings-card {
  width: 100%;
  background: var(--card);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.settings-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--border-hover);
}

.card-header {
  width: 100%;
  padding: 1.5rem 2rem;
  background: var(--card-secondary);
  border-bottom: 1px solid var(--border-color);
  box-sizing: border-box;
}

.card-header h4 {
  margin: 0 0 0.75rem 0;
  color: var(--text-primary);
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.card-description {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.5;
}

.card-content {
  width: 100%;
  padding: 2rem;
  box-sizing: border-box;
}

/* Master Control Full Width */
.master-control {
  grid-column: span 2;
}

.master-control .card-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: white;
}

.master-control .card-header h4,
.master-control .card-description {
  color: white;
}

/* Loss Tracking Section */
.loss-tracking {
  margin-top: 2rem;
}

.track-losses-header {
  margin-bottom: 1rem;
}

.loss-tracking .checkbox-label.enhanced {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  padding: 1rem 1.5rem;
}

.track-losses-input {
  padding: 0.75rem;
  background: var(--card-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.track-losses-input input {
  width: 100%;
  max-width: 200px;
  text-align: center;
  font-size: 1.5rem;
  font-weight: 600;
  padding: 0.75rem 1rem;
  color: var(--primary-color);
}

/* Form Styling - Full Width with Margins */
.form-group {
  width: 100%;
  margin-bottom: 2rem;
  box-sizing: border-box;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  width: 100%;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1rem;
}

/* Enhanced Checkbox Styling - Full Width */
.checkbox-label.enhanced {
  display: flex !important;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  padding: 1.5rem 2rem;
  background: var(--card-secondary);
  border-radius: 8px;
  border: 2px solid transparent;
  transition: all 0.2s ease;
  margin-bottom: 0 !important;
  width: 100%;
  box-sizing: border-box;
}

.checkbox-label.enhanced:hover {
  border-color: var(--primary-color);
  background: var(--card-hover);
}

.checkbox-label.enhanced input[type="checkbox"] {
  margin: 0;
  width: 20px;
  height: 20px;
  accent-color: var(--primary-color);
  flex-shrink: 0;
}

.toggle-text {
  font-weight: 600;
  font-size: 1.1rem;
  line-height: 1.3;
}

/* Status Indicators - With Margins */
.status-container {
  margin-top: 1.5rem;
  width: 100%;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: var(--bg-tertiary);
  border-radius: 8px 8px 0 0;
  width: 100%;
  box-sizing: border-box;
  border: 1px solid var(--border-color);
  border-bottom: none;
}

.status-description {
  padding: 0.75rem 1.5rem;
  background: var(--card-secondary);
  border-radius: 0 0 8px 8px;
  width: 100%;
  box-sizing: border-box;
  font-size: 0.95rem;
  color: var(--text-secondary);
  line-height: 1.5;
  border: 1px solid var(--border-color);
  border-top: none;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.status-dot.active {
  background: var(--success-color);
  box-shadow: 0 0 10px rgba(34, 197, 94, 0.4);
}

.status-dot.inactive {
  background: var(--error-color);
}

.status-text {
  font-size: 1rem;
  color: var(--text-primary);
  line-height: 1.3;
}

/* Enhanced Input Styling - Full Width */
.form-group input[type="number"] {
  width: 100%;
  padding: 15px 20px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background: var(--input-bg);
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}

.form-group input[type="number"]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--bg-tertiary);
}

.form-group input[type="number"]:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(55, 114, 255, 0.2), inset 0 1px 3px rgba(0, 0, 0, 0.1);
  background-color: var(--input-focus);
}

.form-group input[type="number"]:hover:not(:focus):not(:disabled) {
  border-color: var(--border-hover);
  background-color: var(--input-hover);
}

/* Enhanced Input Hints - Full Width with Margins */
.input-hint.detailed {
  width: 100%;
  margin-top: 1rem;
  padding: 1.5rem;
  background: var(--card-secondary);
  border-radius: 8px;
  border-left: 4px solid var(--primary-color);
  font-size: 0.95rem;
  line-height: 1.5;
  color: var(--text-secondary);
  box-sizing: border-box;
}

.input-hint.detailed strong {
  color: var(--text-primary);
  font-weight: 600;
}

/* Explanation Boxes - Full Width */
.scaling-explanation {
  width: 100%;
  margin-bottom: 2rem;
}

.explanation-box {
  width: 100%;
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--card-secondary) 0%, var(--bg-tertiary) 100%);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  box-sizing: border-box;
}

.explanation-box h5 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
}

.explanation-box ul {
  margin: 0;
  padding-left: 1.5rem;
  color: var(--text-secondary);
}

.explanation-box li {
  margin-bottom: 0.5rem;
  line-height: 1.4;
  font-size: 0.95rem;
}

/* Enhanced Array Inputs - Full Width Grid */
.array-inputs {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.array-input-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
  min-width: 90px;
}

.array-input-item label {
  font-weight: 500;
  margin-bottom: 0 !important;
  font-size: 0.9rem;
}

.array-inputs.enhanced {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 0.75rem;
  width: 100%;
  margin-bottom: 1rem;
}

.array-input-item.enhanced {
  background: var(--card-secondary);
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.array-input-item.enhanced:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.array-input-item.enhanced label {
  font-weight: 600;
  margin-bottom: 0.25rem !important;
  font-size: 0.9rem;
  color: var(--text-secondary);
  text-align: center;
}

.array-input-item.enhanced input {
  width: 100%;
  text-align: center;
  font-size: 1.1rem;
  font-weight: 500;
  padding: 0.5rem 0.25rem;
}

.multiplier-effect {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
  text-align: center;
  white-space: nowrap;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Statistics Panel - Full Width */
.statistics-panel.enhanced {
  width: 100%;
  background: var(--card);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-top: 2rem;
  box-sizing: border-box;
}

.stats-header {
  width: 100%;
  padding: 1.5rem 2rem;
  background: var(--card-secondary);
  border-bottom: 1px solid var(--border-color);
  box-sizing: border-box;
}

.stats-header h4 {
  margin: 0 0 0.75rem 0;
  color: var(--text-primary);
  font-size: 1.3rem;
  font-weight: 600;
}

.stats-description {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.4;
}

.statistics-content {
  width: 100%;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  box-sizing: border-box;
}

/* Stats Section Styling - Full Width */
.stats-section {
  width: 100%;
  padding: 1.5rem;
  background: var(--card-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  margin-bottom: 1.5rem;
  box-sizing: border-box;
}

.stats-section:last-child {
  margin-bottom: 0;
}

.stats-section h5 {
  margin: 0 0 1.5rem 0;
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

/* Performance Stats Grid - Full Width */
.stats-grid {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--card);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 0;
  flex: 1;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
}

.stat-value.positive {
  color: var(--success-color);
}

.stat-value.negative {
  color: var(--error-color);
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
  line-height: 1.2;
}

/* Symbol Stats Cards - Full Width */
.symbol-stats-card {
  width: 100%;
  padding: 1.5rem;
  background: var(--card);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  margin-bottom: 1.5rem;
  box-sizing: border-box;
}

.symbol-stats-card:last-child {
  margin-bottom: 0;
}

.symbol-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.symbol-header h6 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
}

.loss-badge {
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 600;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.loss-badge.active {
  background: var(--error-color);
  color: white;
}

.symbol-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--card-secondary);
  border-radius: 6px;
  box-sizing: border-box;
}

.detail-icon {
  font-size: 1rem;
  width: 20px;
  flex-shrink: 0;
}

.detail-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  flex: 1;
  min-width: 0;
}

.detail-value {
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: 600;
  flex-shrink: 0;
}

.detail-value.time {
  font-size: 0.85rem;
  font-weight: 400;
}

/* ATR Analysis Grid - Full Width */
.atr-grid {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.atr-card {
  padding: 1.5rem;
  background: var(--card);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  box-sizing: border-box;
}

.atr-symbol {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.atr-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.atr-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.atr-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.atr-value {
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: 600;
}

.atr-value.points {
  color: var(--primary-color);
}

/* Enhanced No Stats Styling */
.no-stats.enhanced {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--text-secondary);
}

.no-stats-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.5;
}

.no-stats.enhanced h5 {
  margin: 0 0 1.5rem 0;
  color: var(--text-primary);
  font-size: 1.3rem;
  font-weight: 600;
}

.no-stats.enhanced p {
  margin: 1rem 0;
  font-size: 1rem;
  line-height: 1.4;
}

/* Button Enhancements - With Margins */
.button {
  padding: 1rem 2rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  text-decoration: none;
  margin: 0 0.5rem;
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.button.primary {
  background: var(--primary-color);
  color: white;
  box-shadow: 0 2px 4px rgba(55, 114, 255, 0.2);
}

.button.primary:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(55, 114, 255, 0.3);
}

.button.secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.button.secondary:hover:not(:disabled) {
  background: var(--bg-tertiary);
  border-color: var(--border-hover);
}

.button.danger {
  background: var(--error-color);
  color: white;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
}

.button.danger:hover:not(:disabled) {
  background: var(--error-color-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

/* Responsive Design - Full Width Maintained */
@media (max-width: 1200px) {
  .settings-panel {
    grid-template-columns: 1fr;
  }
  
  .master-control {
    grid-column: span 1;
  }
  
  .array-inputs.enhanced {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .entry-spacing-settings {
    padding: 1rem;
  }
  
  .settings-header {
    flex-direction: column;
    gap: 1.5rem;
    padding: 1.5rem;
  }
  
  .settings-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .array-inputs.enhanced {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .array-inputs.enhanced {
    grid-template-columns: 1fr;
  }
  
  .card-content,
  .statistics-content {
    padding: 1.5rem;
  }
  
  .button {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
}

/* Light theme adjustments */
@media (prefers-color-scheme: light) {
  .form-group input[type="number"] {
    background-color: #ffffff;
    border-color: #d1d5db;
    color: #1f2937;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .form-group input[type="number"]:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(55, 114, 255, 0.1), inset 0 1px 2px rgba(0, 0, 0, 0.05);
    background-color: #ffffff;
  }

  .form-group input[type="number"]:hover:not(:focus):not(:disabled) {
    border-color: #9ca3af;
    background-color: #f9fafb;
  }
}
