import requests
import json
import time

def test_mt5_connection():
    """Test direct connection to MT5 via the backend API"""
    print("Testing MT5 connection via backend API...")
    
    # MT5 connection parameters
    connection_data = {
        "account": "*********",
        "password": "{2Rfj>0D",
        "server": "FBS-Demo",
        "path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe"
    }
    
    # Step 1: Check if the backend is running
    try:
        response = requests.get("http://localhost:5001/")
        print(f"Backend status: {response.status_code}")
        if response.ok:
            print(f"Backend response: {response.json()}")
        else:
            print(f"Backend error: {response.text}")
    except Exception as e:
        print(f"Error connecting to backend: {e}")
        print("Make sure the backend server is running on port 5001")
        return
    
    # Step 2: Test the MT5 path
    try:
        print("\nTesting MT5 path...")
        response = requests.post(
            "http://localhost:5001/api/connection/test_path",
            json={"path": connection_data["path"]}
        )
        print(f"Path test status: {response.status_code}")
        if response.ok:
            print(f"Path test response: {response.json()}")
        else:
            print(f"Path test error: {response.text}")
    except Exception as e:
        print(f"Error testing MT5 path: {e}")
        return
    
    # Step 3: Connect to MT5
    try:
        print("\nConnecting to MT5...")
        response = requests.post(
            "http://localhost:5001/api/connection/connect",
            json=connection_data
        )
        print(f"Connection status: {response.status_code}")
        if response.ok:
            print(f"Connection response: {json.dumps(response.json(), indent=2)}")
        else:
            print(f"Connection error: {response.text}")
    except Exception as e:
        print(f"Error connecting to MT5: {e}")
        return
    
    # Step 4: Check connection status
    try:
        print("\nChecking connection status...")
        response = requests.get("http://localhost:5001/api/connection/connection_status")
        print(f"Status check status: {response.status_code}")
        if response.ok:
            print(f"Status check response: {json.dumps(response.json(), indent=2)}")
        else:
            print(f"Status check error: {response.text}")
    except Exception as e:
        print(f"Error checking connection status: {e}")
        return
    
    # Step 5: Disconnect from MT5
    try:
        print("\nDisconnecting from MT5...")
        response = requests.post("http://localhost:5001/api/connection/disconnect")
        print(f"Disconnect status: {response.status_code}")
        if response.ok:
            print(f"Disconnect response: {response.json()}")
        else:
            print(f"Disconnect error: {response.text}")
    except Exception as e:
        print(f"Error disconnecting from MT5: {e}")
        return
    
    print("\nTest completed.")

if __name__ == "__main__":
    test_mt5_connection()
