import React from 'react';
import { safeToFixed, isV<PERSON>d<PERSON><PERSON>ber } from './numberUtils';

/**
 * Get the CSS class name for a signal value
 *
 * @param {string} value - The signal value
 * @returns {string} - The CSS class name
 */
export const getSignalClass = (value) => {
  if (!value) return 'neutral';
  const lowerValue = String(value).toLowerCase();
  return lowerValue === 'buy' || lowerValue === 'bullish' ? 'buy' :
         lowerValue === 'sell' || lowerValue === 'bearish' ? 'sell' : 'neutral';
};

/**
 * Render an indicator value with proper formatting
 *
 * @param {any} value - The indicator value
 * @param {string} type - The type of indicator ('signal' or 'numeric')
 * @returns {string|JSX.Element} - The formatted value
 */
export const renderIndicatorValue = (value, type) => {
  if (type === 'signal') {
    // Handle potential null/undefined values gracefully
    const signalValue = getSignalClass(value);
    return (
      <span className={`signal ${signalValue}`}>
        {value ?? 'NEUTRAL'}
      </span>
    );
  }
  // Use our safe utility function for numeric values
  return safeToFixed(value, 5, 'N/A');
};

/**
 * Generate random price data for demo purposes
 *
 * @param {number} count - Number of data points to generate
 * @returns {Array<number>} - Array of random price data
 */
export const generateRandomPriceData = (count = 20) => {
  const startPrice = 1.1 + Math.random() * 0.1; // Random starting price around 1.1-1.2
  const result = [startPrice];

  for (let i = 1; i < count; i++) {
    // Generate small random changes (-0.5% to +0.5%)
    const change = (Math.random() - 0.5) * 0.01;
    result.push(result[i-1] * (1 + change));
  }

  return result;
};

export { safeToFixed, isValidNumber };
