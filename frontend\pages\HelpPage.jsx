import React, { useState } from 'react';
import '../styles/Pages.css'; // General page styles
import '../styles/BentoLayout.css'; // Specific styles for bento layout

// Placeholder icons (replace with actual icon components or SVGs)
const IconPlaceholder = ({ name, className = "bento-card-icon" }) => <span className={className}>[{name.substring(0,1).toUpperCase()}]</span>;

const HelpPage = () => {
  const [openFaq, setOpenFaq] = useState(null);

  const toggleFaq = (index) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  const features = [
    { name: "Dashboard", icon: "📊", description: "Quick overview of your MT5 account status (Balance, Equity, Profit, Positions)." },
    { name: "Analysis", icon: "📈", description: "In-depth technical analysis for selected symbols and timeframes." },
    { name: "Trade Signals", icon: "💡", description: "Actionable buy/sell signals with SL/TP suggestions." },
    { name: "Autonomous Trading", icon: "🤖", description: "Automated trading based on configurable strategies." }, // Corrected
    { name: "History", icon: "📜", description: "Review your past trades and performance statistics." },
    { name: "Settings", icon: "⚙️", description: "Configure application preferences, MT5 connection, and view license details." },
    { name: "Profile", icon: "👤", description: "View your user and license information." },
  ];

  const faqs = [
    {
      question: "How do I connect to my MT5 account?",
      answer: "Click the 'Connect' or 'Configure MT5' button in the header. In the Connection Modal, enter your MT5 account number, password, server name, and the path to your MT5 terminal executable (e.g., terminal64.exe). The application may suggest a path. Ensure your license is active."
    },
    {
      question: "Where can I see my account balance and equity?",
      answer: "Your account balance, equity, and profit are displayed in the Dashboard section at the top of most pages, and also in the user menu in the Header once connected."
    },
    {
      question: "How does the Analysis page work?",
      answer: "Select a symbol and timeframe. The page fetches and displays various technical indicators, trend analysis, support/resistance levels, and volatility data to aid your trading decisions."
    },
    {
      question: "What are Trade Signals?",
      answer: "This page provides trading signals based on the backend analysis, suggesting potential entry points, stop-loss (SL), and take-profit (TP) levels."
    },
    {
      question: "What is Autonomous Trading?", // Corrected
      answer: "The Autonomous Trading page allows you to manage autonomous trading strategies. These strategies use algorithms and market data to make trading decisions automatically. Use with caution and understanding of the risks."
    },
    {
      question: "How do I change the theme (light/dark mode)?",
      answer: "Go to the Settings page. You should find an option to toggle between light and dark themes. The selected theme is saved for future sessions."
    },
    {
      question: "License Verification Failed. What now?",
      answer: "This means your account details (account number, server) don't match an active license in our system, or your subscription might have expired. Double-check your credentials. If the problem persists, contact support with your account number and server name."
    }
  ];

  const troubleshootingTopics = [
    { 
      title: "Connection Issues", 
      icon: "🔌",
      points: [
        "Verify MT5 account number, password, and server name are exact.",
        "Ensure the MT5 terminal path in Settings points to the correct `terminal.exe` or `terminal64.exe`.",
        "Check if your MetaTrader 5 terminal is running.",
        "Confirm a stable internet connection.",
        "Ensure your license is active (see Profile page).",
        "Review notification pop-ups for specific error messages."
      ]
    },
    { 
      title: "Data Not Loading", 
      icon: "📉",
      points: [
        "Ensure you are connected to MT5; most data requires an active connection.",
        "Check your internet connection.",
        "Try selecting a different symbol or timeframe.",
        "If issues persist, try reconnecting or restarting the application."
      ]
    }
  ];

  return (
    <div className="page help-page bento-container">
      <h2 className="page-title">Help & Support</h2>
      <p className="page-intro">Welcome to Garuda Algo V2! Find answers and learn about our features.</p>

      <div className="bento-grid">
        <div className="bento-card large-card overview-card">
          <div className="bento-card-header">
            <IconPlaceholder name="Overview" />
            <h3>Application Overview</h3>
          </div>
          <p>Garuda Algo V2 is a sophisticated trading assistant designed to integrate seamlessly with MetaTrader 5. It empowers traders by providing advanced market analysis, actionable trade signals, AI-driven autonomous trading capabilities, and comprehensive trade history tracking.</p>
          <h4>Why Algorithmic & Autonomous Trading?</h4>
          <p>In today's fast-paced markets, manual trading can be challenging due to:</p>
          <ul>
            <li><strong>Emotional Decisions:</strong> Fear, greed, and other emotions can lead to impulsive and irrational trading choices.</li>
            <li><strong>Human Bias:</strong> Preconceived notions or recent experiences can cloud judgment and deviate from a sound trading plan.</li>
            <li><strong>Execution Speed:</strong> Manual execution can be too slow to capitalize on fleeting market opportunities or to react quickly to adverse movements.</li>
            <li><strong>Consistency:</strong> Maintaining discipline and consistently applying a trading strategy over time is difficult for humans.</li>
            <li><strong>Time Commitment:</strong> Constantly monitoring markets and executing trades requires significant time and attention.</li>
          </ul>
          <p>Garuda Algo V2 addresses these challenges by:</p>
          <ul>
            <li><strong>Data-Driven Decisions:</strong> Leveraging algorithms for objective analysis and signal generation, free from emotional interference.</li>
            <li><strong>Systematic Execution:</strong> Enabling precise and consistent application of trading strategies through autonomous features.</li>
            <li><strong>Speed & Efficiency:</strong> Reacting to market conditions and executing trades faster than humanly possible.</li>
            <li><strong>Backtesting & Optimization:</strong> (Future Feature) Allowing strategies to be tested and refined based on historical data.</li>
            <li><strong>Reduced Monitoring:</strong> Freeing up your time while the system monitors markets and manages trades based on your configured strategies.</li>
          </ul>
          <p>By utilizing Garuda Algo V2, you can enhance your trading discipline, improve efficiency, and potentially achieve more consistent results.</p>
        </div>

        <div className="bento-card feature-list-card">
          <div className="bento-card-header">
             <IconPlaceholder name="Features" />
            <h3>Key Features</h3>
          </div>
          <ul>
            {features.map(feature => (
              <li key={feature.name}>
                <span className="feature-icon">{feature.icon}</span> 
                <strong>{feature.name}:</strong> {feature.description}
              </li>
            ))}
          </ul>
        </div>
        
        <div className="bento-card faq-card">
          <div className="bento-card-header">
            <IconPlaceholder name="FAQ" />
            <h3>Frequently Asked Questions</h3>
          </div>
          {faqs.map((faq, index) => (
            <div key={index} className="faq-item-bento">
              <button onClick={() => toggleFaq(index)} className="faq-question">
                <span>{faq.question}</span>
                <span>{openFaq === index ? '−' : '+'}</span>
              </button>
              <p className={`faq-answer ${openFaq === index ? 'open' : ''}`}>{faq.answer}</p>
            </div>
          ))}
        </div>

        {troubleshootingTopics.map((topic, index) => (
          <div key={index} className="bento-card troubleshooting-card">
            <div className="bento-card-header">
              <span className="feature-icon">{topic.icon}</span>
              <h3>{topic.title}</h3>
            </div>
            <ul>
              {topic.points.map((point, pIndex) => <li key={pIndex}>{point}</li>)}
            </ul>
          </div>
        ))}
        
        <div className="bento-card contact-card">
          <div className="bento-card-header">
            <IconPlaceholder name="Support" />
            <h3>Contact Support</h3>
          </div>
          <p>If you need further assistance, please email us at <a href="mailto:<EMAIL>"><EMAIL></a> or visit our support portal (link pending).</p>
        </div>
      </div>
    </div>
  );
};

export default HelpPage;
