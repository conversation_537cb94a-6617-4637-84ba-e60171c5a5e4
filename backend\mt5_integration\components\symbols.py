"""
Symbol component for MT5 integration.
"""

import MetaTrader5 as mt5
import math
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Union, Any, Optional

from ..types import SymbolInfo
from ..exceptions import MT5ConnectionError, MT5SymbolError

# Configure logging
logger = logging.getLogger("MT5Integration.Symbols")

class SymbolComponent:
    """Component for handling MT5 symbol operations."""

    def __init__(self, connection_component):
        """
        Initialize the symbol component.

        Args:
            connection_component: The connection component to use for checking connection status
        """
        self._connection = connection_component
        self._symbols_cache: Optional[Dict[str, SymbolInfo]] = None
        self._symbols_cache_duration = timedelta(minutes=5)  # Symbols cache for 5 minutes

    def get_symbols(self, include_metadata: bool = False, use_cache: bool = True,
                   force_refresh: bool = False) -> Union[List[str], List[SymbolInfo]]:
        """
        Get list of available trading symbols from MT5.

        Args:
            include_metadata: If True, returns full SymbolInfo objects instead of just symbol names
            use_cache: Whether to use cached symbol info (if available and not expired)
            force_refresh: Force refresh of cache even if not expired

        Returns:
            List of symbol names or SymbolInfo objects if include_metadata=True

        Raises:
            MT5ConnectionError: If not connected to MT5
            MT5SymbolError: If symbol retrieval fails
        """
        if not self._connection.is_connected():
            logger.error("Cannot fetch symbols: Not connected to MT5")
            raise MT5ConnectionError("Not connected to MT5")

        try:
            current_time = datetime.now()

            # Check cache if enabled and not forcing refresh
            if (use_cache and not force_refresh and self._symbols_cache and
                (current_time - next(iter(self._symbols_cache.values())).last_updated < self._symbols_cache_duration)):
                logger.debug("Returning cached symbol information")
                if include_metadata:
                    return list(self._symbols_cache.values())
                return sorted(self._symbols_cache.keys())

            symbol_dict = {}

            # Try to get all symbols first
            logger.info("Attempting to fetch all available symbols...")
            raw_symbols = mt5.symbols_get()

            if raw_symbols:
                logger.info(f"Found {len(raw_symbols)} symbols in general fetch")
                for symbol_info_mt5 in raw_symbols:
                    try:
                        symbol_info = SymbolInfo(
                            name=symbol_info_mt5.name,
                            digits=symbol_info_mt5.digits,
                            trade_mode=symbol_info_mt5.trade_mode,
                            currency_base=symbol_info_mt5.currency_base,
                            currency_profit=symbol_info_mt5.currency_profit,
                            currency_margin=symbol_info_mt5.currency_margin,
                            visible=symbol_info_mt5.visible,
                            custom=symbol_info_mt5.custom,
                            last_updated=current_time
                        )
                        symbol_dict[symbol_info_mt5.name] = symbol_info
                    except Exception as e:
                        logger.warning(f"Error processing symbol {symbol_info_mt5.name}: {e}")
            else:
                logger.warning("No symbols returned from mt5.symbols_get()")
                error = mt5.last_error()
                if error:
                    logger.error(f"MT5 error: {error}")
                raise MT5SymbolError("Failed to retrieve symbols from MT5")

            # Update cache
            self._symbols_cache = symbol_dict

            # Return appropriate format
            if include_metadata:
                return list(symbol_dict.values())
            return sorted(symbol_dict.keys())

        except MT5SymbolError:
            raise
        except Exception as e:
            logger.exception(f"Unexpected error fetching symbols: {e}")
            raise MT5SymbolError(f"Unexpected error fetching symbols: {e}")

    def get_current_price(self, symbol: str) -> Dict[str, Any]:
        """
        Get the current bid/ask price for a symbol.

        Args:
            symbol (str): The trading symbol (e.g., "EURUSD")

        Returns:
            Dict with bid, ask, and last prices
        """
        try:
            # Check if we're connected first
            if not self._connection.is_connected():
                logger.error(f"Cannot get price for {symbol}: Not connected to MT5")
                return {"error": "Not connected to MT5", "bid": None, "ask": None, "last": None}

            # Get the ticker info
            ticker = mt5.symbol_info_tick(symbol)
            if ticker is None:
                error_msg = mt5.last_error()
                logger.error(f"Failed to get price for {symbol}: {error_msg}")
                return {"error": f"Failed to get price: {error_msg}", "bid": None, "ask": None, "last": None}

            # Get the last price from the symbol info if available
            symbol_info = mt5.symbol_info(symbol)
            last_price = None
            if symbol_info and hasattr(symbol_info, 'last'):
                last_price = float(symbol_info.last) if not math.isnan(symbol_info.last) else None

            # Get bid and ask prices
            bid_price = float(ticker.bid) if not math.isnan(ticker.bid) else None
            ask_price = float(ticker.ask) if not math.isnan(ticker.ask) else None
            last_tick_price = float(ticker.last) if hasattr(ticker, 'last') and not math.isnan(ticker.last) else None

            # Use last_tick_price as fallback for last_price
            if last_price is None:
                last_price = last_tick_price

            # Log the prices for debugging
            logger.info(f"Prices for {symbol}: bid={bid_price}, ask={ask_price}, last={last_price}")

            return {
                "bid": bid_price,
                "ask": ask_price,
                "last": last_price
            }
        except Exception as e:
            logger.error(f"Error getting price for {symbol}: {e}")
            return {"error": f"Error getting price: {e}", "bid": None, "ask": None, "last": None}

    def get_symbol_info(self, symbol: str) -> Optional[SymbolInfo]:
        """
        Get detailed information about a specific symbol.

        Args:
            symbol: The trading symbol (e.g., 'EURUSD')

        Returns:
            SymbolInfo object if successful, None if symbol not found

        Raises:
            MT5ConnectionError: If not connected to MT5
        """
        if not self._connection.is_connected():
            logger.error(f"Cannot get symbol info for {symbol}: Not connected to MT5")
            raise MT5ConnectionError("Not connected to MT5")

        try:
            # Check cache first
            if self._symbols_cache and symbol in self._symbols_cache:
                current_time = datetime.now()
                if current_time - self._symbols_cache[symbol].last_updated < self._symbols_cache_duration:
                    logger.debug(f"Returning cached symbol info for {symbol}")
                    return self._symbols_cache[symbol]

            # Get symbol info from MT5
            logger.info(f"Fetching symbol info for {symbol}...")
            symbol_info_mt5 = mt5.symbol_info(symbol)

            if symbol_info_mt5 is None:
                logger.warning(f"Symbol {symbol} not found")
                return None

            # Create SymbolInfo object
            current_time = datetime.now()
            symbol_info = SymbolInfo(
                name=symbol_info_mt5.name,
                digits=symbol_info_mt5.digits,
                trade_mode=symbol_info_mt5.trade_mode,
                currency_base=symbol_info_mt5.currency_base,
                currency_profit=symbol_info_mt5.currency_profit,
                currency_margin=symbol_info_mt5.currency_margin,
                visible=symbol_info_mt5.visible,
                custom=symbol_info_mt5.custom,
                last_updated=current_time
            )

            # Update cache
            if self._symbols_cache is None:
                self._symbols_cache = {}
            self._symbols_cache[symbol] = symbol_info

            return symbol_info
        except Exception as e:
            logger.error(f"Error getting symbol info for {symbol}: {e}")
            return None

    def fetch_ohlc_data(self, symbol: str, timeframe: int,
                       start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """
        Fetch OHLC data for a given symbol and timeframe.

        Args:
            symbol: The trading symbol (e.g., 'EURUSD')
            timeframe: The timeframe in seconds (e.g., mt5.TIMEFRAME_M1 for 1 minute)
            start_time: The start time for the data range
            end_time: The end time for the data range

        Returns:
            A list of dictionaries containing OHLC data for the specified range.

        Raises:
            MT5ConnectionError: If not connected to MT5
            MT5SymbolError: If symbol retrieval fails
        """
        if not self._connection.is_connected():
            logger.error('Cannot fetch OHLC data: Not connected to MT5')
            raise MT5ConnectionError('Not connected to MT5')

        try:
            # Convert to timestamp integers for MT5
            start_timestamp = int(start_time.timestamp())
            end_timestamp = int(end_time.timestamp())
            logger.info(f'Fetching OHLC data for {symbol} on {timeframe} from {start_time} to {end_time}...')
            
            # For weekly timeframe, we might need to fetch a lot of data
            # MT5 has limitations on how much data can be fetched at once
            # We used to include MN1 here, but now treating it like standard timeframes
            is_long_timeframe = timeframe == mt5.TIMEFRAME_W1  # Only weekly is special now
            
            # For long timeframes, use chunking to fetch data in smaller pieces if needed
            if is_long_timeframe and (end_timestamp - start_timestamp) > 31536000:  # > 1 year in seconds
                logger.info(f'Large date range detected for {timeframe}, using chunked fetching strategy')
                ohlc_list = self._fetch_ohlc_data_chunked(symbol, timeframe, start_timestamp, end_timestamp)
            else:
                # Standard fetch for smaller timeframes or shorter date ranges
                ohlc_data = mt5.copy_rates_range(symbol, timeframe, start_timestamp, end_timestamp)
                
                if ohlc_data is None or len(ohlc_data) == 0:
                    error = mt5.last_error()
                    logger.error(f'Failed to fetch OHLC data: {error}')
                    
                    # If W1/MN1 initially failed, try chunking as fallback
                    if is_long_timeframe:
                        logger.info('Trying chunked fetching as fallback for long timeframe')
                        ohlc_list = self._fetch_ohlc_data_chunked(symbol, timeframe, start_timestamp, end_timestamp)
                    else:
                        raise MT5SymbolError(f'Failed to fetch OHLC data: {error}')
                else:
                    # Convert to list of dictionaries
                    ohlc_list = []
                    for rate in ohlc_data:
                        ohlc_list.append({
                            'time': datetime.fromtimestamp(rate['time']).isoformat(),
                            'open': float(rate['open']),
                            'high': float(rate['high']),
                            'low': float(rate['low']),
                            'close': float(rate['close']),
                            'tick_volume': int(rate['tick_volume']),
                            'spread': int(rate['spread']),
                            'real_volume': int(rate['real_volume'])
                        })

            logger.info(f'Successfully fetched {len(ohlc_list)} OHLC data points for {symbol} on {timeframe}')
            return ohlc_list
            
        except MT5SymbolError:
            raise
        except Exception as e:
            logger.exception('Unexpected error fetching OHLC data')
            raise MT5SymbolError(f'Unexpected error fetching OHLC data: {e}')
            
    def _fetch_ohlc_data_chunked(self, symbol: str, timeframe: int, 
                                start_timestamp: int, end_timestamp: int) -> List[Dict[str, Any]]:
        """
        Fetch OHLC data in smaller chunks to handle large historical requests.
        
        Args:
            symbol: The trading symbol (e.g., 'EURUSD')
            timeframe: The timeframe in seconds (e.g., mt5.TIMEFRAME_W1)
            start_timestamp: The start time as timestamp
            end_timestamp: The end time as timestamp
            
        Returns:
            A list of dictionaries containing OHLC data across the chunks.
        """
        # Maximum chunk size - approximately 1 year of data at a time
        # This helps avoid MT5 API limitations
        MAX_CHUNK_SIZE = 31536000  # 1 year in seconds
        
        all_ohlc_data = []
        current_start = start_timestamp
        chunk_count = 0
        
        while current_start < end_timestamp:
            chunk_count += 1
            # Calculate chunk end (either chunk size or end timestamp)
            chunk_end = min(current_start + MAX_CHUNK_SIZE, end_timestamp)
            
            logger.info(f'Fetching chunk {chunk_count}: {datetime.fromtimestamp(current_start)} to {datetime.fromtimestamp(chunk_end)}')
            
            # Fetch this chunk
            chunk_data = mt5.copy_rates_range(symbol, timeframe, current_start, chunk_end)
            
            if chunk_data is not None and len(chunk_data) > 0:
                # Process and store this chunk's data
                for rate in chunk_data:
                    all_ohlc_data.append({
                        'time': datetime.fromtimestamp(rate['time']).isoformat(),
                        'open': float(rate['open']),
                        'high': float(rate['high']),
                        'low': float(rate['low']),
                        'close': float(rate['close']),
                        'tick_volume': int(rate['tick_volume']),
                        'spread': int(rate['spread']),
                        'real_volume': int(rate['real_volume'])
                    })
                logger.info(f'Chunk {chunk_count} successfully added {len(chunk_data)} data points')
            else:
                error = mt5.last_error()
                logger.warning(f'No data in chunk {chunk_count} or error: {error}')
            
            # Move to next chunk
            current_start = chunk_end
        
        logger.info(f'Chunked fetching complete, collected {len(all_ohlc_data)} total data points')
        return all_ohlc_data
