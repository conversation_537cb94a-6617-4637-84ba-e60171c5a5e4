from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class T3Indicator(BaseIndicator):
    """T3 Moving Average (Tillson T3) indicator."""

    def __init__(self, period: int = 5, vfactor: float = 0.7, source: str = 'close'):
        """
        Initialize T3 Moving Average indicator.

        Args:
            period: The lookback period for the EMAs.
            vfactor: Volume factor (a value between 0 and 1).
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
        """
        super().__init__({
            'period': period,
            'vfactor': vfactor,
            'source': source
        })

    def _ema(self, series: pd.Series, period: int) -> pd.Series:
        """Helper function for EMA calculation."""
        return series.ewm(span=period, adjust=False).mean()

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate T3 Moving Average."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period']:
             return {'t3': np.array([])}

        period = self.params['period']
        vfactor = self.params['vfactor']
        source_col = self.params['source'].lower()

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Calculate EMAs
        ema1 = self._ema(source_data, period)
        ema2 = self._ema(ema1, period)
        ema3 = self._ema(ema2, period)
        ema4 = self._ema(ema3, period)
        ema5 = self._ema(ema4, period)
        ema6 = self._ema(ema5, period)

        # Calculate T3 coefficients
        c1 = -vfactor**3
        c2 = 3 * (vfactor**2 + vfactor**3)
        c3 = -6 * vfactor**2 - 3 * vfactor - 3 * vfactor**3
        c4 = 1 + 3 * vfactor + vfactor**3 + 3 * vfactor**2

        # Calculate T3
        t3_values = c1 * ema6 + c2 * ema5 + c3 * ema4 + c4 * ema3

        self._values = {
            't3': t3_values.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        if not 0 <= self.params['vfactor'] <= 1:
             raise ValueError("Volume Factor (vfactor) must be between 0 and 1")
        return True