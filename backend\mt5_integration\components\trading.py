"""
Trading component for MT5Integration.
"""

import logging
import time
import MetaTrader5 as mt5
from typing import Dict, List, Optional, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class TradingComponent:
    """
    Component for handling trading operations in MT5.
    """
    
    def __init__(self, connection_component):
        """
        Initialize the trading component.
        
        Args:
            connection_component: The connection component for MT5
        """
        self.connection = connection_component
    
    def place_market_order(self, symbol: str, order_type: int, volume: float,
                          sl: Optional[float] = None, tp: Optional[float] = None,
                          deviation: int = 10, magic: int = 0,
                          comment: str = "") -> Dict[str, Any]:
        """
        Place a market order.
        
        Args:
            symbol: Symbol name
            order_type: Order type (mt5.ORDER_TYPE_BUY or mt5.ORDER_TYPE_SELL)
            volume: Order volume (lot size)
            sl: Stop loss price
            tp: Take profit price
            deviation: Maximum price deviation
            magic: Magic number (identifier)
            comment: Order comment
            
        Returns:
            Dict with order result
        """
        try:
            # Check connection
            if not self.connection.is_connected():
                return {
                    "success": False,
                    "message": "Not connected to MT5"
                }
            
            # Get symbol info
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                return {
                    "success": False,
                    "message": f"Symbol {symbol} not found"
                }
            
            # Ensure symbol is selected
            if not symbol_info.visible:
                if not mt5.symbol_select(symbol, True):
                    return {
                        "success": False,
                        "message": f"Failed to select symbol {symbol}"
                    }
            
            # Get current price
            if order_type == mt5.ORDER_TYPE_BUY:
                price = mt5.symbol_info_tick(symbol).ask
            else:
                price = mt5.symbol_info_tick(symbol).bid
            
            # Prepare order request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": volume,
                "type": order_type,
                "price": price,
                "deviation": deviation,
                "magic": magic,
                "comment": comment,
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_FOK
            }
            
            # Add SL/TP if provided
            if sl is not None:
                request["sl"] = sl
            if tp is not None:
                request["tp"] = tp
            
            # Send order
            result = mt5.order_send(request)
            if result is None:
                error_code = mt5.last_error()
                return {
                    "success": False,
                    "message": f"Order failed with error code: {error_code}"
                }
            
            # Check result
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                return {
                    "success": False,
                    "message": f"Order failed with retcode: {result.retcode}",
                    "retcode": result.retcode,
                    "result": result._asdict()
                }
            
            # Success
            return {
                "success": True,
                "message": "Order placed successfully",
                "order": result._asdict()
            }
        except Exception as e:
            logger.exception(f"Exception placing market order: {str(e)}")
            return {
                "success": False,
                "message": f"Exception placing market order: {str(e)}"
            }
    
    def get_open_positions(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """
        Get open positions.
        
        Args:
            symbol: Optional symbol filter
            
        Returns:
            Dict with positions
        """
        try:
            # Check connection
            if not self.connection.is_connected():
                return {
                    "success": False,
                    "message": "Not connected to MT5"
                }
            
            # Get positions
            positions = mt5.positions_get(symbol=symbol) if symbol else mt5.positions_get()
            if positions is None:
                error_code = mt5.last_error()
                if error_code == 0:  # No error, just no positions
                    return {
                        "success": True,
                        "positions": []
                    }
                else:
                    return {
                        "success": False,
                        "message": f"Failed to get positions. MT5 error code: {error_code}"
                    }
            
            # Convert positions to dicts
            positions_list = []
            for position in positions:
                position_dict = position._asdict()
                # Convert time fields to datetime strings
                position_dict["time"] = datetime.fromtimestamp(position_dict["time"]).strftime('%Y-%m-%d %H:%M:%S')
                position_dict["time_update"] = datetime.fromtimestamp(position_dict["time_update"]).strftime('%Y-%m-%d %H:%M:%S')
                positions_list.append(position_dict)
            
            return {
                "success": True,
                "positions": positions_list
            }
        except Exception as e:
            logger.exception(f"Exception getting open positions: {str(e)}")
            return {
                "success": False,
                "message": f"Exception getting open positions: {str(e)}"
            }
    
    def calculate_lot_size(self, symbol: str, risk_percent: float, sl_points: int) -> Dict[str, Any]:
        """
        Calculate lot size based on risk percentage and stop loss points.
        
        Args:
            symbol: Symbol name
            risk_percent: Risk percentage (0-100)
            sl_points: Stop loss in points
            
        Returns:
            Dict with calculated lot size
        """
        try:
            # Check connection
            if not self.connection.is_connected():
                return {
                    "success": False,
                    "message": "Not connected to MT5"
                }
            
            # Get account info
            account_info = mt5.account_info()
            if account_info is None:
                return {
                    "success": False,
                    "message": "Failed to get account info"
                }
            
            # Get symbol info
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                return {
                    "success": False,
                    "message": f"Symbol {symbol} not found"
                }
            
            # Ensure symbol is selected
            if not symbol_info.visible:
                if not mt5.symbol_select(symbol, True):
                    return {
                        "success": False,
                        "message": f"Failed to select symbol {symbol}"
                    }
            
            # Calculate risk amount
            balance = account_info.balance
            risk_amount = balance * (risk_percent / 100)
            
            # Calculate point value
            point_value = symbol_info.trade_tick_value * (symbol_info.point / symbol_info.trade_tick_size)
            
            # Calculate lot size
            if sl_points > 0 and point_value > 0:
                lot_size = risk_amount / (sl_points * point_value)
            else:
                lot_size = 0.01  # Default minimum lot size
            
            # Round to nearest valid lot size
            lot_step = symbol_info.volume_step
            lot_size = round(lot_size / lot_step) * lot_step
            
            # Ensure lot size is within limits
            lot_size = max(symbol_info.volume_min, min(symbol_info.volume_max, lot_size))
            
            return {
                "success": True,
                "lot_size": lot_size,
                "risk_amount": risk_amount,
                "balance": balance
            }
        except Exception as e:
            logger.exception(f"Exception calculating lot size: {str(e)}")
            return {
                "success": False,
                "message": f"Exception calculating lot size: {str(e)}"
            }
    
    def close_position(self, ticket: int) -> Dict[str, Any]:
        """
        Close an open position.
        
        Args:
            ticket: Position ticket
            
        Returns:
            Dict with close result
        """
        try:
            # Check connection
            if not self.connection.is_connected():
                return {
                    "success": False,
                    "message": "Not connected to MT5"
                }
            
            # Get position
            position = mt5.positions_get(ticket=ticket)
            if position is None or len(position) == 0:
                return {
                    "success": False,
                    "message": f"Position {ticket} not found"
                }
            
            position = position[0]
            
            # Determine order type for closing
            close_type = mt5.ORDER_TYPE_SELL if position.type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY
            
            # Get price for closing
            symbol = position.symbol
            price = mt5.symbol_info_tick(symbol).ask if close_type == mt5.ORDER_TYPE_BUY else mt5.symbol_info_tick(symbol).bid
            
            # Prepare close request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": position.volume,
                "type": close_type,
                "position": ticket,
                "price": price,
                "deviation": 10,
                "magic": position.magic,
                "comment": f"Close position #{ticket}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_FOK
            }
            
            # Send close request
            result = mt5.order_send(request)
            if result is None:
                error_code = mt5.last_error()
                return {
                    "success": False,
                    "message": f"Close failed with error code: {error_code}"
                }
            
            # Check result
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                return {
                    "success": False,
                    "message": f"Close failed with retcode: {result.retcode}",
                    "retcode": result.retcode,
                    "result": result._asdict()
                }
            
            # Success
            return {
                "success": True,
                "message": f"Position {ticket} closed successfully",
                "result": result._asdict()
            }
        except Exception as e:
            logger.exception(f"Exception closing position: {str(e)}")
            return {
                "success": False,
                "message": f"Exception closing position: {str(e)}"
            }
    
    def modify_position(self, ticket: int, sl: Optional[float] = None, tp: Optional[float] = None, 
                         retry_count: int = 3, retry_delay: float = 0.5) -> Dict[str, Any]:
        """
        Modify stop loss and/or take profit of an open position.
        
        Args:
            ticket: Position ticket
            sl: New stop loss price (None to keep current)
            tp: New take profit price (None to keep current)
            retry_count: Number of retries if operation fails
            retry_delay: Delay between retries in seconds
            
        Returns:
            Dict with modify result
        """
        for attempt in range(retry_count):
            try:
                # Check connection
                if not self.connection.is_connected():
                    return {
                        "success": False,
                        "message": "Not connected to MT5"
                    }
                
                # Get position
                position = mt5.positions_get(ticket=ticket)
                if position is None or len(position) == 0:
                    return {
                        "success": False,
                        "message": f"Position {ticket} not found"
                    }
                
                position = position[0]
                
                # If SL or TP not provided, use existing values
                if sl is None:
                    sl = position.sl
                
                if tp is None:
                    tp = position.tp
                
                # Prepare modify request
                request = {
                    "action": mt5.TRADE_ACTION_SLTP,
                    "symbol": position.symbol,
                    "position": ticket,
                    "sl": sl,
                    "tp": tp
                }
                
                logger.info(f"Modifying position {ticket}: SL={sl}, TP={tp}")
                
                # Send modify request
                result = mt5.order_send(request)
                if result is None:
                    error_code = mt5.last_error()
                    logger.error(f"Position modify failed with error code: {error_code}")
                    if attempt < retry_count - 1:
                        logger.info(f"Retrying... (attempt {attempt+1}/{retry_count})")
                        time.sleep(retry_delay)
                        continue
                    return {
                        "success": False,
                        "message": f"Modify failed with error code: {error_code}"
                    }
                
                # Check result
                if result.retcode != mt5.TRADE_RETCODE_DONE:
                    logger.error(f"Position modify failed with retcode: {result.retcode}")
                    if attempt < retry_count - 1 and result.retcode in [10004, 10018, 10019, 10021]:
                        # Retry for server busy, market closed, not enough money, or invalid volume
                        logger.info(f"Retrying... (attempt {attempt+1}/{retry_count})")
                        time.sleep(retry_delay)
                        continue
                    return {
                        "success": False,
                        "message": f"Modify failed with retcode: {result.retcode}",
                        "retcode": result.retcode,
                        "result": result._asdict()
                    }
                
                # Success
                logger.info(f"Position {ticket} modified successfully")
                return {
                    "success": True,
                    "message": f"Position {ticket} modified successfully",
                    "sl": sl,
                    "tp": tp
                }
            except Exception as e:
                logger.exception(f"Exception modifying position: {str(e)}")
                if attempt < retry_count - 1:
                    logger.info(f"Retrying after error... (attempt {attempt+1}/{retry_count})")
                    time.sleep(retry_delay)
                    continue
                return {
                    "success": False,
                    "message": f"Exception modifying position: {str(e)}"
                }
