from typing import Dict, Any
import numpy as np
import pandas as pd
from backend.technical.base_indicator import BaseIndicator

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

class ATRIndicator(BaseIndicator):
    """Average True Range (ATR) indicator."""

    def __init__(self, period: int = 14, ma_type: str = 'ema'):
        """
        Initialize ATR indicator.

        Args:
            period: The period for calculating ATR (default: 14)
            ma_type: The type of moving average to use ('sma' or 'ema')
        """
        super().__init__({
            'period': period,
            'ma_type': ma_type
        })
        self.name = 'ATR'

    def calculate(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """Calculate ATR values and return them as pandas Series."""
        try:
            period = self.params['period']
            if data.empty or len(data) < period:
                # Return empty Series with the original index if possible, else empty Series
                index = data.index if not data.empty else None
                return {
                    'atr': pd.Series(dtype=float, index=index, name='atr'),
                    'tr': pd.Series(dtype=float, index=index, name='tr')
                }

            # Calculate True Range
            high_low = data['high'] - data['low']
            high_close = np.abs(data['high'] - data['close'].shift(1))
            low_close = np.abs(data['low'] - data['close'].shift(1))

            # True Range is the greatest of:
            # 1. Current High - Current Low
            # 2. |Current High - Previous Close|
            # 3. |Current Low - Previous Close|
            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = ranges.max(axis=1).rename('tr') # Rename the Series

            # Calculate ATR based on specified moving average type
            if self.params['ma_type'].lower() == 'sma':
                atr = true_range.rolling(window=period).mean().rename('atr') # Rename the Series
            else:  # 'ema'
                atr = true_range.ewm(span=period, adjust=False).mean().rename('atr') # Rename the Series

            # Ensure the first 'period-1' ATR values are NaN if using SMA
            if self.params['ma_type'].lower() == 'sma':
                 atr.iloc[:period-1] = np.nan

            # Ensure the first TR value is NaN (due to shift(1))
            if not true_range.empty:
                true_range.iloc[0] = np.nan

            return {
                'atr': atr,
                'tr': true_range
            }

        except Exception as e:
            print(f"Error calculating ATR: {str(e)}")
            # Return empty Series in case of error
            index = data.index if not data.empty else None
            return {
                 'atr': pd.Series(dtype=float, index=index, name='atr'),
                 'tr': pd.Series(dtype=float, index=index, name='tr')
            }

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_ma_types = ['sma', 'ema']
        if self.params['ma_type'].lower() not in valid_ma_types:
            raise ValueError(f"MA type must be one of {valid_ma_types}")
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        return True
