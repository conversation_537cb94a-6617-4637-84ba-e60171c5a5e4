import React from 'react';
import TrendArrow from './TrendArrow';
import { safeToFixed, renderIndicatorValue } from '../../utils/analysisUtils';
import '../../styles/BentoComponents.css';
import '../../styles/AnalysisBento.css';

/**
 * MovingAveragesCard component displays moving averages analysis
 *
 * @param {Object} movingAverages - The moving averages data
 * @returns {JSX.Element} - The rendered moving averages card
 */
const MovingAveragesCard = ({ movingAverages }) => {
  const direction = movingAverages?.ema_alignment?.includes('Bullish')
    ? 'BUY'
    : movingAverages?.ema_alignment?.includes('Bearish')
      ? 'SELL'
      : 'NEUTRAL';

  return (
    <div className="bento-card bento-span-4">
      <div className="bento-card-header">
        <h3 className="bento-card-title">Moving Averages</h3>
        <div className="signal-with-arrow">
          {renderIndicatorValue(direction, 'signal')}
          <TrendArrow direction={direction} />
        </div>
      </div>
      <div className="bento-card-content analysis-indicator-details">
        <p><span>EMA(20):</span> <span>{safeToFixed(movingAverages?.ema20, 5)}</span></p>
        <p><span>EMA(50):</span> <span>{safeToFixed(movingAverages?.ema50, 5)}</span></p>
        <p><span>EMA(200):</span> <span>{safeToFixed(movingAverages?.ema200, 5)}</span></p>
      </div>
    </div>
  );
};

export default MovingAveragesCard;
