from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class DojiPatternIndicator(BaseIndicator):
    """Doji candlestick pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - body_ratio: Parameter description (default: 0.1)
        """
        default_params = {
            "body_ratio": 0.1,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)

    
    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        open_price = df['open'].values
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        body_ratio = self.params['body_ratio']
        
        # Calculate body size and total range
        body_size = np.abs(close - open_price)
        total_range = high - low
        
        # Calculate upper and lower shadows
        upper_shadow = high - np.maximum(open_price, close)
        lower_shadow = np.minimum(open_price, close) - low
        
        # Identify doji patterns
        is_doji = body_size <= (total_range * body_ratio)
        
        # Classify doji types
        doji_type = np.zeros_like(close)
        doji_type[is_doji] = 1  # Regular doji
        
        # Long-legged doji (long shadows)
        long_shadow = (upper_shadow + lower_shadow) > (total_range * 0.6)
        doji_type[is_doji & long_shadow] = 2
        
        # Dragonfly doji (long lower shadow)
        dragonfly = lower_shadow > (total_range * 0.6)
        doji_type[is_doji & dragonfly] = 3
        
        # Gravestone doji (long upper shadow)
        gravestone = upper_shadow > (total_range * 0.6)
        doji_type[is_doji & gravestone] = 4
        
        # Calculate pattern strength
        strength = np.zeros_like(close)
        strength[is_doji] = 1 - (body_size / total_range)
        
        # Calculate trend context
        trend = np.zeros_like(close)
        trend[close > open_price] = 1
        trend[close < open_price] = -1
        
        # Calculate pattern reliability
        reliability = np.zeros_like(close)
        for i in range(1, len(close)):
            if is_doji[i]:
                # Check if price moved in the direction of the previous trend
                if trend[i-1] != 0:
                    future_return = (close[i+1] - close[i]) / close[i] if i < len(close)-1 else 0
                    if (trend[i-1] > 0 and future_return > 0) or (trend[i-1] < 0 and future_return < 0):
                        reliability[i] = 1
                    elif (trend[i-1] > 0 and future_return < 0) or (trend[i-1] < 0 and future_return > 0):
                        reliability[i] = -1
        
        return {
            'is_doji': is_doji.astype(int),
            'doji_type': doji_type,
            'strength': strength,
            'trend': trend,
            'reliability': reliability,
            'body_size': body_size,
            'upper_shadow': upper_shadow,
            'lower_shadow': lower_shadow,
            'total_range': total_range
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if not 0 < self.params['body_ratio'] < 1:
            raise ValueError("Body ratio must be between 0 and 1")
        return True 