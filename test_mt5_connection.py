import MetaTrader5 as mt5
import sys

def test_mt5_connection():
    print("Testing MT5 connection...")
    
    # Initialize MT5
    if not mt5.initialize():
        print(f"Failed to initialize MT5: {mt5.last_error()}")
        return False
    
    print("MT5 initialized successfully.")
    
    # Check if MT5 is connected
    terminal_info = mt5.terminal_info()
    if terminal_info is None:
        print("Failed to get terminal info.")
        mt5.shutdown()
        return False
    
    print(f"Terminal info: {terminal_info}")
    print(f"Connected: {terminal_info.connected}")
    
    # Try to login
    if not mt5.login(*********, "{2Rfj>0D", "FBS-Demo"):
        print(f"Failed to login: {mt5.last_error()}")
        mt5.shutdown()
        return False
    
    print("Login successful.")
    
    # Get account info
    account_info = mt5.account_info()
    if account_info is None:
        print("Failed to get account info.")
        mt5.shutdown()
        return False
    
    print(f"Account info: {account_info}")
    
    # Shutdown MT5
    mt5.shutdown()
    print("MT5 shutdown successfully.")
    
    return True

if __name__ == "__main__":
    success = test_mt5_connection()
    sys.exit(0 if success else 1)
