from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class VPTIndicator(BaseIndicator):
    """Volume Price Trend (VPT) indicator."""

    def __init__(self):
        """Initialize Volume Price Trend indicator."""
        super().__init__({}) # No parameters

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Volume Price Trend."""
        df = data.to_dataframe()
        if df.empty or 'volume' not in df.columns:
             return {'vpt': np.array([])}

        close = df['close']
        volume = df['volume']

        # Calculate percentage change in closing price
        close_pct_change = close.pct_change()

        # Calculate VPT
        # VPT = Previous VPT + (Volume * %Change in Close)
        vpt_values = (volume * close_pct_change).cumsum()
        vpt_values = vpt_values.fillna(0) # Fill initial NaN

        self._values = {
            'vpt': vpt_values.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        # No parameters to validate
        return True