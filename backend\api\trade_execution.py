from flask import Blueprint, request, jsonify, current_app
import logging
import os
import json
from datetime import datetime, timedelta
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from mt5_integration import MT5Integration
import MetaTrader5 as mt5

# Create a blueprint for trade execution
trade_bp = Blueprint('trade_execution', __name__)
logger = logging.getLogger(__name__)

@trade_bp.route('/execute', methods=['POST'])
@trade_bp.route('/place_order', methods=['POST'])  # Keep old endpoint for backward compatibility
def place_order():
    """
    Place a new order via MT5.

    Request body should contain:
    {
        "symbol": "EURUSD",
        "order_type": "BUY" or "SELL",
        "volume": 0.01,
        "price": 0.0 (for market orders use current price),
        "sl": 0.0 (stop loss price),
        "tp": 0.0 (take profit price),
        "type_time": "GTC" (Good Till Cancel),
        "type_filling": "FOK" (Fill or Kill),
        "deviation": 10 (price deviation in points),
        "magic": 123456 (order magic number),
        "comment": "order comment"
    }
    """
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get request data
        req_data = request.get_json()
        if not req_data:
            return jsonify({'error': 'No order data provided'}), 400

        # Validate required fields
        required_fields = ['symbol', 'order_type', 'volume']
        for field in required_fields:
            if field not in req_data:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        # Extract order parameters
        symbol = req_data.get('symbol')
        order_type = req_data.get('order_type')
        volume = float(req_data.get('volume'))
        price = float(req_data.get('price', 0.0))
        sl = float(req_data.get('sl', 0.0))
        tp = float(req_data.get('tp', 0.0))
        type_time = req_data.get('type_time', 'GTC')
        type_filling = req_data.get('type_filling', 'FOK')
        deviation = int(req_data.get('deviation', 10))
        magic = int(req_data.get('magic', 123456))
        comment = req_data.get('comment', 'GarudaAlgo Order')

        # Map common order types to MT5 order types
        order_types = {
            'BUY': mt5.ORDER_TYPE_BUY,
            'SELL': mt5.ORDER_TYPE_SELL,
            'BUY_LIMIT': mt5.ORDER_TYPE_BUY_LIMIT,
            'SELL_LIMIT': mt5.ORDER_TYPE_SELL_LIMIT,
            'BUY_STOP': mt5.ORDER_TYPE_BUY_STOP,
            'SELL_STOP': mt5.ORDER_TYPE_SELL_STOP
        }

        time_types = {
            'GTC': mt5.ORDER_TIME_GTC,
            'DAY': mt5.ORDER_TIME_DAY,
            'SPECIFIED': mt5.ORDER_TIME_SPECIFIED
        }

        filling_types = {
            'FOK': mt5.ORDER_FILLING_FOK,
            'IOC': mt5.ORDER_FILLING_IOC,
            'RETURN': mt5.ORDER_FILLING_RETURN
        }

        # Check if order type is valid
        if order_type not in order_types:
            return jsonify({'error': f'Invalid order type: {order_type}. Valid types are: {", ".join(order_types.keys())}'}), 400

        # Get MT5 order type
        mt5_order_type = order_types[order_type]

        # Get MT5 time type
        mt5_time_type = time_types.get(type_time, mt5.ORDER_TIME_GTC)

        # Get MT5 filling type
        mt5_filling_type = filling_types.get(type_filling, mt5.ORDER_FILLING_FOK)

        # For market orders, get the current price if not specified
        if mt5_order_type in [mt5.ORDER_TYPE_BUY, mt5.ORDER_TYPE_SELL] and price == 0.0:
            current_price_data = mt5_conn.get_current_price(symbol)

            if not current_price_data:
                return jsonify({'error': f'Could not get current price for {symbol}'}), 500

            if mt5_order_type == mt5.ORDER_TYPE_BUY:
                price = current_price_data.get('ask', 0.0)
                if price == 0.0:
                    return jsonify({'error': f'Invalid ask price for {symbol}'}), 500
            else:
                price = current_price_data.get('bid', 0.0)
                if price == 0.0:
                    return jsonify({'error': f'Invalid bid price for {symbol}'}), 500

        # Prepare order request dictionary
        order_request = {
            'action': mt5.TRADE_ACTION_DEAL,
            'symbol': symbol,
            'volume': volume,
            'type': mt5_order_type,
            'price': price,
            'sl': sl,
            'tp': tp,
            'deviation': deviation,
            'magic': magic,
            'comment': comment,
            'type_time': mt5_time_type,
            'type_filling': mt5_filling_type
        }

        # Ensure symbol is available for trading
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            return jsonify({'error': f'Symbol {symbol} not found'}), 400

        if not symbol_info.visible:
            # Try to enable symbol for trading
            if not mt5.symbol_select(symbol, True):
                return jsonify({'error': f'Symbol {symbol} not available for trading'}), 400

        # Check if we have enough margin for the trade
        account_info = mt5.account_info()
        if account_info is None:
            return jsonify({'error': 'Could not get account information'}), 500

        # Log account info for debugging
        logger.info(f"Account info: Balance={account_info.balance}, Margin={account_info.margin}, Free Margin={account_info.margin_free}")

        # Calculate required margin for this trade
        margin_check = mt5.order_calc_margin(
            mt5_order_type,  # Order type
            symbol,          # Symbol
            volume,          # Volume
            price            # Price
        )

        if margin_check is None:
            error_code = mt5.last_error()
            return jsonify({'error': f'Failed to calculate margin, error code: {error_code}'}), 400

        logger.info(f"Required margin for trade: {margin_check}, Free margin: {account_info.margin_free}")

        # Check if we have enough free margin
        if margin_check > account_info.margin_free:
            return jsonify({'error': 'Not enough free margin to place this trade'}), 400

        logger.info(f"Placing order: {json.dumps(order_request, default=str)}")

        # Log the order details for debugging
        logger.info(f"Order details: Symbol={symbol}, Type={order_type}, Volume={volume}, Price={price}, SL={sl}, TP={tp}")

        # Send order to MT5
        result = mt5.order_send(order_request)
        if result is None:
            error_code = mt5.last_error()
            error_message = f"MT5 order failed with error code: {error_code}"
            logger.error(error_message)
            return jsonify({'error': error_message}), 500

        # Log the raw result for debugging
        logger.info(f"MT5 order_send raw result: {result}")

        # Check order execution result
        logger.info(f"Order result retcode: {result.retcode}")

        # List of success codes
        success_codes = [mt5.TRADE_RETCODE_DONE, mt5.TRADE_RETCODE_PLACED, mt5.TRADE_RETCODE_DONE_PARTIAL]

        if result.retcode not in success_codes:
            error_message = f"Order execution failed with retcode: {result.retcode}"
            logger.error(error_message)
            return jsonify({
                'error': error_message,
                'retcode': result.retcode,
                'result': result._asdict()
            }), 400

        # Order executed successfully
        logger.info(f"Order executed successfully: {result.order}")

        # Return order information
        return jsonify({
            'success': True,
            'order': result.order,
            'volume': result.volume,
            'price': result.price,
            'comment': comment,
            'request': order_request
        })

    except Exception as e:
        logger.exception(f"Error placing order: {str(e)}")
        return jsonify({'error': str(e)}), 500


@trade_bp.route('/positions', methods=['GET'])
def get_positions():
    """Get all open positions"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Optional symbol filter
        symbol = request.args.get('symbol', None)

        # Get positions from MT5
        positions = mt5.positions_get(symbol=symbol) if symbol else mt5.positions_get()

        if positions is None:
            error_code = mt5.last_error()
            if error_code == 0:  # No error, just no positions
                return jsonify([])
            else:
                error_message = f"Failed to get positions. MT5 error code: {error_code}"
                logger.error(error_message)
                return jsonify({'error': error_message}), 500

        # Convert positions to JSON serializable format
        positions_list = []
        for position in positions:
            position_dict = position._asdict()
            # Convert time values to ISO format
            position_dict['time'] = datetime.fromtimestamp(position_dict['time']).isoformat()
            position_dict['time_update'] = datetime.fromtimestamp(position_dict['time_update']).isoformat()
            positions_list.append(position_dict)

        return jsonify(positions_list)

    except Exception as e:
        logger.exception(f"Error getting positions: {str(e)}")
        return jsonify({'error': str(e)}), 500


@trade_bp.route('/position/close', methods=['POST'])
@trade_bp.route('/close_position/<int:position_id>', methods=['POST'])  # Add endpoint for frontend
def close_position(position_id=None):
    """Close an open position"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get position_id from URL parameter or request body
        if position_id is None:
            # If not in URL, try to get from request body
            req_data = request.get_json()
            if not req_data:
                return jsonify({'error': 'No position data provided'}), 400

            position_id = req_data.get('position_id')
            if not position_id:
                return jsonify({'error': 'position_id is required'}), 400
        else:
            # Position ID was provided in URL
            logger.info(f"Closing position from URL parameter: {position_id}")

        # Get position info
        position = mt5.positions_get(ticket=position_id)
        if not position:
            return jsonify({'error': f'Position {position_id} not found'}), 404

        # Position found, get its details
        position = position[0]
        symbol = position.symbol
        volume = position.volume
        position_type = position.type

        # Determine the order type for closing (opposite of position type)
        close_type = mt5.ORDER_TYPE_SELL if position_type == mt5.POSITION_TYPE_BUY else mt5.ORDER_TYPE_BUY

        # Get current price for the symbol
        current_price_data = mt5_conn.get_current_price(symbol)
        if not current_price_data:
            return jsonify({'error': f'Could not get current price for {symbol}'}), 500

        # Use appropriate price for closing
        price = current_price_data.get('bid', 0) if close_type == mt5.ORDER_TYPE_SELL else current_price_data.get('ask', 0)
        if price == 0:
            return jsonify({'error': f'Invalid price for closing {symbol} position'}), 500

        # Create close request
        close_request = {
            'action': mt5.TRADE_ACTION_DEAL,
            'symbol': symbol,
            'volume': volume,
            'type': close_type,
            'position': position_id,
            'price': price,
            'deviation': 10,
            'magic': 123456,
            'comment': 'GarudaAlgo Close Position',
            'type_time': mt5.ORDER_TIME_GTC,
            'type_filling': mt5.ORDER_FILLING_FOK
        }

        logger.info(f"Closing position: {json.dumps(close_request, default=str)}")

        # Send close request to MT5
        result = mt5.order_send(close_request)
        if result is None:
            error_code = mt5.last_error()
            error_message = f"Position close failed with error code: {error_code}"
            logger.error(error_message)
            return jsonify({'error': error_message}), 500

        # Check close execution result
        if result.retcode != mt5.TRADE_RETCODE_DONE:
            error_message = f"Position close failed with retcode: {result.retcode}"
            logger.error(error_message)
            return jsonify({
                'error': error_message,
                'retcode': result.retcode,
                'result': result._asdict()
            }), 400

        # Position closed successfully
        logger.info(f"Position {position_id} closed successfully")

        # Return close information
        return jsonify({
            'success': True,
            'position_id': position_id,
            'symbol': symbol,
            'volume': volume,
            'close_price': result.price,
            'deal': result.deal
        })

    except Exception as e:
        logger.exception(f"Error closing position: {str(e)}")
        return jsonify({'error': str(e)}), 500


@trade_bp.route('/position/modify', methods=['POST'])
def modify_position():
    """Modify stop loss and/or take profit of an open position"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get request data
        req_data = request.get_json()
        if not req_data:
            return jsonify({'error': 'No position data provided'}), 400

        # Check required fields
        position_id = req_data.get('position_id')
        if not position_id:
            return jsonify({'error': 'position_id is required'}), 400

        # Get SL/TP values
        sl = req_data.get('sl')
        tp = req_data.get('tp')

        # At least one of SL or TP must be provided
        if sl is None and tp is None:
            return jsonify({'error': 'At least one of sl or tp must be provided'}), 400

        # Get position info
        position = mt5.positions_get(ticket=position_id)
        if not position:
            return jsonify({'error': f'Position {position_id} not found'}), 404

        # Position found, get its details
        position = position[0]
        symbol = position.symbol

        # If SL or TP not provided, use existing values
        if sl is None:
            sl = position.sl
        else:
            sl = float(sl)

        if tp is None:
            tp = position.tp
        else:
            tp = float(tp)

        # Create modify request
        modify_request = {
            'action': mt5.TRADE_ACTION_SLTP,
            'symbol': symbol,
            'position': position_id,
            'sl': sl,
            'tp': tp
        }

        logger.info(f"Modifying position: {json.dumps(modify_request, default=str)}")

        # Send modify request to MT5
        result = mt5.order_send(modify_request)
        if result is None:
            error_code = mt5.last_error()
            error_message = f"Position modify failed with error code: {error_code}"
            logger.error(error_message)
            return jsonify({'error': error_message}), 500

        # Check modify execution result
        if result.retcode != mt5.TRADE_RETCODE_DONE:
            error_message = f"Position modify failed with retcode: {result.retcode}"
            logger.error(error_message)
            return jsonify({
                'error': error_message,
                'retcode': result.retcode,
                'result': result._asdict()
            }), 400

        # Position modified successfully
        logger.info(f"Position {position_id} modified successfully")

        # Return modify information
        return jsonify({
            'success': True,
            'position_id': position_id,
            'symbol': symbol,
            'sl': sl,
            'tp': tp
        })

    except Exception as e:
        logger.exception(f"Error modifying position: {str(e)}")
        return jsonify({'error': str(e)}), 500


@trade_bp.route('/cancel_order/<int:order_id>', methods=['POST'])
def cancel_order(order_id):
    """Cancel a pending order"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get order info
        order = mt5.orders_get(ticket=order_id)
        if not order:
            return jsonify({'error': f'Order {order_id} not found'}), 404

        # Order found, get its details
        order = order[0]
        symbol = order.symbol

        # Create cancel request
        cancel_request = {
            'action': mt5.TRADE_ACTION_REMOVE,
            'symbol': symbol,
            'order': order_id
        }

        logger.info(f"Canceling order: {json.dumps(cancel_request, default=str)}")

        # Send cancel request to MT5
        result = mt5.order_send(cancel_request)
        if result is None:
            error_code = mt5.last_error()
            error_message = f"Order cancel failed with error code: {error_code}"
            logger.error(error_message)
            return jsonify({'error': error_message}), 500

        # Check cancel execution result
        if result.retcode != mt5.TRADE_RETCODE_DONE:
            error_message = f"Order cancel failed with retcode: {result.retcode}"
            logger.error(error_message)
            return jsonify({
                'error': error_message,
                'retcode': result.retcode,
                'result': result._asdict()
            }), 400

        # Order canceled successfully
        logger.info(f"Order {order_id} canceled successfully")

        # Return cancel information
        return jsonify({
            'success': True,
            'order_id': order_id,
            'symbol': symbol
        })

    except Exception as e:
        logger.exception(f"Error canceling order: {str(e)}")
        return jsonify({'error': str(e)}), 500


@trade_bp.route('/orders', methods=['GET'])
def get_orders():
    """Get all pending orders"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Optional symbol filter
        symbol = request.args.get('symbol', None)

        # Get orders from MT5
        orders = mt5.orders_get(symbol=symbol) if symbol else mt5.orders_get()

        if orders is None:
            error_code = mt5.last_error()
            if error_code == 0:  # No error, just no orders
                return jsonify([])
            else:
                error_message = f"Failed to get orders. MT5 error code: {error_code}"
                logger.error(error_message)
                return jsonify({'error': error_message}), 500

        # Convert orders to JSON serializable format
        orders_list = []
        for order in orders:
            order_dict = order._asdict()
            # Convert time values to ISO format
            order_dict['time_setup'] = datetime.fromtimestamp(order_dict['time_setup']).isoformat()
            order_dict['time_expiration'] = datetime.fromtimestamp(order_dict['time_expiration']).isoformat() if order_dict['time_expiration'] > 0 else None
            orders_list.append(order_dict)

        return jsonify(orders_list)

    except Exception as e:
        logger.exception(f"Error getting orders: {str(e)}")
        return jsonify({'error': str(e)}), 500


@trade_bp.route('/history', methods=['GET'])
def get_trade_history():
    """Get closed trades (history) and summary statistics"""
    try:
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get parameters
        period = request.args.get('period', 'week')
        symbol = request.args.get('symbol', None)
        if symbol == 'all':
            symbol = None
        
        # Calculate time period
        now = datetime.now()
        if period == 'day':
            start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        elif period == 'week':
            start = now - timedelta(days=now.weekday())
            start = start.replace(hour=0, minute=0, second=0, microsecond=0)
        elif period == 'month':
            start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        elif period == 'quarter':
            month = (now.month - 1) // 3 * 3 + 1
            start = now.replace(month=month, day=1, hour=0, minute=0, second=0, microsecond=0)
        elif period == 'year':
            start = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
        else:  # 'all'
            # Using a more recent "very old" date to avoid potential OSError with 1970-01-01
            start = datetime(2000, 1, 1, 0, 0, 0) 
        end = now
        
        # Fetch closed deals
        logger.info(f"Fetching history deals from {start.isoformat()} to {end.isoformat()} for symbol: {symbol if symbol else 'ALL'}")
        deals = mt5.history_deals_get(start, end, symbol=symbol) if symbol else mt5.history_deals_get(start, end)
        
        if deals is None:
            error_code, error_description = mt5.last_error()
            logger.error(f'Failed to fetch trade history from MT5. Error code: {error_code}, Description: {error_description}')
            return jsonify({'error': f'Failed to fetch trade history from MT5: {error_description} (Code: {error_code})'}), 500
        
        logger.info(f"Retrieved {len(deals)} deals from MT5.")
        trades = []
        
        if not deals: # If deals is an empty tuple or list
            logger.info("No deals found for the specified period/symbol.")
            # Proceed to return empty history and zeroed stats
        else: # Process deals if the list is not empty
            logger.info(f"Processing {len(deals)} deals...")
            for i, deal_obj in enumerate(deals):
                # Ensure deal_obj itself is not None, though history_deals_get usually returns tuple of objects or None
                if deal_obj is None:
                    logger.warning(f"Encountered a None deal object at index {i}. Skipping.")
                    continue
                
                d = deal_obj._asdict()
                try:
                    deal_type = d.get('type')
                    # Only include actual trading deals
                    if deal_type not in (mt5.DEAL_TYPE_BUY, mt5.DEAL_TYPE_SELL):
                        continue
                    # Only include exit deals to avoid zero-profit entry records
                    entry_type = d.get('entry')
                    if entry_type != mt5.DEAL_ENTRY_OUT:
                        logger.debug(f"Skipping deal {d.get('ticket')} with entry type {entry_type} (not OUT)")
                        continue
                        
                    # Get profit and log zero-profit deals but don't skip them
                    profit = float(d.get('profit', 0.0))
                    is_zero_profit = False
                    if profit == 0.0:
                        is_zero_profit = True
                        deal_comment = d.get('comment', 'No comment')
                        deal_reason = d.get('reason', 'No reason')
                        deal_position = d.get('position', 'No position')
                        deal_order = d.get('order', 'No order')
                        deal_time = d.get('time', 0)
                        # Print directly to stdout for visibility
                        print(f"\n[ZERO PROFIT DEAL] Ticket={d.get('ticket')}, Symbol={d.get('symbol')}")
                        print(f"  Comment: '{deal_comment}', Reason: {deal_reason}, Entry: {entry_type}")
                        print(f"  Position: {deal_position}, Order: {deal_order}, Time: {deal_time}")
                        print(f"  Volume: {d.get('volume')}, Price: {d.get('price')}\n")

                    deal_time_raw = d.get('time')
                    if deal_time_raw is None or not isinstance(deal_time_raw, (int, float)): 
                        logger.warning(f"Deal {d.get('ticket', 'N/A')} at index {i} has invalid time value: {deal_time_raw}. Skipping.")
                        continue
                    
                    try:
                        open_time_dt = datetime.fromtimestamp(deal_time_raw)
                        open_time_iso = open_time_dt.isoformat()
                    except (OSError, OverflowError, ValueError) as time_err:
                        logger.warning(f"Deal {d.get('ticket', 'N/A')} at index {i} failed time conversion: {time_err}. Raw time: {deal_time_raw}. Skipping.")
                        continue

                    volume = float(d.get('volume', 0.0))
                    # profit already extracted above
                    price = float(d.get('price', 0.0))
                    
                    pips = 0
                    if volume != 0.0: # Avoid division by zero
                        pips = abs(profit / volume)

                    trades.append({
                        'ticket': d.get('ticket'),
                        'symbol': d.get('symbol'),
                        'type': 'BUY' if deal_type == mt5.DEAL_TYPE_BUY else 'SELL',
                        'volume': volume,
                        'open_time': open_time_iso,
                        'close_time': open_time_iso, # For deals, open_time and close_time are the same (deal execution time)
                        'duration_minutes': 0,  # Duration is not directly available from a single deal
                        'open_price': price,    # For deals, this is the execution price
                        'close_price': price,   # Same as open_price for a deal
                        'sl': d.get('sl'), 
                        'tp': d.get('tp'),
                        'profit': profit,
                        'pips': pips,
                        'comment': d.get('comment', ''),
                        'is_zero_profit': is_zero_profit,
                        'reason': d.get('reason', 0),
                        'entry': entry_type
                    })
                except Exception as e_inner_loop:
                    logger.error(f"Unexpected error processing deal {d.get('ticket', 'N/A')} at index {i}: {e_inner_loop}", exc_info=True)
                    # Continue to the next deal to make the endpoint resilient.
                    continue
        
        # Compute stats
        total_trades = len(trades)
        wins = [t for t in trades if t['profit'] > 0]
        losses = [t for t in trades if t['profit'] < 0]
        net_profit = sum(t['profit'] for t in trades)
        gross_profit = sum(t['profit'] for t in wins)
        gross_loss = abs(sum(t['profit'] for t in losses))
        profit_factor = (gross_profit / gross_loss) if gross_loss > 0 else None # Handle division by zero for JSON
        avg_profit = (gross_profit / len(wins)) if wins else 0
        avg_loss = (gross_loss / len(losses)) if losses else 0
        largest_win = max((t['profit'] for t in wins), default=0)
        largest_loss = min((t['profit'] for t in losses), default=0)
        avg_hold_time = 0  # Not available for deals
        win_rate = (len(wins) / total_trades) if total_trades else 0

        stats = {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'net_profit': net_profit,
            'avg_profit': avg_profit,
            'avg_loss': avg_loss,
            'largest_win': largest_win,
            'largest_loss': largest_loss,
            'avg_hold_time_minutes': avg_hold_time
        }
        
        return jsonify({'history': trades, 'stats': stats})
    except Exception as e:
        logger.exception(f"Error in /api/trade/history: {e}")
        return jsonify({'error': str(e)}), 500
