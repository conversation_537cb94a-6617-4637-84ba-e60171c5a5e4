/**
 * Format a date to a readable string
 * @param {Date} date - The date to format
 * @returns {string} - Formatted date (e.g., "2023-04-25")
 */
export function formatDate(date) {
  if (!date || !(date instanceof Date) || isNaN(date)) {
    return 'Invalid date';
  }
  
  return date.toISOString().split('T')[0];
}

/**
 * Format a time to a readable string
 * @param {Date} date - The date to format for time
 * @returns {string} - Formatted time (e.g., "14:30")
 */
export function formatTime(date) {
  if (!date || !(date instanceof Date) || isNaN(date)) {
    return 'Invalid time';
  }
  
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

/**
 * Format a date and time to a readable string
 * @param {Date} date - The date to format
 * @returns {string} - Formatted date and time (e.g., "2023-04-25 14:30")
 */
export function formatDateTime(date) {
  if (!date || !(date instanceof Date) || isNaN(date)) {
    return 'Invalid date/time';
  }
  
  return `${formatDate(date)} ${formatTime(date)}`;
}

/**
 * Format a price value with appropriate decimal places
 * @param {number} price - The price to format
 * @param {number} decimals - Number of decimal places (default: 5 for forex)
 * @returns {string} - Formatted price
 */
export function formatPrice(price, decimals = 5) {
  if (typeof price !== 'number' || isNaN(price)) {
    return '-';
  }
  
  return price.toFixed(decimals);
}

/**
 * Format a percentage value
 * @param {number} value - The percentage value to format
 * @param {number} decimals - Number of decimal places
 * @returns {string} - Formatted percentage with % sign
 */
export function formatPercent(value, decimals = 2) {
  if (typeof value !== 'number' || isNaN(value)) {
    return '-';
  }
  
  return `${value.toFixed(decimals)}%`;
}
