import React from 'react';
import TrendArrow from './TrendArrow';
import RSIGauge from './RSIGauge';
import { renderIndicatorValue } from '../../utils/analysisUtils';
import '../../styles/BentoComponents.css';
import '../../styles/AnalysisBento.css';

/**
 * RSICard component displays RSI analysis
 *
 * @param {Object} rsi - The RSI data
 * @returns {JSX.Element} - The rendered RSI card
 */
const RSICard = ({ rsi }) => {
  return (
    <div className="bento-card bento-span-4 bento-card-centered">
      <div className="bento-card-header">
        <h3 className="bento-card-title">RSI</h3>
        <div className="signal-with-arrow">
          {renderIndicatorValue(rsi?.level, 'signal')}
          <TrendArrow direction={rsi?.level} />
        </div>
      </div>
      <div className="bento-card-content">
        <RSIGauge value={rsi?.value} />
        <p className="mt-2"><span>Divergence:</span> <span>{rsi?.divergence ?? 'None'}</span></p>
      </div>
    </div>
  );
};

export default RSICard;
