from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class PSARIndicator(BaseIndicator):
    """Parabolic Stop and Reverse (PSAR) indicator."""

    def __init__(self, initial_af: float = 0.02, max_af: float = 0.2, af_step: float = 0.02):
        """
        Initialize PSAR indicator.

        Args:
            initial_af: Initial acceleration factor.
            max_af: Maximum acceleration factor.
            af_step: Step for increasing the acceleration factor.
        """
        super().__init__({
            'initial_af': initial_af,
            'max_af': max_af,
            'af_step': af_step
        })

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate PSAR values."""
        df = data.to_dataframe()
        if df.empty or len(df) < 2:
            return {'psar': np.array([]), 'trend': np.array([])}

        high = df['high'].values
        low = df['low'].values
        close = df['close'].values # Although not directly used in standard PSAR calc, good to have

        initial_af = self.params['initial_af']
        max_af = self.params['max_af']
        af_step = self.params['af_step']

        length = len(high)
        psar = np.zeros(length) * np.nan
        trend = np.zeros(length) # 1 for uptrend, -1 for downtrend
        af = np.zeros(length)
        ep = np.zeros(length) * np.nan # Extreme Point

        # Initial values (assuming initial uptrend based on first two bars)
        # A common initialization is to assume trend based on first few bars
        if high[1] > high[0] and low[1] > low[0]:
             trend[0] = 1
             psar[0] = low[0]
             ep[0] = high[0]
        else: # Initial downtrend
             trend[0] = -1
             psar[0] = high[0]
             ep[0] = low[0]
        af[0] = initial_af
        psar[1] = psar[0] # Start PSAR for bar 1 same as bar 0

        # Calculate PSAR
        for i in range(1, length):
            # Determine trend direction for the current bar
            if trend[i-1] == 1: # Previous bar was uptrend
                psar[i] = psar[i-1] + af[i-1] * (ep[i-1] - psar[i-1])
                # Ensure PSAR is not higher than the low of the previous two bars
                psar[i] = min(psar[i], low[i-1], low[i-2] if i > 1 else low[i-1])

                if low[i] < psar[i]: # Trend reversal to downtrend
                    trend[i] = -1
                    psar[i] = ep[i-1] # PSAR becomes the previous EP
                    ep[i] = low[i]    # New EP is the current low
                    af[i] = initial_af
                else: # Continue uptrend
                    trend[i] = 1
                    ep[i] = max(high[i], ep[i-1])
                    if high[i] > ep[i-1]: # New extreme high
                        af[i] = min(af[i-1] + af_step, max_af)
                    else:
                        af[i] = af[i-1]

            else: # Previous bar was downtrend
                psar[i] = psar[i-1] - af[i-1] * (psar[i-1] - ep[i-1])
                 # Ensure PSAR is not lower than the high of the previous two bars
                psar[i] = max(psar[i], high[i-1], high[i-2] if i > 1 else high[i-1])

                if high[i] > psar[i]: # Trend reversal to uptrend
                    trend[i] = 1
                    psar[i] = ep[i-1] # PSAR becomes the previous EP
                    ep[i] = high[i]   # New EP is the current high
                    af[i] = initial_af
                else: # Continue downtrend
                    trend[i] = -1
                    ep[i] = min(low[i], ep[i-1])
                    if low[i] < ep[i-1]: # New extreme low
                        af[i] = min(af[i-1] + af_step, max_af)
                    else:
                        af[i] = af[i-1]

        # Trend signal based on price vs PSAR
        price_vs_psar = np.where(close > psar, 1, np.where(close < psar, -1, 0))

        self._values = {
            'psar': psar,
            'trend': trend, # Trend based on PSAR calculation logic
            'price_vs_psar': price_vs_psar # Trend based on current price vs PSAR
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if not 0 < self.params['initial_af'] < 1:
             raise ValueError("Initial AF must be between 0 and 1")
        if not 0 < self.params['af_step'] < 1:
             raise ValueError("AF Step must be between 0 and 1")
        if not 0 < self.params['max_af'] <= 1:
             raise ValueError("Max AF must be between 0 and 1")
        if self.params['initial_af'] >= self.params['max_af']:
             raise ValueError("Initial AF must be less than Max AF")
        return True