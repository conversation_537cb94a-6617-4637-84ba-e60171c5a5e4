"""
Full Auto Mode - Advanced AI-Driven Trading System

This module implements a fully autonomous trading system that requires minimal user input.
Users only need to select trading pairs, and the system handles everything else:
- Strategy selection and switching
- Risk management and adjustment
- Position management (cut loss, take profit, hedging)
- Market condition adaptation
- Portfolio optimization
"""

import logging
import time
import threading
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
from datetime import datetime, timedelta

from backend.strategy_orchestrator import StrategyOrchestrator, MarketCondition, StrategyType

logger = logging.getLogger(__name__)

class AutoModeState(Enum):
    """Auto mode operational states"""
    INACTIVE = "inactive"
    ANALYZING = "analyzing"
    TRADING = "trading"
    RISK_MANAGEMENT = "risk_management"
    PORTFOLIO_OPTIMIZATION = "portfolio_optimization"
    EMERGENCY_STOP = "emergency_stop"

class PositionAction(Enum):
    """Position management actions"""
    HOLD = "hold"
    PARTIAL_CLOSE = "partial_close"
    FULL_CLOSE = "full_close"
    HEDGE = "hedge"
    SCALE_IN = "scale_in"
    TRAIL_STOP = "trail_stop"
    BREAKEVEN = "breakeven"

@dataclass
class AutoModeConfig:
    """Configuration for full auto mode"""
    # User selections
    selected_symbols: List[str] = field(default_factory=list)
    account_balance: float = 10000.0
    max_account_risk: float = 5.0  # Maximum % of account at risk

    # AI-controlled parameters (auto-adjusted)
    current_risk_level: float = 1.0  # Current risk level (0.1% - 5%)
    active_strategies: Dict[str, str] = field(default_factory=dict)  # symbol -> strategy_type
    position_sizes: Dict[str, float] = field(default_factory=dict)  # symbol -> lot_size

    # Advanced features
    hedging_enabled: bool = True
    dynamic_risk_adjustment: bool = True
    portfolio_correlation_management: bool = True
    news_sentiment_integration: bool = True

    # Performance tracking
    daily_profit_target: float = 2.0  # % of account
    daily_loss_limit: float = 1.0    # % of account
    consecutive_loss_limit: int = 3

    # Emergency controls
    max_drawdown: float = 3.0  # % of account
    emergency_stop_triggered: bool = False

@dataclass
class PositionAnalysis:
    """Analysis of current position"""
    symbol: str
    position_id: str
    entry_price: float
    current_price: float
    unrealized_pnl: float
    unrealized_pnl_percent: float
    time_in_trade: float  # hours
    market_condition: MarketCondition
    recommended_action: PositionAction
    confidence: float
    reasoning: str

class FullAutoMode:
    """
    Advanced AI-driven trading system with minimal user input
    """

    def __init__(self, mt5_instance, analysis_engine):
        self.mt5 = mt5_instance
        self.analysis_engine = analysis_engine
        self.orchestrator = StrategyOrchestrator()

        self.config = AutoModeConfig()
        self.state = AutoModeState.INACTIVE
        self.running = False
        self.thread = None

        # Performance tracking
        self.daily_stats = {
            'start_balance': 0.0,
            'current_balance': 0.0,
            'daily_pnl': 0.0,
            'trades_today': 0,
            'wins_today': 0,
            'consecutive_losses': 0,
            'max_drawdown_today': 0.0
        }

        # Position management
        self.active_positions: Dict[str, Dict] = {}
        self.hedge_positions: Dict[str, str] = {}  # main_position_id -> hedge_position_id

        # Market analysis cache
        self.market_analysis_cache: Dict[str, Tuple[Dict, float]] = {}

        # Risk management
        self.risk_manager = AutoRiskManager(self)
        self.portfolio_optimizer = PortfolioOptimizer(self)

    def start_auto_mode(self, selected_symbols: List[str], account_balance: float = None) -> Dict[str, Any]:
        """
        Start full auto mode with minimal user input
        """
        try:
            if self.running:
                return {"success": False, "message": "Auto mode already running"}

            # Validate symbols
            if not selected_symbols:
                return {"success": False, "message": "No symbols selected"}

            # Get account balance if not provided
            if account_balance is None:
                try:
                    account_info = self.mt5.account.get_account_info()
                    if account_info and hasattr(account_info, 'balance'):
                        account_balance = float(account_info.balance)
                    else:
                        account_balance = 10000.0  # Default
                except Exception as e:
                    logger.warning(f"Failed to get account balance: {str(e)}, using default")
                    account_balance = 10000.0  # Default

            # Initialize configuration
            self.config.selected_symbols = selected_symbols
            self.config.account_balance = account_balance
            self.daily_stats['start_balance'] = account_balance
            self.daily_stats['current_balance'] = account_balance

            # Start auto mode thread
            self.running = True
            self.state = AutoModeState.ANALYZING
            self.thread = threading.Thread(target=self._auto_mode_loop, daemon=True)
            self.thread.start()

            logger.info(f"Full Auto Mode started with symbols: {selected_symbols}")
            logger.info(f"Account balance: {account_balance}, Max risk: {self.config.max_account_risk}%")

            return {
                "success": True,
                "message": "Full Auto Mode activated",
                "config": {
                    "symbols": selected_symbols,
                    "account_balance": account_balance,
                    "max_risk": self.config.max_account_risk,
                    "features": {
                        "hedging": self.config.hedging_enabled,
                        "dynamic_risk": self.config.dynamic_risk_adjustment,
                        "portfolio_optimization": self.config.portfolio_correlation_management
                    }
                }
            }

        except Exception as e:
            logger.error(f"Error starting auto mode: {str(e)}")
            return {"success": False, "message": f"Failed to start auto mode: {str(e)}"}

    def stop_auto_mode(self) -> Dict[str, Any]:
        """
        Stop auto mode and provide summary
        """
        try:
            self.running = False
            self.state = AutoModeState.INACTIVE

            # Calculate session summary
            session_pnl = self.daily_stats['current_balance'] - self.daily_stats['start_balance']
            session_pnl_percent = (session_pnl / self.daily_stats['start_balance']) * 100

            summary = {
                "session_duration": time.time(),  # Will be calculated properly
                "starting_balance": self.daily_stats['start_balance'],
                "ending_balance": self.daily_stats['current_balance'],
                "session_pnl": session_pnl,
                "session_pnl_percent": session_pnl_percent,
                "total_trades": self.daily_stats['trades_today'],
                "winning_trades": self.daily_stats['wins_today'],
                "win_rate": self.daily_stats['wins_today'] / max(1, self.daily_stats['trades_today']),
                "max_drawdown": self.daily_stats['max_drawdown_today'],
                "active_positions": len(self.active_positions)
            }

            logger.info(f"Auto Mode stopped. Session summary: {summary}")

            return {
                "success": True,
                "message": "Auto Mode stopped",
                "summary": summary
            }

        except Exception as e:
            logger.error(f"Error stopping auto mode: {str(e)}")
            return {"success": False, "message": f"Failed to stop auto mode: {str(e)}"}

    def _auto_mode_loop(self):
        """
        Main auto mode loop - handles all trading decisions
        """
        logger.info("Auto Mode main loop started")

        while self.running:
            try:
                # Update account balance
                self._update_account_balance()

                # Check emergency conditions
                if self._check_emergency_conditions():
                    self.state = AutoModeState.EMERGENCY_STOP
                    self._handle_emergency_stop()
                    break

                # Main decision cycle
                if self.state == AutoModeState.ANALYZING:
                    self._analyze_market_conditions()
                    self._select_optimal_strategies()
                    self.state = AutoModeState.TRADING

                elif self.state == AutoModeState.TRADING:
                    self._execute_trading_decisions()
                    self.state = AutoModeState.RISK_MANAGEMENT

                elif self.state == AutoModeState.RISK_MANAGEMENT:
                    self._manage_existing_positions()
                    self._adjust_risk_levels()
                    self.state = AutoModeState.PORTFOLIO_OPTIMIZATION

                elif self.state == AutoModeState.PORTFOLIO_OPTIMIZATION:
                    self._optimize_portfolio()
                    self.state = AutoModeState.ANALYZING

                # Sleep between cycles
                time.sleep(10)  # 10-second cycle

            except Exception as e:
                logger.error(f"Error in auto mode loop: {str(e)}")
                time.sleep(30)  # Longer sleep on error

    def _update_account_balance(self):
        """
        Update current account balance from MT5
        """
        try:
            account_info = self.mt5.account.get_account_info()
            if account_info and hasattr(account_info, 'balance'):
                self.daily_stats['current_balance'] = float(account_info.balance)
                self.config.account_balance = float(account_info.balance)
        except Exception as e:
            logger.warning(f"Failed to update account balance: {str(e)}")

    def _check_emergency_conditions(self) -> bool:
        """
        Check for emergency conditions that require immediate stop
        """
        try:
            # Check maximum drawdown
            current_balance = self.daily_stats['current_balance']
            start_balance = self.daily_stats['start_balance']
            drawdown_percent = ((start_balance - current_balance) / start_balance) * 100

            if drawdown_percent >= self.config.max_drawdown:
                logger.error(f"Emergency stop: Maximum drawdown reached {drawdown_percent:.2f}%")
                return True

            # Check daily loss limit
            daily_pnl_percent = ((current_balance - start_balance) / start_balance) * 100
            if daily_pnl_percent <= -self.config.daily_loss_limit:
                logger.error(f"Emergency stop: Daily loss limit reached {daily_pnl_percent:.2f}%")
                return True

            return False
        except Exception as e:
            logger.error(f"Error checking emergency conditions: {str(e)}")
            return False

    def _handle_emergency_stop(self):
        """
        Handle emergency stop procedures
        """
        try:
            logger.error("EMERGENCY STOP ACTIVATED - Closing all positions")
            self.config.emergency_stop_triggered = True

            # Close all active positions
            for symbol, position_data in self.active_positions.items():
                try:
                    position_id = position_data.get('position_id')
                    if position_id:
                        # Close position via MT5
                        close_result = self.mt5.trading.close_position(position_id)
                        if close_result.get('success'):
                            logger.info(f"Emergency closed position {position_id} for {symbol}")
                        else:
                            logger.error(f"Failed to emergency close position {position_id}: {close_result.get('message')}")
                except Exception as e:
                    logger.error(f"Error closing position {symbol}: {str(e)}")

            # Clear active positions
            self.active_positions.clear()

        except Exception as e:
            logger.error(f"Error in emergency stop: {str(e)}")

    def _manage_existing_positions(self):
        """
        Manage existing positions (placeholder for now)
        """
        try:
            logger.info("Managing existing positions...")
            # Position management logic would go here
            pass
        except Exception as e:
            logger.error(f"Error managing positions: {str(e)}")

    def _adjust_risk_levels(self):
        """
        Adjust risk levels based on performance (placeholder for now)
        """
        try:
            logger.info("Adjusting risk levels...")
            # Risk adjustment logic would go here
            pass
        except Exception as e:
            logger.error(f"Error adjusting risk levels: {str(e)}")

    def _optimize_portfolio(self):
        """
        Optimize portfolio (placeholder for now)
        """
        try:
            logger.info("Optimizing portfolio...")
            # Portfolio optimization logic would go here
            pass
        except Exception as e:
            logger.error(f"Error optimizing portfolio: {str(e)}")

    def _place_buy_order(self, symbol: str, position_size: float, stop_loss: float, take_profit: float, strategy: str) -> Dict[str, Any]:
        """
        Place a buy order
        """
        try:
            result = self.mt5.trading.place_market_order(
                symbol=symbol,
                order_type=0,  # BUY
                volume=position_size,
                sl=stop_loss,
                tp=take_profit,
                comment=f"FullAuto_{strategy}"
            )
            return result
        except Exception as e:
            logger.error(f"Error placing buy order: {str(e)}")
            return {"success": False, "message": str(e)}

    def _place_sell_order(self, symbol: str, position_size: float, stop_loss: float, take_profit: float, strategy: str) -> Dict[str, Any]:
        """
        Place a sell order
        """
        try:
            result = self.mt5.trading.place_market_order(
                symbol=symbol,
                order_type=1,  # SELL
                volume=position_size,
                sl=stop_loss,
                tp=take_profit,
                comment=f"FullAuto_{strategy}"
            )
            return result
        except Exception as e:
            logger.error(f"Error placing sell order: {str(e)}")
            return {"success": False, "message": str(e)}

    def _analyze_market_conditions(self):
        """
        Analyze market conditions for all selected symbols
        """
        try:
            logger.info("Analyzing market conditions...")

            for symbol in self.config.selected_symbols:
                # Get market data
                price_data = self.mt5.get_current_price(symbol)
                if "error" in price_data:
                    continue

                # Get analysis
                analysis_result = self.analysis_engine.get_analysis_for_timeframe(symbol, "H1")
                if not analysis_result.get("success"):
                    continue

                analysis_data = analysis_result.get("analysis", {})
                indicators = analysis_data.get("indicators", {})

                # Determine market condition
                market_condition = self.orchestrator.analyze_market_condition(symbol, price_data, indicators)

                # Cache analysis
                self.market_analysis_cache[symbol] = ({
                    'price_data': price_data,
                    'indicators': indicators,
                    'market_condition': market_condition,
                    'volatility': indicators.get('atr', {}).get('value', 0),
                    'trend_strength': abs(indicators.get('macd', {}).get('histogram', 0))
                }, time.time())

                logger.info(f"{symbol}: Market condition = {market_condition.value}")

        except Exception as e:
            logger.error(f"Error analyzing market conditions: {str(e)}")

    def _select_optimal_strategies(self):
        """
        AI selects optimal strategies for each symbol based on market conditions
        """
        try:
            logger.info("Selecting optimal strategies...")

            for symbol in self.config.selected_symbols:
                if symbol not in self.market_analysis_cache:
                    continue

                analysis, _ = self.market_analysis_cache[symbol]
                market_condition = analysis['market_condition']

                # AI strategy selection logic
                optimal_strategy = self._ai_strategy_selection(symbol, market_condition, analysis)

                # Update active strategies
                current_strategy = self.config.active_strategies.get(symbol)
                if current_strategy != optimal_strategy:
                    self.config.active_strategies[symbol] = optimal_strategy
                    logger.info(f"{symbol}: Strategy changed to {optimal_strategy}")

        except Exception as e:
            logger.error(f"Error selecting strategies: {str(e)}")

    def _ai_strategy_selection(self, symbol: str, market_condition: MarketCondition, analysis: Dict) -> str:
        """
        AI-driven strategy selection based on multiple factors
        """
        try:
            volatility = analysis.get('volatility', 0)
            trend_strength = analysis.get('trend_strength', 0)

            # Get current time for session analysis
            current_hour = datetime.now().hour

            # Strategy scoring system
            strategy_scores = {
                'scalping_momentum': 0.0,
                'intraday_trend': 0.0,
                'swing_reversal': 0.0,
                'position_trading': 0.0
            }

            # Market condition scoring
            if market_condition in [MarketCondition.RANGING, MarketCondition.VOLATILE]:
                strategy_scores['scalping_momentum'] += 0.4

            if market_condition in [MarketCondition.TRENDING_WEAK, MarketCondition.TRENDING_STRONG]:
                strategy_scores['intraday_trend'] += 0.4
                if trend_strength > 0.6:
                    strategy_scores['position_trading'] += 0.3

            if market_condition in [MarketCondition.REVERSAL, MarketCondition.BREAKOUT]:
                strategy_scores['swing_reversal'] += 0.4

            # Volatility adjustment
            if volatility > 0.001:  # High volatility
                strategy_scores['scalping_momentum'] += 0.2
                strategy_scores['swing_reversal'] += 0.1
            else:  # Low volatility
                strategy_scores['intraday_trend'] += 0.2
                strategy_scores['position_trading'] += 0.1

            # Time-based adjustment
            if 8 <= current_hour <= 17:  # Active trading hours
                strategy_scores['scalping_momentum'] += 0.1
                strategy_scores['intraday_trend'] += 0.1
            else:  # Quiet hours
                strategy_scores['swing_reversal'] += 0.1
                strategy_scores['position_trading'] += 0.1

            # Performance-based adjustment
            for strategy, score in strategy_scores.items():
                performance = self.orchestrator.strategy_performance.get(f"{symbol}_{strategy}")
                if performance and performance.recent_performance > 0.7:
                    strategy_scores[strategy] += 0.2
                elif performance and performance.recent_performance < 0.3:
                    strategy_scores[strategy] -= 0.2

            # Select best strategy
            best_strategy = max(strategy_scores, key=strategy_scores.get)
            best_score = strategy_scores[best_strategy]

            logger.info(f"{symbol}: AI selected {best_strategy} (score: {best_score:.3f})")

            return best_strategy

        except Exception as e:
            logger.error(f"Error in AI strategy selection: {str(e)}")
            return 'intraday_trend'  # Default fallback

    def _execute_trading_decisions(self):
        """
        Execute trading decisions based on selected strategies
        """
        try:
            logger.info("Executing trading decisions...")

            for symbol in self.config.selected_symbols:
                if symbol not in self.config.active_strategies:
                    continue

                # Check if we already have a position
                if symbol in self.active_positions:
                    continue  # Skip if already have position

                # Check risk limits
                if not self.risk_manager.can_open_new_position(symbol):
                    continue

                # Generate signals for the selected strategy
                strategy = self.config.active_strategies[symbol]
                signals = self._generate_strategy_signals(symbol, strategy)

                if signals:
                    best_signal = max(signals, key=lambda x: x.get('confidence', 0))
                    if best_signal.get('confidence', 0) >= 60:  # Minimum confidence
                        self._execute_trade(symbol, best_signal, strategy)

        except Exception as e:
            logger.error(f"Error executing trading decisions: {str(e)}")

    def _generate_strategy_signals(self, symbol: str, strategy: str) -> List[Dict]:
        """
        Generate signals for specific strategy
        """
        try:
            if symbol not in self.market_analysis_cache:
                return []

            analysis, _ = self.market_analysis_cache[symbol]

            # Use the signals module to generate signals
            from backend.api.signals import generate_signals_from_analysis

            signals_result = generate_signals_from_analysis(
                symbol, "H1", analysis, analysis['price_data']
            )

            all_signals = signals_result.get('signals', [])

            # Filter signals for the specific strategy
            strategy_signals = [
                signal for signal in all_signals
                if strategy.replace('_', ' ').lower() in signal.get('strategy', '').lower()
            ]

            return strategy_signals

        except Exception as e:
            logger.error(f"Error generating signals for {symbol}/{strategy}: {str(e)}")
            return []

    def _execute_trade(self, symbol: str, signal: Dict, strategy: str):
        """
        Execute a trade with AI-calculated position size
        """
        try:
            # Calculate optimal position size
            position_size = self.risk_manager.calculate_optimal_position_size(symbol, signal)

            if position_size <= 0:
                return

            # Prepare trade parameters
            signal_type = signal.get('signalType', '').upper()
            entry_price = signal.get('entry', {}).get('price', 0)
            stop_loss = signal.get('stopLoss', 0)
            take_profit = signal.get('takeProfit', 0)

            # Execute trade
            if signal_type == 'BUY':
                result = self._place_buy_order(symbol, position_size, stop_loss, take_profit, strategy)
            elif signal_type == 'SELL':
                result = self._place_sell_order(symbol, position_size, stop_loss, take_profit, strategy)
            else:
                return

            if result.get('success'):
                position_id = result.get('position_id')
                self.active_positions[symbol] = {
                    'position_id': position_id,
                    'symbol': symbol,
                    'strategy': strategy,
                    'signal_type': signal_type,
                    'entry_price': entry_price,
                    'position_size': position_size,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'entry_time': time.time(),
                    'confidence': signal.get('confidence', 0)
                }

                self.daily_stats['trades_today'] += 1
                logger.info(f"Trade executed: {signal_type} {symbol} @ {position_size} lots")

        except Exception as e:
            logger.error(f"Error executing trade for {symbol}: {str(e)}")

    def get_auto_mode_status(self) -> Dict[str, Any]:
        """
        Get current auto mode status and statistics
        """
        try:
            return {
                "success": True,
                "status": {
                    "running": self.running,
                    "state": self.state.value,
                    "selected_symbols": self.config.selected_symbols,
                    "active_strategies": self.config.active_strategies,
                    "active_positions": len(self.active_positions),
                    "daily_stats": self.daily_stats,
                    "current_risk_level": self.config.current_risk_level,
                    "emergency_stop": self.config.emergency_stop_triggered
                }
            }
        except Exception as e:
            logger.error(f"Error getting auto mode status: {str(e)}")
            return {"success": False, "error": str(e)}


class AutoRiskManager:
    """
    Advanced risk management for full auto mode
    """

    def __init__(self, auto_mode_instance):
        self.auto_mode = auto_mode_instance
        self.risk_adjustment_history = []

    def can_open_new_position(self, symbol: str) -> bool:
        """
        Determine if we can open a new position based on risk limits
        """
        try:
            # Check account-level risk
            current_risk = self._calculate_current_account_risk()
            if current_risk >= self.auto_mode.config.max_account_risk:
                logger.warning(f"Account risk limit reached: {current_risk:.2f}%")
                return False

            # Check symbol-specific limits
            if symbol in self.auto_mode.active_positions:
                return False  # Already have position

            # Check correlation limits
            if not self._check_correlation_limits(symbol):
                return False

            # Check daily limits
            if not self._check_daily_limits():
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking position limits: {str(e)}")
            return False

    def calculate_optimal_position_size(self, symbol: str, signal: Dict) -> float:
        """
        AI-driven position size calculation
        """
        try:
            # Base risk calculation
            confidence = signal.get('confidence', 60) / 100.0
            entry_price = signal.get('entry', {}).get('price', 0)
            stop_loss = signal.get('stopLoss', 0)

            if not entry_price or not stop_loss:
                return 0.0

            # Calculate risk per trade
            risk_distance = abs(entry_price - stop_loss)
            if risk_distance == 0:
                return 0.0

            # Dynamic risk adjustment based on confidence
            base_risk = self.auto_mode.config.current_risk_level
            confidence_multiplier = 0.5 + (confidence * 1.0)  # 0.5x to 1.5x based on confidence
            adjusted_risk = base_risk * confidence_multiplier

            # Account balance consideration
            account_balance = self.auto_mode.config.account_balance
            risk_amount = (adjusted_risk / 100.0) * account_balance

            # Calculate position size
            # For forex: position_size = risk_amount / (risk_distance * contract_size)
            contract_size = 100000  # Standard lot for major pairs
            position_size = risk_amount / (risk_distance * contract_size)

            # Apply additional constraints
            position_size = self._apply_position_constraints(symbol, position_size, signal)

            logger.info(f"{symbol}: Calculated position size = {position_size:.4f} lots "
                       f"(risk: {adjusted_risk:.2f}%, confidence: {confidence:.2f})")

            return round(position_size, 4)

        except Exception as e:
            logger.error(f"Error calculating position size: {str(e)}")
            return 0.0

    def _calculate_current_account_risk(self) -> float:
        """
        Calculate current account risk from all open positions
        """
        try:
            total_risk = 0.0

            for position_data in self.auto_mode.active_positions.values():
                # Get current unrealized P&L
                position_id = position_data.get('position_id')
                if position_id:
                    # Calculate potential loss if stop loss is hit
                    entry_price = position_data.get('entry_price', 0)
                    stop_loss = position_data.get('stop_loss', 0)
                    position_size = position_data.get('position_size', 0)

                    if entry_price and stop_loss and position_size:
                        risk_distance = abs(entry_price - stop_loss)
                        potential_loss = risk_distance * position_size * 100000  # Contract size
                        risk_percent = (potential_loss / self.auto_mode.config.account_balance) * 100
                        total_risk += risk_percent

            return total_risk

        except Exception as e:
            logger.error(f"Error calculating account risk: {str(e)}")
            return 0.0

    def _check_correlation_limits(self, symbol: str) -> bool:
        """
        Check if adding this symbol would exceed correlation limits
        """
        try:
            # Simple correlation check - avoid too many correlated pairs
            major_pairs = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF']
            eur_pairs = ['EURUSD', 'EURGBP', 'EURJPY', 'EURCHF']
            gbp_pairs = ['GBPUSD', 'EURGBP', 'GBPJPY', 'GBPCHF']

            current_symbols = list(self.auto_mode.active_positions.keys())

            # Check for too many USD pairs
            usd_count = sum(1 for s in current_symbols if 'USD' in s)
            if 'USD' in symbol and usd_count >= 3:
                logger.warning(f"Too many USD pairs already active: {usd_count}")
                return False

            # Check for too many EUR pairs
            eur_count = sum(1 for s in current_symbols if 'EUR' in s)
            if 'EUR' in symbol and eur_count >= 2:
                logger.warning(f"Too many EUR pairs already active: {eur_count}")
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking correlation limits: {str(e)}")
            return True  # Allow on error

    def _check_daily_limits(self) -> bool:
        """
        Check daily trading limits
        """
        try:
            # Check daily loss limit
            daily_pnl_percent = (self.auto_mode.daily_stats['daily_pnl'] /
                               self.auto_mode.daily_stats['start_balance']) * 100

            if daily_pnl_percent <= -self.auto_mode.config.daily_loss_limit:
                logger.warning(f"Daily loss limit reached: {daily_pnl_percent:.2f}%")
                return False

            # Check consecutive losses
            if self.auto_mode.daily_stats['consecutive_losses'] >= self.auto_mode.config.consecutive_loss_limit:
                logger.warning(f"Consecutive loss limit reached: {self.auto_mode.daily_stats['consecutive_losses']}")
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking daily limits: {str(e)}")
            return True

    def _apply_position_constraints(self, symbol: str, position_size: float, signal: Dict) -> float:
        """
        Apply additional position size constraints
        """
        try:
            # Maximum position size per trade
            max_position = 1.0  # 1 lot maximum
            position_size = min(position_size, max_position)

            # Minimum position size
            min_position = 0.01  # 0.01 lot minimum
            position_size = max(position_size, min_position)

            # Market volatility adjustment
            if symbol in self.auto_mode.market_analysis_cache:
                analysis, _ = self.auto_mode.market_analysis_cache[symbol]
                volatility = analysis.get('volatility', 0)

                # Reduce position size in high volatility
                if volatility > 0.002:  # High volatility threshold
                    position_size *= 0.7
                    logger.info(f"{symbol}: Position size reduced due to high volatility")

            return position_size

        except Exception as e:
            logger.error(f"Error applying position constraints: {str(e)}")
            return position_size


class PortfolioOptimizer:
    """
    Portfolio optimization and management for full auto mode
    """

    def __init__(self, auto_mode_instance):
        self.auto_mode = auto_mode_instance

    def optimize_portfolio(self):
        """
        Optimize current portfolio allocation
        """
        try:
            logger.info("Optimizing portfolio...")

            # Analyze current positions
            position_analyses = []
            for symbol, position_data in self.auto_mode.active_positions.items():
                analysis = self._analyze_position(symbol, position_data)
                if analysis:
                    position_analyses.append(analysis)

            # Make optimization decisions
            for analysis in position_analyses:
                self._execute_optimization_action(analysis)

            # Rebalance risk if needed
            self._rebalance_portfolio_risk()

        except Exception as e:
            logger.error(f"Error optimizing portfolio: {str(e)}")

    def _analyze_position(self, symbol: str, position_data: Dict) -> Optional[PositionAnalysis]:
        """
        Analyze individual position for optimization opportunities
        """
        try:
            # Get current market data
            current_price_data = self.auto_mode.mt5.get_current_price(symbol)
            if "error" in current_price_data:
                return None

            current_price = current_price_data.get('bid', 0)
            entry_price = position_data.get('entry_price', 0)
            position_size = position_data.get('position_size', 0)
            signal_type = position_data.get('signal_type', '')
            entry_time = position_data.get('entry_time', time.time())

            # Calculate P&L
            if signal_type == 'BUY':
                unrealized_pnl = (current_price - entry_price) * position_size * 100000
            else:  # SELL
                unrealized_pnl = (entry_price - current_price) * position_size * 100000

            unrealized_pnl_percent = (unrealized_pnl / self.auto_mode.config.account_balance) * 100
            time_in_trade = (time.time() - entry_time) / 3600  # hours

            # Get market condition
            market_condition = MarketCondition.RANGING  # Default
            if symbol in self.auto_mode.market_analysis_cache:
                analysis, _ = self.auto_mode.market_analysis_cache[symbol]
                market_condition = analysis.get('market_condition', MarketCondition.RANGING)

            # Determine recommended action
            action, confidence, reasoning = self._determine_position_action(
                unrealized_pnl_percent, time_in_trade, market_condition, position_data
            )

            return PositionAnalysis(
                symbol=symbol,
                position_id=position_data.get('position_id', ''),
                entry_price=entry_price,
                current_price=current_price,
                unrealized_pnl=unrealized_pnl,
                unrealized_pnl_percent=unrealized_pnl_percent,
                time_in_trade=time_in_trade,
                market_condition=market_condition,
                recommended_action=action,
                confidence=confidence,
                reasoning=reasoning
            )

        except Exception as e:
            logger.error(f"Error analyzing position {symbol}: {str(e)}")
            return None

    def _determine_position_action(self, pnl_percent: float, time_hours: float,
                                 market_condition: MarketCondition, position_data: Dict) -> Tuple[PositionAction, float, str]:
        """
        AI-driven position action determination
        """
        try:
            # Profit taking logic
            if pnl_percent >= 2.0:  # 2% profit
                return PositionAction.PARTIAL_CLOSE, 0.9, "Strong profit - take partial profits"

            if pnl_percent >= 1.0:  # 1% profit
                return PositionAction.TRAIL_STOP, 0.8, "Good profit - trail stop loss"

            if pnl_percent >= 0.5:  # 0.5% profit
                return PositionAction.BREAKEVEN, 0.7, "Small profit - move to breakeven"

            # Loss management logic
            if pnl_percent <= -1.5:  # 1.5% loss
                return PositionAction.FULL_CLOSE, 0.9, "Significant loss - close position"

            if pnl_percent <= -0.8:  # 0.8% loss
                if self.auto_mode.config.hedging_enabled:
                    return PositionAction.HEDGE, 0.7, "Moderate loss - consider hedging"

            # Time-based logic
            if time_hours >= 24:  # 24 hours in trade
                if pnl_percent < 0.2:  # Less than 0.2% profit after 24h
                    return PositionAction.FULL_CLOSE, 0.6, "Long time in trade with minimal profit"

            # Market condition based logic
            if market_condition == MarketCondition.REVERSAL:
                if pnl_percent > 0:
                    return PositionAction.PARTIAL_CLOSE, 0.7, "Market reversal detected - take profits"
                else:
                    return PositionAction.FULL_CLOSE, 0.8, "Market reversal against position"

            # Default: hold position
            return PositionAction.HOLD, 0.5, "Position within acceptable parameters"

        except Exception as e:
            logger.error(f"Error determining position action: {str(e)}")
            return PositionAction.HOLD, 0.0, "Error in analysis"
