.dashboard-container {
  margin-bottom: 24px;
  padding: 0 16px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;
}

.dashboard-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dashboard-title::before {
  content: '';
  display: inline-block;
  width: 20px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.dashboard {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.dashboard-card {
  background-color: var(--card);
  border-radius: var(--radius-md);
  padding: 16px;
  box-shadow: var(--shadow-md);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--border);
}

.dashboard-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
}

.card-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  opacity: 0.15;
  z-index: 0;
}

.card-icon svg {
  width: 24px;
  height: 24px;
  position: relative;
  z-index: 1;
}

.balance-icon::before {
  background-color: #3b82f6;
}

.balance-icon svg {
  color: #3b82f6;
}

.equity-icon::before {
  background-color: #10b981;
}

.equity-icon svg {
  color: #10b981;
}

.profit-icon::before {
  background-color: #ef4444;
}

.profit-icon svg {
  color: #ef4444;
}

.positions-icon::before {
  background-color: #f59e0b;
}

.positions-icon svg {
  color: #f59e0b;
}

.card-content {
  flex: 1;
}

.dashboard-card h3 {
  font-size: 0.875rem;
  color: var(--text-secondary, rgba(255, 255, 255, 0.7));
  margin: 0 0 8px 0;
  font-weight: 500;
}

.dashboard-card .value {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 4px;
  line-height: 1.2;
  letter-spacing: -0.02em;
  color: var(--text, #ffffff);
}

.dashboard-card .label {
  font-size: 0.75rem;
  color: var(--text-secondary, rgba(255, 255, 255, 0.7));
  display: flex;
  align-items: center;
  gap: 4px;
}

.label-icon {
  width: 14px;
  height: 14px;
  opacity: 0.7;
}

.value.positive {
  color: var(--success, #10b981);
}

.value.negative {
  color: var(--error, #ef4444);
}

/* Add subtle background patterns to cards */
.dashboard-card::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100%;
  background-image: radial-gradient(circle, rgba(0,0,0,0.03) 1px, transparent 1px);
  background-size: 12px 12px;
  opacity: 0.5;
  pointer-events: none;
}

.trend-indicator {
  margin-top: 5px;
  height: 3px;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.trend-line {
  height: 100%;
  width: 70%;
  border-radius: 2px;
}

.trend-line.up {
  background-color: var(--success);
}

.trend-line.down {
  background-color: var(--error);
}

.context-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.context-action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  color: var(--text, #ffffff);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: var(--shadow-sm);
}

.context-action-button:hover {
  background-color: var(--card-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary);
  color: var(--primary);
}

.context-action-icon {
  width: 18px;
  height: 18px;
  opacity: 0.8;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  display: inline-block;
}

.refresh-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z'/%3E%3C/svg%3E");
}

.save-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7l-4-4zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm3-10H5V5h10v4z'/%3E%3C/svg%3E");
}

.execute-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M8 5v14l11-7z'/%3E%3C/svg%3E");
}

.market-icon, .limit-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm-2 14l-4-4 1.41-1.41L10 14.17l6.59-6.59L18 9l-8 8z'/%3E%3C/svg%3E");
}

.start-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M8 5v14l11-7z'/%3E%3C/svg%3E");
}

.stop-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M6 6h12v12H6z'/%3E%3C/svg%3E");
}

.export-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M19 12v7H5v-7H3v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zm-6 .67l2.59-2.58L17 11.5l-5 5-5-5 1.41-1.41L11 12.67V3h2z'/%3E%3C/svg%3E");
}

.report-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
}

/* Responsive styles */
@media (max-width: 1200px) {
  .dashboard {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 0 12px;
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .context-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .dashboard-card {
    padding: 12px;
  }

  .card-icon {
    width: 40px;
    height: 40px;
  }

  .dashboard-card .value {
    font-size: 1.5rem;
  }
}

@media (max-width: 576px) {
  .dashboard {
    grid-template-columns: 1fr;
  }

  .context-action-button {
    flex: 1;
    justify-content: center;
  }
}


.market-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z'/%3E%3C/svg%3E");
}

.limit-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z'/%3E%3C/svg%3E");
}

.start-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M8 5v14l11-7z'/%3E%3C/svg%3E");
}

.stop-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M6 6h12v12H6z'/%3E%3C/svg%3E");
}

.export-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M19 12v7H5v-7H3v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zm-6 .67l2.59-2.58L17 11.5l-5 5-5-5 1.41-1.41L11 12.67V3h2z'/%3E%3C/svg%3E");
}

.report-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
}

/* Responsive styles */
@media (max-width: 1200px) {
  .dashboard {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .context-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .dashboard-card {
    padding: 12px;
  }

  .card-icon {
    width: 36px;
    height: 36px;
    background-size: 20px;
  }

  .dashboard-card .value {
    font-size: 1.3rem;
  }
}

@media (max-width: 576px) {
  .dashboard {
    grid-template-columns: 1fr;
  }

  .context-action-button {
    flex: 1;
    justify-content: center;
  }
}
