# -*- mode: python ; coding: utf-8 -*-
import os
import sys
from PyInstaller.utils.hooks import collect_submodules, collect_data_files

# Critical: add Python's own encodings explicitly
import encodings
import encodings.aliases
import encodings.ascii
import encodings.utf_8
import encodings.latin_1
import encodings.cp1252

# Set this variable to True for maximum compatibility mode
# This uses less efficient but more reliable methods for bundling
MAXIMUM_COMPATIBILITY = True

# Define the base directory for consistent path resolution
BASE_DIR = os.path.abspath(os.getcwd())

block_cipher = None

# Get all encoding modules - this is critical for text handling
encodings_hiddenimports = collect_submodules('encodings')

# Add all submodules for your backend package
backend_hiddenimports = collect_submodules('backend')

# Core modules that are often missing
core_hiddenimports = [
    'logging', 
    'logging.handlers',
    'decimal',
    'numbers',
    'datetime',
    'json',
    'random',
    'time',
    'xml',
    'xml.etree',
    'xml.etree.ElementTree',
]

# Top-level third-party libraries
main_hiddenimports = [
    'flask',
    'flask_cors',
    'waitress',
    'MetaTrader5',
    'numpy',
    'numpy.random',
    'numpy.core',
    'pandas',
    'pytz',
]

# Firebase and Google cloud dependencies
firebase_hiddenimports = [
    'firebase_admin',
    'firebase_admin.auth',
    'firebase_admin.credentials',
    'firebase_admin.firestore',
    'firebase_admin.storage',
    'google.api_core',
    'google.api_core.client_options',
    'google.auth',
    'google.auth.credentials',
    'google.auth.transport.grpc',
    'google.auth.transport.requests',
    'google.cloud',
    'google.cloud.firestore_v1',
    'google.cloud.firestore_v1.types',
    'google.cloud.storage',
    'google.oauth2.service_account',
    'google.protobuf',
    'google_crc32c',
    'grpc',
    'grpc._channel',
    'grpcio',
]

# Date and time handling
datetime_hiddenimports = [
    'pytz',
    'pytz.exceptions',
    'pytz.tzfile',
    'pytz.tzinfo',
    'dateutil',
    'dateutil.parser',
    'dateutil.tz',
    'dateutil.tz.tz',
    'dateutil.relativedelta',
]

# Python package management
packaging_hiddenimports = [
    'pkg_resources',
    'pkg_resources.py2_warn',
    'setuptools',
    'setuptools.msvc',
]

# Combine all hidden imports
hiddenimports = (
    core_hiddenimports + 
    main_hiddenimports + 
    firebase_hiddenimports + 
    datetime_hiddenimports + 
    packaging_hiddenimports +
    backend_hiddenimports + 
    encodings_hiddenimports
)

# Additional data files that might be needed
extra_data = []

# Create a custom bootstrap script to ensure encodings are properly loaded
runtime_hooks = []
if MAXIMUM_COMPATIBILITY:
    runtime_hook_content = """
# Custom runtime hook to ensure encodings are properly loaded
import encodings
import encodings.aliases
import encodings.ascii
import encodings.utf_8
import encodings.cp1252
import encodings.idna
import sys
import os

# Add the project root to sys.path to help with imports
sys.path.insert(0, os.path.dirname(sys.executable))
"""
    runtime_hook_path = os.path.join(BASE_DIR, "encoding_hook.py")
    with open(runtime_hook_path, "w") as f:
        f.write(runtime_hook_content)
    runtime_hooks.append(runtime_hook_path)

# Explicitly collect UI assets if needed
datas = [
    ('backend/garuda-algo-firebase-credentials.json', 'backend')
]

# Add Python's own encoding data files if using maximum compatibility
if MAXIMUM_COMPATIBILITY:
    python_libs = os.path.dirname(encodings.__file__)
    datas.append((python_libs, 'encodings'))

a = Analysis(
    ['backend/run_server.py'],
    pathex=[BASE_DIR],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    runtime_hooks=runtime_hooks,
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='garuda_backend',
    debug=True,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    # Use a full path or remove if the icon doesn't exist
    icon=os.path.join(BASE_DIR, 'assets', 'app_icon.ico') if os.path.exists(os.path.join(BASE_DIR, 'assets', 'app_icon.ico')) else None
)
