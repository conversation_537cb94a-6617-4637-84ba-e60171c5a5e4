import pandas as pd
import logging
from typing import Dict, Any
from backend.technical.indicators.trend.bollinger import BollingerBandsIndicator
from backend.technical.indicators.volatility.atr import ATRIndicator

logger = logging.getLogger("AnalysisEngine")

def extract_bollinger_bands(data: pd.DataFrame) -> Dict[str, Any]:
    """
    Extract and analyze Bollinger Bands values.
    
    Args:
        data: DataFrame containing price data
        
    Returns:
        Dictionary with Bollinger Bands analysis results
    """
    try:
        if data.empty or len(data) < 20:  # Check based on custom indicator needs
            return {"error": "Insufficient data for Bollinger Bands analysis"}

        bb_indicator = BollingerBandsIndicator()  # Use defaults: period=20, std=2.0
        bb_result = bb_indicator.calculate(data)

        required_keys = ['upper_band', 'middle_band', 'lower_band', 'bandwidth']
        if not all(key in bb_result for key in required_keys) or \
           any(len(bb_result[key]) == 0 for key in required_keys):
            return {"error": "Custom Bollinger Bands calculation failed or returned empty/incomplete"}

        latest_upper = bb_result['upper_band'][-1]
        latest_middle = bb_result['middle_band'][-1]
        latest_lower = bb_result['lower_band'][-1]
        latest_bandwidth = bb_result['bandwidth'][-1]
        latest_close = data['close'].iloc[-1]

        if any(pd.isna(v) for v in [latest_upper, latest_middle, latest_lower, latest_bandwidth, latest_close]):
            return {"error": "Latest custom Bollinger Bands values contain NaN"}

        # Determine price position relative to bands
        price_position = "Middle"
        if latest_close > latest_upper:
            price_position = "Above Upper"
        elif latest_close < latest_lower:
            price_position = "Below Lower"
        elif latest_close > latest_middle:
            price_position = "Above Middle"
        elif latest_close < latest_middle:
            price_position = "Below Middle"

        # Determine bandwidth trend
        bandwidth_series = pd.Series(bb_result['bandwidth'])
        bandwidth_trend = "Stable"
        if len(bandwidth_series) >= 5 and bandwidth_series.iloc[-5:].notna().all():
            if bandwidth_series.iloc[-5:].is_monotonic_increasing:
                bandwidth_trend = "Expanding"
            elif bandwidth_series.iloc[-5:].is_monotonic_decreasing:
                bandwidth_trend = "Contracting"

        return {
            "upper": latest_upper,
            "middle": latest_middle,
            "lower": latest_lower,
            "price_position": price_position,
            "bandwidth": latest_bandwidth,
            "bandwidth_trend": bandwidth_trend
        }
    except Exception as e:
        logger.exception(f"Exception extracting custom Bollinger Bands: {str(e)}")
        return {"error": f"Exception extracting custom Bollinger Bands: {str(e)}"}

def extract_atr(data: pd.DataFrame) -> Dict[str, Any]:
    """
    Extract and analyze ATR values.
    
    Args:
        data: DataFrame containing price data
        
    Returns:
        Dictionary with ATR analysis results
    """
    try:
        if data.empty or len(data) < 15:  # Check based on custom indicator needs
            return {"error": "Insufficient data for ATR analysis"}

        atr_indicator = ATRIndicator()  # Use defaults: period=14, ma_type='ema'
        atr_result = atr_indicator.calculate(data)
        logger.debug(f"extract_atr calculated result: Type={type(atr_result)}, Value={atr_result}")

        if 'atr' not in atr_result or len(atr_result['atr']) == 0:
            return {"error": "Custom ATR calculation failed or returned empty"}

        latest_atr = atr_result['atr'][-1]
        latest_close = data['close'].iloc[-1]

        if pd.isna(latest_atr) or pd.isna(latest_close):
            return {"error": "Latest custom ATR or Close is NaN"}

        # Calculate ATR percent
        atr_percent = float('nan')
        if latest_close != 0:
            atr_percent = (latest_atr / latest_close) * 100

        # Determine volatility level
        volatility = "Medium"
        if pd.notna(atr_percent):
            if atr_percent < 0.5:
                volatility = "Low"
            elif atr_percent > 1.5:
                volatility = "High"

        # Ensure return values are appropriate types even if NaN
        final_atr_value = float(latest_atr) if pd.notna(latest_atr) else None
        final_atr_percent = float(atr_percent) if pd.notna(atr_percent) else None

        logger.debug(f"extract_atr returning: Type={type({'value': final_atr_value, 'percent': final_atr_percent, 'volatility': volatility})}, Value={{'value': final_atr_value, 'percent': final_atr_percent, 'volatility': volatility}}")

        return {
            "value": final_atr_value,
            "percent": final_atr_percent,
            "volatility": volatility # Volatility defaults to "Medium" if percent is NaN
        }
    except Exception as e:
        logger.exception(f"Exception extracting custom ATR: {str(e)}")
        return {"error": f"Exception extracting custom ATR: {str(e)}"}
