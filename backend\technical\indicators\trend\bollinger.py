from typing import Dict, Any
import numpy as np
import pandas as pd

from backend.technical.base_indicator import BaseIndicator

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

class BollingerBandsIndicator(BaseIndicator):
    """Bollinger Bands indicator."""

    def __init__(self, period: int = 20, num_std_dev: float = 2.0,
                 source: str = 'close', ma_type: str = 'sma'):
        """
        Initialize Bollinger Bands indicator.

        Args:
            period: The period for calculating the moving average and standard deviation.
            num_std_dev: The number of standard deviations for the upper and lower bands.
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
            ma_type: The type of moving average ('sma' or 'ema').
        """
        super().__init__({
            'period': period,
            'num_std_dev': num_std_dev,
            'source': source,
            'ma_type': ma_type
        })
        self.name = 'BollingerBands' # Store name if needed

    def calculate(self, data: pd.DataFrame) -> Dict[str, np.ndarray]: # Changed type hint
        """Calculate Bollinger Bands values."""
        # df = data.to_dataframe() # Removed - data is now DataFrame
        df = data
        period = self.params['period']
        if df.empty or period > len(df): # Check period validity
            print(f"BollingerBandsIndicator Error: Insufficient data for period {period}.")
            nan_array = np.full(len(df), np.nan)
            return { # Return expected keys with NaNs
                'middle_band': nan_array, 'upper_band': nan_array, 'lower_band': nan_array,
                'bandwidth': nan_array, 'percent_b': nan_array, 'std_dev': nan_array,
                'source': nan_array
            }

        num_std_dev = self.params['num_std_dev']
        source_col = self.params['source'].lower()
        ma_type = self.params['ma_type'].lower()

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Calculate Middle Band (Moving Average)
        if ma_type == 'sma':
            middle_band = source_data.rolling(window=period).mean()
        else: # ema
            # Note: Using pandas ewm directly for middle band in BBands is common
            middle_band = source_data.ewm(span=period, adjust=False).mean()

        # Calculate Standard Deviation
        std_dev = source_data.rolling(window=period).std()

        # Calculate Upper and Lower Bands
        upper_band = middle_band + (num_std_dev * std_dev)
        lower_band = middle_band - (num_std_dev * std_dev)

        # Calculate Bandwidth
        bandwidth = ((upper_band - lower_band) / middle_band) * 100

        # Calculate %B (Percent B) - Avoid division by zero
        band_range = upper_band - lower_band
        percent_b = ((source_data - lower_band) / band_range.replace(0, np.nan)).fillna(0.5) # Fill NaN %B with 0.5 (middle)

        return {
            'middle_band': middle_band.values,
            'upper_band': upper_band.values,
            'lower_band': lower_band.values,
            'bandwidth': bandwidth.values,
            'percent_b': percent_b.values,
            'std_dev': std_dev.values,
            'source': source_data.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        valid_ma_types = ['sma', 'ema']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['ma_type'].lower() not in valid_ma_types:
            raise ValueError(f"MA type must be one of {valid_ma_types}")
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        if self.params['num_std_dev'] <= 0:
            raise ValueError("Number of standard deviations must be positive")
        return True
