:root {
  /* Modern Green Theme */
  --primary-color: #0D5D56; /* Deep green */
  --primary-light: #1A7A72;
  --primary-dark: #084842;
  --accent-color: #F0C75E; /* Gold */
  --accent-light: #F7D683;
  --accent-dark: #E5B43D;
  --secondary-color: #E8F1F2; /* Off-white */
  --secondary-light: #FFFFFF;
  --secondary-dark: #D1DBDC;

  /* Background Colors */
  --bg-dark: #0A2E2A;
  --bg-card: #0F3D38;
  --bg-card-hover: #124842;
  --bg-gradient: linear-gradient(135deg, #0A2E2A 0%, #0F3D38 100%);

  /* Text Colors */
  --text-primary: #FFFFFF;
  --text-secondary: rgba(255, 255, 255, 0.87);
  --text-tertiary: rgba(255, 255, 255, 0.6);
  --text-on-light: #121212;

  /* Utility Colors */
  --success-color: #00E676;
  --warning-color: #FFEA00;
  --danger-color: #FF5252;

  /* Shadows */
  --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  --card-shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.35);
  --glow-primary: 0 0 20px rgba(108, 99, 255, 0.5);
  --glow-accent: 0 0 20px rgba(0, 224, 255, 0.5);
  --glow-secondary: 0 0 20px rgba(255, 94, 132, 0.5);

  /* Transitions */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
  --gradient-hero: linear-gradient(135deg, #1A1A2E 0%, #16213E 100%);
  --gradient-card: linear-gradient(135deg, rgba(30, 30, 30, 0.8) 0%, rgba(20, 20, 20, 0.8) 100%);

  /* Border Radius */
  --border-radius-sm: 8px;
  --border-radius-md: 16px;
  --border-radius-lg: 24px;
  --border-radius-xl: 32px;

  /* Glass Effect */
  --glass-bg: rgba(30, 30, 30, 0.7);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-dark);
  overflow-x: hidden;
  background-image:
    radial-gradient(circle at 15% 50%, rgba(108, 99, 255, 0.08) 0%, transparent 25%),
    radial-gradient(circle at 85% 30%, rgba(0, 224, 255, 0.08) 0%, transparent 25%),
    radial-gradient(circle at 50% 80%, rgba(255, 94, 132, 0.08) 0%, transparent 25%);
  background-attachment: fixed;
}

.container {
  width: 100%;
  max-width: 1300px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  position: relative;
  z-index: 1;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: var(--bg-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-dark);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* Header and Navigation */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: rgba(18, 18, 18, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--glass-border);
  z-index: 1000;
  padding: 15px 0;
  transition: all 0.4s ease;
}

.header.scrolled {
  padding: 10px 0;
  background-color: rgba(18, 18, 18, 0.95);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.2);
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all 0.3s ease;
  position: relative;
}

.logo::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--gradient-accent);
  transition: width 0.3s ease;
}

.logo:hover {
  transform: translateY(-2px);
}

.logo:hover::after {
  width: 100%;
}

.logo img {
  height: 40px;
  width: auto;
  filter: drop-shadow(0 0 8px rgba(0, 224, 255, 0.5));
}

.logo h1 {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--text-primary);
  letter-spacing: -0.5px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
}

.nav-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.language-selector {
  display: flex;
  gap: 0;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.05);
  padding: 2px;
  border: 1px solid var(--glass-border);
}

.language-selector button {
  border: none;
  background: none;
  padding: 6px 14px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 600;
  border-radius: var(--border-radius-lg);
  transition: all 0.25s ease;
  color: var(--text-tertiary);
}

.language-selector button:hover {
  color: var(--text-secondary);
}

.language-selector button.active {
  background: var(--gradient-primary);
  color: var(--text-primary);
  box-shadow: var(--glow-primary);
}

/* Buttons */
.btn-primary, .btn-secondary, .btn-cta {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 14px 28px;
  border-radius: var(--border-radius-lg);
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5px;
  border: none;
}

.btn-primary::before, .btn-secondary::before, .btn-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(120deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
  transition: 0.6s;
}

.btn-primary:hover::before, .btn-secondary:hover::before, .btn-cta:hover::before {
  transform: translateX(100%);
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--text-primary);
  box-shadow: var(--glow-primary);
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 0 30px rgba(108, 99, 255, 0.6);
}

.btn-secondary {
  background-color: transparent;
  color: var(--text-primary);
  border: 2px solid var(--primary-color);
  box-shadow: inset 0 0 0 0 var(--primary-color);
  transition: all 0.4s ease-in-out;
}

.btn-secondary:hover {
  box-shadow: inset 0 -100px 0 0 var(--primary-color);
  transform: translateY(-3px);
}

.btn-cta {
  background: var(--gradient-accent);
  color: var(--text-on-light);
  font-size: 1.1rem;
  padding: 16px 32px;
  box-shadow: var(--glow-accent);
  font-weight: 700;
  position: relative;
  z-index: 1;
}

.btn-cta::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-secondary);
  border-radius: var(--border-radius-lg);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.5s ease;
}

.btn-cta:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 0 30px rgba(0, 224, 255, 0.7);
}

.btn-cta:hover::after {
  opacity: 1;
}

.btn-cta:active, .btn-primary:active, .btn-secondary:active {
  transform: translateY(-1px);
}

.btn-icon {
  margin-left: var(--spacing-sm);
  transition: transform 0.3s ease;
}

.btn-primary:hover .btn-icon,
.btn-secondary:hover .btn-icon,
.btn-cta:hover .btn-icon {
  transform: translateX(4px);
}

.full-width {
  width: 100%;
  display: block;
}

/* Hero Section */
.hero {
  padding-top: 140px;
  padding-bottom: 120px;
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 30%, rgba(108, 99, 255, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 224, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 70%, rgba(255, 94, 132, 0.1) 0%, transparent 50%);
  z-index: 0;
}

.hero-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.particle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.3;
  animation: float-particle 15s infinite linear;
}

.particle:nth-child(1) {
  width: 80px;
  height: 80px;
  background: var(--primary-color);
  filter: blur(20px);
  top: 10%;
  left: 10%;
  animation-duration: 25s;
}

.particle:nth-child(2) {
  width: 120px;
  height: 120px;
  background: var(--accent-color);
  filter: blur(30px);
  top: 70%;
  left: 80%;
  animation-duration: 30s;
}

.particle:nth-child(3) {
  width: 100px;
  height: 100px;
  background: var(--secondary-color);
  filter: blur(25px);
  top: 40%;
  left: 60%;
  animation-duration: 20s;
}

@keyframes float-particle {
  0% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(-50px, 50px) rotate(90deg); }
  50% { transform: translate(0, 100px) rotate(180deg); }
  75% { transform: translate(50px, 50px) rotate(270deg); }
  100% { transform: translate(0, 0) rotate(360deg); }
}

.hero-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero-content {
  position: relative;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  background: rgba(108, 99, 255, 0.1);
  border: 1px solid rgba(108, 99, 255, 0.3);
  padding: 8px 16px;
  border-radius: 50px;
  margin-bottom: 24px;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--primary-light);
}

.hero-badge i {
  margin-right: 8px;
  color: var(--primary-color);
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 24px;
  letter-spacing: -1px;
  position: relative;
}

.hero-title .gradient-text {
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  display: inline-block;
}

.hero-title .gradient-text::after {
  content: '';
  position: absolute;
  bottom: 5px;
  left: 0;
  width: 100%;
  height: 8px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  opacity: 0.3;
  border-radius: 4px;
  z-index: -1;
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 36px;
  color: var(--text-secondary);
  line-height: 1.6;
  max-width: 90%;
}

.hero-cta {
  display: flex;
  gap: 20px;
  margin-bottom: 36px;
}

.hero-image {
  position: relative;
  height: 500px;
}

.main-screenshot {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: auto;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--glass-shadow);
  transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  z-index: 2;
  transform: perspective(1000px) rotateY(-8deg) rotateX(5deg);
  border: 1px solid var(--glass-border);
}

.main-screenshot:hover {
  transform: perspective(1000px) rotateY(-4deg) rotateX(2deg) scale(1.02);
  box-shadow: var(--glow-primary);
}

.floating-card {
  position: absolute;
  display: flex;
  align-items: center;
  background: var(--glass-bg);
  padding: 20px;
  border-radius: var(--border-radius-md);
  box-shadow: var(--glass-shadow);
  z-index: 3;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  border: 1px solid var(--glass-border);
}

.floating-card:hover {
  transform: translateY(-5px) scale(1.05) !important;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.card-1 {
  top: 50px;
  left: -40px;
  animation: float 6s ease-in-out infinite;
}

.card-2 {
  bottom: 70px;
  right: -30px;
  animation: float 6s ease-in-out infinite 2s;
}

@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(1deg); }
}

.floating-card i {
  font-size: 1.8rem;
  margin-right: 15px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.card-content h3 {
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: 700;
  margin-bottom: 4px;
}

.card-content p {
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.special-offer {
  display: flex;
  align-items: center;
  gap: 18px;
  background: var(--glass-bg);
  padding: 16px 24px;
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
}

.offer-badge {
  background: var(--gradient-secondary);
  color: var(--text-primary);
  padding: 8px 16px;
  border-radius: var(--border-radius-lg);
  font-size: 0.9rem;
  font-weight: 700;
  animation: pulse 2s infinite;
  box-shadow: var(--glow-secondary);
}

@keyframes pulse {
  0% { transform: scale(1); box-shadow: var(--glow-secondary); }
  50% { transform: scale(1.05); box-shadow: 0 0 25px rgba(255, 94, 132, 0.6); }
  100% { transform: scale(1); box-shadow: var(--glow-secondary); }
}

.hero-wave {
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  z-index: 1;
  filter: drop-shadow(0 -5px 5px rgba(0, 0, 0, 0.1));
}

/* Section Styles */
section {
  padding: 120px 0;
  position: relative;
}

.section-header {
  text-align: center;
  margin-bottom: 70px;
  position: relative;
}

.section-header .section-tag {
  display: inline-flex;
  align-items: center;
  background: rgba(108, 99, 255, 0.1);
  border: 1px solid rgba(108, 99, 255, 0.3);
  padding: 8px 16px;
  border-radius: 50px;
  margin-bottom: 16px;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--primary-light);
}

.section-header .section-tag i {
  margin-right: 8px;
  color: var(--primary-color);
}

.section-header h2 {
  font-size: 2.8rem;
  font-weight: 800;
  margin-bottom: 20px;
  letter-spacing: -0.5px;
  position: relative;
  display: inline-block;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.section-header p {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Benefits Section */
.benefits {
  position: relative;
  overflow: hidden;
}

.benefits::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(108, 99, 255, 0.05) 0%, transparent 70%);
  z-index: 0;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  position: relative;
  z-index: 1;
}

.benefit-card {
  background: var(--glass-bg);
  border-radius: var(--border-radius-lg);
  padding: 40px;
  box-shadow: var(--glass-shadow);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  border: 1px solid var(--glass-border);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.benefit-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(108, 99, 255, 0.05) 0%, rgba(0, 224, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.benefit-card:hover {
  transform: translateY(-15px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(108, 99, 255, 0.3);
}

.benefit-card:hover::before {
  opacity: 1;
}

.benefit-icon {
  width: 80px;
  height: 80px;
  background: var(--gradient-primary);
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  box-shadow: var(--glow-primary);
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.benefit-card:hover .benefit-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 0 30px rgba(108, 99, 255, 0.5);
}

.benefit-icon i {
  font-size: 2rem;
  color: white;
}

.benefit-card h3 {
  font-size: 1.5rem;
  margin-bottom: 16px;
  color: var(--text-primary);
  font-weight: 700;
  position: relative;
}

.benefit-card p {
  color: var(--text-secondary);
  line-height: 1.7;
  font-size: 1.05rem;
}

/* Features Section */
.features {
  position: relative;
  overflow: hidden;
}

.features::after {
  content: '';
  position: absolute;
  bottom: -150px;
  left: -150px;
  width: 500px;
  height: 500px;
  background: radial-gradient(circle, rgba(0, 224, 255, 0.05) 0%, transparent 70%);
  z-index: 0;
  border-radius: 50%;
}

.features-grid {
  display: flex;
  flex-direction: column;
  gap: 100px;
  position: relative;
  z-index: 1;
}

.feature-card {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  position: relative;
}

.feature-card::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  bottom: -50px;
  left: 0;
}

.feature-card:last-child::after {
  display: none;
}

.feature-card.reversed {
  grid-template-columns: 1fr 1fr;
}

.feature-img-wrapper {
  position: relative;
}

.feature-img-wrapper::before {
  content: '';
  position: absolute;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(108, 99, 255, 0.15) 0%, transparent 70%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 0;
  border-radius: 50%;
}

.feature-img {
  width: 100%;
  height: auto;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--glass-shadow);
  transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  border: 1px solid var(--glass-border);
  position: relative;
  z-index: 1;
  transform: perspective(1000px) rotateY(-5deg);
}

.feature-card.reversed .feature-img {
  transform: perspective(1000px) rotateY(5deg);
}

.feature-img:hover {
  transform: perspective(1000px) rotateY(0) translateY(-10px);
  box-shadow: var(--glow-primary);
}

.feature-card.reversed .feature-img:hover {
  transform: perspective(1000px) rotateY(0) translateY(-10px);
}

.feature-content {
  position: relative;
}

.feature-number {
  font-size: 5rem;
  font-weight: 900;
  position: absolute;
  top: -70px;
  left: -20px;
  opacity: 0.1;
  color: var(--primary-color);
  z-index: -1;
}

.feature-card.reversed .feature-number {
  left: auto;
  right: -20px;
}

.feature-tag {
  display: inline-flex;
  align-items: center;
  background: rgba(0, 224, 255, 0.1);
  border: 1px solid rgba(0, 224, 255, 0.3);
  padding: 8px 16px;
  border-radius: 50px;
  margin-bottom: 16px;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--accent-light);
}

.feature-tag i {
  margin-right: 8px;
  color: var(--accent-color);
}

.feature-content h3 {
  font-size: 2.2rem;
  margin-bottom: 20px;
  font-weight: 700;
  letter-spacing: -0.5px;
  line-height: 1.3;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.feature-content p {
  font-size: 1.15rem;
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: 25px;
}

.feature-list {
  margin-top: 25px;
}

.feature-list-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}

.feature-list-icon {
  color: var(--accent-color);
  margin-right: 15px;
  font-size: 1.2rem;
  margin-top: 3px;
}

.feature-list-text {
  color: var(--text-secondary);
  font-size: 1.05rem;
  line-height: 1.6;
}

/* Testimonials */
.testimonials {
  position: relative;
  overflow: hidden;
}

.testimonials::before {
  content: '';
  position: absolute;
  top: -100px;
  right: -100px;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(255, 94, 132, 0.05) 0%, transparent 70%);
  z-index: 0;
}

.testimonials::after {
  content: '';
  position: absolute;
  bottom: -100px;
  left: -100px;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(0, 224, 255, 0.05) 0%, transparent 70%);
  z-index: 0;
}

.testimonial-slider {
  position: relative;
  max-width: 900px;
  margin: 0 auto 40px;
  height: 340px;
  z-index: 1;
}

.testimonial-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
  transform: translateX(50px);
}

.testimonial-slide.active {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

.testimonial-card {
  background: var(--glass-bg);
  border-radius: var(--border-radius-lg);
  padding: 40px;
  box-shadow: var(--glass-shadow);
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid var(--glass-border);
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.testimonial-card::before {
  content: '"';
  position: absolute;
  top: 20px;
  left: 20px;
  font-size: 120px;
  line-height: 1;
  font-family: Georgia, serif;
  color: rgba(108, 99, 255, 0.1);
  z-index: 0;
}

.testimonial-rating {
  margin-bottom: 25px;
  position: relative;
  z-index: 1;
}

.testimonial-rating i {
  color: var(--accent-color);
  margin-right: 5px;
  font-size: 1.2rem;
  filter: drop-shadow(0 0 5px rgba(0, 224, 255, 0.5));
}

.testimonial-text {
  font-size: 1.2rem;
  font-style: italic;
  margin-bottom: 30px;
  flex-grow: 1;
  line-height: 1.7;
  color: var(--text-secondary);
  position: relative;
  z-index: 1;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  z-index: 1;
}

.testimonial-author img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: var(--glow-primary);
  border: 2px solid var(--primary-color);
}

.testimonial-author h4 {
  font-weight: 700;
  color: var(--text-primary);
  font-size: 1.1rem;
  margin-bottom: 5px;
}

.testimonial-author p {
  font-size: 0.95rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.testimonial-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
}

.testimonial-controls button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: none;
  background: var(--glass-bg);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: var(--glass-shadow);
  color: var(--text-primary);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.testimonial-controls button:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-3px);
  box-shadow: var(--glow-primary);
}

.testimonial-controls button:active {
  transform: translateY(-1px);
}

.testimonial-dots {
  display: flex;
  gap: 12px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid var(--glass-border);
}

.dot::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  border: 1px solid var(--primary-color);
  opacity: 0;
  transition: all 0.3s ease;
}

.dot:hover {
  background-color: var(--primary-light);
  transform: scale(1.2);
}

.dot.active {
  background-color: var(--primary-color);
  transform: scale(1.2);
  box-shadow: 0 0 10px var(--primary-color);
}

.dot.active::after {
  opacity: 1;
}

/* Comparison Table */
.comparison {
  position: relative;
  overflow: hidden;
}

.comparison::before {
  content: '';
  position: absolute;
  top: -150px;
  right: -150px;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(108, 99, 255, 0.05) 0%, transparent 70%);
  z-index: 0;
}

.comparison-table {
  overflow-x: auto;
  margin-top: 40px;
  position: relative;
  z-index: 1;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--glass-shadow);
}

table {
  width: 100%;
  border-collapse: collapse;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

thead {
  background: var(--gradient-primary);
  color: white;
}

th {
  padding: 25px 30px;
  text-align: left;
  font-weight: 700;
  font-size: 1.1rem;
  letter-spacing: 0.5px;
}

th:first-child {
  width: 220px;
}

tbody tr {
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

tbody tr:hover {
  background-color: rgba(108, 99, 255, 0.1) !important;
}

tbody tr:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.02);
}

td {
  padding: 20px 30px;
  font-size: 1.05rem;
  transition: all 0.3s ease;
  color: var(--text-secondary);
}

td:first-child {
  font-weight: 600;
  color: var(--text-primary);
}

td i.fa-check {
  color: var(--success-color);
}

td i.fa-times {
  color: var(--danger-color);
}

/* How It Works */
.how-it-works {
  position: relative;
  overflow: hidden;
}

.how-it-works::after {
  content: '';
  position: absolute;
  bottom: -100px;
  left: -100px;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(0, 224, 255, 0.05) 0%, transparent 70%);
  z-index: 0;
}

.steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1000px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 280px;
  position: relative;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.step:hover {
  transform: translateY(-15px);
}

.step-number {
  width: 80px;
  height: 80px;
  background: var(--gradient-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 800;
  font-size: 1.8rem;
  margin-bottom: 30px;
  box-shadow: var(--glow-primary);
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.step:hover .step-number {
  transform: scale(1.1);
  box-shadow: 0 0 30px rgba(108, 99, 255, 0.6);
}

.step-connector {
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  flex-grow: 1;
  margin-top: -40px;
  position: relative;
  z-index: 1;
  opacity: 0.7;
}

.step-content {
  background: var(--glass-bg);
  padding: 35px 30px;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--glass-shadow);
  transition: all 0.3s ease;
  height: 100%;
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.step:hover .step-content {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(108, 99, 255, 0.3);
}

.step-content h3 {
  margin-bottom: 15px;
  color: var(--text-primary);
  font-size: 1.4rem;
  font-weight: 700;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.step-content p {
  font-size: 1.05rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Pricing */
.pricing {
  position: relative;
  overflow: hidden;
}

.pricing::before {
  content: '';
  position: absolute;
  top: -100px;
  left: -100px;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(255, 94, 132, 0.05) 0%, transparent 70%);
  z-index: 0;
}

.pricing-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 40px;
  margin-top: 60px;
  position: relative;
  z-index: 1;
}

.pricing-card {
  background: var(--glass-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--glass-shadow);
  padding: 40px;
  position: relative;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  border: 1px solid var(--glass-border);
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  overflow: hidden;
}

.pricing-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
}

.pricing-card.popular::before {
  background: var(--gradient-accent);
}

.pricing-card:hover::before {
  transform: scaleX(1);
}

.pricing-card:hover {
  transform: translateY(-15px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(108, 99, 255, 0.3);
}

.pricing-card.popular {
  border: 2px solid var(--accent-color);
  transform: scale(1.05);
  z-index: 2;
  box-shadow: var(--glow-accent);
}

.pricing-card.popular:hover {
  transform: scale(1.05) translateY(-15px);
  box-shadow: 0 0 30px rgba(0, 224, 255, 0.5);
}

.popular-badge {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--gradient-accent);
  color: var(--text-on-light);
  padding: 8px 20px;
  border-radius: var(--border-radius-lg);
  font-size: 0.9rem;
  font-weight: 700;
  box-shadow: var(--glow-accent);
  letter-spacing: 0.5px;
}

.pricing-header {
  text-align: center;
  margin-bottom: 35px;
  padding-bottom: 25px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.pricing-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  border-radius: 3px;
}

.pricing-header h3 {
  font-size: 1.8rem;
  color: var(--text-primary);
  margin-bottom: 20px;
  font-weight: 700;
}

.pricing-price {
  font-size: 3.8rem;
  font-weight: 800;
  line-height: 1;
  margin-bottom: 15px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.pricing-card.popular .pricing-price {
  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.currency {
  font-size: 2rem;
  font-weight: 700;
  vertical-align: super;
  margin-right: 5px;
}

.period {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-secondary);
  -webkit-text-fill-color: var(--text-secondary);
}

.pricing-features {
  flex-grow: 1;
  margin-bottom: 30px;
}

.pricing-features ul {
  list-style: none;
  margin-bottom: 0;
}

.pricing-features li {
  padding: 12px 0;
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 1.05rem;
  color: var(--text-secondary);
  border-bottom: 1px dashed rgba(255, 255, 255, 0.05);
}

.pricing-features li:last-child {
  border-bottom: none;
}

.pricing-features i.fa-check {
  color: var(--success-color);
  font-size: 1.2rem;
  filter: drop-shadow(0 0 5px rgba(0, 230, 118, 0.5));
}

.pricing-features i.fa-times {
  color: var(--danger-color);
  font-size: 1.2rem;
  filter: drop-shadow(0 0 5px rgba(255, 82, 82, 0.5));
}

.annual-savings {
  position: relative;
}

.annual-badge {
  position: absolute;
  top: -15px;
  right: -15px;
  background: var(--gradient-secondary);
  color: var(--text-primary);
  padding: 8px 15px;
  border-radius: var(--border-radius-lg);
  font-size: 0.85rem;
  font-weight: 700;
  transform: rotate(15deg);
  box-shadow: var(--glow-secondary);
}

/* CTA Section */
.cta {
  position: relative;
  overflow: hidden;
  text-align: center;
}

.cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 30%, rgba(108, 99, 255, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 224, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 70%, rgba(255, 94, 132, 0.1) 0%, transparent 50%);
  z-index: 0;
}

.cta-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.cta-particle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.3;
  animation: float-particle 15s infinite linear;
}

.cta-particle:nth-child(1) {
  width: 100px;
  height: 100px;
  background: var(--primary-color);
  filter: blur(30px);
  top: 20%;
  left: 10%;
  animation-duration: 25s;
}

.cta-particle:nth-child(2) {
  width: 150px;
  height: 150px;
  background: var(--accent-color);
  filter: blur(40px);
  top: 60%;
  left: 75%;
  animation-duration: 30s;
}

.cta-content {
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  background: var(--glass-bg);
  padding: 60px;
  border-radius: var(--border-radius-xl);
  box-shadow: var(--glass-shadow);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.cta-content h2 {
  font-size: 3rem;
  margin-bottom: 25px;
  font-weight: 800;
  letter-spacing: -0.5px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color), var(--secondary-color));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  display: inline-block;
}

.cta-content h2::after {
  content: '';
  position: absolute;
  bottom: 5px;
  left: 0;
  width: 100%;
  height: 10px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color), var(--secondary-color));
  opacity: 0.2;
  border-radius: 5px;
  z-index: -1;
}

.cta-content p {
  font-size: 1.3rem;
  margin-bottom: 40px;
  color: var(--text-secondary);
  line-height: 1.6;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.btn-cta {
  padding: 18px 40px;
  font-size: 1.2rem;
  letter-spacing: 0.5px;
  box-shadow: var(--glow-accent);
}

.small-text {
  font-size: 0.9rem;
  color: var(--text-tertiary);
  margin-top: 25px;
}

/* Footer */
.footer {
  background-color: rgba(18, 18, 18, 0.8);
  padding: 100px 0 30px;
  position: relative;
  overflow: hidden;
  border-top: 1px solid var(--glass-border);
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 10% 90%, rgba(108, 99, 255, 0.05) 0%, transparent 40%),
    radial-gradient(circle at 90% 10%, rgba(0, 224, 255, 0.05) 0%, transparent 40%);
  z-index: 0;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 60px;
  margin-bottom: 60px;
  position: relative;
  z-index: 1;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: 25px;
  transition: all 0.3s ease;
  position: relative;
}

.footer-logo::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--gradient-accent);
  transition: width 0.3s ease;
}

.footer-logo:hover {
  transform: translateY(-2px);
}

.footer-logo:hover::after {
  width: 100%;
}

.footer-logo img {
  height: 40px;
  width: auto;
  filter: drop-shadow(0 0 8px rgba(0, 224, 255, 0.5));
}

.footer-logo h2 {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--text-primary);
  letter-spacing: -0.5px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.footer-col h3 {
  font-size: 1.2rem;
  color: var(--text-primary);
  margin-bottom: 25px;
  font-weight: 700;
  position: relative;
  display: inline-block;
}

.footer-col h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 30px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  border-radius: 3px;
}

.footer-col ul {
  list-style: none;
}

.footer-col li {
  margin-bottom: 15px;
}

.footer-col a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
  display: inline-block;
}

.footer-col a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  transition: width 0.3s ease;
}

.footer-col a:hover {
  color: var(--text-primary);
}

.footer-col a:hover::after {
  width: 100%;
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icons a {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: var(--glass-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  color: var(--text-secondary);
  box-shadow: var(--glass-shadow);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.social-icons a:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-5px) scale(1.1);
  box-shadow: var(--glow-primary);
}

.copyright {
  text-align: center;
  padding-top: 30px;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  color: var(--text-tertiary);
  font-weight: 500;
  position: relative;
  z-index: 1;
}

/* Download Modal */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 2000;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.modal.show {
  display: flex;
  animation: modalBackdropFadeIn 0.3s forwards;
}

@keyframes modalBackdropFadeIn {
  from { background-color: rgba(0, 0, 0, 0); backdrop-filter: blur(0); }
  to { background-color: rgba(0, 0, 0, 0.7); backdrop-filter: blur(10px); }
}

.modal-content {
  background: var(--glass-bg);
  padding: 60px;
  border-radius: var(--border-radius-xl);
  max-width: 600px;
  width: 100%;
  position: relative;
  animation: modalFadeIn 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  box-shadow: var(--glass-shadow);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

@keyframes modalFadeIn {
  from { opacity: 0; transform: translateY(-70px) scale(0.9); }
  to { opacity: 1; transform: translateY(0) scale(1); }
}

.close-modal {
  position: absolute;
  top: 20px;
  right: 25px;
  font-size: 1.8rem;
  cursor: pointer;
  color: var(--text-secondary);
  transition: all 0.3s ease;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--glass-border);
}

.close-modal:hover {
  color: var(--danger-color);
  background-color: rgba(255, 82, 82, 0.1);
  transform: rotate(90deg);
  box-shadow: 0 0 15px rgba(255, 82, 82, 0.3);
}

.modal-content h2 {
  font-size: 2.2rem;
  margin-bottom: 35px;
  text-align: center;
  font-weight: 700;
  letter-spacing: -0.5px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.download-options {
  margin-bottom: 40px;
}

.download-button {
  display: flex;
  align-items: center;
  gap: 25px;
  padding: 25px 30px;
  background: var(--gradient-primary);
  color: white;
  text-decoration: none;
  border-radius: var(--border-radius-lg);
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  box-shadow: var(--glow-primary);
  position: relative;
  overflow: hidden;
}

.download-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(120deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
  transition: 0.6s;
}

.download-button:hover::before {
  transform: translateX(100%);
}

.download-button:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 0 30px rgba(108, 99, 255, 0.6);
}

.download-button i {
  font-size: 2.8rem;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.download-button span {
  display: flex;
  flex-direction: column;
}

.download-button strong {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.download-button small {
  font-size: 0.95rem;
  opacity: 0.9;
}

.download-note {
  text-align: center;
  color: var(--text-secondary);
  line-height: 1.6;
  background: rgba(255, 255, 255, 0.05);
  padding: 20px;
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--glass-border);
}

.download-note p {
  margin-bottom: 10px;
}

.download-note a {
  color: var(--accent-color);
  text-decoration: none;
  font-weight: 600;
  position: relative;
  transition: all 0.3s ease;
}

.download-note a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
  transition: width 0.3s ease;
}

.download-note a:hover {
  color: var(--primary-color);
}

.download-note a:hover::after {
  width: 100%;
}

/* FAQ Section */
.faq {
  position: relative;
  overflow: hidden;
}

.faq::before {
  content: '';
  position: absolute;
  top: -100px;
  right: -100px;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(108, 99, 255, 0.05) 0%, transparent 70%);
  z-index: 0;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 30px;
  position: relative;
  z-index: 1;
}

.faq-item {
  background: var(--glass-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--glass-shadow);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.faq-item:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  transform: translateY(-5px);
  border-color: rgba(108, 99, 255, 0.3);
}

.faq-question {
  padding: 25px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  position: relative;
}

.faq-question::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 30px;
  right: 30px;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.faq-question h3 {
  font-size: 1.15rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  padding-right: 40px;
}

.faq-icon {
  color: var(--accent-color);
  transition: all 0.3s ease;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 224, 255, 0.1);
  border: 1px solid rgba(0, 224, 255, 0.3);
}

.faq-item.active .faq-icon {
  transform: rotate(180deg);
  background: var(--accent-color);
  color: var(--text-on-light);
  box-shadow: var(--glow-accent);
}

.faq-answer {
  padding: 0 30px;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
  padding: 20px 30px 30px;
  max-height: 300px;
}

.faq-answer p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
  font-size: 1.05rem;
}

/* Scroll Animations */
.benefit-card, .feature-card, .pricing-card, .step, .faq-item {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease, transform 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.benefit-card.animate, .feature-card.animate, .pricing-card.animate, .step.animate, .faq-item.animate {
  opacity: 1;
  transform: translateY(0);
}

.benefit-card:nth-child(2), .feature-card:nth-child(2), .pricing-card:nth-child(2), .step:nth-child(3), .faq-item:nth-child(2) {
  transition-delay: 0.2s;
}

.benefit-card:nth-child(3), .feature-card:nth-child(3), .pricing-card:nth-child(3), .step:nth-child(5), .faq-item:nth-child(3) {
  transition-delay: 0.4s;
}

.benefit-card:nth-child(4), .feature-card:nth-child(4), .pricing-card:nth-child(4), .faq-item:nth-child(4) {
  transition-delay: 0.6s;
}

.faq-item:nth-child(5) {
  transition-delay: 0.8s;
}

.faq-item:nth-child(6) {
  transition-delay: 1s;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .hero-title {
    font-size: 3rem;
  }

  .feature-content h3 {
    font-size: 2rem;
  }

  .step-content {
    padding: 30px 25px;
  }

  .cta-content {
    padding: 50px 40px;
  }
}

@media (max-width: 992px) {
  section {
    padding: 100px 0;
  }

  .hero-container {
    grid-template-columns: 1fr;
    gap: 50px;
  }

  .hero-image {
    grid-row: 1;
    height: 400px;
    margin-bottom: 20px;
  }

  .main-screenshot {
    position: relative;
    top: 0;
    right: 0;
    transform: perspective(1000px) rotateY(0) rotateX(0);
  }

  .hero-title {
    font-size: 2.8rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
    max-width: 100%;
  }

  .feature-card, .feature-card.reversed {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .feature-card.reversed .feature-img {
    grid-row: 1;
  }

  .feature-number {
    top: -50px;
    left: 0;
  }

  .feature-card.reversed .feature-number {
    left: 0;
    right: auto;
  }

  .testimonial-slider {
    height: 380px;
  }

  .pricing-card.popular {
    transform: scale(1.03);
  }

  .pricing-card.popular:hover {
    transform: scale(1.03) translateY(-15px);
  }

  .cta-content h2 {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 15px 0;
  }

  .navbar {
    flex-direction: column;
    gap: 15px;
  }

  .hero {
    padding-top: 160px;
    padding-bottom: 100px;
    min-height: auto;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-cta {
    gap: 15px;
  }

  .section-header h2 {
    font-size: 2.2rem;
  }

  .section-header p {
    font-size: 1.1rem;
  }

  .benefit-card {
    padding: 30px;
  }

  .steps {
    flex-direction: column;
    gap: 60px;
  }

  .step {
    max-width: 100%;
    width: 100%;
  }

  .step-connector {
    width: 3px;
    height: 50px;
    margin-top: 0;
    margin-bottom: 10px;
  }

  .testimonial-card {
    padding: 30px;
  }

  .testimonial-text {
    font-size: 1.1rem;
  }

  .modal-content {
    padding: 40px 30px;
    max-width: 90%;
  }

  .faq-grid {
    grid-template-columns: 1fr;
  }

  .cta-content {
    padding: 40px 30px;
  }

  .cta-buttons {
    flex-direction: column;
    gap: 15px;
  }
}

@media (max-width: 576px) {
  .hero-badge {
    margin-bottom: 15px;
  }

  .hero-title {
    font-size: 2.2rem;
  }

  .hero-cta {
    flex-direction: column;
    width: 100%;
  }

  .hero-cta .btn-primary, .hero-cta .btn-secondary {
    width: 100%;
  }

  .floating-card {
    display: none;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .section-header p {
    font-size: 1rem;
  }

  .benefit-icon {
    width: 65px;
    height: 65px;
  }

  .feature-content h3 {
    font-size: 1.8rem;
  }

  .feature-content p {
    font-size: 1rem;
  }

  .testimonial-slider {
    height: 420px;
  }

  .pricing-card {
    padding: 30px 20px;
  }

  .pricing-card.popular {
    transform: none;
  }

  .pricing-card.popular:hover {
    transform: translateY(-15px);
  }

  .cta-content {
    padding: 30px 20px;
  }

  .cta-content h2 {
    font-size: 2rem;
  }

  .cta-content p {
    font-size: 1.1rem;
  }

  .btn-cta {
    width: 100%;
  }

  .download-button {
    padding: 20px;
    gap: 15px;
  }

  .download-button i {
    font-size: 2.2rem;
  }

  .download-button strong {
    font-size: 1.1rem;
  }
}
