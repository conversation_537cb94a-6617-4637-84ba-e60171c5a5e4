#!/usr/bin/env python3
"""
Flask app runner with Entry Spacing API integration
Run this file to start your Flask app with Entry Spacing endpoints enabled
"""

from flask import Flask
from flask_cors import CORS
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import your existing Flask app setup here
# You'll need to replace this with your actual app import
# For example: from your_main_app import app

# If you don't have an existing app, here's a basic setup:
app = Flask(__name__)
CORS(app, origins=["http://localhost:3000"])

# Import and register all your existing blueprints here
# For example:
# from backend.api.market_data_api import market_data_bp
# from backend.api.trading_api import trading_bp
# from backend.api.autonomous_api import autonomous_bp

# Register the Entry Spacing API blueprint
from backend.api.entry_spacing_api import entry_spacing_bp
app.register_blueprint(entry_spacing_bp)

# Add any other blueprints you have
# app.register_blueprint(market_data_bp)
# app.register_blueprint(trading_bp)
# app.register_blueprint(autonomous_bp)

@app.route('/health')
def health_check():
    return {'status': 'ok', 'entry_spacing_enabled': True}

if __name__ == '__main__':
    print("Starting Flask app with Entry Spacing API...")
    print("Entry Spacing endpoints available at:")
    print("  GET/POST /api/entry_spacing/settings")
    print("  GET /api/entry_spacing/statistics") 
    print("  POST /api/entry_spacing/reset")
    print("  POST /api/entry_spacing/check_entry_allowed")
    print("  POST /api/entry_spacing/record_trade")
    print("")
    print("Frontend should now show the Entry Spacing tab!")
    
    app.run(host='localhost', port=5001, debug=True)
