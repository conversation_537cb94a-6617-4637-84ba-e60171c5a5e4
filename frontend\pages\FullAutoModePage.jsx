import React, { useState, useEffect } from 'react';
import '../styles/full-auto-mode.css';

const FullAutoModePage = () => {
  // State management
  const [isRunning, setIsRunning] = useState(false);
  const [selectedSymbols, setSelectedSymbols] = useState([]);
  const [availableSymbols, setAvailableSymbols] = useState({
    major_pairs: [],
    minor_pairs: [],
    exotic_pairs: []
  });
  const [accountBalance, setAccountBalance] = useState('');
  const [autoModeStatus, setAutoModeStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Fetch available symbols on component mount
  useEffect(() => {
    fetchAvailableSymbols();
    fetchAutoModeStatus();

    // Set up status polling when running
    let statusInterval;
    if (isRunning) {
      statusInterval = setInterval(fetchAutoModeStatus, 5000); // Poll every 5 seconds
    }

    return () => {
      if (statusInterval) clearInterval(statusInterval);
    };
  }, [isRunning]);

  const fetchAvailableSymbols = async () => {
    try {
      // Mock data for now - replace with actual API call when backend is ready
      const mockSymbols = {
        major_pairs: [
          { name: 'EURUSD' },
          { name: 'GBPUSD' },
          { name: 'USDJPY' },
          { name: 'USDCHF' },
          { name: 'AUDUSD' },
          { name: 'USDCAD' },
          { name: 'NZDUSD' }
        ],
        minor_pairs: [
          { name: 'EURJPY' },
          { name: 'GBPJPY' },
          { name: 'EURGBP' },
          { name: 'AUDCAD' },
          { name: 'AUDCHF' },
          { name: 'CADCHF' }
        ],
        exotic_pairs: [
          { name: 'USDTRY' },
          { name: 'USDZAR' },
          { name: 'USDMXN' },
          { name: 'USDSEK' }
        ]
      };

      setAvailableSymbols(mockSymbols);

      // Uncomment when backend API is ready:
      // const response = await fetch('/api/autonomous/full-auto/available-symbols');
      // const data = await response.json();
      //
      // if (data.success) {
      //   setAvailableSymbols(data.symbols);
      // } else {
      //   setError('Failed to fetch available symbols');
      // }
    } catch (err) {
      setError('Error fetching symbols: ' + err.message);
    }
  };

  const fetchAutoModeStatus = async () => {
    try {
      // Mock status for now - replace with actual API call when backend is ready
      if (isRunning) {
        const mockStatus = {
          state: 'ANALYZING_MARKET',
          selected_symbols: selectedSymbols,
          active_positions: 3,
          current_risk_level: 2.5,
          daily_stats: {
            daily_pnl: 125.50,
            trades_today: 8,
            wins_today: 6
          }
        };
        setAutoModeStatus(mockStatus);
      }

      // Uncomment when backend API is ready:
      // const response = await fetch('/api/autonomous/full-auto/status');
      // const data = await response.json();
      //
      // if (data.success) {
      //   setAutoModeStatus(data.status);
      //   setIsRunning(data.status?.running || false);
      // }
    } catch (err) {
      console.error('Error fetching auto mode status:', err);
    }
  };

  const handleSymbolToggle = (symbol) => {
    setSelectedSymbols(prev => {
      if (prev.includes(symbol)) {
        return prev.filter(s => s !== symbol);
      } else {
        return [...prev, symbol];
      }
    });
  };

  const handleQuickSelect = (type) => {
    switch (type) {
      case 'major':
        setSelectedSymbols(availableSymbols.major_pairs.map(s => s.name));
        break;
      case 'conservative':
        setSelectedSymbols(['EURUSD', 'GBPUSD', 'USDJPY']);
        break;
      case 'aggressive':
        setSelectedSymbols([
          ...availableSymbols.major_pairs.slice(0, 5).map(s => s.name),
          ...availableSymbols.minor_pairs.slice(0, 3).map(s => s.name)
        ]);
        break;
      case 'clear':
        setSelectedSymbols([]);
        break;
      default:
        break;
    }
  };

  const startFullAutoMode = async () => {
    if (selectedSymbols.length === 0) {
      setError('Please select at least one trading pair');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Mock start for now - replace with actual API call when backend is ready
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
      setIsRunning(true);
      setError('');

      // Uncomment when backend API is ready:
      // const payload = {
      //   symbols: selectedSymbols
      // };
      //
      // if (accountBalance && !isNaN(parseFloat(accountBalance))) {
      //   payload.account_balance = parseFloat(accountBalance);
      // }
      //
      // const response = await fetch('/api/autonomous/full-auto/start', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify(payload),
      // });
      //
      // const data = await response.json();
      //
      // if (data.success) {
      //   setIsRunning(true);
      //   setError('');
      // } else {
      //   setError(data.message || 'Failed to start Full Auto Mode');
      // }
    } catch (err) {
      setError('Error starting Full Auto Mode: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const stopFullAutoMode = async () => {
    setLoading(true);
    setError('');

    try {
      // Mock stop for now - replace with actual API call when backend is ready
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
      setIsRunning(false);
      setError('');

      // Show mock session summary
      const mockSummary = {
        starting_balance: 10000,
        ending_balance: 10125.50,
        session_pnl: 125.50,
        session_pnl_percent: 1.26,
        total_trades: 8,
        win_rate: 0.75
      };

      alert(`Session Summary:
Starting Balance: $${mockSummary.starting_balance}
Ending Balance: $${mockSummary.ending_balance}
Session P&L: $${mockSummary.session_pnl} (${mockSummary.session_pnl_percent.toFixed(2)}%)
Total Trades: ${mockSummary.total_trades}
Win Rate: ${(mockSummary.win_rate * 100).toFixed(1)}%`);

      // Uncomment when backend API is ready:
      // const response = await fetch('/api/autonomous/full-auto/stop', {
      //   method: 'POST',
      // });
      //
      // const data = await response.json();
      //
      // if (data.success) {
      //   setIsRunning(false);
      //   setError('');
      //   // Show session summary
      //   if (data.summary) {
      //     alert(`Session Summary:
      // Starting Balance: $${data.summary.starting_balance}
      // Ending Balance: $${data.summary.ending_balance}
      // Session P&L: $${data.summary.session_pnl} (${data.summary.session_pnl_percent.toFixed(2)}%)
      // Total Trades: ${data.summary.total_trades}
      // Win Rate: ${(data.summary.win_rate * 100).toFixed(1)}%`);
      //   }
      // } else {
      //   setError(data.message || 'Failed to stop Full Auto Mode');
      // }
    } catch (err) {
      setError('Error stopping Full Auto Mode: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const renderSymbolGrid = (symbols, title) => {
    if (!symbols || symbols.length === 0) return null;

    return (
      <div className="symbol-category">
        <h4>{title}</h4>
        <div className="symbol-grid">
          {symbols.map((symbol) => {
            const symbolName = typeof symbol === 'string' ? symbol : symbol.name;
            const isSelected = selectedSymbols.includes(symbolName);

            return (
              <div
                key={symbolName}
                className={`symbol-card ${isSelected ? 'selected' : ''}`}
                onClick={() => handleSymbolToggle(symbolName)}
              >
                <span className="symbol-name">{symbolName}</span>
                {isSelected && <span className="selected-indicator">✓</span>}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="full-auto-mode-page">
      <div className="page-header">
        <h1>🤖 Full Auto Mode</h1>
        <p className="page-description">
          Advanced AI-driven trading with minimal setup. Just select your trading pairs and let the AI handle everything else.
        </p>
      </div>

      {error && (
        <div className="error-message">
          <span className="error-icon">⚠️</span>
          {error}
        </div>
      )}

      {!isRunning ? (
        <div className="setup-section">
          <div className="setup-card">
            <h2>🎯 Setup Full Auto Mode</h2>

            {/* Account Balance Input */}
            <div className="form-group">
              <label htmlFor="account-balance">Account Balance (Optional)</label>
              <input
                id="account-balance"
                type="number"
                placeholder="Auto-detected from MT5"
                value={accountBalance}
                onChange={(e) => setAccountBalance(e.target.value)}
                disabled={loading}
              />
              <span className="form-help">Leave empty to auto-detect from your MT5 account</span>
            </div>

            {/* Quick Selection Buttons */}
            <div className="quick-select-section">
              <h3>Quick Selection</h3>
              <div className="quick-select-buttons">
                <button
                  className="quick-select-btn conservative"
                  onClick={() => handleQuickSelect('conservative')}
                  disabled={loading}
                >
                  🛡️ Conservative (3 Major Pairs)
                </button>
                <button
                  className="quick-select-btn balanced"
                  onClick={() => handleQuickSelect('major')}
                  disabled={loading}
                >
                  ⚖️ Balanced (All Major Pairs)
                </button>
                <button
                  className="quick-select-btn aggressive"
                  onClick={() => handleQuickSelect('aggressive')}
                  disabled={loading}
                >
                  🚀 Aggressive (Major + Minor Pairs)
                </button>
                <button
                  className="quick-select-btn clear"
                  onClick={() => handleQuickSelect('clear')}
                  disabled={loading}
                >
                  🗑️ Clear All
                </button>
              </div>
            </div>

            {/* Symbol Selection */}
            <div className="symbol-selection-section">
              <h3>Select Trading Pairs ({selectedSymbols.length} selected)</h3>

              {renderSymbolGrid(availableSymbols.major_pairs, "🌟 Major Pairs (Recommended)")}
              {renderSymbolGrid(availableSymbols.minor_pairs, "📈 Minor Pairs")}
              {renderSymbolGrid(availableSymbols.exotic_pairs, "🌍 Exotic Pairs")}
            </div>

            {/* Selected Symbols Summary */}
            {selectedSymbols.length > 0 && (
              <div className="selected-summary">
                <h4>Selected Pairs:</h4>
                <div className="selected-symbols">
                  {selectedSymbols.map(symbol => (
                    <span key={symbol} className="selected-symbol-tag">
                      {symbol}
                      <button
                        className="remove-symbol"
                        onClick={() => handleSymbolToggle(symbol)}
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Start Button */}
            <div className="start-section">
              <button
                className="start-auto-mode-btn"
                onClick={startFullAutoMode}
                disabled={loading || selectedSymbols.length === 0}
              >
                {loading ? '🔄 Starting...' : '🚀 Start Full Auto Mode'}
              </button>

              <div className="auto-mode-features">
                <h4>🤖 AI Features Included:</h4>
                <ul>
                  <li>✅ Automatic strategy selection based on market conditions</li>
                  <li>✅ Dynamic risk management and position sizing</li>
                  <li>✅ Intelligent profit taking and loss cutting</li>
                  <li>✅ Hedging and portfolio optimization</li>
                  <li>✅ Real-time market condition analysis</li>
                  <li>✅ Emergency stop and risk controls</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="running-section">
          <div className="status-card">
            <h2>🟢 Full Auto Mode Active</h2>

            {autoModeStatus && (
              <div className="status-grid">
                <div className="status-item">
                  <span className="status-label">State:</span>
                  <span className="status-value">{autoModeStatus.state}</span>
                </div>
                <div className="status-item">
                  <span className="status-label">Active Symbols:</span>
                  <span className="status-value">{autoModeStatus.selected_symbols?.join(', ')}</span>
                </div>
                <div className="status-item">
                  <span className="status-label">Active Positions:</span>
                  <span className="status-value">{autoModeStatus.active_positions}</span>
                </div>
                <div className="status-item">
                  <span className="status-label">Current Risk Level:</span>
                  <span className="status-value">{autoModeStatus.current_risk_level}%</span>
                </div>

                {autoModeStatus.daily_stats && (
                  <>
                    <div className="status-item">
                      <span className="status-label">Daily P&L:</span>
                      <span className={`status-value ${autoModeStatus.daily_stats.daily_pnl >= 0 ? 'positive' : 'negative'}`}>
                        ${autoModeStatus.daily_stats.daily_pnl?.toFixed(2)}
                      </span>
                    </div>
                    <div className="status-item">
                      <span className="status-label">Trades Today:</span>
                      <span className="status-value">{autoModeStatus.daily_stats.trades_today}</span>
                    </div>
                    <div className="status-item">
                      <span className="status-label">Win Rate:</span>
                      <span className="status-value">
                        {autoModeStatus.daily_stats.trades_today > 0
                          ? ((autoModeStatus.daily_stats.wins_today / autoModeStatus.daily_stats.trades_today) * 100).toFixed(1)
                          : 0}%
                      </span>
                    </div>
                  </>
                )}
              </div>
            )}

            <div className="control-section">
              <button
                className="stop-auto-mode-btn"
                onClick={stopFullAutoMode}
                disabled={loading}
              >
                {loading ? '🔄 Stopping...' : '🛑 Stop Full Auto Mode'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Info Panel */}
      <div className="info-panel">
        <h3>ℹ️ About Full Auto Mode</h3>
        <div className="info-content">
          <p>
            <strong>Full Auto Mode</strong> is an advanced AI-driven trading system that requires minimal user input.
            You only need to select which currency pairs to trade, and the AI handles everything else:
          </p>

          <div className="feature-grid">
            <div className="feature-item">
              <h4>🧠 Smart Strategy Selection</h4>
              <p>AI automatically chooses the best strategy (scalping, intraday, swing, position) based on current market conditions.</p>
            </div>

            <div className="feature-item">
              <h4>⚖️ Dynamic Risk Management</h4>
              <p>Automatically adjusts position sizes and risk levels based on market volatility and account performance.</p>
            </div>

            <div className="feature-item">
              <h4>🎯 Intelligent Position Management</h4>
              <p>AI decides when to take profits, cut losses, hedge positions, or extend trades based on market analysis.</p>
            </div>

            <div className="feature-item">
              <h4>🛡️ Advanced Protection</h4>
              <p>Multiple safety layers including emergency stops, correlation limits, and daily loss limits.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FullAutoModePage;
