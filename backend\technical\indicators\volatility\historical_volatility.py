from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class HistoricalVolatilityIndicator(BaseIndicator):
    """Historical Volatility indicator."""

    def __init__(self, period: int = 20, window: int = 252, source: str = 'close'):
        """
        Initialize Historical Volatility indicator.

        Args:
            period: The period for calculating log returns standard deviation.
            window: The number of trading periods in a year (e.g., 252 for daily).
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
        """
        super().__init__({
            'period': period,
            'window': window,
            'source': source
        })

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Historical Volatility values."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period'] + 1: # Need period+1 for returns
             return {'historical_volatility': np.array([])}

        period = self.params['period']
        window = self.params['window']
        source_col = self.params['source'].lower()

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Calculate Log Returns
        log_returns = np.log(source_data / source_data.shift(1))

        # Calculate Rolling Standard Deviation of Log Returns
        rolling_std = log_returns.rolling(window=period).std()

        # Annualize the volatility
        historical_volatility = rolling_std * np.sqrt(window)

        self._values = {
            'historical_volatility': historical_volatility.values,
            'log_returns': log_returns.values # Optional: return intermediate calc
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        if self.params['window'] < 1:
            raise ValueError("Window must be greater than 0")
        return True