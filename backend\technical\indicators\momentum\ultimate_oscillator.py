from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class UltimateOscillatorIndicator(BaseIndicator):
    """Ultimate Oscillator indicator."""

    def __init__(self, period1: int = 7, period2: int = 14, period3: int = 28,
                 weight1: float = 4.0, weight2: float = 2.0, weight3: float = 1.0):
        """
        Initialize Ultimate Oscillator indicator.

        Args:
            period1: Short period (typically 7).
            period2: Medium period (typically 14).
            period3: Long period (typically 28).
            weight1: Weight for the short period component (typically 4).
            weight2: Weight for the medium period component (typically 2).
            weight3: Weight for the long period component (typically 1).
        """
        super().__init__({
            'period1': period1,
            'period2': period2,
            'period3': period3,
            'weight1': weight1,
            'weight2': weight2,
            'weight3': weight3
        })

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Ultimate Oscillator values."""
        df = data.to_dataframe()
        min_len = max(self.params['period1'], self.params['period2'], self.params['period3'])
        if df.empty or len(df) < min_len + 1: # Need shift(1)
             return {'ultimate_oscillator': np.array([])}

        period1 = self.params['period1']
        period2 = self.params['period2']
        period3 = self.params['period3']
        weight1 = self.params['weight1']
        weight2 = self.params['weight2']
        weight3 = self.params['weight3']

        low = df['low']
        high = df['high']
        close = df['close']
        close_prev = close.shift(1)

        # Calculate Buying Pressure (BP)
        true_low = pd.concat([low, close_prev], axis=1).min(axis=1)
        bp = close - true_low

        # Calculate True Range (TR)
        true_high = pd.concat([high, close_prev], axis=1).max(axis=1)
        tr = true_high - true_low

        # Calculate sums over different periods
        bp_sum1 = bp.rolling(window=period1).sum()
        tr_sum1 = tr.rolling(window=period1).sum()

        bp_sum2 = bp.rolling(window=period2).sum()
        tr_sum2 = tr.rolling(window=period2).sum()

        bp_sum3 = bp.rolling(window=period3).sum()
        tr_sum3 = tr.rolling(window=period3).sum()

        # Calculate average ratios, avoiding division by zero
        avg1 = (bp_sum1 / tr_sum1.replace(0, np.nan)).fillna(0)
        avg2 = (bp_sum2 / tr_sum2.replace(0, np.nan)).fillna(0)
        avg3 = (bp_sum3 / tr_sum3.replace(0, np.nan)).fillna(0)

        # Calculate Ultimate Oscillator
        total_weight = weight1 + weight2 + weight3
        if total_weight == 0:
             # Avoid division by zero if all weights are zero
             uo_values = pd.Series(np.nan, index=df.index)
        else:
             uo_values = 100 * (weight1 * avg1 + weight2 * avg2 + weight3 * avg3) / total_weight

        self._values = {
            'ultimate_oscillator': uo_values.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        periods = [self.params['period1'], self.params['period2'], self.params['period3']]
        weights = [self.params['weight1'], self.params['weight2'], self.params['weight3']]
        if any(p < 1 for p in periods):
            raise ValueError("All periods must be greater than 0")
        if any(w < 0 for w in weights):
             raise ValueError("Weights cannot be negative")
        if sum(weights) <= 0:
             raise ValueError("Sum of weights must be positive")
        # Optional: Check if periods are ordered (p1 < p2 < p3) - common but not strictly required
        # if not (self.params['period1'] < self.params['period2'] < self.params['period3']):
        #     print("Warning: Periods for Ultimate Oscillator are typically ordered short < medium < long.")
        return True