# Phase 2: Time-Based Trading Filters

## Overview

This phase focuses on implementing time-based trading filters to restrict trading activities to specific market sessions. This feature will help reduce false signals during off-hours or low-liquidity periods, improving overall trading performance without requiring manual user intervention.

## Current Implementation

Currently, the Garuda-Algo autonomous trading system trades at any time without consideration for optimal market sessions. This can lead to:

- Execution of trades during low-liquidity periods with higher spreads
- False signals during market opens/closes
- Trading during weekend gaps or news events
- Sub-optimal performance for strategies that work best in specific sessions

## Implementation Details

### 1. Time Filter Core Logic

Create a new file `backend/time_filter.py` for the time filtering logic:

```python
import datetime
import pytz

class TimeFilter:
    """Class to manage time-based trading filters."""
    
    def __init__(self, config):
        """Initialize with time filter configuration."""
        self.config = config
        self.time_filters = config.get("time_filters", {})
        self.enabled = self.time_filters.get("enabled", False)
        self.sessions = self.time_filters.get("trading_sessions", [])
        self.time_zone = self.time_filters.get("time_zone", "UTC")
        self.non_trading_days = self.time_filters.get("non_trading_days", ["Saturday", "Sunday"])
        
        # Map day names to integers (0 = Monday, 6 = Sunday)
        self.day_map = {
            "Monday": 0, "Tuesday": 1, "Wednesday": 2, "Thursday": 3,
            "Friday": 4, "Saturday": 5, "Sunday": 6
        }
        
    def is_trading_allowed(self, symbol=None, strategy=None):
        """Check if trading is currently allowed based on time filters.
        
        Args:
            symbol: Optional symbol to check (some symbols may have specific hours)
            strategy: Optional strategy to check (some strategies may have specific hours)
        
        Returns:
            bool: True if trading is allowed, False otherwise
        """
        # If filters are disabled, always allow trading
        if not self.enabled:
            return True
            
        # Get current time in configured timezone
        now = datetime.datetime.now(pytz.timezone(self.time_zone))
        
        # Check if today is a non-trading day
        day_of_week = now.strftime("%A")
        if day_of_week in self.non_trading_days:
            return False
            
        # Check if current time is within any enabled session
        current_hour = now.hour
        current_minute = now.minute
        
        # Convert current time to minutes for easier comparison
        current_time_minutes = current_hour * 60 + current_minute
        
        # Check each session
        for session in self.sessions:
            if not session.get("enabled", True):
                continue
                
            start_hour = session.get("start_hour", 0)
            start_minute = session.get("start_minute", 0)
            end_hour = session.get("end_hour", 24)
            end_minute = session.get("end_minute", 0)
            
            # Convert to minutes for comparison
            start_time_minutes = start_hour * 60 + start_minute
            end_time_minutes = end_hour * 60 + end_minute
            
            # Check if current time is within session
            # Handle sessions that span across midnight
            if start_time_minutes <= end_time_minutes:
                # Normal case: session starts and ends on the same day
                if start_time_minutes <= current_time_minutes <= end_time_minutes:
                    return True
            else:
                # Session spans across midnight
                if current_time_minutes >= start_time_minutes or current_time_minutes <= end_time_minutes:
                    return True
        
        # If we get here, current time is not within any enabled session
        return False
    
    def get_next_session_start(self):
        """Get the start time of the next trading session.
        
        Returns:
            datetime: Start time of the next trading session
        """
        # Implementation details
        # Find the next enabled session based on current time
        # Handle cases where next session is tomorrow
        pass
    
    def get_current_session(self):
        """Get the current trading session if any.
        
        Returns:
            dict: Current session details or None if not in a session
        """
        # Implementation details
        # Check if current time is within any session
        # Return session details including name, remaining time, etc.
        pass
        
    def is_session_active(self, session_name):
        """Check if a specific named session is currently active.
        
        Args:
            session_name: Name of the session to check
            
        Returns:
            bool: True if session is active, False otherwise
        """
        # Implementation details
        # Find session by name and check if current time is within it
        pass
```

### 2. Integration with Trading Loop

Modify `backend/api/autonomous.py` to use the time filter in the trading loop:

```python
from backend.time_filter import TimeFilter

def _trading_loop(app):
    """Main autonomous trading loop."""
    global AUTONOMOUS_RUNNING
    
    # Create time filter
    time_filter = TimeFilter(AUTONOMOUS_CONFIG)
    
    # ... existing code ...
    
    while AUTONOMOUS_RUNNING:
        try:
            # ... existing connection checks ...
            
            # Check if trading is allowed based on time filters
            if not time_filter.is_trading_allowed():
                session_info = time_filter.get_next_session_start()
                logger.info(f"Trading currently not allowed due to time filters. Next session starts at {session_info}")
                time.sleep(60)  # Sleep for a minute before checking again
                continue
                
            # ... existing trading logic ...
            
            # Process each symbol in the configuration
            for symbol in AUTONOMOUS_CONFIG["symbols"]:
                # ... existing symbol checks ...
                
                # Additional check for symbol-specific time filters
                # Some symbols might have special trading hours
                if not time_filter.is_trading_allowed(symbol=symbol):
                    logger.info(f"Trading for {symbol} not allowed at current time")
                    continue
                    
                # ... rest of symbol processing ...
```

### 3. Strategy-Specific Time Windows

Implement strategy-specific time windows to optimize each strategy for its ideal trading session:

```python
# In _trading_loop function

# Get the active strategy's timeframe
timeframe = AUTONOMOUS_CONFIG["timeframes"][0]

# For active strategies with specific settings
strategy_id = active_strategy["strategy_id"]
strategy_settings = AUTONOMOUS_CONFIG["strategy_settings"].get(strategy_id, {})

# Check if strategy has specific time windows
strategy_sessions = strategy_settings.get("best_sessions", [])
if strategy_sessions:
    # Check if any of the strategy's preferred sessions are active
    session_active = False
    for session_name in strategy_sessions:
        if time_filter.is_session_active(session_name):
            session_active = True
            break
    
    if not session_active:
        logger.info(f"Strategy {strategy_id} performs best in {strategy_sessions} sessions, which are not currently active. Skipping.")
        continue
```

### 4. Time Zone Handling

Implement proper time zone handling to ensure accurate session detection:

```python
# In time_filter.py, add functions for time zone management

def get_available_timezones():
    """Get list of available time zones."""
    return pytz.all_timezones

def validate_timezone(timezone_str):
    """Validate if a time zone string is valid."""
    try:
        pytz.timezone(timezone_str)
        return True
    except pytz.exceptions.UnknownTimeZoneError:
        return False
        
def convert_time(dt, from_tz, to_tz):
    """Convert a datetime from one timezone to another."""
    from_tz = pytz.timezone(from_tz)
    to_tz = pytz.timezone(to_tz)
    
    # If datetime is naive, assume it's in from_tz
    if dt.tzinfo is None:
        dt = from_tz.localize(dt)
        
    # Convert to target timezone
    return dt.astimezone(to_tz)
```

### 5. Market Open/Close Detection

Implement functions to detect market opens and closes, which can be volatile periods:

```python
def is_market_opening(symbol, timeframe="H1"):
    """Check if we're within the market opening period for a symbol.
    
    Some symbols have specific market opening times that can be volatile.
    
    Args:
        symbol: The symbol to check
        timeframe: Timeframe to consider for open (H1 = within 1 hour of open)
        
    Returns:
        bool: True if within opening period, False otherwise
    """
    # Implementation details
    # Use symbol information from MT5 to determine market open times
    # Check if current time is within the defined range of market open
    pass
    
def is_market_closing(symbol, timeframe="H1"):
    """Check if we're within the market closing period for a symbol.
    
    Args:
        symbol: The symbol to check
        timeframe: Timeframe to consider for close (H1 = within 1 hour of close)
        
    Returns:
        bool: True if within closing period, False otherwise
    """
    # Implementation details
    pass
```

## Files to Modify/Create

1. **backend/time_filter.py** (new):
   - Implement TimeFilter class
   - Add time zone utilities
   - Add session management functions

2. **backend/api/autonomous.py**:
   - Integrate time filter checks in trading loop
   - Add strategy-specific time window checks

3. **backend/autonomous_trader.py**:
   - Update to use time filter for trading decisions

4. **requirements.txt**:
   - Ensure `pytz` is included for time zone handling

## Testing Plan

1. **Basic Time Filter Functionality**:
   - Test allowed/disallowed trading times with different configurations
   - Verify handling of sessions that span across midnight

2. **Time Zone Handling**:
   - Test with different time zones
   - Verify proper conversion between time zones

3. **Session Detection**:
   - Test correct identification of active trading sessions
   - Verify calculation of next session start

4. **Strategy-Specific Windows**:
   - Test that strategies only trade during their optimal sessions
   - Verify proper logging of skipped opportunities

5. **Edge Cases**:
   - Test behavior during DST changes
   - Test with unusual session configurations

## Acceptance Criteria

- Trading is only performed during configured trading sessions
- Different market sessions (Asian, London, New York) are correctly identified
- Strategy-specific time windows are respected
- Time zone handling works correctly for different server locations
- Non-trading days are properly observed
- Logging clearly indicates when trading is skipped due to time filters
