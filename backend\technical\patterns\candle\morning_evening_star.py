from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class MorningEveningStarPatternIndicator(BaseIndicator):
    """Morning Star and Evening Star candlestick pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - body_ratio: Parameter description (default: 0.3)
        """
        default_params = {
            "body_ratio": 0.3,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)

    
    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        open_price = df['open'].values
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        body_ratio = self.params['body_ratio']
        
        # Calculate body sizes and ranges
        body_size = np.abs(close - open_price)
        total_range = high - low
        
        # Calculate previous and next candle's data
        prev_open = np.zeros_like(open_price)
        prev_open[1:] = open_price[:-1]
        prev_close = np.zeros_like(close)
        prev_close[1:] = close[:-1]
        prev_high = np.zeros_like(high)
        prev_high[1:] = high[:-1]
        prev_low = np.zeros_like(low)
        prev_low[1:] = low[:-1]
        
        next_open = np.zeros_like(open_price)
        next_open[:-1] = open_price[1:]
        next_close = np.zeros_like(close)
        next_close[:-1] = close[1:]
        next_high = np.zeros_like(high)
        next_high[:-1] = high[1:]
        next_low = np.zeros_like(low)
        next_low[:-1] = low[1:]
        
        # Identify morning star patterns
        is_morning_star = (
            (prev_close < prev_open) &  # First candle is bearish
            (body_size <= (total_range * body_ratio)) &  # Middle candle has small body
            (next_close > next_open) &  # Third candle is bullish
            (next_close > (prev_open + prev_close) / 2)  # Third candle closes above midpoint
        )
        
        # Identify evening star patterns
        is_evening_star = (
            (prev_close > prev_open) &  # First candle is bullish
            (body_size <= (total_range * body_ratio)) &  # Middle candle has small body
            (next_close < next_open) &  # Third candle is bearish
            (next_close < (prev_open + prev_close) / 2)  # Third candle closes below midpoint
        )
        
        # Classify pattern types
        pattern_type = np.zeros_like(close)
        pattern_type[is_morning_star] = 1
        pattern_type[is_evening_star] = -1
        
        # Calculate pattern strength
        strength = np.zeros_like(close)
        for i in range(1, len(close)-1):
            if is_morning_star[i] or is_evening_star[i]:
                # Calculate the gap between first and third candle
                if is_morning_star[i]:
                    gap = next_close[i] - prev_close[i]
                else:  # Evening star
                    gap = prev_close[i] - next_close[i]
                strength[i] = gap / prev_close[i]  # Normalize by price
        
        # Calculate trend context
        trend = np.zeros_like(close)
        for i in range(1, len(close)):
            if i >= 20:  # Use 20-period SMA for trend
                sma = np.mean(close[i-20:i])
                trend[i] = 1 if close[i] > sma else -1
        
        # Calculate pattern reliability
        reliability = np.zeros_like(close)
        for i in range(1, len(close)-1):
            if is_morning_star[i] or is_evening_star[i]:
                # Check if price moved in the expected direction
                if i < len(close)-2:
                    future_return = (close[i+2] - close[i]) / close[i]
                    if is_morning_star[i]:
                        reliability[i] = 1 if future_return > 0 else -1
                    else:  # Evening star
                        reliability[i] = 1 if future_return < 0 else -1
        
        return {
            'is_morning_star': is_morning_star.astype(int),
            'is_evening_star': is_evening_star.astype(int),
            'pattern_type': pattern_type,
            'strength': strength,
            'trend': trend,
            'reliability': reliability,
            'body_size': body_size,
            'total_range': total_range
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if not 0 < self.params['body_ratio'] < 1:
            raise ValueError("Body ratio must be between 0 and 1")
        return True 