# -*- mode: python ; coding: utf-8 -*-
import sys
import os
from pathlib import Path

# Add project root to path to allow absolute imports
project_root = os.path.abspath(os.path.join(os.path.dirname(os.path.abspath(SPECPATH)), '..'))
sys.path.insert(0, project_root)

block_cipher = None

# Create dist folder inside the admin directory
dist_path = os.path.join(SPECPATH, 'dist')

a = Analysis(
    ['main_admin.py'],
    pathex=[project_root, os.path.join(project_root, 'admin')],
    binaries=[],
    datas=[],
    # We'll add the credentials file if it exists

    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtWidgets',
        'PyQt5.QtGui',
        'firebase_admin',
        'google.cloud.firestore'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='GarudaAlgo_Admin_Panel',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # Set to False for GUI application
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(project_root, 'app_icon.ico'),  # Use the existing app icon
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='GarudaAlgo_Admin_Panel',
)
