import React, { useState, useEffect } from 'react';
import { useNotification } from '../components/Notification';
import TradeManagementPanel from '../components/TradeManagementPanel';
import EntrySpacingSettings from '../components/EntrySpacingSettings';
import '../styles/Pages.css';
import '../styles/autonomous.css';
import '../styles/trade-management.css';
import '../styles/risk-management.css';
import '../styles/entry-spacing.css';

const AutonomousTradingPage = () => {
  const [strategies, setStrategies] = useState([]);
  const [activeStrategies, setActiveStrategies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [symbolsLoading, setSymbolsLoading] = useState(true);
  const [monitoringData, setMonitoringData] = useState(null);
  const [monitoringLoading, setMonitoringLoading] = useState(false);
  const [monitoringError, setMonitoringError] = useState(null);
  const [logs, setLogs] = useState([]);
  const [logsLoading, setLogsLoading] = useState(false);
  const [logsError, setLogsError] = useState(null);
  const [activeTab, setActiveTab] = useState('config'); // 'config', 'monitor', 'logs', 'entry-spacing'

  // Symbols state with empty default
  const [symbols, setSymbols] = useState([]);
  const [selectedStrategy, setSelectedStrategy] = useState('');
  const [selectedSymbol, setSelectedSymbol] = useState('');
  const [timeframe, setTimeframe] = useState('H1');
  const [riskPerTrade, setRiskPerTrade] = useState(1);
  const [maxOpenTrades, setMaxOpenTrades] = useState(3);
  const [minConfidence, setMinConfidence] = useState(60); // Default min confidence threshold
  const [isActivating, setIsActivating] = useState(false);
  const [botStatus, setBotStatus] = useState({ running: false });

  // Risk management state variables
  const [maxTotalRiskPercent, setMaxTotalRiskPercent] = useState(5.0);
  const [breakevenTriggerPercent, setBreakevenTriggerPercent] = useState(0.5);
  const [trailingStopTriggerPercent, setTrailingStopTriggerPercent] = useState(1.0);
  const [trailingStopDistancePercent, setTrailingStopDistancePercent] = useState(0.5);
  const [maxProfitTarget, setMaxProfitTarget] = useState(100.0);
  const [maxLossTarget, setMaxLossTarget] = useState(50.0);

  // Risk per trade mode and amount
  const [riskMode, setRiskMode] = useState('percent'); // 'percent' or 'amount'
  const [riskAmount, setRiskAmount] = useState(100); // Fixed dollar amount
  const [accountBalance, setAccountBalance] = useState(0); // Account balance for calculations

  // Account currency state
  const [accountCurrency, setAccountCurrency] = useState('USD');

  // Breakeven configuration mode
  const [breakevenMode, setBreakevenMode] = useState('percent'); // 'percent' or 'pips'
  const [breakevenTriggerPips, setBreakevenTriggerPips] = useState(50);

  // Trailing stop trigger configuration mode
  const [trailingTriggerMode, setTrailingTriggerMode] = useState('percent'); // 'percent' or 'pips'
  const [trailingStopTriggerPips, setTrailingStopTriggerPips] = useState(100);

  // Trailing stop distance configuration mode
  const [trailingStopMode, setTrailingStopMode] = useState('percent'); // 'percent' or 'pips'
  const [trailingStopDistancePips, setTrailingStopDistancePips] = useState(100);

  const notify = useNotification();

  // Helper functions for risk calculations
  const calculateEquivalentAmount = (percent) => {
    if (accountBalance > 0) {
      return (parseFloat(percent) / 100) * accountBalance;
    }
    return 0;
  };

  const calculateEquivalentPercent = (amount) => {
    if (accountBalance > 0) {
      return (parseFloat(amount) / accountBalance) * 100;
    }
    return 0;
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  // Currency symbol mapping
  const getCurrencySymbol = (currency) => {
    const currencySymbols = {
      'USD': '$',
      'EUR': '€',
      'GBP': '£',
      'JPY': '¥',
      'CHF': 'CHF',
      'CAD': 'C$',
      'AUD': 'A$',
      'NZD': 'NZ$',
      'SEK': 'kr',
      'NOK': 'kr',
      'DKK': 'kr',
      'PLN': 'zł',
      'CZK': 'Kč',
      'HUF': 'Ft',
      'RUB': '₽',
      'CNY': '¥',
      'INR': '₹',
      'BRL': 'R$',
      'MXN': '$',
      'ZAR': 'R',
      'SGD': 'S$',
      'HKD': 'HK$',
      'THB': '฿',
      'TRY': '₺',
      'KRW': '₩',
      // ASEAN Countries
      'IDR': 'Rp',     // Indonesia
      'MYR': 'RM',     // Malaysia
      'PHP': '₱',      // Philippines
      'VND': '₫',      // Vietnam
      'LAK': '₭',      // Laos
      'KHR': '៛',      // Cambodia
      'MMK': 'K',      // Myanmar
      'BND': 'B$'      // Brunei
      // Note: Singapore (SGD) and Thailand (THB) already included above
    };
    return currencySymbols[currency] || currency;
  };

  // Fetch monitoring data
  const fetchMonitoringData = async () => {
    try {
      setMonitoringLoading(true);
      setMonitoringError(null);
      const response = await fetch('http://localhost:5001/api/autonomous/monitor');
      if (response.ok) {
        const data = await response.json();
        setMonitoringData(data);
      } else {
        const error = await response.json();
        setMonitoringError(error.error || 'Failed to fetch monitoring data');
      }
    } catch (error) {
      setMonitoringError(error.message);
    } finally {
      setMonitoringLoading(false);
    }
  };

  // Fetch logs data
  const fetchLogs = async (lines = 100) => {
    try {
      setLogsLoading(true);
      setLogsError(null);
      const response = await fetch(`http://localhost:5001/api/autonomous/logs?lines=${lines}`);
      if (response.ok) {
        const data = await response.json();
        setLogs(data.logs || []);
      } else {
        const error = await response.json();
        setLogsError(error.error || 'Failed to fetch logs');
        console.error('Failed to fetch logs:', error);
      }
    } catch (error) {
      setLogsError('An error occurred while fetching logs');
      console.error('Error fetching logs:', error);
    } finally {
      setLogsLoading(false);
    }
  };

  // Fetch available strategies and symbols
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setSymbolsLoading(true);
      try {
        // Fetch available strategies
        const strategiesResponse = await fetch('http://localhost:5001/api/autonomous/strategies');
        if (strategiesResponse.ok) {
          const strategiesData = await strategiesResponse.json();
          setStrategies(strategiesData);
          if (strategiesData.length > 0) {
            setSelectedStrategy(strategiesData[0].id);
          }
        }

        // Fetch symbols from the API
        try {
          console.log('Fetching symbols from API...');
          // First try the standard symbols endpoint
          let symbolsResponse = await fetch('http://localhost:5001/api/symbols');

          // If that fails, try the market_data/symbols endpoint as fallback
          if (!symbolsResponse.ok) {
            console.log('Trying alternative symbols endpoint...');
            symbolsResponse = await fetch('http://localhost:5001/api/symbols');
          }

          if (symbolsResponse.ok) {
            const symbolsData = await symbolsResponse.json();
            if (Array.isArray(symbolsData) && symbolsData.length > 0) {
              console.log(`Loaded ${symbolsData.length} symbols from API`);
              setSymbols(symbolsData);
              // Set default selected symbol if we have symbols and none is selected
              if (!selectedSymbol && symbolsData.length > 0) {
                // Prefer EURUSD if available, otherwise use first symbol
                const defaultSymbol = symbolsData.includes('EURUSD') ? 'EURUSD' : symbolsData[0];
                setSelectedSymbol(defaultSymbol);
              }
            } else {
              throw new Error('Invalid symbols data format or empty array');
            }
          } else {
            throw new Error(`Failed to fetch symbols: ${symbolsResponse.status}`);
          }
        } catch (symbolError) {
          console.error('Error fetching symbols:', symbolError);

          // Fallback to a default list of common symbols
          const defaultSymbols = [
            'EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD',
            'USDCHF', 'NZDUSD', 'EURGBP', 'EURJPY', 'GBPJPY',
            'XAUUSD', 'XAGUSD', 'BTCUSD', 'ETHUSD'
          ];

          console.warn('Using default symbols list due to API error');
          setSymbols(defaultSymbols);

          // Set default selected symbol
          if (!selectedSymbol) {
            setSelectedSymbol('EURUSD');
          }
        } finally {
          setSymbolsLoading(false);
        }

        // Fetch bot status
        const statusResponse = await fetch('http://localhost:5001/api/autonomous/status');
        if (statusResponse.ok) {
          const statusData = await statusResponse.json();
          setBotStatus(statusData);
        }

        // Fetch active strategies
        const activeResponse = await fetch('http://localhost:5001/api/autonomous/active');
        if (activeResponse.ok) {
          const activeData = await activeResponse.json();
          setActiveStrategies(activeData);
        }

        // Fetch account currency and balance
        try {
          const accountResponse = await fetch('http://localhost:5001/api/account');
          if (accountResponse.ok) {
            const accountData = await accountResponse.json();
            console.log('Account data received:', accountData); // Debug log
            if (accountData.currency) {
              console.log('Setting account currency to:', accountData.currency); // Debug log
              setAccountCurrency(accountData.currency);
            } else {
              console.warn('No currency field in account data:', accountData);
            }
            if (accountData.balance !== undefined) {
              console.log('Setting account balance to:', accountData.balance); // Debug log
              setAccountBalance(accountData.balance);
            } else {
              console.warn('No balance field in account data:', accountData);
            }
          } else {
            console.error('Failed to fetch account data, status:', accountResponse.status);
          }
        } catch (error) {
          console.warn('Could not fetch account currency and balance, using defaults:', error);
        }
      } catch (error) {
        console.error('Failed to fetch data:', error);
        notify.error('Error', 'Failed to fetch autonomous trading data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
    // Initial fetch of monitoring data and logs
    fetchMonitoringData();
    fetchLogs();

    // Set up polling for active strategies, status, monitoring data, and logs
    const intervalId = setInterval(() => {
      fetchActiveStrategies();
      fetchBotStatus();
      fetchMonitoringData();
      if (activeTab === 'logs') {
        fetchLogs();
      }
    }, 10000); // Poll every 10 seconds

    return () => clearInterval(intervalId);
  }, [activeTab]);

  const fetchActiveStrategies = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/autonomous/active');
      if (response.ok) {
        const data = await response.json();
        setActiveStrategies(data);
      }
    } catch (error) {
      console.error('Failed to fetch active strategies:', error);
    }
  };

  const fetchBotStatus = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/autonomous/status');
      if (response.ok) {
        const data = await response.json();
        setBotStatus(data);
      }
    } catch (error) {
      console.error('Failed to fetch bot status:', error);
    }
  };

  const handleActivateStrategy = async () => {
    // Enhanced validation
    if (!selectedStrategy) {
      notify.warning('Validation Error', 'Please select a strategy');
      return;
    }

    if (!selectedSymbol) {
      notify.warning('Validation Error', 'Please select a symbol');
      return;
    }

    if (symbolsLoading) {
      notify.warning('Please Wait', 'Symbols are still loading');
      return;
    }

    if (symbols.length === 0) {
      notify.error('Error', 'No symbols available. Please check MT5 connection');
      return;
    }

    setIsActivating(true);
    try {
      const response = await fetch('http://localhost:5001/api/autonomous/activate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          strategy_id: selectedStrategy,
          symbol: selectedSymbol,
          timeframe: timeframe,
          risk_percent: riskMode === 'percent' ? parseFloat(riskPerTrade) : null,
          risk_amount: riskMode === 'amount' ? parseFloat(riskAmount) : null,
          risk_mode: riskMode,
          max_trades: parseInt(maxOpenTrades),
          min_confidence: parseInt(minConfidence),
          max_total_risk_percent: parseFloat(maxTotalRiskPercent),
          // Breakeven configuration
          breakeven_trigger_percent: breakevenMode === 'percent' ? parseFloat(breakevenTriggerPercent) : null,
          breakeven_trigger_pips: breakevenMode === 'pips' ? parseFloat(breakevenTriggerPips) : null,
          breakeven_mode: breakevenMode,
          // Trailing stop trigger configuration
          trailing_stop_trigger_percent: trailingTriggerMode === 'percent' ? parseFloat(trailingStopTriggerPercent) : null,
          trailing_stop_trigger_pips: trailingTriggerMode === 'pips' ? parseFloat(trailingStopTriggerPips) : null,
          trailing_trigger_mode: trailingTriggerMode,
          // Trailing stop distance configuration
          trailing_stop_distance_percent: trailingStopMode === 'percent' ? parseFloat(trailingStopDistancePercent) : null,
          trailing_stop_distance_pips: trailingStopMode === 'pips' ? parseFloat(trailingStopDistancePips) : null,
          trailing_stop_mode: trailingStopMode,
          max_profit_target: parseFloat(maxProfitTarget),
          max_loss_target: parseFloat(maxLossTarget)
        })
      });

      if (response.ok) {
        const result = await response.json();
        notify.success('Success', `Strategy activated: ${result.strategy_name}`);
        fetchActiveStrategies();
      } else {
        const error = await response.json();
        notify.error('Error', `Failed to activate strategy: ${error.message}`);
      }
    } catch (error) {
      console.error('Failed to activate strategy:', error);
      notify.error('Error', 'Failed to activate strategy');
    } finally {
      setIsActivating(false);
    }
  };

  const handleDeactivateStrategy = async (strategyId) => {
    try {
      const response = await fetch(`http://localhost:5001/api/autonomous/deactivate/${strategyId}`, {
        method: 'POST'
      });

      if (response.ok) {
        notify.success('Success', 'Strategy deactivated');
        fetchActiveStrategies();
      } else {
        const error = await response.json();
        notify.error('Error', `Failed to deactivate strategy: ${error.message}`);
      }
    } catch (error) {
      console.error('Failed to deactivate strategy:', error);
      notify.error('Error', 'Failed to deactivate strategy');
    }
  };

  // Handle entry spacing settings change
  const handleEntrySpacingSettingsChange = (settings) => {
    // Could trigger a refresh of bot status or other updates
    console.log('Entry spacing settings updated:', settings);
    // Optionally refresh bot status
    fetchBotStatus();
  };

  // Render monitoring section
  const renderMonitoringSection = () => {
    if (monitoringLoading && !monitoringData) {
      return <div className="loading-indicator">Loading monitoring data...</div>;
    }

    if (monitoringError) {
      return (
        <div className="error-message">
          <p>Error loading monitoring data: {monitoringError}</p>
          <button onClick={fetchMonitoringData} className="button secondary">
            Retry
          </button>
        </div>
      );
    }

    if (!monitoringData) {
      return <div className="no-data">No monitoring data available.</div>;
    }

    const { performance, positions, history, system_health } = monitoringData;

    return (
      <div className="monitoring-section">
        <div className="monitoring-header">
          <h3>Monitoring Dashboard</h3>
          <div className="last-updated">
            Last updated: {new Date().toLocaleTimeString()}
            <button onClick={fetchMonitoringData} className="refresh-button" title="Refresh">
              ↻
            </button>
          </div>
        </div>

        <div className="monitoring-grid">
          {/* Performance metrics */}
          <div className="monitoring-card performance-card">
            <h4>Performance Metrics</h4>
            <div className="metrics-grid">
              <div className="metric">
                <span className="metric-label">Today's P/L:</span>
                <span className={`metric-value ${performance.overall.profit_today >= 0 ? 'positive' : 'negative'}`}>
                  {getCurrencySymbol(accountCurrency)}{Math.abs(performance.overall.profit_today).toFixed(2)}
                </span>
              </div>
              <div className="metric">
                <span className="metric-label">This Week:</span>
                <span className={`metric-value ${performance.overall.profit_week >= 0 ? 'positive' : 'negative'}`}>
                  {getCurrencySymbol(accountCurrency)}{Math.abs(performance.overall.profit_week).toFixed(2)}
                </span>
              </div>
              <div className="metric">
                <span className="metric-label">Total P/L:</span>
                <span className={`metric-value ${performance.overall.total_profit >= 0 ? 'positive' : 'negative'}`}>
                  {getCurrencySymbol(accountCurrency)}{Math.abs(performance.overall.total_profit).toFixed(2)}
                </span>
              </div>
              <div className="metric">
                <span className="metric-label">Win Rate:</span>
                <span className="metric-value">
                  {(performance.overall.win_rate * 100).toFixed(1)}%
                </span>
              </div>
              <div className="metric">
                <span className="metric-label">Total Trades:</span>
                <span className="metric-value">{performance.overall.trades_count}</span>
              </div>
            </div>
          </div>

          {/* System health */}
          <div className="monitoring-card health-card">
            <h4>System Health</h4>
            <div className="health-status">
              <div className="health-item">
                <span className="health-label">Status:</span>
                <span className={`health-value ${system_health.running ? 'running' : 'stopped'}`}>
                  {system_health.running ? 'Running' : 'Stopped'}
                </span>
              </div>
              <div className="health-item">
                <span className="health-label">Connection:</span>
                <span className={`health-value ${system_health.connection_status === 'connected' ? 'connected' : 'disconnected'}`}>
                  {system_health.connection_status}
                </span>
              </div>
              <div className="health-item">
                <span className="health-label">Active Strategies:</span>
                <span className="health-value">{system_health.strategies_count}</span>
              </div>
              <div className="health-item">
                <span className="health-label">Margin Level:</span>
                <span className={`health-value ${system_health.account_margin_level > 200 ? 'good' : system_health.account_margin_level > 100 ? 'warning' : 'danger'}`}>
                  {system_health.account_margin_level ? system_health.account_margin_level.toFixed(2) + '%' : 'N/A'}
                </span>
              </div>
            </div>
          </div>

          {/* Risk Management Status */}
          <div className="monitoring-card risk-card">
            <h4>Risk Management Status</h4>
            <div className="metrics-grid">
              <div className="metric">
                <span className="metric-label">Current Risk:</span>
                <span className={`metric-value ${(parseFloat(system_health.current_risk_percent || 0) <= parseFloat(system_health.max_risk_percent || 5)) ? 'good' : 'warning'}`}>
                  {system_health.current_risk_percent ? parseFloat(system_health.current_risk_percent).toFixed(2) : '0.00'}% / {system_health.max_risk_percent ? parseFloat(system_health.max_risk_percent).toFixed(2) : '5.00'}%
                </span>
              </div>
              <div className="metric">
                <span className="metric-label">Break-Even Positions:</span>
                <span className="metric-value">{system_health.breakeven_positions || 0}</span>
              </div>
              <div className="metric">
                <span className="metric-label">Trailing Stop Positions:</span>
                <span className="metric-value">{system_health.trailing_positions || 0}</span>
              </div>
              <div className="metric">
                <span className="metric-label">Current P/L:</span>
                <span className={`metric-value ${positions.reduce((sum, pos) => sum + parseFloat(pos.profit || 0), 0) >= 0 ? 'positive' : 'negative'}`}>
                  {getCurrencySymbol(accountCurrency)}{positions.reduce((sum, pos) => sum + parseFloat(pos.profit || 0), 0).toFixed(2)}
                </span>
              </div>
              <div className="metric">
                <span className="metric-label">Profit Target:</span>
                <span className="metric-value">{getCurrencySymbol(accountCurrency)}{maxProfitTarget > 0 ? parseFloat(maxProfitTarget).toFixed(2) : 'Disabled'}</span>
              </div>
              <div className="metric">
                <span className="metric-label">Loss Limit:</span>
                <span className="metric-value">{getCurrencySymbol(accountCurrency)}{maxLossTarget > 0 ? parseFloat(maxLossTarget).toFixed(2) : 'Disabled'}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Trade Management Panel */}
        <TradeManagementPanel positionsCount={positions.length} refreshMonitoringData={fetchMonitoringData} />

        {/* Active positions */}
        <div className="monitoring-card positions-card">
          <h4>Active Positions ({positions.length})</h4>
          {positions.length === 0 ? (
            <p className="no-data">No active positions</p>
          ) : (
            <div className="table-container">
              <table className="positions-table">
                <thead>
                  <tr>
                    <th>Symbol</th>
                    <th>Strategy</th>
                    <th>Type</th>
                    <th>Volume</th>
                    <th>Open Price</th>
                    <th>Current Price</th>
                    <th>SL</th>
                    <th>TP</th>
                    <th>Profit</th>
                    <th>Duration</th>
                  </tr>
                </thead>
                <tbody>
                  {positions.map((position) => (
                    <tr key={position.ticket} className={position.profit >= 0 ? 'profit' : 'loss'}>
                      <td>{position.symbol}</td>
                      <td>{position.strategy_name}</td>
                      <td className={position.type === 0 ? 'buy' : 'sell'}>
                        {position.type === 0 ? 'BUY' : 'SELL'}
                      </td>
                      <td>{position.volume.toFixed(2)}</td>
                      <td>{position.price_open.toFixed(5)}</td>
                      <td>{position.price_current.toFixed(5)}</td>
                      <td>{position.sl ? position.sl.toFixed(5) : '-'}</td>
                      <td>{position.tp ? position.tp.toFixed(5) : '-'}</td>
                      <td className={position.profit >= 0 ? 'positive' : 'negative'}>
                        {getCurrencySymbol(accountCurrency)}{position.profit.toFixed(2)}
                      </td>
                      <td>
                        {position.duration.days > 0 ? `${position.duration.days}d ` : ''}
                        {position.duration.hours.toString().padStart(2, '0')}:
                        {position.duration.minutes.toString().padStart(2, '0')}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Recent trades */}
        <div className="monitoring-card history-card">
          <h4>Recent Trades ({history.length})</h4>
          {history.length === 0 ? (
            <p className="no-data">No recent trades</p>
          ) : (
            <div className="table-container">
              <table className="history-table">
                <thead>
                  <tr>
                    <th>Time</th>
                    <th>Symbol</th>
                    <th>Strategy</th>
                    <th>Type</th>
                    <th>Volume</th>
                    <th>Price</th>
                    <th>Profit</th>
                  </tr>
                </thead>
                <tbody>
                  {history.map((deal) => (
                    <tr key={deal.ticket} className={deal.profit >= 0 ? 'profit' : 'loss'}>
                      <td>{new Date(deal.time).toLocaleString()}</td>
                      <td>{deal.symbol}</td>
                      <td>{deal.strategy_name}</td>
                      <td className={deal.type === 0 ? 'buy' : 'sell'}>
                        {deal.type === 0 ? 'BUY' : 'SELL'}
                      </td>
                      <td>{deal.volume.toFixed(2)}</td>
                      <td>{deal.price.toFixed(5)}</td>
                      <td className={deal.profit >= 0 ? 'positive' : 'negative'}>
                        {getCurrencySymbol(accountCurrency)}{Math.abs(deal.profit).toFixed(2)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Recent logs/events */}
        {monitoringData.recent_logs && monitoringData.recent_logs.length > 0 && (
          <div className="monitoring-card logs-card">
            <h4>Recent Logs/Warnings</h4>
            <div className="logs-container">
              {monitoringData.recent_logs.map((log, index) => (
                <div key={index} className={`log-entry ${log.level}`}>
                  <span className="log-time">{log.timestamp}</span>
                  <span className="log-level">{log.level}</span>
                  <span className="log-message">{log.message}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Render logs section
  const renderLogsSection = () => {
    if (logsLoading && logs.length === 0) {
      return <div className="loading-indicator">Loading logs...</div>;
    }

    if (logsError) {
      return (
        <div className="error-message">
          <p>Error loading logs: {logsError}</p>
          <button onClick={fetchLogs} className="button secondary">
            Retry
          </button>
        </div>
      );
    }

    if (logs.length === 0) {
      return <div className="no-data">No logs available.</div>;
    }

    return (
      <div className="logs-section">
        <div className="logs-header">
          <h3>Autonomous Trading Logs</h3>
          <div className="logs-actions">
            <button onClick={() => fetchLogs(100)} className="refresh-button" title="Refresh">
              ↻
            </button>
            <select
              onChange={(e) => fetchLogs(parseInt(e.target.value))}
              className="logs-line-selector"
            >
              <option value="50">Last 50 lines</option>
              <option value="100" selected>Last 100 lines</option>
              <option value="200">Last 200 lines</option>
              <option value="500">Last 500 lines</option>
            </select>
          </div>
        </div>

        <div className="logs-container">
          {logs.map((log, index) => (
            <div key={index} className={`log-entry ${log.level}`}>
              <span className="log-time">{log.timestamp}</span>
              <span className="log-level">{log.level}</span>
              <span className="log-message">{log.message}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="page-container autonomous-page">
      <div className="page-header">
        <h2>Autonomous Trading</h2>
        <div className={`bot-status ${botStatus.running ? 'running' : 'stopped'}`}>
          <div className="status-indicator"></div>
          <span>Bot Status: {botStatus.running ? 'Running' : 'Stopped'}</span>
        </div>
      </div>

      <div className="page-tabs">
        <button
          className={`tab-button ${activeTab === 'config' ? 'active' : ''}`}
          onClick={() => setActiveTab('config')}
        >
          Configure Strategies
        </button>
        <button
          className={`tab-button ${activeTab === 'monitor' ? 'active' : ''}`}
          onClick={() => {
            setActiveTab('monitor');
            fetchMonitoringData(); // Refresh data when tab is clicked
          }}
        >
          Monitoring Dashboard
        </button>
        <button
          className={`tab-button ${activeTab === 'entry-spacing' ? 'active' : ''}`}
          onClick={() => setActiveTab('entry-spacing')}
        >
          Entry Spacing
        </button>
        <button
          className={`tab-button ${activeTab === 'logs' ? 'active' : ''}`}
          onClick={() => {
            setActiveTab('logs');
            fetchLogs(); // Refresh logs when tab is clicked
          }}
        >
          System Logs
        </button>
      </div>

      {activeTab === 'config' ? (
        <>
          <div className="autonomous-info-panel">
            <h3>What is Autonomous Trading?</h3>
            <p>
              Autonomous trading allows the system to automatically execute trades based on predefined strategies
              without constant human intervention. Configure and activate a strategy below to start trading.
            </p>
          </div>

          <div className="page-content">
            <div className="config-panel">
              <h3>Configure Strategy</h3>

              <div className="form-group">
                <label htmlFor="strategy">Strategy</label>
                <select
                  id="strategy"
                  value={selectedStrategy}
                  onChange={(e) => setSelectedStrategy(e.target.value)}
                  disabled={loading}
                >
                  {strategies.length === 0 ? (
                    <option value="">Loading strategies...</option>
                  ) : (
                    strategies.map(strategy => (
                      <option key={strategy.id} value={strategy.id}>{strategy.name}</option>
                    ))
                  )}
                </select>
                {selectedStrategy && strategies.length > 0 && (
                  <div className="strategy-description">
                    {strategies.find(s => s.id === selectedStrategy)?.description || 'No description available'}
                  </div>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="symbol">Symbol</label>
                <select
                  id="symbol"
                  value={selectedSymbol}
                  onChange={(e) => setSelectedSymbol(e.target.value)}
                  disabled={loading || symbolsLoading}
                >
                  {symbolsLoading ? (
                    <option value="">Loading symbols...</option>
                  ) : symbols.length === 0 ? (
                    <option value="">No symbols available</option>
                  ) : (
                    symbols.map(symbol => (
                      <option key={symbol} value={symbol}>{symbol}</option>
                    ))
                  )}
                </select>
                {symbolsLoading && (
                  <div className="input-hint">Fetching available symbols from MT5...</div>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="timeframe">Timeframe</label>
                <select
                  id="timeframe"
                  value={timeframe}
                  onChange={(e) => setTimeframe(e.target.value)}
                  disabled={loading}
                >
                  <option value="M1">1 Minute</option>
                  <option value="M5">5 Minutes</option>
                  <option value="M15">15 Minutes</option>
                  <option value="M30">30 Minutes</option>
                  <option value="H1">1 Hour</option>
                  <option value="H4">4 Hours</option>
                  <option value="D1">Daily</option>
                  <option value="W1">Weekly</option>
                  <option value="MN1">Monthly</option>
                </select>
              </div>

              <div className="form-row">
                <div className="form-group half">
                  <label htmlFor="riskPerTrade">Risk Per Trade</label>

                  {/* Mode Toggle Switch for Risk Per Trade */}
                  <div className="mode-toggle">
                    <span className={riskMode === 'percent' ? 'active-mode' : ''}>Percent Mode</span>
                    <label className="switch">
                      <input
                        type="checkbox"
                        checked={riskMode === 'amount'}
                        onChange={() => {
                          const newMode = riskMode === 'percent' ? 'amount' : 'percent';
                          setRiskMode(newMode);
                        }}
                        disabled={loading}
                      />
                      <span className="slider round"></span>
                    </label>
                    <span className={riskMode === 'amount' ? 'active-mode' : ''}>Amount Mode</span>
                  </div>

                  <input
                    type="number"
                    id="riskPerTrade"
                    value={riskMode === 'percent' ? riskPerTrade : riskAmount}
                    onChange={(e) => {
                      if (riskMode === 'percent') {
                        setRiskPerTrade(e.target.value);
                      } else {
                        setRiskAmount(e.target.value);
                      }
                    }}
                    min={riskMode === 'percent' ? "0.1" : "1"}
                    max={riskMode === 'percent' ? "10" : "10000"}
                    step={riskMode === 'percent' ? "0.1" : "1"}
                    disabled={loading}
                    placeholder={riskMode === 'percent' ? "Enter percentage" : `Enter ${getCurrencySymbol(accountCurrency)} amount`}
                  />

                  {/* Equivalent Value Display */}
                  {accountBalance > 0 && (
                    <div className="equivalent-value">
                      {riskMode === 'percent' ? (
                        <span className="equivalent-label">
                          ≈ {getCurrencySymbol(accountCurrency)}{formatCurrency(calculateEquivalentAmount(riskPerTrade))}
                        </span>
                      ) : (
                        <span className="equivalent-label">
                          ≈ {calculateEquivalentPercent(riskAmount).toFixed(2)}% of balance
                        </span>
                      )}
                    </div>
                  )}

                  <div className="input-hint">
                    {riskMode === 'percent'
                      ? 'Percentage of account balance risked per trade'
                      : `Fixed ${getCurrencySymbol(accountCurrency)} amount risked per trade`
                    }
                    {accountBalance > 0 && (
                      <div className="balance-info">
                        Account Balance: {getCurrencySymbol(accountCurrency)}{formatCurrency(accountBalance)}
                      </div>
                    )}
                  </div>
                </div>

                <div className="form-group half">
                  <label htmlFor="maxOpenTrades">Max Open Trades</label>
                  <input
                    type="number"
                    id="maxOpenTrades"
                    value={maxOpenTrades}
                    onChange={(e) => setMaxOpenTrades(e.target.value)}
                    min="1"
                    max="10"
                    step="1"
                    disabled={loading}
                  />
                  <div className="input-hint">Maximum number of concurrent trades</div>
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="minConfidence">Minimum Signal Confidence ({minConfidence}%)</label>
                <div className="slider-container">
                  <input
                    type="range"
                    id="minConfidence"
                    value={minConfidence}
                    onChange={(e) => setMinConfidence(parseInt(e.target.value))}
                    min="30"
                    max="95"
                    step="5"
                    disabled={loading}
                    className="confidence-slider"
                  />
                  <div className="slider-labels">
                    <span>30%</span>
                    <span>50%</span>
                    <span>70%</span>
                    <span>95%</span>
                  </div>
                </div>
                <div className="input-hint">
                  {minConfidence < 50 ?
                    "Low threshold - more trades but higher risk of false signals" :
                    minConfidence < 70 ?
                      "Moderate threshold - balanced between trade frequency and quality" :
                      "High threshold - fewer trades but higher quality signals"}
                </div>
              </div>

              <div className="risk-management-section">
                <h4>Risk Management Settings</h4>

                <div className="form-group">
                  <label htmlFor="max-total-risk">Max Total Risk (%)</label>
                  <input
                    id="max-total-risk"
                    type="number"
                    min="1"
                    max="20"
                    step="0.5"
                    value={maxTotalRiskPercent}
                    onChange={(e) => setMaxTotalRiskPercent(e.target.value)}
                    disabled={loading}
                  />
                  <span className="form-help">Maximum total risk across all positions</span>
                </div>

                <div className="form-group">
                  <label htmlFor="breakeven-trigger">Breakeven Trigger</label>

                  {/* Mode Toggle Switch for Breakeven */}
                  <div className="mode-toggle">
                    <span className={breakevenMode === 'percent' ? 'active-mode' : ''}>Percent Mode</span>
                    <label className="switch">
                      <input
                        type="checkbox"
                        checked={breakevenMode === 'pips'}
                        onChange={() => {
                          const newMode = breakevenMode === 'percent' ? 'pips' : 'percent';
                          setBreakevenMode(newMode);
                        }}
                        disabled={loading}
                      />
                      <span className="slider round"></span>
                    </label>
                    <span className={breakevenMode === 'pips' ? 'active-mode' : ''}>Pips Mode</span>
                  </div>

                  <input
                    id="breakeven-trigger"
                    type="number"
                    min={breakevenMode === 'percent' ? "0.1" : "1"}
                    max={breakevenMode === 'percent' ? "5" : "500"}
                    step={breakevenMode === 'percent' ? "0.1" : "1"}
                    value={breakevenMode === 'percent' ? breakevenTriggerPercent : breakevenTriggerPips}
                    onChange={(e) => {
                      if (breakevenMode === 'percent') {
                        setBreakevenTriggerPercent(e.target.value);
                      } else {
                        setBreakevenTriggerPips(e.target.value);
                      }
                    }}
                    disabled={loading}
                    placeholder={breakevenMode === 'percent' ? "Enter percentage" : "Enter pips"}
                  />
                  <span className="form-help">
                    {breakevenMode === 'percent'
                      ? 'Move SL to breakeven after reaching this profit percentage'
                      : 'Move SL to breakeven after reaching this profit in pips'
                    }
                  </span>
                </div>

                <div className="form-group">
                  <label htmlFor="trailing-trigger">Trailing Stop Trigger</label>

                  {/* Mode Toggle Switch for Trailing Stop Trigger */}
                  <div className="mode-toggle">
                    <span className={trailingTriggerMode === 'percent' ? 'active-mode' : ''}>Percent Mode</span>
                    <label className="switch">
                      <input
                        type="checkbox"
                        checked={trailingTriggerMode === 'pips'}
                        onChange={() => {
                          const newMode = trailingTriggerMode === 'percent' ? 'pips' : 'percent';
                          setTrailingTriggerMode(newMode);
                        }}
                        disabled={loading}
                      />
                      <span className="slider round"></span>
                    </label>
                    <span className={trailingTriggerMode === 'pips' ? 'active-mode' : ''}>Pips Mode</span>
                  </div>

                  <input
                    id="trailing-trigger"
                    type="number"
                    min={trailingTriggerMode === 'percent' ? "0.1" : "1"}
                    max={trailingTriggerMode === 'percent' ? "5" : "500"}
                    step={trailingTriggerMode === 'percent' ? "0.1" : "1"}
                    value={trailingTriggerMode === 'percent' ? trailingStopTriggerPercent : trailingStopTriggerPips}
                    onChange={(e) => {
                      if (trailingTriggerMode === 'percent') {
                        setTrailingStopTriggerPercent(e.target.value);
                      } else {
                        setTrailingStopTriggerPips(e.target.value);
                      }
                    }}
                    disabled={loading}
                    placeholder={trailingTriggerMode === 'percent' ? "Enter percentage" : "Enter pips"}
                  />
                  <span className="form-help">
                    {trailingTriggerMode === 'percent'
                      ? 'Start trailing stop after reaching this profit percentage'
                      : 'Start trailing stop after reaching this profit in pips'
                    }
                  </span>
                </div>

                <div className="form-group">
                  <label htmlFor="trailing-distance">Trailing Stop Distance</label>

                  {/* Mode Toggle Switch for Trailing Stop */}
                  <div className="mode-toggle">
                    <span className={trailingStopMode === 'percent' ? 'active-mode' : ''}>Percent Mode</span>
                    <label className="switch">
                      <input
                        type="checkbox"
                        checked={trailingStopMode === 'pips'}
                        onChange={() => {
                          const newMode = trailingStopMode === 'percent' ? 'pips' : 'percent';
                          setTrailingStopMode(newMode);
                        }}
                        disabled={loading}
                      />
                      <span className="slider round"></span>
                    </label>
                    <span className={trailingStopMode === 'pips' ? 'active-mode' : ''}>Pips Mode</span>
                  </div>

                  <input
                    id="trailing-distance"
                    type="number"
                    min={trailingStopMode === 'percent' ? "0.1" : "1"}
                    max={trailingStopMode === 'percent' ? "5" : "1000"}
                    step={trailingStopMode === 'percent' ? "0.1" : "1"}
                    value={trailingStopMode === 'percent' ? trailingStopDistancePercent : trailingStopDistancePips}
                    onChange={(e) => {
                      if (trailingStopMode === 'percent') {
                        setTrailingStopDistancePercent(e.target.value);
                      } else {
                        setTrailingStopDistancePips(e.target.value);
                      }
                    }}
                    disabled={loading}
                    placeholder={trailingStopMode === 'percent' ? "Enter percentage" : "Enter pips"}
                  />
                  <span className="form-help">
                    {trailingStopMode === 'percent'
                      ? 'Distance to maintain for trailing stop as percentage of price'
                      : 'Distance to maintain for trailing stop in pips'
                    }
                  </span>
                </div>

                <div className="form-group">
                  <label htmlFor="max-profit">Max Profit Target ({getCurrencySymbol(accountCurrency)})</label>
                  <input
                    id="max-profit"
                    type="number"
                    min="0"
                    step="10"
                    value={maxProfitTarget}
                    onChange={(e) => setMaxProfitTarget(e.target.value)}
                    disabled={loading}
                  />
                  <span className="form-help">Stop bot after reaching this profit (0 to disable)</span>
                </div>

                <div className="form-group">
                  <label htmlFor="max-loss">Max Loss Limit ({getCurrencySymbol(accountCurrency)})</label>
                  <input
                    id="max-loss"
                    type="number"
                    min="0"
                    step="10"
                    value={maxLossTarget}
                    onChange={(e) => setMaxLossTarget(e.target.value)}
                    disabled={loading}
                  />
                  <span className="form-help">Stop bot after reaching this loss (0 to disable)</span>
                </div>
              </div>

              <div className="form-actions">
                <button
                  className="button primary"
                  onClick={handleActivateStrategy}
                  disabled={isActivating || loading}
                >
                  {isActivating ? 'Activating...' : 'Activate Strategy'}
                </button>
              </div>
            </div>

            <div className="active-strategies-panel">
              <h3>Active Strategies</h3>
              {loading ? (
                <div className="loading-indicator">Loading strategies...</div>
              ) : activeStrategies.length === 0 ? (
                <div className="no-strategies">
                  <p>No active strategies. Activate a strategy to start autonomous trading.</p>
                </div>
              ) : (
                <div className="strategies-list">
                  {activeStrategies.map(strategy => (
                    <div key={strategy.id} className="strategy-card">
                      <div className="strategy-header">
                        <h4>{strategy.name}</h4>
                        <span className={`status ${strategy.status === 'active' ? 'active' : 'inactive'}`}>
                          {strategy.status === 'active' ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      <div className="strategy-details">
                        <div className="detail-row">
                          <div className="detail-item">
                            <span className="detail-label">Symbol:</span>
                            <span className="detail-value">{strategy.symbol}</span>
                          </div>
                          <div className="detail-item">
                            <span className="detail-label">Timeframe:</span>
                            <span className="detail-value">{strategy.timeframe}</span>
                          </div>
                        </div>
                        <div className="detail-row">
                          <div className="detail-item">
                            <span className="detail-label">Risk:</span>
                            <span className="detail-value">{strategy.risk_percent}%</span>
                          </div>
                          <div className="detail-item">
                            <span className="detail-label">Max Trades:</span>
                            <span className="detail-value">{strategy.max_trades}</span>
                          </div>
                        </div>
                        <div className="detail-row">
                          <div className="detail-item">
                            <span className="detail-label">Min Confidence:</span>
                            <span className="detail-value">{strategy.min_confidence || 60}%</span>
                          </div>
                        </div>

                        <div className="strategy-section">
                          <h5>Risk Management</h5>
                          <div className="detail-row">
                            <div className="detail-item">
                              <span className="detail-label">Max Total Risk:</span>
                              <span className="detail-value">{maxTotalRiskPercent}%</span>
                            </div>
                            <div className="detail-item">
                              <span className="detail-label">Break-Even Trigger:</span>
                              <span className="detail-value">
                                {breakevenMode === 'pips'
                                  ? `${breakevenTriggerPips} pips`
                                  : `${breakevenTriggerPercent}%`
                                }
                              </span>
                            </div>
                          </div>
                          <div className="detail-row">
                            <div className="detail-item">
                              <span className="detail-label">Trailing Trigger:</span>
                              <span className="detail-value">
                                {trailingTriggerMode === 'pips'
                                  ? `${trailingStopTriggerPips} pips`
                                  : `${trailingStopTriggerPercent}%`
                                }
                              </span>
                            </div>
                            <div className="detail-item">
                              <span className="detail-label">Trailing Distance:</span>
                              <span className="detail-value">
                                {trailingStopMode === 'pips'
                                  ? `${trailingStopDistancePips} pips`
                                  : `${trailingStopDistancePercent}%`
                                }
                              </span>
                            </div>
                          </div>
                          <div className="detail-row">
                            <div className="detail-item">
                              <span className="detail-label">Profit Target:</span>
                              <span className="detail-value">{getCurrencySymbol(accountCurrency)}{maxProfitTarget > 0 ? maxProfitTarget : 'Disabled'}</span>
                            </div>
                            <div className="detail-item">
                              <span className="detail-label">Loss Limit:</span>
                              <span className="detail-value">{getCurrencySymbol(accountCurrency)}{maxLossTarget > 0 ? maxLossTarget : 'Disabled'}</span>
                            </div>
                          </div>
                        </div>
                        {strategy.activated_at && (
                          <div className="detail-item full">
                            <span className="detail-label">Active Since:</span>
                            <span className="detail-value">{new Date(strategy.activated_at).toLocaleString()}</span>
                          </div>
                        )}
                        {strategy.profit !== undefined && (
                          <div className="detail-item">
                            <span className="detail-label">P/L:</span>
                            <span className={`detail-value ${strategy.profit >= 0 ? 'positive' : 'negative'}`}>
                              {getCurrencySymbol(accountCurrency)}{Math.abs(strategy.profit || 0).toFixed(2)}
                            </span>
                          </div>
                        )}
                        {strategy.trades_count !== undefined && (
                          <div className="detail-item">
                            <span className="detail-label">Trades:</span>
                            <span className="detail-value">{strategy.trades_count || 0}</span>
                          </div>
                        )}
                      </div>
                      <div className="strategy-actions">
                        <button
                          className="button danger"
                          onClick={() => handleDeactivateStrategy(strategy.id)}
                        >
                          Deactivate
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </>
      ) : activeTab === 'monitor' ? (
        <div className="monitoring-container">
          {renderMonitoringSection()}
        </div>
      ) : activeTab === 'entry-spacing' ? (
        <div className="entry-spacing-container">
          <EntrySpacingSettings onSettingsChange={handleEntrySpacingSettingsChange} />
        </div>
      ) : (
        <div className="logs-container-wrapper">
          {renderLogsSection()}
        </div>
      )}
    </div>
  );
};

export default AutonomousTradingPage;
