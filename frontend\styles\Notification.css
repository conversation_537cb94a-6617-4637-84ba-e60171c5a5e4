.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 350px;
}

.notification {
  background-color: var(--card);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 15px;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  animation: slideInRight 0.3s ease forwards;
  border-left: 4px solid var(--primary);
}

.notification.success {
  border-left-color: var(--success);
}

.notification.error {
  border-left-color: var(--error);
}

.notification.warning {
  border-left-color: var(--warning);
}

.notification.info {
  border-left-color: var(--info);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--text);
}

.notification-message {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0 0 0 10px;
  line-height: 1;
}

.notification-close:hover {
  color: var(--text);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* Responsive styles */
@media (max-width: 576px) {
  .notification-container {
    left: 20px;
    right: 20px;
    max-width: none;
  }
}
