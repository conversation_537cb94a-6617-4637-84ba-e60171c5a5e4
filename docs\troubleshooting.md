# Troubleshooting Guide

This document provides solutions for common issues encountered when developing or using the Garuda Algo Trading application.

## Connection Issues

### MT5 Connection Failures

**Symptoms:**
- "Failed to connect to MT5" error message
- Timeout errors
- Connection status shows "Disconnected" despite attempts to connect

**Solutions:**
1. Verify MetaTrader 5 is running and logged in
2. Check credentials in `backend/mt5_settings.json`
3. Ensure MT5 allows API connections:
   - In MT5, go to Tools > Options > Expert Advisors
   - Enable "Allow WebRequests for listed URL"
   - Add "localhost" to the allowed URLs
4. Restart MT5 and the application
5. Check logs in `backend/mt5_integration.log` for specific error messages

### Backend-Frontend Communication Issues

**Symptoms:**
- UI not updating with market data
- Trading actions not being executed
- "Cannot connect to server" errors

**Solutions:**
1. Check if backend server is running (check process in Task Manager)
2. Verify WebSocket connection:
   - Open browser console while app is running
   - Look for WebSocket connection errors
3. Check for port conflicts:
   - Default port is 8000, ensure no other applications are using it
   - Change port in configuration if needed
4. Review server logs in terminal or log files
5. Restart both backend server and frontend application

## Data Issues

### Missing or Incomplete Market Data

**Symptoms:**
- Empty charts
- Missing candles or price information
- "No data available" messages

**Solutions:**
1. Verify symbol is valid in MT5
2. Check timeframe access permissions in your MT5 account
3. Ensure sufficient history is downloaded in MT5:
   - Open the symbol chart in MT5
   - Right-click and select "Chart Properties"
   - Set "Max bars in history" to a higher value
4. Check network connection stability
5. Review data retrieval logs in `backend/analysis_engine.log`

### Incorrect Analysis Results

**Symptoms:**
- Indicators showing unexpected values
- Signals contradicting visual chart patterns
- Analysis results inconsistent with MT5

**Solutions:**
1. Verify indicator parameters match expected settings
2. Check data timestamps for alignment issues
3. Compare raw calculations with MT5's built-in indicators
4. Look for data gaps that might affect calculations
5. Enable debug logging for detailed calculation steps

## Performance Issues

### Slow Application Response

**Symptoms:**
- UI lag or freezing
- Delayed data updates
- High CPU/memory usage

**Solutions:**
1. Limit the number of active symbols being analyzed
2. Reduce update frequency in settings
3. Close unnecessary application windows/tabs
4. Check RAM and CPU usage:
   - Use Task Manager to identify resource bottlenecks
   - Consider running app on a more powerful machine
5. Review performance logs for bottlenecks

### Memory Leaks

**Symptoms:**
- Increasing memory usage over time
- Application crashing after extended use
- Gradual performance degradation

**Solutions:**
1. Restart application periodically
2. Check for circular references in React components
3. Verify that WebSocket connections are properly closed
4. Review Python object lifecycle management
5. Enable memory profiling for detailed analysis

## Trading Issues

### Failed Order Execution

**Symptoms:**
- "Order rejected" errors
- Orders not appearing in MT5
- Incorrect order parameters

**Solutions:**
1. Verify trading is enabled in your MT5 account
2. Check for symbol trading restrictions
3. Ensure sufficient margin available
4. Verify order parameters are within broker limits:
   - Volume within min/max allowed
   - Stop loss/take profit within required distance
5. Review execution logs in `backend/autonomous_trader.log`

### Strategy Misbehavior

**Symptoms:**
- Unexpected trade entries or exits
- Strategy not following documented rules
- Inconsistent performance

**Solutions:**
1. Review strategy parameters in settings
2. Check indicator calculations against expected values
3. Verify market conditions match strategy requirements
4. Enable strategy debugging for step-by-step execution
5. Compare behavior with backtesting results

## Installation Issues

### Dependency Problems

**Symptoms:**
- Import errors
- Missing module errors
- Version conflicts

**Solutions:**
1. Verify Python version (3.12+ required)
2. Reinstall dependencies:
   ```bash
   pip uninstall -r requirements.txt
   pip install -r requirements.txt
   ```
3. Check for conflicting packages
4. Create a fresh virtual environment
5. Look for specific error messages in console output

### Build Failures

**Symptoms:**
- Application fails to start
- Build process errors
- Packaging errors

**Solutions:**
1. Clean node_modules and reinstall:
   ```bash
   rm -rf frontend/node_modules
   cd frontend && npm install
   ```
2. Check for syntax errors in recent code changes
3. Verify electron and node versions compatibility
4. Clear cache:
   ```bash
   npm cache clean --force
   ```
5. Check build logs for specific error messages
