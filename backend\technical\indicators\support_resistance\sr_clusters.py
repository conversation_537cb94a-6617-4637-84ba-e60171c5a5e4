from typing import Dict
import numpy as np
import pandas as pd

from backend.technical.base_indicator import BaseIndicator

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

class SRClusterIndicator(BaseIndicator):
    """Horizontal Support/Resistance Cluster indicator."""

    def __init__(self, lookback: int = 100, price_buffer_pct: float = 0.002, min_touches: int = 3, window: int = 30, threshold_multiplier: float = 0.5):
        """
        Initialize S/R Cluster indicator.

        Args:
            lookback: Number of periods to look back for highs/lows.
            price_buffer_pct: Price buffer as percentage of median price to group levels.
            min_touches: Minimum number of touches (highs/lows) to form a cluster.
            window: Window size for clustering (used in calculate_from_levels).
            threshold_multiplier: Threshold multiplier for clustering (used in calculate_from_levels).
        """
        super().__init__({
            'lookback': lookback,
            'price_buffer_pct': price_buffer_pct,
            'min_touches': min_touches,
            'window': window,
            'threshold_multiplier': threshold_multiplier
        })

    def calculate(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """Calculate S/R cluster levels."""
        df = data.copy()
        if df.empty or len(df) < self.params['lookback']:
             # Cannot determine clusters without enough data
             return {'cluster_levels': np.array([]), 'cluster_touches': np.array([])}

        lookback = self.params['lookback']
        price_buffer_pct = self.params['price_buffer_pct']
        min_touches = self.params['min_touches']

        # Use the last 'lookback' periods
        subset = df.iloc[-lookback:]
        highs = subset['high'].values
        lows = subset['low'].values
        all_levels = np.concatenate([highs, lows])
        all_levels = all_levels[~np.isnan(all_levels)] # Remove NaNs

        if len(all_levels) == 0:
             return {'cluster_levels': np.array([]), 'cluster_touches': np.array([])}

        # Calculate buffer based on median price
        median_price = np.median(all_levels)
        buffer = median_price * price_buffer_pct

        # Find clusters (simplified approach for indicator output)
        # Sort levels to make clustering easier
        all_levels = np.sort(all_levels)
        clusters = [] # List of tuples: (mean_level, touches)

        if len(all_levels) > 0:
            current_cluster_levels = [all_levels[0]]
            current_cluster_touches = 1

            for i in range(1, len(all_levels)):
                # If current level is close to the mean of the current cluster
                if abs(all_levels[i] - np.mean(current_cluster_levels)) < buffer:
                    current_cluster_levels.append(all_levels[i])
                    current_cluster_touches += 1
                else:
                    # Finalize previous cluster if it meets criteria
                    if current_cluster_touches >= min_touches:
                        clusters.append((np.mean(current_cluster_levels), current_cluster_touches))
                    # Start new cluster
                    current_cluster_levels = [all_levels[i]]
                    current_cluster_touches = 1

            # Add the last cluster if it meets criteria
            if current_cluster_touches >= min_touches:
                 clusters.append((np.mean(current_cluster_levels), current_cluster_touches))

        # Sort clusters by price level
        clusters.sort(key=lambda x: x[0])

        # Prepare output arrays (constant for the whole period)
        n = len(df)
        num_clusters = len(clusters)
        cluster_levels_array = np.full((n, num_clusters), np.nan)
        cluster_touches_array = np.full((n, num_clusters), np.nan)

        if num_clusters > 0:
            levels, touches = zip(*clusters)
            for i in range(n):
                cluster_levels_array[i, :] = levels
                cluster_touches_array[i, :] = touches


        # For standardization, return a fixed structure even if clusters change
        # Returning dynamic number of levels is tricky for standard output format.
        # Option 1: Return fixed number of strongest clusters?
        # Option 2: Return all clusters found in the lookback period as constant arrays? (Chosen here)
        # Option 3: Return only the most recent cluster data?

        self._values = {
            # These arrays will have shape (n_bars, n_clusters_found)
            # The values will be the same for all bars, representing the clusters found in the lookback window
            'cluster_levels': cluster_levels_array,
            'cluster_touches': cluster_touches_array
        }
        # Note: A more practical indicator might return only N strongest clusters
        # or levels closest to the current price. This implementation returns all found.
        return self._values

    def calculate_from_levels(self, levels, current_price, level_details):
        """
        Calculate clusters from a list of price levels.

        Args:
            levels: List of price levels to cluster
            current_price: Current price for reference
            level_details: Dictionary with details about each level

        Returns:
            Dictionary with resistance and support clusters
        """
        if not levels or len(levels) == 0:
            return {'resistance_clusters': [], 'support_clusters': []}

        # Sort levels
        sorted_levels = sorted(levels)

        # Calculate buffer based on price range and threshold multiplier
        price_range = max(sorted_levels) - min(sorted_levels)

        # Adjust buffer based on price magnitude to ensure proper clustering
        # For large numbers like 84685, we need a larger buffer
        current_price_value = current_price
        if isinstance(current_price, dict) and 'bid' in current_price:
            current_price_value = current_price['bid']

        price_magnitude = len(str(int(current_price_value)))
        magnitude_factor = 10 ** (price_magnitude - 3) if price_magnitude > 3 else 1

        # Calculate buffer with magnitude adjustment
        base_buffer = price_range * self.params['threshold_multiplier'] / len(sorted_levels)
        buffer = max(base_buffer, magnitude_factor)  # Ensure minimum buffer based on price magnitude

        # Find clusters
        clusters = []
        current_cluster = [sorted_levels[0]]

        for i in range(1, len(sorted_levels)):
            if abs(sorted_levels[i] - sorted_levels[i-1]) < buffer:
                current_cluster.append(sorted_levels[i])
            else:
                if len(current_cluster) >= 2:  # At least 2 levels to form a cluster
                    clusters.append(current_cluster)
                current_cluster = [sorted_levels[i]]

        # Add the last cluster if it's valid
        if len(current_cluster) >= 2:
            clusters.append(current_cluster)

        # Process clusters into resistance and support
        resistance_clusters = []
        support_clusters = []

        # Get the current price value
        current_price_value = current_price
        if isinstance(current_price, dict) and 'bid' in current_price:
            current_price_value = current_price['bid']

        for cluster in clusters:
            # Calculate average level and strength
            avg_level = sum(cluster) / len(cluster)

            # Collect types and strengths from level_details
            types = []
            strengths = []
            for level in cluster:
                if level in level_details:
                    types.extend(level_details[level].get('types', []))
                    strengths.extend(level_details[level].get('strengths', [1]))

            # Calculate average strength
            avg_strength = sum(strengths) / len(strengths) if strengths else 1

            # Create cluster info
            cluster_info = {
                'level': avg_level,
                'price': avg_level,  # Add price field for frontend compatibility
                'strength': avg_strength,
                'types': list(set(types)),  # Remove duplicates
                'source': 'Cluster',  # Add source field for frontend compatibility
                'touches': len(cluster),  # Add touches field for frontend compatibility
                'count': len(cluster)
            }

            # Add to appropriate list based on relation to current price
            if avg_level > current_price_value:
                resistance_clusters.append(cluster_info)
            else:
                support_clusters.append(cluster_info)

        # Sort clusters
        resistance_clusters.sort(key=lambda x: x['level'])
        support_clusters.sort(key=lambda x: x['level'], reverse=True)

        return {
            'resistance_clusters': resistance_clusters,
            'support_clusters': support_clusters,
            'sr_cluster_zones': resistance_clusters + support_clusters  # Add combined list for direct inclusion
        }

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if not isinstance(self.params['lookback'], int) or self.params['lookback'] < 1:
            raise ValueError("Lookback must be a positive integer")
        if not isinstance(self.params['price_buffer_pct'], float) or self.params['price_buffer_pct'] <= 0:
             raise ValueError("Price buffer percentage must be positive")
        if not isinstance(self.params['min_touches'], int) or self.params['min_touches'] < 1:
             raise ValueError("Minimum touches must be a positive integer")
        if not isinstance(self.params['window'], int) or self.params['window'] < 1:
             raise ValueError("Window must be a positive integer")
        if not isinstance(self.params['threshold_multiplier'], float) or self.params['threshold_multiplier'] <= 0:
             raise ValueError("Threshold multiplier must be positive")
        return True
