from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class MoneyFlowIndexIndicator(BaseIndicator):
    """Money Flow Index indicator for measuring money flow and volume."""
    
    def __init__(self, period: int = 14):
        """Initialize Money Flow Index indicator."""
        super().__init__({'period': period})
    
    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Money Flow Index values."""
        df = data.to_dataframe()
        if df.empty:
            return {}
        
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        volume = df['volume'].values
        period = self.params['period']
        
        # Calculate typical price
        typical_price = (high + low + close) / 3
        
        # Calculate raw money flow
        raw_money_flow = typical_price * volume
        
        # Calculate positive and negative money flow
        price_change = np.diff(typical_price)
        price_change = np.insert(price_change, 0, 0)  # First value is 0
        
        positive_flow = np.where(price_change > 0, raw_money_flow, 0)
        negative_flow = np.where(price_change < 0, raw_money_flow, 0)
        
        # Calculate positive and negative money flow ratios
        positive_mf = pd.Series(positive_flow).rolling(window=period).sum()
        negative_mf = pd.Series(negative_flow).rolling(window=period).sum()
        
        # Calculate money flow ratio
        money_flow_ratio = positive_mf / negative_mf
        
        # Calculate Money Flow Index
        mfi = 100 - (100 / (1 + money_flow_ratio))
        
        # Calculate overbought/oversold levels
        overbought = 80
        oversold = 20
        
        # Calculate divergence
        price_trend = np.sign(np.diff(close))
        mfi_trend = np.sign(np.diff(mfi))
        divergence = np.where(price_trend != mfi_trend, 1, 0)
        divergence = np.insert(divergence, 0, 0)
        
        self._values = {
            'mfi': mfi.values,
            'money_flow_ratio': money_flow_ratio.values,
            'positive_mf': positive_mf.values,
            'negative_mf': negative_mf.values,
            'divergence': divergence,
            'overbought': np.full_like(mfi, overbought),
            'oversold': np.full_like(mfi, oversold)
        }
        return self._values 