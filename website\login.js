document.addEventListener('DOMContentLoaded', function() {
  const loginForm = document.getElementById('login-form');
  const emailInput = document.getElementById('email');
  const passwordInput = document.getElementById('password');
  const emailError = document.getElementById('email-error');
  const passwordError = document.getElementById('password-error');
  const loginError = document.getElementById('login-error');
  const verificationMessage = document.getElementById('verification-message');
  const resetPasswordLink = document.getElementById('reset-password');
  
  // Form validation
  function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  }
  
  // Show error message
  function showError(element, show) {
    element.style.display = show ? 'block' : 'none';
  }
  
  // Input validation events
  emailInput.addEventListener('blur', function() {
    const isValid = validateEmail(emailInput.value);
    showError(emailError, !isValid);
  });
  
  passwordInput.addEventListener('blur', function() {
    showError(passwordError, !passwordInput.value);
  });
  
  // Form submission
  loginForm.addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Get form values
    const email = emailInput.value;
    const password = passwordInput.value;
    
    // Validate fields
    const isEmailValid = validateEmail(email);
    const isPasswordValid = !!password;
    
    // Show errors for invalid fields
    showError(emailError, !isEmailValid);
    showError(passwordError, !isPasswordValid);
    
    // Hide previous error messages
    showError(loginError, false);
    showError(verificationMessage, false);
    
    // If all validations pass, submit the form
    if (isEmailValid && isPasswordValid) {
      // Login user with Firebase
      window.firebaseAuth.loginUser(email, password)
        .then(userCredential => {
          return window.firebaseAuth.checkUserVerification(userCredential.user.uid);
        })
        .then(isVerified => {
          if (isVerified) {
            window.location.href = 'dashboard.html';
          } else {
            showError(verificationMessage, true);
          }
        })
        .catch(error => {
          console.error('Login error:', error);
          showError(loginError, true);
        });
    }
  });
  
  // Password reset functionality
  resetPasswordLink.addEventListener('click', function(e) {
    e.preventDefault();
    
    const email = emailInput.value;
    const isEmailValid = validateEmail(email);
    
    if (!isEmailValid) {
      showError(emailError, true);
      alert('Please enter a valid email address to reset your password.');
      return;
    }
    
    // Send password reset email
    window.firebaseAuth.resetPassword(email)
      .then(() => {
        alert('Password reset email sent. Please check your inbox.');
      })
      .catch(error => {
        console.error('Password reset error:', error);
        alert('Failed to send password reset email: ' + error.message);
      });
  });
});
