from typing import Dict, Any
import numpy as np
import pandas as pd

from backend.technical.base_indicator import BaseIndicator

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

class PivotPointsIndicator(BaseIndicator):
    """Pivot Points indicator supporting multiple calculation methods."""

    def __init__(self, timeframe: str = 'D', method: str = 'classic'):
        """
        Initialize Pivot Points indicator.

        Args:
            timeframe: The timeframe for calculating pivots ('D', 'W', 'M').
                       Determines the previous period's HLC to use.
            method: Calculation method ('classic', 'fibonacci', 'woodie', 'camarilla', 'demark').
        """
        super().__init__({'timeframe': timeframe, 'method': method})

    def calculate(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """Calculate Pivot Points (PP, S1-S3, R1-R3, potentially S4/R4) using the specified method."""
        df = data.copy()
        # Define expected keys based on method early on
        method = self.params['method'].lower()
        expected_keys = ['pivot_point', 's1', 's2', 's3', 'r1', 'r2', 'r3']
        if method == 'camarilla':
            expected_keys.extend(['s4', 'r4'])

        if df.empty:
             return {key: np.array([]) for key in expected_keys}

        # Check for 'open' column if needed
        if method == 'woodie' and 'open' not in df.columns:
             raise ValueError("Woodie pivot points require 'open' column in MarketData")
        if method == 'demark' and 'open' not in df.columns:
             raise ValueError("Demark pivot points require 'open' column in MarketData")

        timeframe = self.params['timeframe'].upper()

        # Resample to get previous period's High, Low, Close
        # Ensure index is datetime
        if not isinstance(df.index, pd.DatetimeIndex):
             df.index = pd.to_datetime(df.index)

        resample_freq = timeframe # 'D', 'W', 'M'
        # Use 'B' business day for daily if needed, but 'D' covers weekends for crypto etc.
        # Weekly needs careful handling of start day (e.g., 'W-MON')
        if timeframe == 'W':
            resample_freq = 'W-MON' # Assume week starts on Monday

        # Shift(1) gets the *previous* period's data relative to the current bar's start
        prev_high = df['high'].resample(resample_freq).max().shift(1)
        prev_low = df['low'].resample(resample_freq).min().shift(1)
        prev_close = df['close'].resample(resample_freq).last().shift(1)
        prev_open = df['open'].resample(resample_freq).first().shift(1) if 'open' in df.columns else None

        # Reindex to match the original dataframe's index for calculation alignment
        # Use forward fill to apply the previous period's HLC to all bars in the current period
        prev_high = prev_high.reindex(df.index, method='ffill')
        prev_low = prev_low.reindex(df.index, method='ffill')
        prev_close = prev_close.reindex(df.index, method='ffill')

        # --- Calculate Pivot Levels based on method ---
        pivot_point = pd.Series(np.nan, index=df.index)
        s1, s2, s3, s4 = pd.Series(np.nan, index=df.index), pd.Series(np.nan, index=df.index), pd.Series(np.nan, index=df.index), pd.Series(np.nan, index=df.index)
        r1, r2, r3, r4 = pd.Series(np.nan, index=df.index), pd.Series(np.nan, index=df.index), pd.Series(np.nan, index=df.index), pd.Series(np.nan, index=df.index)
        range_hl = prev_high - prev_low

        if method == 'classic':
            pivot_point = (prev_high + prev_low + prev_close) / 3
            s1 = (2 * pivot_point) - prev_high
            s2 = pivot_point - range_hl
            s3 = prev_low - 2 * (prev_high - pivot_point) # Classic S3 variation
            # s3 = s1 - range_hl # Alternative S3
            r1 = (2 * pivot_point) - prev_low
            r2 = pivot_point + range_hl
            r3 = prev_high + 2 * (pivot_point - prev_low) # Classic R3 variation
            # r3 = r1 + range_hl # Alternative R3

        elif method == 'fibonacci':
            pivot_point = (prev_high + prev_low + prev_close) / 3
            s1 = pivot_point - 0.382 * range_hl
            s2 = pivot_point - 0.618 * range_hl
            s3 = pivot_point - 1.000 * range_hl
            r1 = pivot_point + 0.382 * range_hl
            r2 = pivot_point + 0.618 * range_hl
            r3 = pivot_point + 1.000 * range_hl

        elif method == 'woodie':
            # Ensure prev_open is available and aligned
            if prev_open is None:
                 raise ValueError("Woodie method requires 'open' data, which was not found or aligned.")
            pivot_point = (prev_high + prev_low + 2 * prev_open) / 4
            s1 = (2 * pivot_point) - prev_high
            s2 = pivot_point - range_hl
            s3 = prev_low - 2 * (prev_high - pivot_point) # Using classic S3 variation
            # s3 = s1 - range_hl
            r1 = (2 * pivot_point) - prev_low
            r2 = pivot_point + range_hl
            r3 = prev_high + 2 * (pivot_point - prev_low) # Using classic R3 variation
            # r3 = r1 + range_hl

        elif method == 'camarilla':
            pivot_point = (prev_high + prev_low + prev_close) / 3 # PP is same as classic
            r4 = prev_close + range_hl * 1.1 / 2
            r3 = prev_close + range_hl * 1.1 / 4
            r2 = prev_close + range_hl * 1.1 / 6
            r1 = prev_close + range_hl * 1.1 / 12
            s1 = prev_close - range_hl * 1.1 / 12
            s2 = prev_close - range_hl * 1.1 / 6
            s3 = prev_close - range_hl * 1.1 / 4
            s4 = prev_close - range_hl * 1.1 / 2

        elif method == 'demark':
             # Ensure prev_open is available and aligned
            if prev_open is None:
                 raise ValueError("Demark method requires 'open' data, which was not found or aligned.")
            x_values = pd.Series(np.nan, index=df.index)
            cond1 = prev_close < prev_open
            cond2 = prev_close > prev_open
            cond3 = prev_close == prev_open

            x_values[cond1] = prev_high[cond1] + (2 * prev_low[cond1]) + prev_close[cond1]
            x_values[cond2] = (2 * prev_high[cond2]) + prev_low[cond2] + prev_close[cond2]
            x_values[cond3] = prev_high[cond3] + prev_low[cond3] + (2 * prev_close[cond3])

            pivot_point = x_values / 4 # Demark defines this as the pivot
            r1 = x_values / 2 - prev_low
            s1 = x_values / 2 - prev_high
            # Demark typically only defines PP, R1, S1. Others are not standard.
            # We'll leave r2, r3, s2, s3 as NaN for Demark.

        else:
             # This case should be caught by validate_params, but added for safety
             raise ValueError(f"Unknown pivot point method: {method}")

        # --- Prepare output ---
        self._values = {
            'pivot_point': pivot_point.values,
            's1': s1.values,
            's2': s2.values,
            's3': s3.values,
            'r1': r1.values,
            'r2': r2.values,
            'r3': r3.values,
        }
        # Add S4/R4 only if Camarilla
        if method == 'camarilla':
            self._values['s4'] = s4.values
            self._values['r4'] = r4.values

        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_timeframes = ['D', 'W', 'M']
        valid_methods = ['classic', 'fibonacci', 'woodie', 'camarilla', 'demark']
        if self.params['timeframe'].upper() not in valid_timeframes:
            raise ValueError(f"Timeframe must be one of {valid_timeframes}")
        if self.params['method'].lower() not in valid_methods:
            raise ValueError(f"Method must be one of {valid_methods}")
        return True
