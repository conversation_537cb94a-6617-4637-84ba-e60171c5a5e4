import logging
import time
import threading
import json
import os
from typing import Dict, List, Optional, Tuple, Union, Any
from datetime import datetime
import MetaTrader5 as mt5
import random

# Setup logging with proper format for the monitoring dashboard
logger = logging.getLogger('autonomous_trader')
logger.setLevel(logging.INFO)

# Check if handler already exists to avoid duplicate handlers
if not logger.handlers:
    log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'autonomous_trader.log')
    file_handler = logging.FileHandler(log_file)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # Add console handler for development
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

class AutonomousTrader:
    """
    A class to handle autonomous trading based on recommendations.
    """
    
    def __init__(self, mt5_module, analysis_engine, recommendation_engine):
        """
        Initialize the autonomous trader.
        
        Args:
            mt5_module: An instance of the MT5Integration class
            analysis_engine: An instance of the AnalysisEngine class
            recommendation_engine: An instance of the RecommendationEngine class
        """
        self.mt5 = mt5_module
        self.analysis = analysis_engine
        self.recommendation = recommendation_engine
        self.running = False
        self.thread = None
        self.config = self._load_config()
        self.last_scan_time = {}
        self.magic_number = 12345  # Magic number for autonomous trades
    
    def start(self) -> Dict[str, Any]:
        """
        Start autonomous trading.
        
        Returns:
            Dict with success status and message
        """
        if self.running:
            return {
                "success": False,
                "message": "Autonomous trading is already running"
            }
        
        try:
            # Check connection
            if not self.mt5.check_connection()["connected"]:
                return {
                    "success": False,
                    "message": "Not connected to MT5"
                }
            
            # Start autonomous trading thread
            self.running = True
            self.thread = threading.Thread(target=self._trading_loop)
            self.thread.daemon = True
            self.thread.start()
            
            logger.info("Autonomous trading started")
            
            return {
                "success": True,
                "message": "Autonomous trading started",
                "config": self.config
            }
        except Exception as e:
            logger.exception(f"Exception starting autonomous trading: {str(e)}")
            self.running = False
            return {
                "success": False,
                "message": f"Exception starting autonomous trading: {str(e)}"
            }
    
    def stop(self) -> Dict[str, Any]:
        """
        Stop autonomous trading.
        
        Returns:
            Dict with success status and message
        """
        if not self.running:
            return {
                "success": False,
                "message": "Autonomous trading is not running"
            }
        
        try:
            self.running = False
            if self.thread:
                self.thread.join(timeout=5)
            
            logger.info("Autonomous trading stopped")
            
            return {
                "success": True,
                "message": "Autonomous trading stopped"
            }
        except Exception as e:
            logger.exception(f"Exception stopping autonomous trading: {str(e)}")
            return {
                "success": False,
                "message": f"Exception stopping autonomous trading: {str(e)}"
            }
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get autonomous trading status.
        
        Returns:
            Dict with status information
        """
        return {
            "running": self.running,
            "config": self.config,
            "last_scan_time": self.last_scan_time
        }
    
    def update_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update autonomous trading configuration.
        
        Args:
            config: New configuration
            
        Returns:
            Dict with success status and message
        """
        try:
            # Validate configuration
            if "symbols" not in config or not isinstance(config["symbols"], list):
                return {
                    "success": False,
                    "message": "Invalid symbols configuration"
                }
            
            if "timeframes" not in config or not isinstance(config["timeframes"], list):
                return {
                    "success": False,
                    "message": "Invalid timeframes configuration"
                }
            
            if "scan_interval" not in config or not isinstance(config["scan_interval"], int):
                return {
                    "success": False,
                    "message": "Invalid scan interval configuration"
                }
            
            if "min_confidence" not in config or not isinstance(config["min_confidence"], int):
                return {
                    "success": False,
                    "message": "Invalid minimum confidence configuration"
                }
            
            if "max_trades" not in config or not isinstance(config["max_trades"], int):
                return {
                    "success": False,
                    "message": "Invalid maximum trades configuration"
                }
            
            if "allowed_styles" not in config or not isinstance(config["allowed_styles"], list):
                return {
                    "success": False,
                    "message": "Invalid allowed styles configuration"
                }
            
            # Update configuration
            self.config = config
            self._save_config()
            
            logger.info(f"Autonomous trading configuration updated: {config}")
            
            return {
                "success": True,
                "message": "Configuration updated successfully",
                "config": self.config
            }
        except Exception as e:
            logger.exception(f"Exception updating configuration: {str(e)}")
            return {
                "success": False,
                "message": f"Exception updating configuration: {str(e)}"
            }
    
    def _trading_loop(self):
        """
        Main autonomous trading loop.
        """
        logger.info("Autonomous trading loop started")
        
        while self.running:
            try:
                # Check connection
                if not self.mt5.check_connection()["connected"]:
                    logger.error("MT5 connection lost")
                    time.sleep(30)  # Wait before retrying
                    continue
                
                # Get account info
                account_info_result = self.mt5.get_account_info()
                if not account_info_result["success"]:
                    logger.error(f"Failed to get account info: {account_info_result['message']}")
                    time.sleep(30)  # Wait before retrying
                    continue
                
                account_info = account_info_result["account_info"]
                
                # Get open positions
                positions_result = self.mt5.get_open_positions()
                if not positions_result["success"]:
                    logger.error(f"Failed to get open positions: {positions_result['message']}")
                    time.sleep(30)  # Wait before retrying
                    continue
                
                open_positions = positions_result["positions"]
                
                # Check if maximum trades limit is reached
                if len(open_positions) >= self.config["max_trades"]:
                    logger.info(f"Maximum trades limit reached ({len(open_positions)}/{self.config['max_trades']})")
                    time.sleep(60)  # Wait before checking again
                    continue
                
                # Scan symbols
                for symbol in self.config["symbols"]:
                    # Check if it's time to scan this symbol
                    current_time = time.time()
                    last_scan = self.last_scan_time.get(symbol, 0)
                    
                    if current_time - last_scan < self.config["scan_interval"]:
                        continue  # Skip this symbol if it was scanned recently
                    
                    # Update last scan time
                    self.last_scan_time[symbol] = current_time
                    
                    # Check if we already have a position for this symbol
                    symbol_positions = [p for p in open_positions if p["symbol"] == symbol]
                    if symbol_positions and not self.config.get("allow_multiple_positions", False):
                        logger.info(f"Already have a position for {symbol}, skipping")
                        continue
                    
                    # Generate recommendations
                    recommendations_result = self.recommendation.generate_recommendations(
                        symbol, self.config["timeframes"], account_info
                    )
                    
                    if not recommendations_result["success"]:
                        logger.error(f"Failed to generate recommendations for {symbol}: {recommendations_result['message']}")
                        continue
                    
                    recommendations = recommendations_result["recommendations"]
                    
                    # Filter recommendations based on criteria
                    valid_recommendations = [
                        r for r in recommendations 
                        if r["confidence"] >= self.config["min_confidence"] 
                        and r["style"] in self.config["allowed_styles"]
                    ]
                    
                    if not valid_recommendations:
                        logger.info(f"No valid recommendations for {symbol}")
                        continue
                    
                    # Execute the highest confidence recommendation
                    best_recommendation = valid_recommendations[0]  # Already sorted by confidence
                    
                    # Execute trade
                    result = self._execute_recommendation(best_recommendation)
                    
                    if result["success"]:
                        logger.info(f"Successfully executed trade for {symbol}: {result['message']}")
                    else:
                        logger.error(f"Failed to execute trade for {symbol}: {result['message']}")
                
                # Sleep before next scan
                time.sleep(10)
            
            except Exception as e:
                logger.exception(f"Exception in autonomous trading loop: {str(e)}")
                time.sleep(60)  # Wait before retrying
    
    def _execute_recommendation(self, recommendation: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a trade recommendation
        
        Args:
            recommendation: Dictionary with recommendation details
            
        Returns:
            Dict with execution results
        """
        if not recommendation:
            return {
                "success": False,
                "message": "No recommendation provided"
            }
        
        symbol = recommendation.get("symbol")
        order_type = mt5.ORDER_TYPE_BUY if recommendation.get("type") == "buy" else mt5.ORDER_TYPE_SELL
        volume = recommendation.get("volume", 0.01)
        entry_price = recommendation.get("entry")
        sl = recommendation.get("sl")
        tp = recommendation.get("tp")
        magic = recommendation.get("magic", self.magic_number)
        strategy_name = recommendation.get("strategy", "unknown")
        confidence = recommendation.get("confidence", 0)
        
        logger.info(f"Executing trade: {recommendation.get('type').upper()} {symbol} "
                    f"at {entry_price}, SL: {sl}, TP: {tp}, volume: {volume}, "
                    f"strategy: {strategy_name}, confidence: {confidence}%")
        
        # Prepare request
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": volume,
            "type": order_type,
            "price": entry_price,
            "sl": sl,
            "tp": tp,
            "magic": magic,
            "comment": f"Auto:{strategy_name}:{confidence}%",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }
        
        # Send order
        try:
            result = mt5.order_send(request)
            if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                logger.info(f"Trade executed successfully. Order #{result.order}, "
                           f"Volume: {volume}, Price: {result.price}")
                
                # Log trade details for monitoring
                self._log_trade(recommendation, {
                    "order": result.order,
                    "volume": volume,
                    "price": result.price,
                    "time": datetime.now().isoformat()
                })
                
                return {
                    "success": True,
                    "message": "Trade executed successfully",
                    "order": result.order,
                    "volume": volume,
                    "price": result.price
                }
            else:
                error_code = result.retcode if result else "Unknown"
                error_message = f"Failed to execute trade. Error code: {error_code}"
                logger.error(f"{error_message}. Symbol: {symbol}, Type: {recommendation.get('type')}, "
                            f"Price: {entry_price}, Volume: {volume}")
                return {
                    "success": False,
                    "message": error_message,
                    "error_code": error_code
                }
        except Exception as e:
            error_message = f"Exception executing trade: {str(e)}"
            logger.exception(f"{error_message}. Symbol: {symbol}, Type: {recommendation.get('type')}")
            return {
                "success": False,
                "message": error_message
            }
    
    def _log_trade(self, recommendation: Dict[str, Any], order: Dict[str, Any]):
        """
        Log a trade to file.
        
        Args:
            recommendation: Trade recommendation
            order: Order result
        """
        try:
            log_entry = {
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "recommendation": recommendation,
                "order": order
            }
            
            # Ensure log directory exists
            os.makedirs("logs", exist_ok=True)
            
            # Append to log file
            with open("logs/autonomous_trades.json", "a") as f:
                f.write(json.dumps(log_entry) + "\n")
        except Exception as e:
            logger.exception(f"Exception logging trade: {str(e)}")
    
    def _load_config(self) -> Dict[str, Any]:
        """
        Load autonomous trading configuration from file.
        
        Returns:
            Dict with configuration
        """
        try:
            if os.path.exists("autonomous_config.json"):
                with open("autonomous_config.json", "r") as f:
                    return json.load(f)
            
            # Default configuration
            return {
                "symbols": ["EURUSD", "GBPUSD", "USDJPY", "XAUUSD"],
                "timeframes": ["M15", "H1", "H4"],
                "scan_interval": 300,  # 5 minutes
                "min_confidence": 70,
                "max_trades": 5,
                "allowed_styles": ["Scalping", "Short-term", "Swing"],
                "allow_multiple_positions": False
            }
        except Exception as e:
            logger.exception(f"Exception loading configuration: {str(e)}")
            
            # Fallback to default configuration
            return {
                "symbols": ["EURUSD", "GBPUSD", "USDJPY", "XAUUSD"],
                "timeframes": ["M15", "H1", "H4"],
                "scan_interval": 300,  # 5 minutes
                "min_confidence": 70,
                "max_trades": 5,
                "allowed_styles": ["Scalping", "Short-term", "Swing"],
                "allow_multiple_positions": False
            }
    
    def _save_config(self):
        """
        Save autonomous trading configuration to file.
        """
        try:
            with open("autonomous_config.json", "w") as f:
                json.dump(self.config, f, indent=4)
        except Exception as e:
            logger.exception(f"Exception saving configuration: {str(e)}")

    def execute_strategy(self, strategy_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a specific trading strategy based on configuration and produce trade recommendations
        
        Returns dict with recommendation details and success status
        """
        logger.info(f"Executing strategy: {strategy_config.get('name', 'Unnamed')} on {strategy_config.get('symbol')}")
        
        try:
            if not self.mt5.check_connection()["connected"]:
                logger.error("MT5 not connected when trying to execute strategy")
                return {
                    "success": False,
                    "error": "MT5 not connected",
                    "recommendation": None
                }
                
            strategy_id = strategy_config.get("id")
            if not strategy_id:
                logger.error("Strategy missing ID")
                return {
                    "success": False,
                    "error": "Strategy configuration missing ID",
                    "recommendation": None
                }
                
            symbol = strategy_config.get("symbol")
            if not symbol:
                logger.error(f"Symbol missing in strategy {strategy_id}")
                return {
                    "success": False,
                    "error": "Symbol not specified",
                    "recommendation": None
                }
            
            # Check if we're already at the maximum allowed open trades
            max_trades = strategy_config.get("max_open_trades", 3)
            magic_number = strategy_config.get("magic", 12345)
            
            # Count existing positions for this strategy
            positions = mt5.positions_get(magic=magic_number)
            existing_count = len(positions) if positions is not None else 0
            
            if existing_count >= max_trades:
                logger.info(f"Strategy {strategy_id} already has {existing_count} positions, maximum {max_trades} allowed")
                return {
                    "success": True,
                    "recommendation": None,
                    "message": f"Maximum number of trades ({max_trades}) already reached"
                }
                
            # Determine trade direction based on strategy and analysis
            recommendation_type = strategy_config.get("strategy_type", "trend_following").lower()
            
            # Get timeframe from config
            timeframe_str = strategy_config.get("timeframe", "H1")
            timeframe = self.string_to_timeframe(timeframe_str)
            
            logger.info(f"Analyzing {symbol} on {timeframe_str} timeframe using {recommendation_type} strategy")
            
            # Check for rate limits to avoid MT5 server disconnection
            if self.check_rate_limit():
                # Fetch and analyze market data
                try:
                    # Get relevant bars for this symbol and timeframe
                    bars = self.mt5.get_bars(symbol, timeframe, 200)  # Get 200 bars for analysis
                    
                    if bars is None or len(bars) < 50:  # Need at least 50 bars for reliable analysis
                        logger.warning(f"Insufficient data for {symbol} on {timeframe_str}, got {len(bars) if bars else 0} bars")
                        return {
                            "success": False,
                            "error": f"Insufficient price data for {symbol} on {timeframe_str}",
                            "recommendation": None
                        }
                        
                    # Calculate indicators based on strategy type
                    if recommendation_type == "trend_following":
                        recommendation, confidence, details = self._analyze_trend(bars, symbol)
                    elif recommendation_type == "mean_reversion":
                        recommendation, confidence, details = self._analyze_mean_reversion(bars, symbol)
                    elif recommendation_type == "breakout":
                        recommendation, confidence, details = self._analyze_breakout(bars, symbol)
                    else:
                        # Default to trend analysis
                        recommendation, confidence, details = self._analyze_trend(bars, symbol)
                    
                    min_confidence = strategy_config.get("min_confidence", 65)
                    
                    if confidence < min_confidence:
                        logger.info(f"Signal for {symbol} has confidence {confidence}%, below threshold of {min_confidence}%")
                        return {
                            "success": True,
                            "recommendation": None,
                            "message": f"Signal confidence ({confidence}%) below threshold ({min_confidence}%)"
                        }
                    
                    risk_percent = strategy_config.get("risk_per_trade", 1.0)
                    stop_loss_pips = strategy_config.get("stop_loss_pips", 50)
                    take_profit_pips = strategy_config.get("take_profit_pips", 100)
                    
                    # Get current account info for position sizing
                    account_info = mt5.get_account_info()
                    if not account_info:
                        logger.error("Failed to get account info from MT5")
                        return {
                            "success": False,
                            "error": "Failed to get account info",
                            "recommendation": None
                        }
                    
                    # Position sizing based on risk management
                    current_price = bars[-1]["close"]
                    point_value = mt5.symbol_info(symbol).point
                    
                    # Calculate position size based on risk
                    equity = account_info.equity
                    risk_amount = equity * (risk_percent / 100)
                    
                    if recommendation == "buy":
                        sl_price = current_price - (stop_loss_pips * point_value * 10)  # Convert pips to price
                        tp_price = current_price + (take_profit_pips * point_value * 10)
                    else:  # sell
                        sl_price = current_price + (stop_loss_pips * point_value * 10)
                        tp_price = current_price - (take_profit_pips * point_value * 10)
                    
                    price_risk = abs(current_price - sl_price)
                    volume = round(risk_amount / price_risk, 2)  # Round to 2 decimal places
                    
                    # Ensure minimum volume
                    min_volume = mt5.symbol_info(symbol).volume_min
                    if volume < min_volume:
                        volume = min_volume
                    
                    # Log the trade decision and key parameters
                    logger.info(f"Trade signal generated: {recommendation.upper()} {symbol} at {current_price}, "
                                f"SL: {sl_price}, TP: {tp_price}, Volume: {volume}, Confidence: {confidence}%")
                    
                    # Prepare order
                    order_type = mt5.ORDER_TYPE_BUY if recommendation == "buy" else mt5.ORDER_TYPE_SELL
                    
                    # Prepare recommendation object
                    trade_recommendation = {
                        "symbol": symbol,
                        "type": recommendation,
                        "entry": current_price,
                        "sl": sl_price,
                        "tp": tp_price,
                        "volume": volume,
                        "confidence": confidence,
                        "magic": magic_number,
                        "timeframe": timeframe_str,
                        "strategy": strategy_id,
                        "timestamp": datetime.now().isoformat(),
                        "details": details
                    }
                    
                    return {
                        "success": True,
                        "recommendation": trade_recommendation
                    }
                    
                except Exception as e:
                    logger.exception(f"Error analyzing {symbol}: {str(e)}")
                    return {
                        "success": False,
                        "error": f"Analysis error: {str(e)}",
                        "recommendation": None
                    }
            else:
                logger.warning("Rate limit reached, skipping strategy execution")
                return {
                    "success": False,
                    "error": "Rate limit reached",
                    "recommendation": None
                }
        
        except Exception as e:
            logger.exception(f"Unexpected error in execute_strategy: {str(e)}")
            return {
                "success": False,
                "error": f"Strategy execution error: {str(e)}",
                "recommendation": None
            }
