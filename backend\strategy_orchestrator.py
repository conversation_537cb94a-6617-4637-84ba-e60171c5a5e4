"""
Intelligent Multi-Strategy Orchestrator

This module handles prioritization and coordination of multiple autonomous trading strategies
running simultaneously. It analyzes market conditions, strategy performance, and signal quality
to make intelligent decisions about which strategies to execute.
"""

import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import numpy as np

logger = logging.getLogger(__name__)

class MarketCondition(Enum):
    """Market condition types for strategy matching"""
    TRENDING_STRONG = "trending_strong"
    TRENDING_WEAK = "trending_weak"
    RANGING = "ranging"
    VOLATILE = "volatile"
    BREAKOUT = "breakout"
    REVERSAL = "reversal"

class StrategyType(Enum):
    """Strategy types with their optimal market conditions"""
    SCALPING = "scalping_momentum"
    INTRADAY = "intraday_trend"
    SWING = "swing_reversal"
    POSITION = "position_trading"

@dataclass
class StrategySignal:
    """Container for strategy signals with metadata"""
    strategy_id: str
    strategy_name: str
    strategy_type: StrategyType
    symbol: str
    timeframe: str
    signal_type: str  # BUY/SELL
    confidence: float
    risk_amount: float
    timestamp: float
    market_condition: MarketCondition
    performance_score: float = 0.0
    priority_score: float = 0.0

@dataclass
class StrategyPerformance:
    """Track strategy performance metrics"""
    strategy_id: str
    total_trades: int = 0
    winning_trades: int = 0
    total_profit: float = 0.0
    recent_trades: List[Dict] = None
    win_rate: float = 0.0
    avg_profit: float = 0.0
    recent_performance: float = 0.0
    
    def __post_init__(self):
        if self.recent_trades is None:
            self.recent_trades = []

class StrategyOrchestrator:
    """
    Intelligent orchestrator for multiple autonomous trading strategies
    """
    
    def __init__(self):
        self.strategy_performance: Dict[str, StrategyPerformance] = {}
        self.market_condition_cache: Dict[str, Tuple[MarketCondition, float]] = {}
        self.signal_history: List[StrategySignal] = []
        
        # Strategy-to-market condition mapping
        self.strategy_market_affinity = {
            StrategyType.SCALPING: [MarketCondition.RANGING, MarketCondition.VOLATILE],
            StrategyType.INTRADAY: [MarketCondition.TRENDING_WEAK, MarketCondition.TRENDING_STRONG],
            StrategyType.SWING: [MarketCondition.REVERSAL, MarketCondition.BREAKOUT],
            StrategyType.POSITION: [MarketCondition.TRENDING_STRONG, MarketCondition.BREAKOUT]
        }
        
        # Prioritization weights
        self.weights = {
            'market_match': 0.35,      # How well strategy matches market condition
            'signal_confidence': 0.25,  # Signal confidence level
            'recent_performance': 0.20, # Recent strategy performance
            'risk_optimization': 0.15,  # Risk balance across strategies
            'timeframe_priority': 0.05  # Timeframe-based priority
        }

    def analyze_market_condition(self, symbol: str, price_data: Dict, indicators: Dict) -> MarketCondition:
        """
        Analyze current market condition for a symbol
        """
        try:
            # Check cache first (valid for 5 minutes)
            cache_key = f"{symbol}"
            if cache_key in self.market_condition_cache:
                condition, timestamp = self.market_condition_cache[cache_key]
                if time.time() - timestamp < 300:  # 5 minutes
                    return condition
            
            # Extract key indicators
            atr = indicators.get('atr', {}).get('value', 0)
            rsi = indicators.get('rsi', {}).get('value', 50)
            macd = indicators.get('macd', {})
            bb = indicators.get('bollinger_bands', {})
            
            # Volatility analysis
            volatility_high = atr > indicators.get('atr_avg', atr * 1.2)
            
            # Trend analysis
            macd_line = macd.get('macd', 0)
            macd_signal = macd.get('signal', 0)
            macd_histogram = macd.get('histogram', 0)
            
            trend_strength = abs(macd_histogram)
            trend_direction = 1 if macd_line > macd_signal else -1
            
            # Range analysis
            bb_upper = bb.get('upper', 0)
            bb_lower = bb.get('lower', 0)
            bb_middle = bb.get('middle', 0)
            current_price = price_data.get('bid', price_data.get('close', 0))
            
            # Determine market condition
            if volatility_high and trend_strength > 0.5:
                if abs(rsi - 50) > 20:  # Extreme RSI
                    condition = MarketCondition.BREAKOUT
                else:
                    condition = MarketCondition.VOLATILE
            elif trend_strength > 0.3:
                if trend_strength > 0.6:
                    condition = MarketCondition.TRENDING_STRONG
                else:
                    condition = MarketCondition.TRENDING_WEAK
            elif (rsi > 70 or rsi < 30) and trend_strength < 0.2:
                condition = MarketCondition.REVERSAL
            else:
                condition = MarketCondition.RANGING
            
            # Cache the result
            self.market_condition_cache[cache_key] = (condition, time.time())
            
            logger.info(f"Market condition for {symbol}: {condition.value}")
            return condition
            
        except Exception as e:
            logger.error(f"Error analyzing market condition for {symbol}: {str(e)}")
            return MarketCondition.RANGING  # Default fallback

    def calculate_strategy_priority(self, signal: StrategySignal, 
                                  active_strategies: List[Dict],
                                  current_risk_exposure: float,
                                  max_total_risk: float) -> float:
        """
        Calculate priority score for a strategy signal
        """
        try:
            priority_score = 0.0
            
            # 1. Market condition match (35% weight)
            strategy_type = StrategyType(signal.strategy_type)
            optimal_conditions = self.strategy_market_affinity.get(strategy_type, [])
            
            if signal.market_condition in optimal_conditions:
                market_match_score = 1.0
            elif len(optimal_conditions) > 1 and signal.market_condition in [
                MarketCondition.TRENDING_WEAK, MarketCondition.VOLATILE
            ]:
                market_match_score = 0.7  # Partial match
            else:
                market_match_score = 0.3  # Poor match
            
            priority_score += market_match_score * self.weights['market_match']
            
            # 2. Signal confidence (25% weight)
            confidence_score = signal.confidence / 100.0
            priority_score += confidence_score * self.weights['signal_confidence']
            
            # 3. Recent performance (20% weight)
            performance = self.strategy_performance.get(signal.strategy_id)
            if performance and performance.total_trades > 0:
                performance_score = min(1.0, max(0.0, performance.recent_performance))
            else:
                performance_score = 0.5  # Neutral for new strategies
            
            priority_score += performance_score * self.weights['recent_performance']
            
            # 4. Risk optimization (15% weight)
            remaining_risk = max_total_risk - current_risk_exposure
            risk_ratio = signal.risk_amount / max(remaining_risk, signal.risk_amount)
            risk_score = max(0.0, 1.0 - risk_ratio)  # Higher score for lower risk usage
            
            priority_score += risk_score * self.weights['risk_optimization']
            
            # 5. Timeframe priority (5% weight)
            timeframe_priority = {
                'M1': 0.9, 'M5': 0.8, 'M15': 0.7, 'M30': 0.6,
                'H1': 0.5, 'H4': 0.4, 'D1': 0.3, 'W1': 0.2, 'MN1': 0.1
            }
            tf_score = timeframe_priority.get(signal.timeframe, 0.5)
            priority_score += tf_score * self.weights['timeframe_priority']
            
            signal.priority_score = priority_score
            
            logger.info(f"Priority calculated for {signal.strategy_name} ({signal.symbol}): "
                       f"{priority_score:.3f} (market:{market_match_score:.2f}, "
                       f"conf:{confidence_score:.2f}, perf:{performance_score:.2f}, "
                       f"risk:{risk_score:.2f})")
            
            return priority_score
            
        except Exception as e:
            logger.error(f"Error calculating priority for {signal.strategy_name}: {str(e)}")
            return 0.0

    def resolve_signal_conflicts(self, signals: List[StrategySignal]) -> List[StrategySignal]:
        """
        Resolve conflicts between opposing signals for the same symbol
        """
        try:
            # Group signals by symbol
            symbol_groups = {}
            for signal in signals:
                if signal.symbol not in symbol_groups:
                    symbol_groups[signal.symbol] = []
                symbol_groups[signal.symbol].append(signal)
            
            resolved_signals = []
            
            for symbol, symbol_signals in symbol_groups.items():
                if len(symbol_signals) == 1:
                    resolved_signals.extend(symbol_signals)
                    continue
                
                # Check for opposing signals
                buy_signals = [s for s in symbol_signals if s.signal_type.upper() == 'BUY']
                sell_signals = [s for s in symbol_signals if s.signal_type.upper() == 'SELL']
                
                if buy_signals and sell_signals:
                    # Conflict detected - choose highest priority
                    all_conflicting = buy_signals + sell_signals
                    all_conflicting.sort(key=lambda x: x.priority_score, reverse=True)
                    
                    winner = all_conflicting[0]
                    logger.warning(f"Signal conflict resolved for {symbol}: "
                                 f"Chose {winner.strategy_name} ({winner.signal_type}) "
                                 f"with priority {winner.priority_score:.3f}")
                    
                    resolved_signals.append(winner)
                else:
                    # No conflict - add all signals
                    resolved_signals.extend(symbol_signals)
            
            return resolved_signals
            
        except Exception as e:
            logger.error(f"Error resolving signal conflicts: {str(e)}")
            return signals

    def prioritize_strategies(self, strategy_signals: List[Dict],
                            active_strategies: List[Dict],
                            current_risk_exposure: float,
                            max_total_risk: float,
                            market_data: Dict) -> List[StrategySignal]:
        """
        Main method to prioritize and orchestrate multiple strategy signals
        """
        try:
            if not strategy_signals:
                return []
            
            # Convert to StrategySignal objects
            processed_signals = []
            
            for signal_data in strategy_signals:
                try:
                    # Determine strategy type
                    strategy_name = signal_data.get('strategy', '').lower()
                    if 'scalping' in strategy_name:
                        strategy_type = StrategyType.SCALPING
                    elif 'intraday' in strategy_name:
                        strategy_type = StrategyType.INTRADAY
                    elif 'swing' in strategy_name:
                        strategy_type = StrategyType.SWING
                    elif 'position' in strategy_name:
                        strategy_type = StrategyType.POSITION
                    else:
                        strategy_type = StrategyType.INTRADAY  # Default
                    
                    # Analyze market condition
                    symbol = signal_data.get('symbol', '')
                    price_data = market_data.get(symbol, {})
                    indicators = price_data.get('indicators', {})
                    market_condition = self.analyze_market_condition(symbol, price_data, indicators)
                    
                    # Create signal object
                    signal = StrategySignal(
                        strategy_id=signal_data.get('strategy_id', ''),
                        strategy_name=signal_data.get('strategy', ''),
                        strategy_type=strategy_type,
                        symbol=symbol,
                        timeframe=signal_data.get('timeframe', 'H1'),
                        signal_type=signal_data.get('signalType', ''),
                        confidence=signal_data.get('confidence', 0),
                        risk_amount=signal_data.get('risk_amount', 0),
                        timestamp=time.time(),
                        market_condition=market_condition
                    )
                    
                    # Calculate priority
                    priority = self.calculate_strategy_priority(
                        signal, active_strategies, current_risk_exposure, max_total_risk
                    )
                    signal.priority_score = priority
                    
                    processed_signals.append(signal)
                    
                except Exception as e:
                    logger.error(f"Error processing signal: {str(e)}")
                    continue
            
            # Resolve conflicts
            resolved_signals = self.resolve_signal_conflicts(processed_signals)
            
            # Sort by priority
            resolved_signals.sort(key=lambda x: x.priority_score, reverse=True)
            
            # Log prioritization results
            logger.info("Strategy prioritization results:")
            for i, signal in enumerate(resolved_signals[:5]):  # Top 5
                logger.info(f"  {i+1}. {signal.strategy_name} ({signal.symbol}) - "
                           f"Priority: {signal.priority_score:.3f}, "
                           f"Confidence: {signal.confidence}%, "
                           f"Market: {signal.market_condition.value}")
            
            return resolved_signals
            
        except Exception as e:
            logger.error(f"Error in strategy prioritization: {str(e)}")
            return []

    def update_strategy_performance(self, strategy_id: str, trade_result: Dict):
        """
        Update strategy performance metrics after trade completion
        """
        try:
            if strategy_id not in self.strategy_performance:
                self.strategy_performance[strategy_id] = StrategyPerformance(strategy_id)
            
            perf = self.strategy_performance[strategy_id]
            
            # Update trade statistics
            perf.total_trades += 1
            profit = trade_result.get('profit', 0)
            perf.total_profit += profit
            
            if profit > 0:
                perf.winning_trades += 1
            
            # Update recent trades (keep last 10)
            perf.recent_trades.append({
                'timestamp': time.time(),
                'profit': profit,
                'success': profit > 0
            })
            
            if len(perf.recent_trades) > 10:
                perf.recent_trades = perf.recent_trades[-10:]
            
            # Calculate metrics
            perf.win_rate = perf.winning_trades / perf.total_trades
            perf.avg_profit = perf.total_profit / perf.total_trades
            
            # Calculate recent performance (last 5 trades)
            recent_5 = perf.recent_trades[-5:]
            if recent_5:
                recent_profits = [t['profit'] for t in recent_5]
                recent_wins = sum(1 for p in recent_profits if p > 0)
                perf.recent_performance = (recent_wins / len(recent_5)) * 0.6 + \
                                        (sum(recent_profits) / len(recent_profits) / 100) * 0.4
            
            logger.info(f"Updated performance for {strategy_id}: "
                       f"Win rate: {perf.win_rate:.2%}, "
                       f"Avg profit: {perf.avg_profit:.2f}, "
                       f"Recent performance: {perf.recent_performance:.3f}")
            
        except Exception as e:
            logger.error(f"Error updating strategy performance: {str(e)}")

    def get_strategy_recommendations(self, market_data: Dict) -> Dict[str, List[str]]:
        """
        Get strategy recommendations based on current market conditions
        """
        try:
            recommendations = {}
            
            for symbol, data in market_data.items():
                indicators = data.get('indicators', {})
                market_condition = self.analyze_market_condition(symbol, data, indicators)
                
                # Find optimal strategies for this market condition
                optimal_strategies = []
                for strategy_type, conditions in self.strategy_market_affinity.items():
                    if market_condition in conditions:
                        optimal_strategies.append(strategy_type.value)
                
                recommendations[symbol] = {
                    'market_condition': market_condition.value,
                    'optimal_strategies': optimal_strategies,
                    'confidence': 'high' if len(optimal_strategies) > 0 else 'low'
                }
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating strategy recommendations: {str(e)}")
            return {}
