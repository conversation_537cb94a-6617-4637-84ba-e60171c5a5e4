# API Documentation

## Backend API Endpoints

### MT5 Connection
```
GET /api/connection/status
POST /api/connection/connect
POST /api/connection/disconnect
```

### Market Data
```
GET /api/market/symbols
GET /api/market/price/{symbol}
GET /api/market/ohlc/{symbol}/{timeframe}
```

### Trading Operations
```
POST /api/trade/order
GET /api/trade/positions
POST /api/trade/close/{ticket}
POST /api/trade/modify/{ticket}
```

### Analysis
```
GET /api/analysis/{symbol}/{timeframe}
GET /api/analysis/indicators/{symbol}
GET /api/analysis/patterns/{symbol}
```

### Recommendations
```
GET /api/recommendations/{symbol}
GET /api/recommendations/active
POST /api/recommendations/settings
```

### Autonomous Trading
```
POST /api/auto/start
POST /api/auto/stop
GET /api/auto/status
POST /api/auto/settings
```

## WebSocket Events

### Market Updates
```
market.price.update
market.tick
market.ohlc.update
```

### Trading Events
```
trade.executed
trade.modified
trade.closed
```

### Analysis Events
```
analysis.complete
pattern.detected
signal.generated
```

### System Events
```
connection.status
error.occurred
system.notification
```

## Error Handling

### Error Response Format
```json
{
  "error": {
    "code": "string",
    "message": "string",
    "details": {}
  }
}
```

### Common Error Codes
- `AUTH_ERROR`: Authentication failed
- `CONN_ERROR`: MT5 connection issues
- `MARKET_ERROR`: Market data errors
- `TRADE_ERROR`: Trading operation errors
- `VALIDATION_ERROR`: Input validation errors

## Rate Limits
- Market data: 60 requests/minute
- Trading operations: 30 requests/minute
- Analysis requests: 20 requests/minute

## Authentication
- API Key required in headers
- Session-based authentication for WebSocket
- JWT tokens for secure operations
