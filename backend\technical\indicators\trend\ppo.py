from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class PPOIndicator(BaseIndicator):
    """Percentage Price Oscillator (PPO) indicator."""

    def __init__(self, fast_period: int = 12, slow_period: int = 26,
                 signal_period: int = 9, source: str = 'close'):
        """
        Initialize Percentage Price Oscillator indicator.

        Args:
            fast_period: The period for the fast EMA.
            slow_period: The period for the slow EMA.
            signal_period: The period for the signal line EMA.
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
        """
        super().__init__({
            'fast_period': fast_period,
            'slow_period': slow_period,
            'signal_period': signal_period,
            'source': source
        })

    def _ema(self, series: pd.Series, period: int) -> pd.Series:
        """Helper function for EMA calculation."""
        return series.ewm(span=period, adjust=False).mean()

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Percentage Price Oscillator."""
        df = data.to_dataframe()
        min_len = max(self.params['fast_period'], self.params['slow_period'])
        if df.empty or len(df) < min_len:
             return {'ppo_line': np.array([]), 'signal_line': np.array([]), 'histogram': np.array([])}

        fast_period = self.params['fast_period']
        slow_period = self.params['slow_period']
        signal_period = self.params['signal_period']
        source_col = self.params['source'].lower()

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Calculate EMAs
        fast_ema = self._ema(source_data, fast_period)
        slow_ema = self._ema(source_data, slow_period)

        # Calculate PPO Line
        # Avoid division by zero
        slow_ema_safe = slow_ema.replace(0, np.nan)
        ppo_line = 100 * (fast_ema - slow_ema) / slow_ema_safe
        ppo_line = ppo_line.fillna(0) # Fill NaNs

        # Calculate Signal Line
        signal_line = self._ema(ppo_line, signal_period)

        # Calculate Histogram
        histogram = ppo_line - signal_line

        self._values = {
            'ppo_line': ppo_line.values,
            'signal_line': signal_line.values,
            'histogram': histogram.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['fast_period'] >= self.params['slow_period']:
            raise ValueError("Fast period must be less than slow period")
        if any(p < 1 for p in [self.params['fast_period'], self.params['slow_period'], self.params['signal_period']]):
            raise ValueError("All periods must be greater than 0")
        return True