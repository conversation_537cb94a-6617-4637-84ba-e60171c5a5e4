import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, List, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("SignalGenerator")

def generate_signal_recommendation(analysis_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate trading signal recommendation based on multiple technical indicators
    and market structure analysis.
    
    Args:
        analysis_data: Dictionary containing all technical analysis results
        
    Returns:
        Dictionary containing signal recommendation with entry/exit points
    """
    logger.info("Generating signal recommendation from analysis data")
    
    # Initialize with neutral signal
    signal_recommendation = {
        "signal": "NEUTRAL",
        "confidence": 50,
        "entry_price": None,
        "stop_loss": None,
        "take_profit": None,
        "risk_reward": 0,
        "indicators": {},
        "explanation": "Insufficient data for signal generation."
    }
    
    # Extract current price
    try:
        current_price = None
        if analysis_data.get("current_price") and isinstance(analysis_data["current_price"], dict):
            # Try bid price first, fall back to last
            if analysis_data["current_price"].get("bid") is not None:
                current_price = analysis_data["current_price"]["bid"]
            elif analysis_data["current_price"].get("last") is not None:
                current_price = analysis_data["current_price"]["last"]
        
        # If no current price found, we can't proceed
        if current_price is None:
            logger.warning("No valid current price found in analysis data")
            return signal_recommendation
            
        signal_recommendation["entry_price"] = current_price
        
    except Exception as e:
        logger.error(f"Error extracting current price: {e}")
        return signal_recommendation
    
    # Initialize indicator results
    indicator_signals = {}
    
    # Analyze RSI
    rsi_signal = analyze_rsi(analysis_data.get("rsi", {}))
    if rsi_signal:
        indicator_signals["rsi"] = rsi_signal
    
    # Analyze MACD
    macd_signal = analyze_macd(analysis_data.get("macd", {}))
    if macd_signal:
        indicator_signals["macd"] = macd_signal
    
    # Analyze Moving Averages
    ma_signal = analyze_moving_averages(analysis_data.get("moving_averages", {}))
    if ma_signal:
        indicator_signals["moving_averages"] = ma_signal
    
    # Analyze Support/Resistance
    sr_signal = analyze_support_resistance(analysis_data.get("support_resistance", {}), current_price)
    if sr_signal:
        indicator_signals["support_resistance"] = sr_signal
    
    # Analyze Trend
    trend_signal = analyze_trend(analysis_data.get("trend", {}))
    if trend_signal:
        indicator_signals["trend"] = trend_signal
    
    # No signals could be generated
    if not indicator_signals:
        logger.warning("No indicator signals could be generated")
        return signal_recommendation
    
    # Generate composite signal
    composite_signal = generate_composite_signal(indicator_signals, current_price)
    if not composite_signal:
        logger.warning("Could not generate composite signal")
        return signal_recommendation
    
    # Set stop loss and take profit levels
    if sr_signal and composite_signal.get("signal") != "NEUTRAL":
        stop_loss, take_profit = calculate_stop_loss_take_profit(
            signal=composite_signal.get("signal", "NEUTRAL"),
            current_price=current_price,
            sr_data=analysis_data.get("support_resistance", {}),
            atr_data=analysis_data.get("atr", {})
        )
        
        composite_signal["stop_loss"] = stop_loss
        composite_signal["take_profit"] = take_profit
        
        # Calculate risk/reward ratio
        if stop_loss and take_profit and composite_signal.get("signal").upper() in ["BUY", "STRONG_BUY"]:
            risk = abs(current_price - stop_loss)
            reward = abs(take_profit - current_price)
            if risk > 0:
                composite_signal["risk_reward"] = round(reward / risk, 2)
        elif stop_loss and take_profit and composite_signal.get("signal").upper() in ["SELL", "STRONG_SELL"]:
            risk = abs(stop_loss - current_price)
            reward = abs(current_price - take_profit)
            if risk > 0:
                composite_signal["risk_reward"] = round(reward / risk, 2)
    
    return composite_signal

def analyze_rsi(rsi_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Analyze RSI indicator to determine trading signal
    """
    if not rsi_data or "value" not in rsi_data:
        return None
    
    rsi_value = rsi_data.get("value")
    if not isinstance(rsi_value, (int, float)):
        # Try to get the last value if it's a list or series
        if isinstance(rsi_value, list) and rsi_value:
            rsi_value = rsi_value[-1]
        else:
            return None
    
    signal = None
    strength = 0
    
    if rsi_value < 30:
        signal = "OVERSOLD"
        strength = 30 - rsi_value  # 0-30 range, higher = stronger signal
    elif rsi_value > 70:
        signal = "OVERBOUGHT"
        strength = rsi_value - 70  # 0-30 range, higher = stronger signal
    elif rsi_value < 45:
        signal = "BULLISH"
        strength = 45 - rsi_value  # 0-15 range, higher = stronger signal
    elif rsi_value > 55:
        signal = "BEARISH"
        strength = rsi_value - 55  # 0-15 range, higher = stronger signal
    else:
        signal = "NEUTRAL"
        strength = 0
    
    return {
        "signal": signal,
        "value": rsi_value,
        "strength": min(10, strength)  # Cap strength at 10
    }

def analyze_macd(macd_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Analyze MACD indicator to determine trading signal
    """
    if not macd_data:
        return None
    
    macd_value = macd_data.get("macd")
    signal_value = macd_data.get("signal")
    histogram = macd_data.get("histogram")
    
    if macd_value is None or signal_value is None or histogram is None:
        return None
    
    # If values are lists or series, get the last two values to detect crossovers
    if isinstance(histogram, list) and len(histogram) >= 2:
        current_hist = histogram[-1]
        prev_hist = histogram[-2]
        
        # Detect crossover conditions
        if current_hist > 0 and prev_hist <= 0:
            return {"signal": "BULLISH_CROSSOVER", "strength": 10}
        elif current_hist < 0 and prev_hist >= 0:
            return {"signal": "BEARISH_CROSSOVER", "strength": 10}
        elif current_hist > 0:
            return {"signal": "BULLISH", "strength": min(10, abs(current_hist * 20))}
        elif current_hist < 0:
            return {"signal": "BEARISH", "strength": min(10, abs(current_hist * 20))}
    
    # Single value analysis
    if isinstance(histogram, (int, float)):
        if histogram > 0 and macd_value > signal_value:
            return {"signal": "BULLISH", "strength": min(10, abs(histogram * 20))}
        elif histogram < 0 and macd_value < signal_value:
            return {"signal": "BEARISH", "strength": min(10, abs(histogram * 20))}
    
    return {"signal": "NEUTRAL", "strength": 0}

def analyze_moving_averages(ma_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Analyze moving averages to determine trading signal
    """
    if not ma_data or "ema_signals" not in ma_data:
        return None
    
    ema_signals = ma_data.get("ema_signals", {})
    sma_signals = ma_data.get("sma_signals", {})
    
    # Count bullish and bearish signals
    bullish_count = 0
    bearish_count = 0
    total_count = 0
    
    # Process EMA signals
    for period, signal in ema_signals.items():
        if signal == "BUY":
            bullish_count += 1
        elif signal == "SELL":
            bearish_count += 1
        total_count += 1
    
    # Process SMA signals
    for period, signal in sma_signals.items():
        if signal == "BUY":
            bullish_count += 1
        elif signal == "SELL":
            bearish_count += 1
        total_count += 1
    
    if total_count == 0:
        return None
    
    # Calculate signal strength (0-10)
    bullish_strength = (bullish_count / total_count) * 10
    bearish_strength = (bearish_count / total_count) * 10
    
    if bullish_count > bearish_count:
        return {"signal": "BULLISH", "strength": bullish_strength}
    elif bearish_count > bullish_count:
        return {"signal": "BEARISH", "strength": bearish_strength}
    else:
        return {"signal": "NEUTRAL", "strength": 0}

def analyze_support_resistance(sr_data: Dict[str, Any], current_price: float) -> Optional[Dict[str, Any]]:
    """
    Analyze support and resistance levels to determine trading signal
    """
    if not sr_data or not isinstance(current_price, (int, float)):
        return None
    
    support_levels = sr_data.get("support_levels", [])
    resistance_levels = sr_data.get("resistance_levels", [])
    
    if not support_levels and not resistance_levels:
        return None
    
    # Sort levels by price
    sorted_supports = sorted([(level.get("price", 0), level.get("strength", 0)) 
                             for level in support_levels if level.get("price") is not None], 
                            key=lambda x: x[0])
    
    sorted_resistances = sorted([(level.get("price", 0), level.get("strength", 0)) 
                               for level in resistance_levels if level.get("price") is not None], 
                              key=lambda x: x[0])
    
    # Find nearest support and resistance
    nearest_support = None
    nearest_resistance = None
    support_strength = 0
    resistance_strength = 0
    
    # Find nearest support below current price
    for price, strength in reversed(sorted_supports):
        if price < current_price:
            nearest_support = price
            support_strength = strength
            break
    
    # Find nearest resistance above current price
    for price, strength in sorted_resistances:
        if price > current_price:
            nearest_resistance = price
            resistance_strength = strength
            break
    
    if nearest_support is None and nearest_resistance is None:
        return None
    
    # Calculate distance to nearest support and resistance as a percentage
    support_distance = None
    resistance_distance = None
    
    if nearest_support is not None:
        support_distance = (current_price - nearest_support) / current_price * 100
    
    if nearest_resistance is not None:
        resistance_distance = (nearest_resistance - current_price) / current_price * 100
    
    # Determine signal based on proximity and strength
    signal = None
    strength = 0
    
    if support_distance is not None and resistance_distance is not None:
        # We have both support and resistance
        if support_distance < 0.5 and support_strength >= 5:
            signal = "NEAR_SUPPORT"
            strength = min(10, (0.5 - support_distance) * 20) * (support_strength / 10)
        elif resistance_distance < 0.5 and resistance_strength >= 5:
            signal = "NEAR_RESISTANCE"
            strength = min(10, (0.5 - resistance_distance) * 20) * (resistance_strength / 10)
        elif support_distance < resistance_distance:
            signal = "CLOSER_TO_SUPPORT"
            strength = min(5, (resistance_distance - support_distance))
        else:
            signal = "CLOSER_TO_RESISTANCE"
            strength = min(5, (support_distance - resistance_distance))
    elif support_distance is not None:
        # Only have support
        if support_distance < 0.5 and support_strength >= 5:
            signal = "NEAR_SUPPORT"
            strength = min(10, (0.5 - support_distance) * 20) * (support_strength / 10)
        else:
            signal = "ABOVE_SUPPORT"
            strength = min(3, 3 - support_distance if support_distance < 3 else 0)
    elif resistance_distance is not None:
        # Only have resistance
        if resistance_distance < 0.5 and resistance_strength >= 5:
            signal = "NEAR_RESISTANCE"
            strength = min(10, (0.5 - resistance_distance) * 20) * (resistance_strength / 10)
        else:
            signal = "BELOW_RESISTANCE"
            strength = min(3, 3 - resistance_distance if resistance_distance < 3 else 0)
    
    if signal is None:
        return None
    
    return {
        "signal": signal,
        "nearest_support": nearest_support,
        "nearest_resistance": nearest_resistance,
        "support_distance": support_distance,
        "resistance_distance": resistance_distance,
        "strength": strength
    }

def analyze_trend(trend_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Analyze trend indicator to determine trading signal
    """
    if not trend_data or "overall" not in trend_data:
        return None
    
    overall = trend_data.get("overall", "").upper()
    strength = trend_data.get("strength", 0)
    
    if not overall or not isinstance(strength, (int, float)):
        return None
    
    signal = None
    if "BULL" in overall:
        signal = "BULLISH"
    elif "BEAR" in overall:
        signal = "BEARISH"
    else:
        signal = "NEUTRAL"
    
    return {
        "signal": signal,
        "strength": min(10, strength / 10)  # Convert 0-100 to 0-10
    }

def generate_composite_signal(
    indicator_signals: Dict[str, Dict[str, Any]],
    current_price: float
) -> Dict[str, Any]:
    """
    Generate a composite trading signal from individual indicator signals
    """
    if not indicator_signals or not current_price:
        return None
    
    # Initialize signal weights
    weights = {
        "trend": 0.25,            # Overall trend has high importance
        "support_resistance": 0.25,  # Price structure is critical
        "macd": 0.20,            # MACD for momentum
        "rsi": 0.20,             # RSI for overbought/oversold
        "moving_averages": 0.10   # Moving averages for trend confirmation
    }
    
    # Initialize signal scores
    bullish_score = 0
    bearish_score = 0
    total_weight = 0
    explanation_parts = []
    signals_summary = {}
    
    # Process each indicator
    for indicator, signal_data in indicator_signals.items():
        if not signal_data or "signal" not in signal_data:
            continue
        
        signal = signal_data.get("signal", "")
        strength = signal_data.get("strength", 0)
        weight = weights.get(indicator, 0.1)
        total_weight += weight
        
        signals_summary[indicator] = signal
        
        if "BULL" in signal.upper() or "BUY" in signal.upper() or "OVERSOLD" in signal.upper():
            bullish_score += strength * weight
            explanation_parts.append(f"{indicator.replace('_', ' ').title()} indicates {signal.lower()} conditions")
        elif "BEAR" in signal.upper() or "SELL" in signal.upper() or "OVERBOUGHT" in signal.upper():
            bearish_score += strength * weight
            explanation_parts.append(f"{indicator.replace('_', ' ').title()} indicates {signal.lower()} conditions")
        elif "SUPPORT" in signal.upper():
            bullish_score += strength * weight
            explanation_parts.append(f"Price is near support level")
        elif "RESISTANCE" in signal.upper():
            bearish_score += strength * weight
            explanation_parts.append(f"Price is near resistance level")
    
    # Normalize scores
    if total_weight > 0:
        bullish_score = (bullish_score / total_weight) * 10
        bearish_score = (bearish_score / total_weight) * 10
    
    # Generate composite signal
    composite_signal = {
        "signal": "NEUTRAL",
        "confidence": 50,
        "entry_price": current_price,
        "indicators": signals_summary,
        "explanation": "Market conditions are neutral or conflicting."
    }
    
    # Now using more dynamic thresholds based on max score magnitude
    max_score = max(bullish_score, bearish_score)
    dynamic_threshold = max(1.5, min(3.0, max_score / 4))  # Adaptive threshold based on signal strength
    dynamic_strong_threshold = max(3.5, min(6.0, max_score / 2))  # Adaptive strong threshold
    
    logger.info(f"Signal scores - Bullish: {bullish_score:.2f}, Bearish: {bearish_score:.2f}, Dynamic threshold: {dynamic_threshold:.2f}")
    
    # Calculate score differential and normalized confidence
    score_diff = abs(bullish_score - bearish_score)
    score_ratio = max(0.01, score_diff / (max_score if max_score > 0 else 1))
    
    # Add random micro-variation to prevent identical confidence scores
    # Small random component (±1.5%) ensures even identical market conditions show slight variations
    random_component = np.random.uniform(-1.5, 1.5)
    
    if bullish_score - bearish_score > dynamic_threshold:
        # Bullish signal
        if bullish_score - bearish_score > dynamic_strong_threshold:
            composite_signal["signal"] = "STRONG_BUY"
            # More variable confidence calculation to avoid consistently high values:
            # 1. Lower base value (60-68) varies with bullish score
            # 2. Impact has more variability based on score components
            # 3. Maximum cap varies between 86-95% based on score ratio
            # 4. Larger random component for more variation
            random_component = np.random.uniform(-3.5, 3.5)  # Increased random variation
            base = 60 + min(8, bullish_score / 2.5)
            impact = min(22, 12 * score_ratio + 8 * np.log1p(score_diff / 4))
            
            # Dynamic maximum cap - very strong signals can reach ~95%, others lower
            max_cap = int(86 + 9 * (score_ratio ** 0.7))
            logger.info(f"STRONG_BUY calculation - Base: {base:.2f}, Impact: {impact:.2f}, Max cap: {max_cap}, Random: {random_component:.2f}")
            
            composite_signal["confidence"] = int(min(max_cap, base + impact + random_component))
        else:
            composite_signal["signal"] = "BUY"
            # More variable confidence for buy signals
            base = 55 + min(10, bullish_score / 3)
            impact = min(25, 15 * score_ratio + 7 * np.log1p(score_diff / 3))
            composite_signal["confidence"] = int(min(85, base + impact + random_component))
        
        explanation = "Bullish signals detected: " + ". ".join(explanation_parts)
        composite_signal["explanation"] = explanation
        
    elif bearish_score - bullish_score > dynamic_threshold:
        # Bearish signal
        if bearish_score - bullish_score > dynamic_strong_threshold:
            composite_signal["signal"] = "STRONG_SELL"
            # More variable confidence calculation to avoid consistently high values
            # Using the same approach as STRONG_BUY for consistency
            random_component = np.random.uniform(-3.5, 3.5)  # Increased random variation
            base = 60 + min(8, bearish_score / 2.5)
            impact = min(22, 12 * score_ratio + 8 * np.log1p(score_diff / 4))
            
            # Dynamic maximum cap - very strong signals can reach ~95%, others lower
            max_cap = int(86 + 9 * (score_ratio ** 0.7))
            logger.info(f"STRONG_SELL calculation - Base: {base:.2f}, Impact: {impact:.2f}, Max cap: {max_cap}, Random: {random_component:.2f}")
            
            composite_signal["confidence"] = int(min(max_cap, base + impact + random_component))
        else:
            composite_signal["signal"] = "SELL"
            # More variable confidence for sell signals
            base = 55 + min(10, bearish_score / 3)
            impact = min(25, 15 * score_ratio + 7 * np.log1p(score_diff / 3))
            composite_signal["confidence"] = int(min(85, base + impact + random_component))
        
        explanation = "Bearish signals detected: " + ". ".join(explanation_parts)
        composite_signal["explanation"] = explanation
    else:
        # Neutral signal - now with more variance based on how close the scores are
        base_confidence = 50
        score_closeness = max(0, 10 - score_diff * 3)  # Higher when scores are closer
        variation = min(10, score_diff * 7)  # Higher when there's more difference
        
        if bullish_score > bearish_score:
            direction = 1  # Slightly bullish
        else:
            direction = -1  # Slightly bearish
        
        neutral_confidence = base_confidence + (direction * variation) + random_component
        composite_signal["confidence"] = int(max(40, min(60, neutral_confidence)))
        
        if explanation_parts:
            composite_signal["explanation"] = "Mixed signals detected: " + ". ".join(explanation_parts)
            
    # Log confidence calculation for debugging
    logger.info(f"Generated signal: {composite_signal['signal']} with confidence: {composite_signal['confidence']}%")
    
    return composite_signal

def calculate_stop_loss_take_profit(
    signal: str,
    current_price: float,
    sr_data: Dict[str, Any],
    atr_data: Dict[str, Any]
) -> tuple:
    """
    Calculate stop loss and take profit levels based on support/resistance and ATR
    """
    if not signal or not current_price:
        return None, None
    
    # Default ATR multipliers
    stop_loss_atr_multiplier = 1.5
    take_profit_atr_multiplier = 3.0
    
    # Get ATR value
    atr_value = None
    if atr_data and "value" in atr_data:
        atr_value = atr_data.get("value")
        if isinstance(atr_value, list) and atr_value:
            atr_value = atr_value[-1]  # Get the last value if it's a list
    
    # Fallback to percentage if no ATR
    if not atr_value or not isinstance(atr_value, (int, float)) or atr_value <= 0:
        # Use default percentage of current price
        atr_value = current_price * 0.005  # 0.5%
    
    # Extract support and resistance levels
    support_levels = sr_data.get("support_levels", [])
    resistance_levels = sr_data.get("resistance_levels", [])
    
    # Sort levels by price
    sorted_supports = sorted([(level.get("price", 0), level.get("strength", 0)) 
                             for level in support_levels if level.get("price") is not None], 
                            key=lambda x: x[0])
    
    sorted_resistances = sorted([(level.get("price", 0), level.get("strength", 0)) 
                               for level in resistance_levels if level.get("price") is not None], 
                              key=lambda x: x[0])
    
    stop_loss = None
    take_profit = None
    
    # For BUY signals
    if "BUY" in signal:
        # Find the nearest support below for stop loss
        for price, strength in reversed(sorted_supports):
            if price < current_price:
                # Use this level if it's strong enough
                if strength >= 5:
                    stop_loss = price - (atr_value * 0.5)  # Just below support
                    break
        
        # If no suitable support found, use ATR
        if stop_loss is None:
            stop_loss = current_price - (atr_value * stop_loss_atr_multiplier)
        
        # Find nearest resistance above for take profit
        for price, strength in sorted_resistances:
            if price > current_price:
                # Use this level if it's strong enough
                if strength >= 5:
                    take_profit = price + (atr_value * 0.5)  # Just above resistance
                    break
        
        # If no suitable resistance found, use ATR
        if take_profit is None:
            take_profit = current_price + (atr_value * take_profit_atr_multiplier)
    
    # For SELL signals
    elif "SELL" in signal:
        # Find nearest resistance above for stop loss
        for price, strength in sorted_resistances:
            if price > current_price:
                # Use this level if it's strong enough
                if strength >= 5:
                    stop_loss = price + (atr_value * 0.5)  # Just above resistance
                    break
        
        # If no suitable resistance found, use ATR
        if stop_loss is None:
            stop_loss = current_price + (atr_value * stop_loss_atr_multiplier)
        
        # Find the nearest support below for take profit
        for price, strength in reversed(sorted_supports):
            if price < current_price:
                # Use this level if it's strong enough
                if strength >= 5:
                    take_profit = price - (atr_value * 0.5)  # Just below support
                    break
        
        # If no suitable support found, use ATR
        if take_profit is None:
            take_profit = current_price - (atr_value * take_profit_atr_multiplier)
    
    # Round values to appropriate precision
    if stop_loss is not None:
        stop_loss = round(stop_loss, 5)
    if take_profit is not None:
        take_profit = round(take_profit, 5)
    
    return stop_loss, take_profit
