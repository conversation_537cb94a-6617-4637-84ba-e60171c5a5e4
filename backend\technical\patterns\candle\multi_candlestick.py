from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class MultiCandlestickPatternIndicator(BaseIndicator):
    """Multi-candlestick pattern indicator for complex candlestick formations."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - body_ratio: Parameter description (default: 0.6)
                - shadow_ratio: Parameter description (default: 0.1)
                - gap_threshold: Parameter description (default: 0.01)
        """
        default_params = {
            "body_ratio": 0.6,
            "shadow_ratio": 0.1,
            "gap_threshold": 0.01,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)


    def _is_strong_candle(self, open_price: float, high: float, low: float,
                         close: float) -> bool:
        """Check if candle has a strong body with small shadows."""
        total_range = high - low
        if total_range == 0:
            return False
            
        body = abs(close - open_price)
        upper_shadow = high - max(open_price, close)
        lower_shadow = min(open_price, close) - low
        
        body_ratio = body / total_range
        shadow_ratio = max(upper_shadow, lower_shadow) / total_range
        
        return (body_ratio >= self.params['body_ratio'] and 
                shadow_ratio <= self.params['shadow_ratio'])

    def _has_gap(self, prev_close: float, curr_open: float,
                 direction: int) -> bool:
        """Check for price gap in specified direction."""
        if direction > 0:  # Bullish gap
            return curr_open > prev_close * (1 + self.params['gap_threshold'])
        else:  # Bearish gap
            return curr_open < prev_close * (1 - self.params['gap_threshold'])

    def _is_kicking(self, opens: np.ndarray, highs: np.ndarray,
                   lows: np.ndarray, closes: np.ndarray, idx: int) -> tuple:
        """Identify Bullish/Bearish Kicking patterns."""
        if idx < 1:
            return False, 0
            
        # Check for two strong candles of opposite color with gap
        prev_strong = self._is_strong_candle(opens[idx-1], highs[idx-1],
                                           lows[idx-1], closes[idx-1])
        curr_strong = self._is_strong_candle(opens[idx], highs[idx],
                                           lows[idx], closes[idx])
                                           
        if not (prev_strong and curr_strong):
            return False, 0
            
        prev_bearish = closes[idx-1] < opens[idx-1]
        curr_bullish = closes[idx] > opens[idx]
        
        # Bullish Kicking: Bearish candle followed by gapped up bullish candle
        if prev_bearish and curr_bullish and self._has_gap(closes[idx-1], opens[idx], 1):
            return True, 1
            
        # Bearish Kicking: Bullish candle followed by gapped down bearish candle
        if not prev_bearish and not curr_bullish and self._has_gap(closes[idx-1], opens[idx], -1):
            return True, -1
            
        return False, 0

    def _is_three_methods(self, opens: np.ndarray, highs: np.ndarray,
                         lows: np.ndarray, closes: np.ndarray, idx: int) -> tuple:
        """Identify Rising/Falling Three Methods patterns."""
        if idx < 4:
            return False, 0
            
        # Check first candle is strong
        first_strong = self._is_strong_candle(opens[idx-4], highs[idx-4],
                                            lows[idx-4], closes[idx-4])
        if not first_strong:
            return False, 0
            
        first_bullish = closes[idx-4] > opens[idx-4]
        
        # Check middle three candles
        if first_bullish:
            # Rising Three Methods
            if not all(closes[i] < opens[i] for i in range(idx-3, idx)):
                return False, 0
            if not all(lows[i] > lows[idx-4] for i in range(idx-3, idx)):
                return False, 0
            if not (closes[idx] > opens[idx] and closes[idx] > highs[idx-4]):
                return False, 0
            return True, 1
        else:
            # Falling Three Methods
            if not all(closes[i] > opens[i] for i in range(idx-3, idx)):
                return False, 0
            if not all(highs[i] < highs[idx-4] for i in range(idx-3, idx)):
                return False, 0
            if not (closes[idx] < opens[idx] and closes[idx] < lows[idx-4]):
                return False, 0
            return True, -1

    def _is_abandoned_baby(self, opens: np.ndarray, highs: np.ndarray,
                          lows: np.ndarray, closes: np.ndarray, idx: int) -> tuple:
        """Identify Abandoned Baby patterns."""
        if idx < 2:
            return False, 0
            
        # Check for doji in middle
        middle_range = highs[idx-1] - lows[idx-1]
        if middle_range == 0:
            return False, 0
            
        middle_body = abs(closes[idx-1] - opens[idx-1])
        if middle_body / middle_range > 0.1:  # Should be very small body
            return False, 0
            
        # Check gaps
        first_bearish = closes[idx-2] < opens[idx-2]
        last_bullish = closes[idx] > opens[idx]
        
        # Bullish Abandoned Baby
        if (first_bearish and last_bullish and
            self._has_gap(closes[idx-2], highs[idx-1], -1) and
            self._has_gap(lows[idx-1], opens[idx], 1)):
            return True, 1
            
        # Bearish Abandoned Baby
        if (not first_bearish and not last_bullish and
            self._has_gap(closes[idx-2], lows[idx-1], 1) and
            self._has_gap(highs[idx-1], opens[idx], -1)):
            return True, -1
            
        return False, 0

    def _is_unique_three_river(self, opens: np.ndarray, highs: np.ndarray,
                              lows: np.ndarray, closes: np.ndarray, idx: int) -> tuple:
        """Identify Unique Three River Bottom pattern."""
        if idx < 2:
            return False, 0
            
        # First candle: Long bearish
        if not (closes[idx-2] < opens[idx-2] and 
                self._is_strong_candle(opens[idx-2], highs[idx-2],
                                     lows[idx-2], closes[idx-2])):
            return False, 0
            
        # Second candle: Bearish with lower low but higher close
        if not (closes[idx-1] < opens[idx-1] and
                lows[idx-1] < lows[idx-2] and
                closes[idx-1] > closes[idx-2]):
            return False, 0
            
        # Third candle: Small bullish within second candle's range
        if not (closes[idx] > opens[idx] and
                highs[idx] < highs[idx-1] and
                lows[idx] > lows[idx-1]):
            return False, 0
            
        return True, 1

    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        opens = df['open'].values
        highs = df['high'].values
        lows = df['low'].values
        closes = df['close'].values
        
        is_pattern = np.zeros_like(closes)
        pattern_type = np.zeros_like(closes)  # 1=Bullish, -1=Bearish
        pattern_id = np.zeros_like(closes)    # 1=Kicking, 2=Three Methods, 3=Abandoned Baby, 4=Unique Three River
        
        # Scan for patterns
        for i in range(len(closes)):
            # Check Kicking pattern
            is_valid, k_type = self._is_kicking(opens, highs, lows, closes, i)
            if is_valid:
                window = slice(i-1, i+1)
                is_pattern[window] = 1
                pattern_type[window] = k_type
                pattern_id[window] = 1
                continue
                
            # Check Three Methods pattern
            is_valid, tm_type = self._is_three_methods(opens, highs, lows, closes, i)
            if is_valid:
                window = slice(i-4, i+1)
                is_pattern[window] = 1
                pattern_type[window] = tm_type
                pattern_id[window] = 2
                continue
                
            # Check Abandoned Baby pattern
            is_valid, ab_type = self._is_abandoned_baby(opens, highs, lows, closes, i)
            if is_valid:
                window = slice(i-2, i+1)
                is_pattern[window] = 1
                pattern_type[window] = ab_type
                pattern_id[window] = 3
                continue
                
            # Check Unique Three River pattern
            is_valid, ur_type = self._is_unique_three_river(opens, highs, lows, closes, i)
            if is_valid:
                window = slice(i-2, i+1)
                is_pattern[window] = 1
                pattern_type[window] = ur_type
                pattern_id[window] = 4
        
        # Calculate pattern characteristics
        strength = np.zeros_like(closes)
        reliability = np.zeros_like(closes)
        
        for i in range(len(closes)-1):
            if is_pattern[i]:
                # Calculate pattern strength based on:
                # 1. Candle sizes
                # 2. Gap sizes (if applicable)
                # 3. Price range
                window_start = max(0, i - 4)  # Maximum 5 candles back
                window = slice(window_start, i + 1)
                
                price_range = max(highs[window]) - min(lows[window])
                if price_range > 0:
                    body_sizes = abs(closes[window] - opens[window])
                    avg_body = np.mean(body_sizes)
                    strength[i] = avg_body / price_range
                
                # Calculate reliability based on next day's movement
                if i < len(closes)-1:
                    future_return = (closes[i+1] - closes[i]) / closes[i]
                    reliability[i] = 1 if (future_return * pattern_type[i]) > 0 else -1
        
        # Calculate trend context
        trend = np.zeros_like(closes)
        for i in range(20, len(closes)):
            sma = np.mean(closes[i-20:i])
            trend[i] = 1 if closes[i] > sma else -1
        
        return {
            'is_pattern': is_pattern.astype(int),
            'pattern_type': pattern_type,  # 1=Bullish, -1=Bearish
            'pattern_id': pattern_id,      # 1=Kicking, 2=Three Methods, 3=Abandoned Baby, 4=Unique Three River
            'strength': strength,
            'trend': trend,
            'reliability': reliability
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if not 0 < self.params['body_ratio'] < 1:
            raise ValueError("Body ratio must be between 0 and 1")
        if not 0 < self.params['shadow_ratio'] < 1:
            raise ValueError("Shadow ratio must be between 0 and 1")
        if not 0 < self.params['gap_threshold'] < 1:
            raise ValueError("Gap threshold must be between 0 and 1")
        return True 