/**
 * Helper functions for formatting and working with numbers safely
 */

/**
 * Safely converts a value to fixed decimal places, handling null/undefined values
 * @param {number|null|undefined} value - The number to format
 * @param {number} decimalPlaces - Number of decimal places
 * @param {string} fallback - Value to return if input is not a valid number
 * @returns {string} Formatted number string or fallback
 */
export function safeToFixed(value, decimalPlaces = 2, fallback = '-') {
  if (value === null || value === undefined || isNaN(Number(value))) {
    return fallback;
  }
  
  try {
    const num = Number(value);
    return num.toFixed(decimalPlaces);
  } catch (e) {
    console.warn(`Error in safeToFixed with value ${value}:`, e);
    return fallback;
  }
}

/**
 * Checks if a value is a valid number (not null, undefined, or NaN)
 * @param {any} value - Value to check
 * @returns {boolean} Whether the value is a valid number
 */
export function isValidNumber(value) {
  return value !== null && value !== undefined && !isNaN(Number(value));
}
