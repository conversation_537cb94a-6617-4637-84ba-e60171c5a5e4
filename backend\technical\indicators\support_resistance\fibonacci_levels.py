from typing import Dict, Any
import numpy as np
import pandas as pd

from backend.technical.base_indicator import BaseIndicator

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

class FibonacciLevelsIndicator(BaseIndicator):
    """Fibonacci Retracement and Extension Levels indicator (Basic).

    Note: This basic version calculates levels based on the absolute high and low
    of the entire provided dataset. A more advanced version would require
    detecting significant swing high/low points.
    """

    def __init__(self):
        """Initialize Fibonacci Levels indicator."""
        # No parameters needed for this basic version
        super().__init__({})

    def calculate(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """Calculate Fibonacci levels based on the dataset's high/low."""
        df = data.copy()
        if df.empty or len(df) < 2:
             return {
                 'retracement_0.0': np.array([]), 'retracement_23.6': np.array([]),
                 'retracement_38.2': np.array([]), 'retracement_50.0': np.array([]),
                 'retracement_61.8': np.array([]), 'retracement_78.6': np.array([]),
                 'retracement_100.0': np.array([]),
                 'extension_127.2': np.array([]), 'extension_161.8': np.array([]),
                 'extension_261.8': np.array([])
             }

        # Find the absolute high and low of the entire period
        period_high = df['high'].max()
        period_low = df['low'].min()
        price_range = period_high - period_low

        if price_range == 0: # Avoid division by zero if price hasn't moved
             # Return NaNs or zeros of the correct length
             nan_array = np.full(len(df), np.nan)
             return {
                 'retracement_0.0': nan_array, 'retracement_23.6': nan_array,
                 'retracement_38.2': nan_array, 'retracement_50.0': nan_array,
                 'retracement_61.8': nan_array, 'retracement_78.6': nan_array,
                 'retracement_100.0': nan_array,
                 'extension_127.2': nan_array, 'extension_161.8': nan_array,
                 'extension_261.8': nan_array
             }


        # --- Assuming Uptrend for Calculation (Low to High) ---
        # Retracement Levels (Uptrend)
        r0_u = period_high
        r23_6_u = period_high - 0.236 * price_range
        r38_2_u = period_high - 0.382 * price_range
        r50_0_u = period_high - 0.500 * price_range
        r61_8_u = period_high - 0.618 * price_range
        r78_6_u = period_high - 0.786 * price_range # Less common, but sometimes used
        r100_u = period_low

        # Extension Levels (Uptrend)
        e127_2_u = period_high + 0.272 * price_range # 1.272
        e161_8_u = period_high + 0.618 * price_range # 1.618
        e261_8_u = period_high + 1.618 * price_range # 2.618

        # --- Assuming Downtrend for Calculation (High to Low) ---
         # Retracement Levels (Downtrend)
        r0_d = period_low
        r23_6_d = period_low + 0.236 * price_range
        r38_2_d = period_low + 0.382 * price_range
        r50_0_d = period_low + 0.500 * price_range
        r61_8_d = period_low + 0.618 * price_range
        r78_6_d = period_low + 0.786 * price_range
        r100_d = period_high

        # Extension Levels (Downtrend)
        e127_2_d = period_low - 0.272 * price_range # 1.272
        e161_8_d = period_low - 0.618 * price_range # 1.618
        e261_8_d = period_low - 1.618 * price_range # 2.618


        # Return levels as constant arrays for the whole period
        # A real indicator would update these based on detected swings
        length = len(df)
        self._values = {
            # Uptrend Levels (assuming last move was up)
            'retracement_0.0_up': np.full(length, r0_u),
            'retracement_23.6_up': np.full(length, r23_6_u),
            'retracement_38.2_up': np.full(length, r38_2_u),
            'retracement_50.0_up': np.full(length, r50_0_u),
            'retracement_61.8_up': np.full(length, r61_8_u),
            'retracement_78.6_up': np.full(length, r78_6_u),
            'retracement_100.0_up': np.full(length, r100_u),
            'extension_127.2_up': np.full(length, e127_2_u),
            'extension_161.8_up': np.full(length, e161_8_u),
            'extension_261.8_up': np.full(length, e261_8_u),
             # Downtrend Levels (assuming last move was down)
            'retracement_0.0_down': np.full(length, r0_d),
            'retracement_23.6_down': np.full(length, r23_6_d),
            'retracement_38.2_down': np.full(length, r38_2_d),
            'retracement_50.0_down': np.full(length, r50_0_d),
            'retracement_61.8_down': np.full(length, r61_8_d),
            'retracement_78.6_down': np.full(length, r78_6_d),
            'retracement_100.0_down': np.full(length, r100_d),
            'extension_127.2_down': np.full(length, e127_2_d),
            'extension_161.8_down': np.full(length, e161_8_d),
            'extension_261.8_down': np.full(length, e261_8_d),
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        # No parameters to validate in this basic version
        return True
