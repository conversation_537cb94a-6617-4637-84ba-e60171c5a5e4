// DOM Elements
const mt5PathInput = document.getElementById('mt5-path');
const browseButton = document.getElementById('browse-mt5-path');
const accountTypeSelect = document.getElementById('account-type');
const loginInput = document.getElementById('login');
const passwordInput = document.getElementById('password');
const serverInput = document.getElementById('server');
const connectionForm = document.getElementById('connection-form');
const connectionModal = document.getElementById('connection-modal');
const connectButton = document.getElementById('connect-button');
const disconnectButton = document.getElementById('disconnect-button');
const connectionStatusDiv = document.getElementById('connection-status');

const headerAccountNameSpan = document.getElementById('account-name'); // In header
const userLicenseNameDiv = document.getElementById('account-name-display'); // In "User & License" card
const accountBalanceDiv = document.getElementById('account-balance');
const planTypeDiv = document.getElementById('plan-type-display');
const planEndDateDiv = document.getElementById('plan-end-date-display');
const licenseStatusDiv = document.getElementById('license-status-display');

// Global state
let settings = {};
let isConnected = false;
let connectionAttemptInProgress = false;
const backendUrl = 'http://localhost:5001';

// Helper for safe formatting
const safeFormat = {
    currency: (value, currencyCode = 'USD', locale = 'en-US') => {
        if (value === null || value === undefined || isNaN(parseFloat(value))) {
            return currencyCode === 'USD' ? '$0.00' : `0.00 ${currencyCode}`;
        }
        try {
            return new Intl.NumberFormat(locale, { style: 'currency', currency: currencyCode }).format(value);
        } catch (e) {
            console.warn(`Error formatting currency: ${e}. Falling back to simple format.`);
            return `${parseFloat(value).toFixed(2)} ${currencyCode}`;
        }
    }
};


// Load saved settings from the main process
async function loadSettings() {
    try {
        const allSettings = await window.api.getSettings(); // Gets { backendSettings, formValues }
        const backendCreds = allSettings.backendSettings;
        const formUiValues = allSettings.formValues;

        let finalSettingsToUse = {};

        if (backendCreds) {
            // Prioritize backend settings for core credentials
            mt5PathInput.value = backendCreds.path || '';
            loginInput.value = backendCreds.login || '';
            passwordInput.value = backendCreds.password || ''; // Password from backend file
            serverInput.value = backendCreds.server || '';
            
            finalSettingsToUse.path = backendCreds.path;
            finalSettingsToUse.login = backendCreds.login;
            finalSettingsToUse.password = backendCreds.password; // Store password in 'settings' global
            finalSettingsToUse.server = backendCreds.server;
        }
        
        if (formUiValues) {
            // Use form values for accountType and as fallback if backendCreds didn't provide a field
            accountTypeSelect.value = formUiValues.accountType || 'demo';
            if (!mt5PathInput.value && formUiValues.path) mt5PathInput.value = formUiValues.path;
            if (!loginInput.value && formUiValues.login) loginInput.value = formUiValues.login;
            // Do not override passwordInput with formUiValues.password as it's not stored there
            if (!serverInput.value && formUiValues.server) serverInput.value = formUiValues.server;

            // Update finalSettingsToUse with form values if not already set by backendCreds
            if (finalSettingsToUse.path === undefined && formUiValues.path !== undefined) finalSettingsToUse.path = formUiValues.path;
            if (finalSettingsToUse.login === undefined && formUiValues.login !== undefined) finalSettingsToUse.login = formUiValues.login;
            if (finalSettingsToUse.server === undefined && formUiValues.server !== undefined) finalSettingsToUse.server = formUiValues.server;
            finalSettingsToUse.accountType = formUiValues.accountType;
        } else {
            // Default accountType if no form values either
            accountTypeSelect.value = 'demo';
            finalSettingsToUse.accountType = 'demo';
        }
        
        settings = finalSettingsToUse; // Update global 'settings'
        console.log("Renderer loaded settings:", settings);

    } catch (error) {
        console.error('Failed to load settings:', error);
        // Use the imported notification system if available, otherwise fallback to local
        const notify = window.notifications?.error || showNotification;
        notify('Error', 'Failed to load settings.');
    }
}


// Function to show notifications (simple version, can be replaced by a more robust system)
function showNotification(title, message, type = 'info') {
    // This is a placeholder. Implement a proper notification system if needed.
    console.log(`Notification (${type}): ${title} - ${message}`);
    // Example: alert(`${title}: ${message}`);
    if (window.notifications && typeof window.notifications[type] === 'function') {
        window.notificationstype;
    } else {
        alert(`${title}: ${message}`); // Fallback
    }
}

// Update connection status display
async function updateConnectionStatus() {
    if (connectionAttemptInProgress) {
        connectionStatusDiv.textContent = 'Status: Connecting...';
        connectionStatusDiv.className = 'status-connecting';
        return;
    }
    try {
        const response = await fetch(`${backendUrl}/api/connection/connection_status`);
        if (response.ok) {
            const data = await response.json();
            isConnected = data.connected;
            connectionStatusDiv.textContent = `Status: ${data.state || 'Unknown'}`;
            connectionStatusDiv.className = data.connected ? 'status-connected' : 'status-disconnected';
            connectButton.disabled = data.connected;
            disconnectButton.disabled = !data.connected; // Enable/disable disconnect button
            
            if (data.connected && data.account_info) {
                if(headerAccountNameSpan) headerAccountNameSpan.textContent = data.account_info.name || 'N/A';
                if(accountBalanceDiv) accountBalanceDiv.textContent = `${safeFormat.currency(data.account_info.balance, data.account_info.currency)}`;
                // Update dashboard cards if connected
                document.getElementById('account-equity').textContent = safeFormat.currency(data.account_info.equity, data.account_info.currency);
            } else {
                if(headerAccountNameSpan) headerAccountNameSpan.textContent = 'N/A';
                if(accountBalanceDiv) accountBalanceDiv.textContent = '$0.00'; // Reset to default
                document.getElementById('account-equity').textContent = '$0.00';
            }

            if (data.connected && data.license_info) {
                const license = data.license_info;
                if(userLicenseNameDiv) userLicenseNameDiv.textContent = license.user_name || 'N/A';
                if(planTypeDiv) planTypeDiv.textContent = license.plan_type || 'N/A';
                if(licenseStatusDiv) licenseStatusDiv.textContent = license.status || 'N/A';
                if(planEndDateDiv) {
                    planEndDateDiv.textContent = license.plan_end_date 
                        ? new Date(license.plan_end_date).toLocaleDateString(undefined, { year: 'numeric', month: 'short', day: 'numeric' })
                        : 'N/A';
                }
            } else {
                if(userLicenseNameDiv) userLicenseNameDiv.textContent = 'N/A';
                if(planTypeDiv) planTypeDiv.textContent = 'N/A';
                if(planEndDateDiv) planEndDateDiv.textContent = 'N/A';
                if(licenseStatusDiv) licenseStatusDiv.textContent = 'N/A';
            }

        } else {
            connectionStatusDiv.textContent = 'Status: Error fetching status';
            connectionStatusDiv.className = 'status-error';
            connectButton.disabled = false;
        }
    } catch (error) {
        console.error('Error updating connection status:', error);
        connectionStatusDiv.textContent = 'Status: Error (Backend offline?)';
        connectionStatusDiv.className = 'status-error';
        isConnected = false;
        connectButton.disabled = false;
        disconnectButton.disabled = true;
    }
}

// Auto-connect if settings are available
async function autoConnect() {
    if (connectionAttemptInProgress || isConnected) return;
    
    try {
        connectionAttemptInProgress = true;
        
        // The global 'settings' variable is populated by loadSettings()
        // It contains:
        // - path, login, server, password (if available from user_mt5_settings.json via backendSettings)
        // - accountType (from electron-store's mt5-form-values)
        
        // Check if we have enough info to connect
        if (!settings || !settings.path || !settings.login || !settings.server) {
            console.log("Auto-connect: Insufficient saved settings to attempt pre-fill.");
            connectionAttemptInProgress = false;
            return;
        }
        
        // Pre-fill the form from the 'settings' global variable.
        // User will still need to interact with the modal (e.g., click "Connect").
        mt5PathInput.value = settings.path || '';
        loginInput.value = settings.login || '';
        serverInput.value = settings.server || '';
        // If settings.password exists (from user_mt5_settings.json), pre-fill it.
        passwordInput.value = settings.password || ''; 
        accountTypeSelect.value = settings.accountType || 'demo';
        
        // Show modal for user to enter password
        connectionModal.classList.add('active');
        
        // Focus password field if it's empty, otherwise focus the connect button
        if (!passwordInput.value) {
            passwordInput.focus();
        } else {
            document.getElementById('connect-submit')?.focus(); // Check if element exists
        }
        
        // Dispatch event for React to detect
        window.dispatchEvent(new CustomEvent('mt5:connectionModalOpened'));
        
    } catch (error) {
        console.error('Error during auto-connect:', error);
        const notify = window.notifications?.error || showNotification;
        notify('Error', 'Auto-connect failed.');
    } finally {
        connectionAttemptInProgress = false;
    }
}

// Handle connection form submission
async function handleConnectionSubmit(event) {
    event.preventDefault();
    if (connectionAttemptInProgress) return;

    connectionAttemptInProgress = true;
    updateConnectionStatus(); // Show "Connecting..."

    const path = mt5PathInput.value;
    const accountType = accountTypeSelect.value;
    const login = loginInput.value;
    const password = passwordInput.value;
    const server = serverInput.value;

    const connectionParams = { path, login: parseInt(login), password, server };

    try {
        const response = await fetch(`${backendUrl}/api/connection/connect`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(connectionParams)
        });
        const data = await response.json();

        if (response.ok && data.success) {
            isConnected = true;
            // Save settings (excluding password) to electron-store for UI convenience
            await window.api.saveSettings({ path, login, server, accountType });

            let successMessage = `Connected to MT5. Account: ${data.account_info?.name || 'N/A'}`;
            if (data.license_info) {
                const license = data.license_info;
                successMessage += ` | User: ${license.user_name || 'N/A'} | Plan: ${license.plan_type || 'N/A'}`;
                if (license.plan_end_date) {
                    successMessage += ` (Ends: ${new Date(license.plan_end_date).toLocaleDateString(undefined, { year: 'numeric', month: 'short', day: 'numeric' })})`;
                }
                successMessage += ` | Status: ${license.status || 'N/A'}`;
            }
            const notifySuccess = window.notifications?.success || showNotification;
            notifySuccess('Success', successMessage);
            
            connectionModal.classList.remove('active');
        } else {
            isConnected = false;
            const notifyError = window.notifications?.error || showNotification;
            notifyError('Error', data.message || 'Connection failed');
        }
    } catch (error) {
        isConnected = false;
        console.error('Connection error:', error);
        const notifyError = window.notifications?.error || showNotification;
        notifyError('Error', 'Failed to connect to backend.');
    } finally {
        connectionAttemptInProgress = false;
        updateConnectionStatus();
    }
}

// Handle disconnect button click
async function handleDisconnect() {
    if (connectionAttemptInProgress) return;

    connectionAttemptInProgress = true;
    updateConnectionStatus(); // Show "Connecting..." (or "Disconnecting...")

    try {
        const response = await fetch(`${backendUrl}/api/connection/disconnect`, { method: 'POST' });
        const data = await response.json();
        if (response.ok && data.success) {
            isConnected = false;
            const notifySuccess = window.notifications?.success || showNotification;
            notifySuccess('Success', 'Disconnected from MT5.');
        } else {
            const notifyError = window.notifications?.error || showNotification;
            notifyError('Error', data.message || 'Disconnection failed.');
        }
    } catch (error) {
        console.error('Disconnection error:', error);
        const notifyError = window.notifications?.error || showNotification;
        notifyError('Error', 'Failed to connect to backend for disconnection.');
    } finally {
        connectionAttemptInProgress = false;
        updateConnectionStatus();
    }
}

// Event Listeners
document.addEventListener('DOMContentLoaded', async () => {
    await loadSettings();
    updateConnectionStatus(); // Initial status update
    // autoConnect(); // Consider if you want to auto-show modal on startup

    if (browseButton) {
        browseButton.addEventListener('click', async () => {
            const filePath = await window.api.browseFilePath();
            if (filePath) {
                mt5PathInput.value = filePath;
            }
        });
    }

    if (connectionForm) {
        connectionForm.addEventListener('submit', handleConnectionSubmit);
    }

    if (connectButton) {
        connectButton.addEventListener('click', () => {
            connectionModal.classList.add('active');
        });
    }

    if (disconnectButton) {
        disconnectButton.addEventListener('click', handleDisconnect);
    }

    // Close modal functionality
    const closeButton = document.querySelector('.modal .close-button');
    if (closeButton) {
        closeButton.addEventListener('click', () => {
            connectionModal.classList.remove('active');
        });
    }
    // Also close modal if clicking outside the content
    if (connectionModal) {
        connectionModal.addEventListener('click', (event) => {
            if (event.target === connectionModal) {
                connectionModal.classList.remove('active');
            }
        });
    }
    
    // Periodically update connection status
    setInterval(updateConnectionStatus, 15000); // Update every 15 seconds
});
