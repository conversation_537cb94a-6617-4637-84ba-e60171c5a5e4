from PyQt5.QtWidgets import QWidget
from PyQt5.QtCore import pyqtSignal, QRunnable, QThreadPool
import admin.core.firebase_admin_manager as fb_mgr

class TaskRunner(QRunnable):
    def __init__(self, task_id: str, params: dict, signals: QWidget):
        super().__init__()
        self.task_id = task_id
        self.params = params or {}
        self.signals = signals

    def run(self):
        try:
            result = self.execute_task()
            self.signals.task_successful.emit(self.task_id, result)
        except Exception as e:
            self.signals.task_failed.emit(self.task_id, str(e))

    def execute_task(self):
        if self.task_id == 'admin_get_accounts':
            return fb_mgr.get_authorized_accounts()
        elif self.task_id == 'admin_add_account':
            success, message = fb_mgr.add_authorized_account(**self.params)
            return {"success": success, "message": message}
        elif self.task_id == 'admin_update_account':
            success, message = fb_mgr.update_authorized_account(**self.params)
            return {"success": success, "message": message}
        elif self.task_id == 'admin_delete_account':
            success, message = fb_mgr.delete_authorized_account(**self.params)
            return {"success": success, "message": message}
        elif self.task_id == 'admin_get_sessions':
            return fb_mgr.get_trading_sessions(**self.params)
        else:
            raise ValueError(f"Unknown task_id: {self.task_id}")

class BaseWorkerWidget(QWidget):
    task_successful = pyqtSignal(str, object)
    task_failed = pyqtSignal(str, str)

    def __init__(self, parent=None, mt5_connector=None):
        super().__init__(parent)
        self.mt5_connector = mt5_connector
        self.threadpool = QThreadPool()

    def start_task(self, task_id: str, params: dict = None):
        runner = TaskRunner(task_id, params, self)
        self.threadpool.start(runner)
