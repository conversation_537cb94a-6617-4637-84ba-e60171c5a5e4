from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class VolumeProfileIndicator(BaseIndicator):
    """Volume Profile indicator."""
    
    def __init__(self, num_bins: int = 24, period: int = 0,
                 anchor: str = 'daily', value_area: float = 0.70):
        """
        Initialize Volume Profile indicator.
        
        Args:
            num_bins: Number of price bins for the profile
            period: The period for calculating profile (0 for session-based)
            anchor: Profile anchor point ('daily', 'weekly', 'monthly')
            value_area: Percentage of volume to consider for value area (0.0-1.0)
        """
        super().__init__({
            'num_bins': num_bins,
            'period': period,
            'anchor': anchor,
            'value_area': value_area
        })
    
    def _calculate_profile(self, prices: np.ndarray, volumes: np.ndarray,
                         num_bins: int) -> tuple:
        """Calculate volume profile for a given price range."""
        if len(prices) == 0:
            return np.array([]), np.array([]), 0, 0, 0, 0
        
        # Calculate price bins
        price_min = np.min(prices)
        price_max = np.max(prices)
        bin_edges = np.linspace(price_min, price_max, num_bins + 1)
        bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
        
        # Calculate volume per price level
        volume_profile, _ = np.histogram(prices, bins=bin_edges, weights=volumes)
        
        # Find POC (Point of Control)
        poc_idx = np.argmax(volume_profile)
        poc_price = bin_centers[poc_idx]
        poc_volume = volume_profile[poc_idx]
        
        # Calculate Value Area
        total_volume = np.sum(volume_profile)
        target_volume = total_volume * self.params['value_area']
        
        # Start from POC and expand both ways
        cumulative_volume = volume_profile[poc_idx]
        va_high_idx = poc_idx
        va_low_idx = poc_idx
        
        while cumulative_volume < target_volume and (va_high_idx < len(volume_profile) - 1 or va_low_idx > 0):
            # Get volumes above and below current range
            vol_above = volume_profile[va_high_idx + 1] if va_high_idx < len(volume_profile) - 1 else 0
            vol_below = volume_profile[va_low_idx - 1] if va_low_idx > 0 else 0
            
            # Add the larger volume to the value area
            if vol_above > vol_below and va_high_idx < len(volume_profile) - 1:
                va_high_idx += 1
                cumulative_volume += vol_above
            elif va_low_idx > 0:
                va_low_idx -= 1
                cumulative_volume += vol_below
        
        va_high = bin_edges[va_high_idx + 1]
        va_low = bin_edges[va_low_idx]
        
        return volume_profile, bin_centers, poc_price, poc_volume, va_high, va_low
    
    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Volume Profile values."""
        df = data.to_dataframe()
        if df.empty:
            return {}
        
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        volume = df['volume'].values
        period = self.params['period']
        num_bins = self.params['num_bins']
        anchor = self.params['anchor'].lower()
        
        # Calculate price points (we'll use OHLC4 for profile calculation)
        price = (high + low + close + df['open'].values) / 4
        
        if period > 0:
            # Rolling window profile
            profiles = []
            bin_centers_list = []
            poc_prices = np.zeros_like(price)
            poc_volumes = np.zeros_like(price)
            va_highs = np.zeros_like(price)
            va_lows = np.zeros_like(price)
            
            for i in range(len(price)):
                start_idx = max(0, i - period + 1)
                profile, bin_centers, poc_price, poc_volume, va_high, va_low = self._calculate_profile(
                    price[start_idx:i+1], volume[start_idx:i+1], num_bins
                )
                profiles.append(profile)
                bin_centers_list.append(bin_centers)
                poc_prices[i] = poc_price
                poc_volumes[i] = poc_volume
                va_highs[i] = va_high
                va_lows[i] = va_low
        else:
            # Session-based profile
            dates = pd.to_datetime(df.index)
            if anchor == 'weekly':
                reset_mask = dates.weekday == 0
            elif anchor == 'monthly':
                reset_mask = dates.day == 1
            else:  # daily
                reset_mask = dates.time == dates.time.min()
            
            profiles = []
            bin_centers_list = []
            poc_prices = np.zeros_like(price)
            poc_volumes = np.zeros_like(price)
            va_highs = np.zeros_like(price)
            va_lows = np.zeros_like(price)
            
            start_idx = 0
            for i in range(len(price)):
                if i == len(price) - 1 or reset_mask[i + 1]:
                    profile, bin_centers, poc_price, poc_volume, va_high, va_low = self._calculate_profile(
                        price[start_idx:i+1], volume[start_idx:i+1], num_bins
                    )
                    for j in range(start_idx, i + 1):
                        profiles.append(profile)
                        bin_centers_list.append(bin_centers)
                        poc_prices[j] = poc_price
                        poc_volumes[j] = poc_volume
                        va_highs[j] = va_high
                        va_lows[j] = va_low
                    start_idx = i + 1
        
        # Calculate relative volume strength
        rel_volume = np.zeros_like(price)
        for i in range(len(price)):
            if len(bin_centers_list[i]) > 0:
                # Find which bin the current price falls into
                bin_idx = np.digitize(price[i], bin_centers_list[i]) - 1
                if 0 <= bin_idx < len(profiles[i]):
                    rel_volume[i] = profiles[i][bin_idx] / np.mean(profiles[i])
        
        # Calculate price location within value area
        price_location = np.zeros_like(price)
        price_location[price > va_highs] = 1  # Above value area
        price_location[price < va_lows] = -1  # Below value area
        
        # Calculate distance from POC
        poc_distance = ((price - poc_prices) / poc_prices) * 100
        
        # Calculate volume distribution
        vol_above_poc = np.zeros_like(price)
        vol_below_poc = np.zeros_like(price)
        
        for i in range(len(price)):
            if len(bin_centers_list[i]) > 0:
                poc_bin_idx = np.digitize(poc_prices[i], bin_centers_list[i]) - 1
                if 0 <= poc_bin_idx < len(profiles[i]):
                    vol_above_poc[i] = np.sum(profiles[i][poc_bin_idx+1:])
                    vol_below_poc[i] = np.sum(profiles[i][:poc_bin_idx])
        
        self._values = {
            'profiles': np.stack(profiles) if profiles else np.array([]),
            'bin_centers': np.stack(bin_centers_list) if bin_centers_list else np.array([]),
            'poc_price': poc_prices,
            'poc_volume': poc_volumes,
            'va_high': va_highs,
            'va_low': va_lows,
            'rel_volume': rel_volume,
            'price_location': price_location,
            'poc_distance': poc_distance,
            'vol_above_poc': vol_above_poc,
            'vol_below_poc': vol_below_poc
        }
        return self._values
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_anchors = ['daily', 'weekly', 'monthly']
        if self.params['anchor'].lower() not in valid_anchors:
            raise ValueError(f"Anchor must be one of {valid_anchors}")
        if self.params['num_bins'] < 2:
            raise ValueError("Number of bins must be greater than 1")
        if self.params['period'] < 0:
            raise ValueError("Period must be greater than or equal to 0")
        if not 0 < self.params['value_area'] <= 1:
            raise ValueError("Value area must be between 0 and 1")
        return True 