from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class VolumeWeightedATRIndicator(BaseIndicator):
    """Volume Weighted Average True Range indicator."""
    
    def __init__(self, period: int = 14):
        """Initialize Volume Weighted ATR indicator."""
        super().__init__({'period': period})
    
    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Volume Weighted ATR values."""
        df = data.to_dataframe()
        if df.empty:
            return {}
        
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        volume = df['volume'].values
        period = self.params['period']
        
        # Calculate True Range
        tr1 = high[1:] - low[1:]  # Current High - Current Low
        tr2 = np.abs(high[1:] - close[:-1])  # Current High - Previous Close
        tr3 = np.abs(low[1:] - close[:-1])  # Current Low - Previous Close
        
        tr = np.maximum(np.maximum(tr1, tr2), tr3)
        tr = np.insert(tr, 0, high[0] - low[0])  # First TR is High - Low
        
        # Calculate Volume Weighted ATR
        vw_tr = tr * volume
        vw_atr = pd.Series(vw_tr).rolling(window=period).sum() / pd.Series(volume).rolling(window=period).sum()
        
        # Calculate standard ATR for comparison
        atr = pd.Series(tr).rolling(window=period).mean()
        
        # Calculate volume ratio
        volume_ratio = pd.Series(volume).rolling(window=period).mean() / volume
        
        self._values = {
            'vw_atr': vw_atr.values,
            'atr': atr.values,
            'volume_ratio': volume_ratio.values,
            'true_range': tr
        }
        return self._values 