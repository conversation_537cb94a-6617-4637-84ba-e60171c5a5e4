# Post-Login UI Workflow - Product Requirements Document

## 1. Overview
This document defines the requirements for the post-login user interface workflow, covering both default dashboard view and specialized analysis view scenarios.

## 2. User Flows

### 2.1 Default Dashboard Flow
1. User completes MT5 connection/login
2. System validates credentials and establishes connection
3. ConnectionTab.js detects successful connection (status = 'connected')
4. System checks for redirect parameter (none present)
5. <PERSON> navigates to Dashboard tab via electronAPI
6. Dashboard component:
   - Requests account data from `/api/positions`
   - Displays account summary (balance, equity)
   - Shows current positions
   - Displays recent trade history
   - Renders quick action buttons

### 2.2 Analysis Page Flow
1. User completes MT5 connection/login from research workflow
2. URL contains `?redirect=analysis&symbol=EURUSD&timeframe=H1`
3. ConnectionTab.js detects successful connection
4. System extracts redirect parameter ('analysis')
5. <PERSON> navigates to Analysis tab via electronAPI
6. Analysis component:
   - Extracts symbol and timeframe from URL params
   - Requests historical data from `/api/ohlc`
   - Renders multi-timeframe chart
   - Applies default technical indicators
   - Displays analysis tools panel

## 3. Success Criteria
1. **Redirection Accuracy**: 95% of logins result in correct redirection
2. **Dashboard Performance**: Loads within 2 seconds post-login
3. **Analysis Performance**: Loads required data within 3 seconds
4. **Data Completeness**: All required account/position data available on dashboard
5. **Analysis Functionality**: All charting tools operational on analysis page

## 4. Technical Specifications

### 4.1 Frontend Implementation
- **Connection Handling**:
  ```javascript
  // ConnectionTab.js
  this.connectionManager.onStatusChange = (status) => {
      if (status === ConnectionStates.CONNECTED) {
          const urlParams = new URLSearchParams(window.location.search);
          const redirectTo = urlParams.get('redirect') || 'dashboard';
          window.electronAPI.navigateToTab(redirectTo);
      }
  };
  ```

- **Dashboard Component**:
  - Must handle account data updates in real-time
  - Should implement error states for failed API calls
  - Requires responsive design for different screen sizes

- **Analysis Component**:
  - Must support URL parameter parsing
  - Should implement chart caching for better performance
  - Needs to handle multiple concurrent data requests

### 4.2 Backend Requirements
- **API Endpoints**:
  - `GET /api/positions` - Returns current account positions
  - `GET /api/ohlc?symbol=X&timeframe=Y` - Returns historical OHLC data
  - Both endpoints must respond within 500ms under normal load

## 5. Dependencies
1. **Electron API Extension**:
   ```javascript
   // preload.js
   contextBridge.exposeInMainWorld('electronAPI', {
       navigateToTab: (tabId) => ipcRenderer.send('navigate-to-tab', tabId)
   });
   ```

2. **MT5 Connection Service**:
   - Must emit proper connection status events
   - Should maintain stable WebSocket connection

3. **Data Services**:
   - Position data service must be real-time
   - Historical data service must support multiple timeframes

## 6. Acceptance Criteria

### 6.1 Dashboard View
- [ ] Automatically loads after successful login (no redirect param)
- [ ] Displays complete account summary within 2 seconds
- [ ] Shows current positions with correct values
- [ ] Updates in real-time when positions change
- [ ] Quick action buttons are functional

### 6.2 Analysis View
- [ ] Loads when URL contains `?redirect=analysis`
- [ ] Correctly parses symbol and timeframe parameters
- [ ] Displays complete chart within 3 seconds
- [ ] Technical indicators render properly
- [ ] Analysis tools are interactive

### 6.3 Common Requirements
- [ ] Handles connection errors gracefully
- [ ] Maintains security of account data
- [ ] Works across supported browsers/OS
- [ ] Meets all performance metrics