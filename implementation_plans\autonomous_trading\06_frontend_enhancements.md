# Phase 6: Frontend Enhancements

## Overview

This phase focuses on updating the user interface to support the new autonomous trading features, providing users with better visibility into the system's operation and more control over its behavior.

## Current Implementation

Currently, the Garuda-Algo frontend for autonomous trading has these limitations:
- Limited configuration options for autonomous trading
- Basic monitoring capabilities without detailed risk metrics
- No visualization of trading sessions or market conditions
- Limited user feedback about trading decisions

## Implementation Details

### 1. Enhanced Configuration UI

Update the autonomous trading configuration interface to include new settings:

**Time-Based Filters Section**
```jsx
<div className="config-section">
  <h3>Trading Sessions</h3>
  <div className="form-group">
    <label className="switch">
      <input
        type="checkbox"
        checked={config.time_filters.enabled}
        onChange={(e) => updateConfig({
          ...config,
          time_filters: {
            ...config.time_filters,
            enabled: e.target.checked
          }
        })}
      />
      <span className="slider round"></span>
    </label>
    <span className="label-text">Enable Time-Based Filters</span>
  </div>
  
  {config.time_filters.enabled && (
    <div className="session-config">
      <div className="time-zone-selector">
        <label>Time Zone:</label>
        <select
          value={config.time_filters.time_zone}
          onChange={(e) => updateConfig({
            ...config,
            time_filters: {
              ...config.time_filters,
              time_zone: e.target.value
            }
          })}
        >
          {availableTimeZones.map(tz => (
            <option key={tz} value={tz}>{tz}</option>
          ))}
        </select>
      </div>
      
      <div className="sessions-container">
        {config.time_filters.trading_sessions.map((session, index) => (
          <div key={index} className="session-item">
            <div className="session-header">
              <label className="switch">
                <input
                  type="checkbox"
                  checked={session.enabled}
                  onChange={(e) => {
                    const updatedSessions = [...config.time_filters.trading_sessions];
                    updatedSessions[index] = {
                      ...updatedSessions[index],
                      enabled: e.target.checked
                    };
                    updateConfig({
                      ...config,
                      time_filters: {
                        ...config.time_filters,
                        trading_sessions: updatedSessions
                      }
                    });
                  }}
                />
                <span className="slider round"></span>
              </label>
              <span className="session-name">{session.name}</span>
            </div>
            
            <div className="time-range">
              <div className="time-input">
                <label>Start:</label>
                <input
                  type="time"
                  value={`${String(session.start_hour).padStart(2, '0')}:${String(session.start_minute).padStart(2, '0')}`}
                  onChange={(e) => {
                    const [hours, minutes] = e.target.value.split(':').map(Number);
                    const updatedSessions = [...config.time_filters.trading_sessions];
                    updatedSessions[index] = {
                      ...updatedSessions[index],
                      start_hour: hours,
                      start_minute: minutes
                    };
                    updateConfig({
                      ...config,
                      time_filters: {
                        ...config.time_filters,
                        trading_sessions: updatedSessions
                      }
                    });
                  }}
                />
              </div>
              <div className="time-input">
                <label>End:</label>
                <input
                  type="time"
                  value={`${String(session.end_hour).padStart(2, '0')}:${String(session.end_minute).padStart(2, '0')}`}
                  onChange={(e) => {
                    const [hours, minutes] = e.target.value.split(':').map(Number);
                    const updatedSessions = [...config.time_filters.trading_sessions];
                    updatedSessions[index] = {
                      ...updatedSessions[index],
                      end_hour: hours,
                      end_minute: minutes
                    };
                    updateConfig({
                      ...config,
                      time_filters: {
                        ...config.time_filters,
                        trading_sessions: updatedSessions
                      }
                    });
                  }}
                />
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="non-trading-days">
        <label>Non-Trading Days:</label>
        <div className="day-selectors">
          {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map(day => (
            <div key={day} className="day-selector">
              <label className="checkbox-container">
                <input
                  type="checkbox"
                  checked={config.time_filters.non_trading_days.includes(day)}
                  onChange={(e) => {
                    const updatedDays = e.target.checked
                      ? [...config.time_filters.non_trading_days, day]
                      : config.time_filters.non_trading_days.filter(d => d !== day);
                    updateConfig({
                      ...config,
                      time_filters: {
                        ...config.time_filters,
                        non_trading_days: updatedDays
                      }
                    });
                  }}
                />
                <span className="checkmark"></span>
                <span className="day-label">{day.substring(0, 3)}</span>
              </label>
            </div>
          ))}
        </div>
      </div>
    </div>
  )}
</div>
```

**Risk Management Section**
```jsx
<div className="config-section">
  <h3>Risk Management</h3>
  
  <div className="risk-params">
    <div className="form-group">
      <label>Per-Trade Risk (%):</label>
      <input
        type="number"
        min="0.1"
        max="10"
        step="0.1"
        value={config.risk_percent}
        onChange={(e) => updateConfig({
          ...config,
          risk_percent: parseFloat(e.target.value)
        })}
      />
    </div>
    
    <div className="form-group">
      <label>Maximum Daily Risk (%):</label>
      <input
        type="number"
        min="0.5"
        max="20"
        step="0.5"
        value={config.risk_management.max_daily_risk}
        onChange={(e) => updateConfig({
          ...config,
          risk_management: {
            ...config.risk_management,
            max_daily_risk: parseFloat(e.target.value)
          }
        })}
      />
    </div>
    
    <div className="form-group">
      <label>Maximum Risk Per Symbol (%):</label>
      <input
        type="number"
        min="0.5"
        max="10"
        step="0.5"
        value={config.risk_management.max_risk_per_symbol}
        onChange={(e) => updateConfig({
          ...config,
          risk_management: {
            ...config.risk_management,
            max_risk_per_symbol: parseFloat(e.target.value)
          }
        })}
      />
    </div>
    
    <div className="form-group">
      <label className="switch">
        <input
          type="checkbox"
          checked={config.risk_management.reduce_risk_after_loss}
          onChange={(e) => updateConfig({
            ...config,
            risk_management: {
              ...config.risk_management,
              reduce_risk_after_loss: e.target.checked
            }
          })}
        />
        <span className="slider round"></span>
      </label>
      <span className="label-text">Reduce Risk After Consecutive Losses</span>
    </div>
    
    <div className="form-group">
      <label className="switch">
        <input
          type="checkbox"
          checked={config.risk_management.close_trades_on_reversal}
          onChange={(e) => updateConfig({
            ...config,
            risk_management: {
              ...config.risk_management,
              close_trades_on_reversal: e.target.checked
            }
          })}
        />
        <span className="slider round"></span>
      </label>
      <span className="label-text">Close Trades on Market Reversal</span>
    </div>
  </div>
</div>
```

### 2. Enhanced Monitoring Dashboard

Implement a more detailed monitoring dashboard with risk metrics and market condition information:

```jsx
<div className="monitoring-dashboard">
  <div className="dashboard-header">
    <h3>Autonomous Trading Monitor</h3>
    <div className="refresh-controls">
      <span>Last Updated: {new Date(lastUpdateTime).toLocaleTimeString()}</span>
      <button onClick={refreshData} className="refresh-button">
        <i className="refresh-icon"></i>
        Refresh
      </button>
    </div>
  </div>
  
  <div className="dashboard-grid">
    {/* Trading Status */}
    <div className="status-card">
      <div className="card-header">
        <h4>Trading Status</h4>
      </div>
      <div className="status-indicator">
        <div className={`status-light ${botStatus.running ? 'active' : 'inactive'}`}></div>
        <span className="status-text">{botStatus.running ? 'Running' : 'Stopped'}</span>
      </div>
      <div className="connection-status">
        <div className={`status-light ${isConnected ? 'connected' : 'disconnected'}`}></div>
        <span className="status-text">{isConnected ? 'Connected to MT5' : 'Disconnected'}</span>
      </div>
      <div className="active-count">
        <span className="label">Active Strategies:</span>
        <span className="value">{activeStrategies.length}</span>
      </div>
    </div>
    
    {/* Risk Metrics */}
    <div className="risk-card">
      <div className="card-header">
        <h4>Risk Metrics</h4>
      </div>
      <div className="risk-meters">
        <div className="risk-meter">
          <div className="meter-label">Daily Risk Used</div>
          <div className="meter-container">
            <div 
              className="meter-fill" 
              style={{
                width: `${Math.min(100, (monitoringData.risk_metrics.daily_risk_used / monitoringData.risk_metrics.max_daily_risk) * 100)}%`,
                backgroundColor: getDailyRiskColor(monitoringData.risk_metrics.daily_risk_used, monitoringData.risk_metrics.max_daily_risk)
              }}
            ></div>
          </div>
          <div className="meter-value">
            {monitoringData.risk_metrics.daily_risk_used.toFixed(2)}% / {monitoringData.risk_metrics.max_daily_risk.toFixed(2)}%
          </div>
        </div>
        
        <div className="risk-meter">
          <div className="meter-label">Current Drawdown</div>
          <div className="meter-container">
            <div 
              className="meter-fill" 
              style={{
                width: `${Math.min(100, (monitoringData.risk_metrics.current_drawdown / 10) * 100)}%`,
                backgroundColor: getDrawdownColor(monitoringData.risk_metrics.current_drawdown)
              }}
            ></div>
          </div>
          <div className="meter-value">
            {monitoringData.risk_metrics.current_drawdown.toFixed(2)}%
          </div>
        </div>
      </div>
      <div className="risk-alerts">
        {monitoringData.risk_metrics.alerts.map((alert, index) => (
          <div key={index} className={`risk-alert ${alert.severity}`}>
            <i className={`alert-icon ${alert.severity}`}></i>
            <span className="alert-text">{alert.message}</span>
          </div>
        ))}
      </div>
    </div>
    
    {/* Trading Sessions */}
    <div className="sessions-card">
      <div className="card-header">
        <h4>Trading Sessions</h4>
      </div>
      <div className="sessions-display">
        {monitoringData.sessions.map((session, index) => (
          <div key={index} className={`session-item ${session.active ? 'active' : 'inactive'}`}>
            <span className="session-name">{session.name}</span>
            <span className="session-time">{session.time_range}</span>
            <span className="session-status">{session.active ? 'Active' : 'Inactive'}</span>
          </div>
        ))}
      </div>
      <div className="next-session">
        <span className="label">Next Session:</span>
        <span className="value">{monitoringData.next_session || 'None'}</span>
      </div>
    </div>
    
    {/* Market Conditions */}
    <div className="market-card">
      <div className="card-header">
        <h4>Market Conditions</h4>
      </div>
      <div className="conditions-grid">
        {Object.entries(monitoringData.market_conditions).map(([symbol, condition]) => (
          <div key={symbol} className={`condition-item ${condition.condition.toLowerCase()}`}>
            <span className="symbol">{symbol}</span>
            <span className="condition">{condition.condition}</span>
            <div className="condition-metrics">
              <span className="metric">Volatility: {condition.volatility.toFixed(2)}</span>
              <span className="metric">Trend: {condition.trend}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
  
  {/* Active Positions */}
  <div className="positions-section">
    <h4>Active Positions</h4>
    <div className="table-container">
      <table className="positions-table">
        <thead>
          <tr>
            <th>Symbol</th>
            <th>Type</th>
            <th>Volume</th>
            <th>Open Price</th>
            <th>Current Price</th>
            <th>SL</th>
            <th>TP</th>
            <th>Profit</th>
            <th>Strategy</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {monitoringData.positions.map((position) => (
            <tr key={position.ticket} className={position.profit >= 0 ? 'profit' : 'loss'}>
              <td>{position.symbol}</td>
              <td className={position.type === 'buy' ? 'buy' : 'sell'}>{position.type}</td>
              <td>{position.volume}</td>
              <td>{position.price_open}</td>
              <td>{position.price_current}</td>
              <td>{position.sl}</td>
              <td>{position.tp}</td>
              <td className={position.profit >= 0 ? 'profit' : 'loss'}>
                {position.profit >= 0 ? '+' : ''}{position.profit.toFixed(2)}
              </td>
              <td>{position.strategy}</td>
              <td>
                <button 
                  className="close-position-btn"
                  onClick={() => handleClosePosition(position.ticket)}
                >
                  Close
                </button>
              </td>
            </tr>
          ))}
          {monitoringData.positions.length === 0 && (
            <tr>
              <td colSpan="10" className="no-data">No active positions</td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  </div>
</div>
```

### 3. API Integration

Implement the necessary API calls to interact with the backend:

```jsx
// Get monitoring data with expanded information
const fetchMonitoringData = async () => {
  try {
    setMonitoringLoading(true);
    setMonitoringError(null);
    
    const response = await fetch('http://localhost:5001/api/autonomous/monitor_enhanced');
    if (response.ok) {
      const data = await response.json();
      setMonitoringData(data);
      setLastUpdateTime(Date.now());
    } else {
      const errorData = await response.json();
      setMonitoringError(errorData.error || 'Failed to fetch monitoring data');
    }
  } catch (error) {
    setMonitoringError('Error fetching monitoring data: ' + error.message);
  } finally {
    setMonitoringLoading(false);
  }
};

// Update configuration with new enhanced settings
const saveConfiguration = async (config) => {
  try {
    setSaving(true);
    setSaveError(null);
    
    const response = await fetch('http://localhost:5001/api/autonomous/update_settings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(config)
    });
    
    if (response.ok) {
      const data = await response.json();
      setConfiguration(data.config);
      notify.success('Success', 'Configuration saved successfully');
    } else {
      const errorData = await response.json();
      setSaveError(errorData.error || 'Failed to save configuration');
      notify.error('Error', 'Failed to save configuration');
    }
  } catch (error) {
    setSaveError('Error saving configuration: ' + error.message);
    notify.error('Error', 'Failed to save configuration: ' + error.message);
  } finally {
    setSaving(false);
  }
};
```

### 4. Styles and Theming

Implement CSS styling for the new components:

```css
/* Time-based filter styles */
.config-section {
  margin-bottom: 2rem;
  background-color: var(--card);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.session-config {
  margin-top: 1rem;
}

.time-zone-selector {
  margin-bottom: 1.5rem;
}

.session-item {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

.session-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.session-name {
  font-weight: 500;
  margin-left: 1rem;
}

.time-range {
  display: flex;
  gap: 1rem;
}

.time-input {
  flex: 1;
}

.non-trading-days {
  margin-top: 1.5rem;
}

.day-selectors {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

/* Risk management styles */
.risk-params {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

/* Monitoring dashboard styles */
.monitoring-dashboard {
  margin-top: 2rem;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.risk-meter {
  margin-bottom: 1rem;
}

.meter-container {
  height: 8px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin: 0.5rem 0;
}

.meter-fill {
  height: 100%;
  transition: width 0.3s ease;
}

/* Session styles */
.sessions-display {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.session-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem;
  border-radius: 0.25rem;
}

.session-item.active {
  background-color: rgba(0, 255, 0, 0.1);
  border-left: 3px solid green;
}

.session-item.inactive {
  background-color: rgba(0, 0, 0, 0.05);
  opacity: 0.7;
}

/* Market condition styles */
.conditions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

.condition-item {
  padding: 0.75rem;
  border-radius: 0.25rem;
  background-color: rgba(0, 0, 0, 0.05);
}

.condition-item.trending_up {
  background-color: rgba(0, 128, 0, 0.1);
  border-left: 3px solid green;
}

.condition-item.trending_down {
  background-color: rgba(255, 0, 0, 0.1);
  border-left: 3px solid red;
}

.condition-item.ranging {
  background-color: rgba(0, 0, 255, 0.1);
  border-left: 3px solid blue;
}

.condition-item.volatile {
  background-color: rgba(255, 165, 0, 0.1);
  border-left: 3px solid orange;
}
```

## Files to Modify/Create

1. **frontend/pages/AutonomousTradingPage.jsx**:
   - Update to include enhanced configuration UI
   - Add improved monitoring dashboard
   - Implement API integration for new features

2. **frontend/styles/autonomous.css**:
   - Add styles for new UI components
   - Implement responsive design for dashboard

3. **frontend/components/TimeFilterConfig.jsx** (new):
   - Create component for time filter configuration
   - Implement time zone selection
   - Add session management

4. **frontend/components/RiskManagementConfig.jsx** (new):
   - Create component for risk management configuration
   - Implement sliders and toggles for risk parameters

5. **frontend/components/MonitoringDashboard.jsx** (new):
   - Create comprehensive monitoring dashboard
   - Implement visualization of market conditions
   - Add risk metrics display

## Testing Plan

1. **UI Rendering**:
   - Test rendering of new components
   - Verify responsive design on different screen sizes
   - Check accessibility features

2. **Form Validation**:
   - Test validation of user inputs
   - Verify error handling for invalid configurations
   - Test form submission

3. **API Integration**:
   - Test API calls to fetch configuration
   - Verify saving of updated configuration
   - Test monitoring data refresh

4. **Monitoring Dashboard**:
   - Test display of various monitoring metrics
   - Verify position management functionality
   - Test real-time updates

## Acceptance Criteria

- UI provides access to all new configuration options
- Time-based filters can be easily configured
- Risk management parameters can be adjusted
- Monitoring dashboard shows comprehensive information
- UI is responsive and works on different screen sizes
- All user interactions provide appropriate feedback
