from typing import Dict, Any, List
import numpy as np
import pandas as pd
from scipy.signal import find_peaks

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class DiamondPatternIndicator(BaseIndicator):
    """Diamond pattern indicator for identifying diamond tops and bottoms."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - min_points: Parameter description (default: 5)
                - min_width: Parameter description (default: 15)
                - price_tolerance: Parameter description (default: 0.02)
                - expansion_factor: Parameter description (default: 1.2)
                - symmetry_tolerance: Parameter description (default: 0.3)
        """
        default_params = {
            "min_points": 5,
            "min_width": 15,
            "price_tolerance": 0.02,
            "expansion_factor": 1.2,
            "symmetry_tolerance": 0.3,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)


    def _is_diamond(self, prices: np.ndarray, peaks: np.ndarray,
                   troughs: np.ndarray) -> tuple:
        """Identify Diamond patterns and their type."""
        if len(peaks) < self.params['min_points'] or len(troughs) < self.params['min_points']:
            return False, 0, 0, 0
            
        # Find pattern boundaries
        start_idx = min(peaks[0], troughs[0])
        end_idx = max(peaks[-1], troughs[-1])
        mid_idx = (start_idx + end_idx) // 2
        
        # Check minimum width
        if end_idx - start_idx < self.params['min_width']:
            return False, 0, 0, 0
            
        # Calculate trend lines for first half (expanding)
        first_half_peaks = peaks[peaks <= mid_idx]
        first_half_troughs = troughs[troughs <= mid_idx]
        
        if len(first_half_peaks) < 2 or len(first_half_troughs) < 2:
            return False, 0, 0, 0
            
        peak_slope1, peak_intercept1 = np.polyfit(first_half_peaks, prices[first_half_peaks], 1)
        trough_slope1, trough_intercept1 = np.polyfit(first_half_troughs, prices[first_half_troughs], 1)
        
        # Calculate trend lines for second half (contracting)
        second_half_peaks = peaks[peaks > mid_idx]
        second_half_troughs = troughs[troughs > mid_idx]
        
        if len(second_half_peaks) < 2 or len(second_half_troughs) < 2:
            return False, 0, 0, 0
            
        peak_slope2, peak_intercept2 = np.polyfit(second_half_peaks, prices[second_half_peaks], 1)
        trough_slope2, trough_intercept2 = np.polyfit(second_half_troughs, prices[second_half_troughs], 1)
        
        # Check for expansion in first half
        width_start = abs((peak_slope1 * start_idx + peak_intercept1) - 
                         (trough_slope1 * start_idx + trough_intercept1))
        width_mid = abs((peak_slope1 * mid_idx + peak_intercept1) - 
                       (trough_slope1 * mid_idx + trough_intercept1))
        
        if width_mid < width_start * self.params['expansion_factor']:
            return False, 0, 0, 0
            
        # Check for contraction in second half
        width_end = abs((peak_slope2 * end_idx + peak_intercept2) - 
                       (trough_slope2 * end_idx + trough_intercept2))
        
        if width_end > width_mid * 0.5:  # Should contract significantly
            return False, 0, 0, 0
            
        # Check symmetry
        first_half_height = max(prices[start_idx:mid_idx+1]) - min(prices[start_idx:mid_idx+1])
        second_half_height = max(prices[mid_idx:end_idx+1]) - min(prices[mid_idx:end_idx+1])
        
        if abs(first_half_height - second_half_height) / max(first_half_height, second_half_height) > self.params['symmetry_tolerance']:
            return False, 0, 0, 0
            
        # Determine pattern type (top or bottom)
        pre_pattern_trend = np.mean(prices[max(0, start_idx-10):start_idx])
        if prices[start_idx] > pre_pattern_trend:
            pattern_type = 1  # Diamond top
        else:
            pattern_type = -1  # Diamond bottom
            
        return True, start_idx, end_idx, pattern_type

    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        
        is_pattern = np.zeros_like(close)
        pattern_type = np.zeros_like(close)  # 1=Diamond Top, -1=Diamond Bottom
        
        # Find peaks and troughs
        peaks, _ = find_peaks(high, distance=5)
        troughs, _ = find_peaks(-low, distance=5)
        
        # Scan for diamond patterns
        for i in range(len(close)):
            valid_peaks = peaks[peaks >= i]
            valid_troughs = troughs[troughs >= i]
            
            if len(valid_peaks) >= self.params['min_points'] and len(valid_troughs) >= self.params['min_points']:
                is_valid, start, end, d_type = self._is_diamond(close, valid_peaks, valid_troughs)
                if is_valid:
                    is_pattern[start:end+1] = 1
                    pattern_type[start:end+1] = d_type
        
        # Calculate pattern characteristics
        strength = np.zeros_like(close)
        consolidation_score = np.zeros_like(close)
        
        for i in range(len(close)):
            if is_pattern[i]:
                # Calculate pattern height relative to price
                window = slice(i, min(i + self.params['min_width'], len(close)))
                pattern_height = max(high[window]) - min(low[window])
                strength[i] = pattern_height / close[i]
                
                # Calculate consolidation score based on:
                # 1. Decreasing volatility
                # 2. Narrowing price range
                # 3. Volume pattern (if available)
                price_std = np.std(close[window])
                price_range = max(close[window]) - min(close[window])
                consolidation_score[i] = 1 - (price_std / (price_range + 1e-6))
        
        # Calculate trend context
        trend = np.zeros_like(close)
        for i in range(20, len(close)):
            sma = np.mean(close[i-20:i])
            trend[i] = 1 if close[i] > sma else -1
        
        # Calculate pattern reliability
        reliability = np.zeros_like(close)
        for i in range(len(close)-1):
            if is_pattern[i]:
                if i < len(close)-1:
                    future_return = (close[i+1] - close[i]) / close[i]
                    if pattern_type[i] > 0:  # Diamond Top
                        reliability[i] = 1 if future_return < 0 else -1
                    else:  # Diamond Bottom
                        reliability[i] = 1 if future_return > 0 else -1
        
        return {
            'is_pattern': is_pattern.astype(int),
            'pattern_type': pattern_type,  # 1=Diamond Top, -1=Diamond Bottom
            'strength': strength,
            'consolidation_score': consolidation_score,  # Higher score indicates stronger consolidation
            'trend': trend,
            'reliability': reliability
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['min_points'] < 4:
            raise ValueError("Minimum points must be at least 4")
        if self.params['min_width'] < 10:
            raise ValueError("Minimum width must be at least 10 periods")
        if not 0 < self.params['price_tolerance'] < 1:
            raise ValueError("Price tolerance must be between 0 and 1")
        if self.params['expansion_factor'] <= 1:
            raise ValueError("Expansion factor must be greater than 1")
        if not 0 < self.params['symmetry_tolerance'] < 1:
            raise ValueError("Symmetry tolerance must be between 0 and 1")
        return True 