from typing import Dict, Any, List
import numpy as np
import pandas as pd
from scipy.signal import find_peaks

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class WedgePatternIndicator(BaseIndicator):
    """Wedge chart pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - min_points: Parameter description (default: 5)
                - min_width: Parameter description (default: 20)
                - price_tolerance: Parameter description (default: 0.02)
                - slope_tolerance: Parameter description (default: 0.1)
        """
        default_params = {
            "min_points": 5,
            "min_width": 20,
            "price_tolerance": 0.02,
            "slope_tolerance": 0.1,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)


    def _is_wedge(self, prices: np.ndarray, peaks: np.ndarray,
                 troughs: np.ndarray, is_rising: bool = True) -> tuple:
        """Identify Wedge patterns."""
        if len(peaks) < self.params['min_points'] or len(troughs) < self.params['min_points']:
            return False, 0, 0
            
        # Calculate trend lines
        peak_x = peaks
        peak_y = prices[peaks]
        peak_slope, peak_intercept = np.polyfit(peak_x, peak_y, 1)
        
        trough_x = troughs
        trough_y = prices[troughs]
        trough_slope, trough_intercept = np.polyfit(trough_x, trough_y, 1)
        
        # Check wedge characteristics
        if is_rising:
            # Rising wedge: both lines slope upward, resistance line has shallower slope
            if peak_slope <= 0 or trough_slope <= 0:
                return False, 0, 0
            if peak_slope >= trough_slope:
                return False, 0, 0
        else:
            # Falling wedge: both lines slope downward, support line has shallower slope
            if peak_slope >= 0 or trough_slope >= 0:
                return False, 0, 0
            if peak_slope <= trough_slope:
                return False, 0, 0
                
        # Check for convergence
        slope_diff = abs(peak_slope - trough_slope)
        avg_slope = (abs(peak_slope) + abs(trough_slope)) / 2
        if slope_diff / avg_slope < self.params['slope_tolerance']:
            return False, 0, 0
            
        # Find pattern boundaries
        start_idx = min(peaks[0], troughs[0])
        end_idx = max(peaks[-1], troughs[-1])
        
        # Check minimum width
        if end_idx - start_idx < self.params['min_width']:
            return False, 0, 0
            
        # Check price points fit within wedge
        for i in range(start_idx, end_idx+1):
            upper_bound = peak_slope * i + peak_intercept
            lower_bound = trough_slope * i + trough_intercept
            
            if prices[i] > upper_bound + self.params['price_tolerance'] * prices[i]:
                return False, 0, 0
            if prices[i] < lower_bound - self.params['price_tolerance'] * prices[i]:
                return False, 0, 0
                
        return True, start_idx, end_idx

    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        close = df['close'].values
        is_pattern = np.zeros_like(close)
        pattern_type = np.zeros_like(close)
        
        # Find peaks and troughs
        peaks, _ = find_peaks(close, distance=5)
        troughs, _ = find_peaks(-close, distance=5)
        
        # Scan for rising wedges (bearish)
        for i in range(len(close)):
            valid_peaks = peaks[peaks >= i]
            valid_troughs = troughs[troughs >= i]
            
            if len(valid_peaks) >= self.params['min_points'] and len(valid_troughs) >= self.params['min_points']:
                is_valid, start, end = self._is_wedge(close, valid_peaks, valid_troughs, is_rising=True)
                if is_valid:
                    is_pattern[start:end+1] = 1
                    pattern_type[start:end+1] = -1  # Bearish pattern
        
        # Scan for falling wedges (bullish)
        for i in range(len(close)):
            valid_peaks = peaks[peaks >= i]
            valid_troughs = troughs[troughs >= i]
            
            if len(valid_peaks) >= self.params['min_points'] and len(valid_troughs) >= self.params['min_points']:
                is_valid, start, end = self._is_wedge(close, valid_peaks, valid_troughs, is_rising=False)
                if is_valid:
                    is_pattern[start:end+1] = 1
                    pattern_type[start:end+1] = 1  # Bullish pattern
        
        # Calculate pattern strength
        strength = np.zeros_like(close)
        for i in range(len(close)):
            if is_pattern[i]:
                # Calculate the height of the pattern relative to price
                pattern_height = max(close[i:i+self.params['min_width']]) - min(close[i:i+self.params['min_width']])
                strength[i] = pattern_height / close[i]
        
        # Calculate trend context
        trend = np.zeros_like(close)
        for i in range(1, len(close)):
            if i >= 20:  # Use 20-period SMA for trend
                sma = np.mean(close[i-20:i])
                trend[i] = 1 if close[i] > sma else -1
        
        # Calculate pattern reliability
        reliability = np.zeros_like(close)
        for i in range(1, len(close)):
            if is_pattern[i]:
                # Check if price moved in the expected direction
                if i < len(close)-1:
                    future_return = (close[i+1] - close[i]) / close[i]
                    if pattern_type[i] > 0:  # Bullish pattern
                        reliability[i] = 1 if future_return > 0 else -1
                    else:  # Bearish pattern
                        reliability[i] = 1 if future_return < 0 else -1
        
        return {
            'is_pattern': is_pattern.astype(int),
            'pattern_type': pattern_type,  # 1 for Falling Wedge (Bullish), -1 for Rising Wedge (Bearish)
            'strength': strength,
            'trend': trend,
            'reliability': reliability
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['min_points'] < 3:
            raise ValueError("Minimum points must be at least 3")
        if self.params['min_width'] < 10:
            raise ValueError("Minimum width must be at least 10 periods")
        if not 0 < self.params['price_tolerance'] < 1:
            raise ValueError("Price tolerance must be between 0 and 1")
        if not 0 < self.params['slope_tolerance'] < 1:
            raise ValueError("Slope tolerance must be between 0 and 1")
        return True 