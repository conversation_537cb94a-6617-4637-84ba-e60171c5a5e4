from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class ThreeSoldiersCrowsPatternIndicator(BaseIndicator):
    """Three White Soldiers and Three Black Crows candlestick pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - body_ratio: Parameter description (default: 0.6)
                - shadow_ratio: Parameter description (default: 0.2)
        """
        default_params = {
            "body_ratio": 0.6,
            "shadow_ratio": 0.2,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)

    
    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        open_price = df['open'].values
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        body_ratio = self.params['body_ratio']
        shadow_ratio = self.params['shadow_ratio']
        
        # Calculate body sizes and ranges
        body_size = np.abs(close - open_price)
        total_range = high - low
        
        # Calculate shadows
        upper_shadow = high - np.maximum(open_price, close)
        lower_shadow = np.minimum(open_price, close) - low
        
        # Calculate previous and next candle's data
        prev_open = np.zeros_like(open_price)
        prev_open[1:] = open_price[:-1]
        prev_close = np.zeros_like(close)
        prev_close[1:] = close[:-1]
        prev_high = np.zeros_like(high)
        prev_high[1:] = high[:-1]
        prev_low = np.zeros_like(low)
        prev_low[1:] = low[:-1]
        
        prev2_open = np.zeros_like(open_price)
        prev2_open[2:] = open_price[:-2]
        prev2_close = np.zeros_like(close)
        prev2_close[2:] = close[:-2]
        prev2_high = np.zeros_like(high)
        prev2_high[2:] = high[:-2]
        prev2_low = np.zeros_like(low)
        prev2_low[2:] = low[:-2]
        
        # Identify three white soldiers patterns
        is_three_soldiers = (
            (close > open_price) &  # Current candle is bullish
            (prev_close > prev_open) &  # Previous candle is bullish
            (prev2_close > prev2_open) &  # Two candles ago is bullish
            (body_size >= (total_range * body_ratio)) &  # Significant body size
            (upper_shadow <= (total_range * shadow_ratio)) &  # Small upper shadow
            (close > prev_close) &  # Each candle closes higher
            (prev_close > prev2_close)
        )
        
        # Identify three black crows patterns
        is_three_crows = (
            (close < open_price) &  # Current candle is bearish
            (prev_close < prev_open) &  # Previous candle is bearish
            (prev2_close < prev2_open) &  # Two candles ago is bearish
            (body_size >= (total_range * body_ratio)) &  # Significant body size
            (lower_shadow <= (total_range * shadow_ratio)) &  # Small lower shadow
            (close < prev_close) &  # Each candle closes lower
            (prev_close < prev2_close)
        )
        
        # Classify pattern types
        pattern_type = np.zeros_like(close)
        pattern_type[is_three_soldiers] = 1
        pattern_type[is_three_crows] = -1
        
        # Calculate pattern strength
        strength = np.zeros_like(close)
        for i in range(2, len(close)):
            if is_three_soldiers[i] or is_three_crows[i]:
                # Calculate total price movement
                if is_three_soldiers[i]:
                    movement = close[i] - prev2_close[i]
                else:  # Three crows
                    movement = prev2_close[i] - close[i]
                strength[i] = movement / prev2_close[i]  # Normalize by price
        
        # Calculate trend context
        trend = np.zeros_like(close)
        for i in range(1, len(close)):
            if i >= 20:  # Use 20-period SMA for trend
                sma = np.mean(close[i-20:i])
                trend[i] = 1 if close[i] > sma else -1
        
        # Calculate pattern reliability
        reliability = np.zeros_like(close)
        for i in range(2, len(close)):
            if is_three_soldiers[i] or is_three_crows[i]:
                # Check if price moved in the expected direction
                if i < len(close)-1:
                    future_return = (close[i+1] - close[i]) / close[i]
                    if is_three_soldiers[i]:
                        reliability[i] = 1 if future_return > 0 else -1
                    else:  # Three crows
                        reliability[i] = 1 if future_return < 0 else -1
        
        return {
            'is_three_soldiers': is_three_soldiers.astype(int),
            'is_three_crows': is_three_crows.astype(int),
            'pattern_type': pattern_type,
            'strength': strength,
            'trend': trend,
            'reliability': reliability,
            'body_size': body_size,
            'upper_shadow': upper_shadow,
            'lower_shadow': lower_shadow,
            'total_range': total_range
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if not 0 < self.params['body_ratio'] < 1:
            raise ValueError("Body ratio must be between 0 and 1")
        if not 0 < self.params['shadow_ratio'] < 1:
            raise ValueError("Shadow ratio must be between 0 and 1")
        return True 