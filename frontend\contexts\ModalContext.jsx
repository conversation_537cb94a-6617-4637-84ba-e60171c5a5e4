import React, { createContext, useState, useContext } from 'react';
import TradeExecutionDialog from '../components/execution/TradeExecutionDialog';

// Create a context for modal management
const ModalContext = createContext();

// Custom hook to use the modal context
export const useModal = () => useContext(ModalContext);

// Modal provider component
export const ModalProvider = ({ children }) => {
  // State for trade execution modal
  const [tradeExecutionModal, setTradeExecutionModal] = useState({
    visible: false,
    signalType: '',
    symbol: '',
    entry: null,
    stopLoss: 0,
    takeProfit: 0,
    onExecute: null,
    isProcessing: false
  });

  // Function to open the trade execution modal
  const openTradeExecutionModal = (modalProps) => {
    console.log('Opening trade execution modal with props:', modalProps);
    setTradeExecutionModal({
      ...modalProps,
      visible: true
    });
  };

  // Function to close the trade execution modal
  const closeTradeExecutionModal = () => {
    setTradeExecutionModal(prev => ({
      ...prev,
      visible: false
    }));
  };

  return (
    <ModalContext.Provider
      value={{
        openTradeExecutionModal,
        closeTradeExecutionModal
      }}
    >
      {children}
      
      {/* Render modals at the root level */}
      <TradeExecutionDialog
        visible={tradeExecutionModal.visible}
        signalType={tradeExecutionModal.signalType}
        symbol={tradeExecutionModal.symbol}
        entry={tradeExecutionModal.entry}
        stopLoss={tradeExecutionModal.stopLoss}
        takeProfit={tradeExecutionModal.takeProfit}
        onClose={closeTradeExecutionModal}
        onExecute={(orderData) => {
          if (tradeExecutionModal.onExecute) {
            tradeExecutionModal.onExecute(orderData);
          }
        }}
        isProcessing={tradeExecutionModal.isProcessing}
      />
    </ModalContext.Provider>
  );
};
