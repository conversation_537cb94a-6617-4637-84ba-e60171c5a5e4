import React from 'react';
import '../styles/HorizontalPriceLevel.css';
import { safeToFixed } from '../utils/numberUtils';

const HorizontalPriceLevel = ({ currentPrice, supportLevels = [], resistanceLevels = [] }) => {
  // Format the price for display
  const formatPrice = (price) => {
    if (typeof price === 'number') {
      return safeToFixed(price, 5);
    }
    if (price === undefined || price === null || isNaN(price)) {
      return 'N/A';
    }
    return price.toString();
  };

  // Extract price values from support/resistance levels
  const extractPrices = (levels) => {
    if (!Array.isArray(levels)) return [];

    return levels.map(level => {
      if (level && typeof level === 'object') {
        // Try to extract price from different possible properties
        return level.price || level.level || level.value;
      }
      if (typeof level === 'number') {
        return level;
      }
      if (typeof level === 'string' && !isNaN(parseFloat(level))) {
        return parseFloat(level);
      }
      return null;
    }).filter(price => price !== null && !isNaN(price));
  };

  // Get support and resistance prices
  const supportPrices = extractPrices(supportLevels).sort((a, b) => b - a); // Sort descending
  const resistancePrices = extractPrices(resistanceLevels).sort((a, b) => a - b); // Sort ascending

  // Find nearest support and resistance
  const nearestSupport = supportPrices.find(price => price < currentPrice) || (supportPrices.length > 0 ? supportPrices[0] : null);
  const nearestResistance = resistancePrices.find(price => price > currentPrice) || (resistancePrices.length > 0 ? resistancePrices[0] : null);

  // Calculate position of current price between support and resistance
  let currentPricePosition = 50; // Default to middle

  if (nearestSupport && nearestResistance) {
    const range = nearestResistance - nearestSupport;
    if (range > 0) {
      currentPricePosition = ((currentPrice - nearestSupport) / range) * 100;
      // Clamp between 0 and 100
      currentPricePosition = Math.max(0, Math.min(100, currentPricePosition));
    }
  } else if (nearestSupport && !nearestResistance) {
    currentPricePosition = 75; // Above support, no resistance
  } else if (!nearestSupport && nearestResistance) {
    currentPricePosition = 25; // Below resistance, no support
  }

  return (
    <div className="price-level-visualization">
      <div className="price-level-header">
        <div className="price-level-title">Price Levels</div>
        <div className="price-level-current">Current: {formatPrice(currentPrice)}</div>
      </div>

      {/* Horizontal price bar with meaningful position */}
      <div style={{
        position: 'relative',
        width: '100%',
        height: '20px',
        background: 'linear-gradient(to right, rgba(0,128,0,0.3), rgba(255,0,0,0.3))',
        borderRadius: '2px',
        marginBottom: '25px',
        border: '1px solid rgba(255,255,255,0.1)'
      }}>
        {/* Orange current price marker with actual position */}
        <div style={{
          position: 'absolute',
          top: '0',
          left: `${currentPricePosition}%`,
          width: '6px',
          height: '20px',
          backgroundColor: 'orange',
          transform: 'translateX(-50%)'
        }}></div>
      </div>

      {/* Current price value */}
      <div style={{
        textAlign: 'center',
        marginBottom: '10px',
        fontSize: '0.9rem',
        fontWeight: 'bold'
      }}>
        {formatPrice(currentPrice)}
      </div>

      {/* Support/Resistance labels with actual values */}
      <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '0.85rem' }}>
        <div>Support: {nearestSupport ? formatPrice(nearestSupport) : 'N/A'}</div>
        <div style={{ textAlign: 'center' }}>Current Price</div>
        <div style={{ textAlign: 'right' }}>Resistance: {nearestResistance ? formatPrice(nearestResistance) : 'N/A'}</div>
      </div>
    </div>
  );
};

export default HorizontalPriceLevel;
