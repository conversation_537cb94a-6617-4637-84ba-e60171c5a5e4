from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.analysis.technical.indicators.volatility.atr import ATRIndicator # ADX uses ATR
from src.core.models.market_data import MarketData

class ADXIndicator(BaseIndicator):
    """Average Directional Index (ADX) indicator."""

    def __init__(self, period: int = 14, atr_period: int = 14):
        """
        Initialize ADX indicator.

        Args:
            period: The period for calculating ADX and DI lines.
            atr_period: The period for the underlying ATR calculation.
        """
        # Note: ADX typically uses <PERSON>'s smoothing (approximated by EMA with alpha=1/period)
        super().__init__({
            'period': period,
            'atr_period': atr_period
        })
        # Internal ATR indicator
        self._atr_indicator = ATRIndicator(period=atr_period, ma_type='ema')


    def _wilder_smooth(self, series: pd.Series, period: int) -> pd.Series:
        """Apply <PERSON>'s smoothing (equivalent to EMA with alpha = 1/period)."""
        return series.ewm(alpha=1/period, adjust=False).mean()

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate ADX values."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period'] or len(df) < self.params['atr_period']:
             return {'adx': np.array([]), 'di_plus': np.array([]), 'di_minus': np.array([])}

        period = self.params['period']

        high = df['high']
        low = df['low']
        close = df['close']

        # Calculate ATR first
        atr_result = self._atr_indicator.calculate(data)
        atr = pd.Series(atr_result['atr'], index=df.index) # Ensure index alignment

        # Calculate Directional Movement (+DM, -DM)
        move_up = high.diff()
        move_down = -low.diff()

        plus_dm = np.where((move_up > move_down) & (move_up > 0), move_up, 0)
        minus_dm = np.where((move_down > move_up) & (move_down > 0), move_down, 0)

        plus_dm_series = pd.Series(plus_dm, index=df.index)
        minus_dm_series = pd.Series(minus_dm, index=df.index)

        # Smooth +DM and -DM using Wilder's smoothing
        smooth_plus_dm = self._wilder_smooth(plus_dm_series, period)
        smooth_minus_dm = self._wilder_smooth(minus_dm_series, period)

        # Calculate Directional Indicators (+DI, -DI)
        # Ensure ATR is not zero to avoid division errors
        atr_safe = atr.replace(0, np.nan) # Replace 0 with NaN temporarily
        di_plus = 100 * (smooth_plus_dm / atr_safe)
        di_minus = 100 * (smooth_minus_dm / atr_safe)
        di_plus = di_plus.fillna(0) # Fill NaNs resulting from division
        di_minus = di_minus.fillna(0) # Fill NaNs resulting from division


        # Calculate Directional Movement Index (DX)
        di_diff = np.abs(di_plus - di_minus)
        di_sum = di_plus + di_minus
        # Avoid division by zero if di_sum is zero
        dx = 100 * (di_diff / di_sum.replace(0, np.nan))
        dx = dx.fillna(0) # Fill NaNs resulting from division

        # Calculate Average Directional Index (ADX) using Wilder's smoothing
        adx = self._wilder_smooth(dx, period)

        self._values = {
            'adx': adx.values,
            'di_plus': di_plus.values,
            'di_minus': di_minus.values,
            'dx': dx.values # Optional: return DX as well
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['period'] < 1:
            raise ValueError("ADX Period must be greater than 0")
        if self.params['atr_period'] < 1:
            raise ValueError("ATR Period must be greater than 0")
        return True