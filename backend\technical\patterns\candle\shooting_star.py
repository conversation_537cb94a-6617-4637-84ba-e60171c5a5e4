from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class ShootingStarPatternIndicator(BaseIndicator):
    """Shooting Star and Inverted Hammer candlestick pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - body_ratio: Parameter description (default: 0.3)
                - shadow_ratio: Parameter description (default: 0.6)
        """
        default_params = {
            "body_ratio": 0.3,
            "shadow_ratio": 0.6,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)

    
    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        open_price = df['open'].values
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        body_ratio = self.params['body_ratio']
        shadow_ratio = self.params['shadow_ratio']
        
        # Calculate body size and total range
        body_size = np.abs(close - open_price)
        total_range = high - low
        
        # Calculate shadows
        upper_shadow = high - np.maximum(open_price, close)
        lower_shadow = np.minimum(open_price, close) - low
        
        # Identify shooting star patterns
        is_shooting_star = (
            (body_size <= (total_range * body_ratio)) &  # Small body
            (upper_shadow >= (total_range * shadow_ratio)) &  # Long upper shadow
            (lower_shadow <= (total_range * 0.1))  # Small lower shadow
        )
        
        # Classify shooting star types
        pattern_type = np.zeros_like(close)
        pattern_type[is_shooting_star] = 1  # Regular shooting star
        
        # Inverted hammer (in downtrend)
        trend = np.zeros_like(close)
        for i in range(1, len(close)):
            if i >= 20:  # Use 20-period SMA for trend
                sma = np.mean(close[i-20:i])
                trend[i] = 1 if close[i] > sma else -1
        
        inverted_hammer = is_shooting_star & (trend < 0)
        pattern_type[inverted_hammer] = 2
        
        # Calculate pattern strength
        strength = np.zeros_like(close)
        strength[is_shooting_star] = upper_shadow[is_shooting_star] / total_range[is_shooting_star]
        
        # Calculate pattern reliability
        reliability = np.zeros_like(close)
        for i in range(1, len(close)):
            if is_shooting_star[i]:
                # Check if price moved in the expected direction
                if i < len(close)-1:
                    future_return = (close[i+1] - close[i]) / close[i]
                    if trend[i] > 0:  # Uptrend
                        reliability[i] = 1 if future_return < 0 else -1  # Shooting star should lead to reversal
                    else:  # Downtrend
                        reliability[i] = 1 if future_return > 0 else -1  # Inverted hammer should lead to reversal
        
        return {
            'is_shooting_star': is_shooting_star.astype(int),
            'pattern_type': pattern_type,
            'strength': strength,
            'trend': trend,
            'reliability': reliability,
            'body_size': body_size,
            'upper_shadow': upper_shadow,
            'lower_shadow': lower_shadow,
            'total_range': total_range
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if not 0 < self.params['body_ratio'] < 1:
            raise ValueError("Body ratio must be between 0 and 1")
        if not 0 < self.params['shadow_ratio'] < 1:
            raise ValueError("Shadow ratio must be between 0 and 1")
        return True 