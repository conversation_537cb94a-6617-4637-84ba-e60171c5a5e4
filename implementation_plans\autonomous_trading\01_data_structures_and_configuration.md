# Phase 1: Data Structures and Configuration Management

## Overview

This phase focuses on enhancing the data structures and configuration management for the autonomous trading system to support all subsequent improvements. This foundation will ensure that new features can be properly configured, stored, and managed.

## Current Implementation

Currently, the Garuda-Algo autonomous trading system uses the following configuration structure in `backend/api/autonomous.py`:

```python
AUTONOMOUS_CONFIG = {
    "symbols": ["EURUSD", "GBPUSD", "USDJPY", "XAUUSD"],
    "timeframes": ["M1", "M5", "M15", "M30", "H1", "H4", "D1", "W1", "MN1"],
    "scan_interval": 300,
    "min_confidence": 60,
    "max_trades": 5,
    "allowed_styles": ["Scalping", "Short-term", "Swing", "Position"],
    "allow_multiple_positions": False,
    "risk_percent": 1.0
}
```

Configuration is currently stored in-memory with saving/loading to a JSON file.

## Implementation Details

### 1. Enhanced Configuration Structure

```python
AUTONOMOUS_CONFIG = {
    # Existing fields
    "symbols": ["EURUSD", "GBPUSD", "USDJPY", "XAUUSD"],
    "timeframes": ["M1", "M5", "M15", "M30", "H1", "H4", "D1", "W1", "MN1"],
    "scan_interval": 300,
    "min_confidence": 60,
    "max_trades": 5,
    "allowed_styles": ["Scalping", "Short-term", "Swing", "Position"],
    "allow_multiple_positions": False,
    "risk_percent": 1.0,
    
    # New fields - Time Filters
    "time_filters": {
        "enabled": False,
        "trading_sessions": [
            {
                "name": "Asian",
                "enabled": True,
                "start_hour": 0,
                "start_minute": 0,
                "end_hour": 9,
                "end_minute": 0
            },
            {
                "name": "London",
                "enabled": True,
                "start_hour": 8,
                "start_minute": 0,
                "end_hour": 16,
                "end_minute": 0
            },
            {
                "name": "New York",
                "enabled": True,
                "start_hour": 13,
                "start_minute": 0,
                "end_hour": 22,
                "end_minute": 0
            }
        ],
        "time_zone": "UTC",
        "non_trading_days": ["Saturday", "Sunday"]
    },
    
    # New fields - Risk Management
    "risk_management": {
        "max_daily_risk": 5.0,          # Maximum risk percentage per day
        "max_risk_per_symbol": 2.0,     # Maximum risk percentage per symbol
        "reduce_risk_after_loss": True, # Reduce risk after consecutive losses
        "risk_reduction_factor": 0.5,   # Factor to reduce risk by after losses
        "max_consecutive_losses": 3,    # Number of consecutive losses before risk reduction
        "hedging_enabled": False,       # Enable hedging for risk management
        "close_trades_on_reversal": True, # Close trades when market shows reversal signals
        "reversal_confirmation_level": 70, # Confidence level required for reversal confirmation
        "trailing_stop_enabled": False, # Enable trailing stops for profitable positions
        "trailing_stop_activation": 0.5, # Profit level (% of risk) to activate trailing stop
        "risk_reward_min_ratio": 1.5    # Minimum risk-reward ratio for trade entry
    },
    
    # New fields - Market Condition Handling
    "market_condition": {
        "volatility_filter_enabled": True, # Avoid trading during extreme volatility
        "volatility_threshold": 2.0,      # Relative volatility threshold (e.g., 2x average)
        "trend_filter_enabled": True,     # Only trade in direction of main trend
        "trend_timeframe": "H4",         # Timeframe to use for trend determination
        "ranging_market_filter": True,    # Adjust parameters in ranging markets
        "correlation_filter": False,      # Avoid over-exposure to correlated instruments
        "max_correlation_threshold": 0.7, # Maximum correlation coefficient for trading
        "adapt_to_spread": True          # Adjust SL/TP based on current spread
    },
    
    # New fields - Optimized Strategy Settings
    "strategy_settings": {
        "strategy_id_1": {
            "best_timeframes": ["M15", "H1"],  # Optimal timeframes
            "best_sessions": ["London"],       # Optimal sessions
            "risk_multiplier": 1.0,            # Risk multiplier for this strategy
            "active": True                     # Whether strategy is active
        },
        # Additional strategies follow same pattern
    },
    
    # New fields - Multi-Timeframe Confirmation
    "multi_timeframe": {
        "enabled": False,
        "confirmation_required": True,
        "confirmation_timeframes": ["M15", "H1", "H4"],
        "min_timeframes_agreement": 2,  # Minimum timeframes that must agree
        "higher_timeframe_weight": 1.5, # Weight for higher timeframe signals
        "lower_timeframe_entry": True   # Use lower timeframe for entry timing
    }
}
```

### 2. Configuration Validation Functions

Implement the following validation functions in `backend/api/autonomous.py`:

```python
def validate_time_filters(config):
    """Validate time filter configuration."""
    # Implementation details
    # Check that time sessions are valid
    # Validate time zone exists
    # etc.
    pass

def validate_risk_management(config):
    """Validate risk management configuration."""
    # Implementation details
    # Check percentage values are within range
    # etc.
    pass

def validate_market_condition(config):
    """Validate market condition configuration."""
    # Implementation details
    pass

def validate_strategy_settings(config):
    """Validate strategy settings configuration."""
    # Implementation details
    pass

def validate_multi_timeframe(config):
    """Validate multi-timeframe configuration."""
    # Implementation details
    pass

def validate_configuration(config):
    """Validate the entire configuration."""
    # Call individual validation functions
    # Return True/False with error messages
    pass
```

### 3. Configuration Storage and Retrieval

Enhance the current configuration management system:

```python
def load_configuration():
    """Load configuration from JSON file."""
    # Implementation details
    # Handle defaults for new fields if file contains outdated format
    # Upgrade old configurations
    pass

def save_configuration(config):
    """Save configuration to JSON file."""
    # Implementation details
    # Validate before saving
    # Handle versioning/backups
    pass
```

### 4. Strategy Default Parameter Repository

Create a repository of optimized default parameters for each strategy type:

```python
# In a new file: backend/strategy_defaults.py
STRATEGY_DEFAULTS = {
    "scalping_momentum": {
        # Detailed strategy parameters
        "rsi_period": 14,
        "rsi_overbought": 70,
        "rsi_oversold": 30,
        "macd_fast": 12,
        "macd_slow": 26,
        "macd_signal": 9,
        "best_timeframes": ["M5", "M15"],
        "best_sessions": ["London", "New York"],
        "min_risk_reward": 1.5,
        # etc.
    },
    # Other strategies follow the same pattern
}

# Function to get optimized parameters based on market conditions
def get_optimized_parameters(strategy_id, market_condition="normal"):
    """Get optimized parameters for a strategy based on market condition."""
    # Implementation details - return adjusted parameters based on market condition
    pass
```

### 5. Status Tracking and Metrics Structure

Implement enhanced status and metrics tracking structures:

```python
# In a new file: backend/autonomous_metrics.py
class AutonomousMetrics:
    """Class to track and manage autonomous trading metrics."""
    
    def __init__(self):
        self.metrics = {
            "daily_risk_used": 0.0,
            "symbol_risk": {},  # Risk per symbol
            "drawdown": {
                "current": 0.0,
                "max": 0.0,
                "start_balance": 0.0,
                "current_balance": 0.0
            },
            "positions": {
                "total": 0,
                "per_symbol": {},
                "per_strategy": {}
            },
            "performance": {
                "win_count": 0,
                "loss_count": 0,
                "consecutive_wins": 0,
                "consecutive_losses": 0,
                "total_profit": 0.0,
                "total_loss": 0.0,
                "per_symbol": {},
                "per_strategy": {}
            }
        }
        self.trade_log = []
        
    def update_metrics(self, account_info, positions):
        """Update metrics based on current account and positions."""
        # Implementation details
        pass
        
    def log_trade(self, trade_info):
        """Log a trade and update metrics."""
        # Implementation details
        pass
        
    def get_daily_risk(self):
        """Get daily risk currently used."""
        # Implementation details
        pass
        
    def get_symbol_risk(self, symbol):
        """Get risk currently allocated to a symbol."""
        # Implementation details
        pass
        
    def reset_daily_metrics(self):
        """Reset daily metrics at start of new trading day."""
        # Implementation details
        pass
```

## Files to Modify/Create

1. **backend/api/autonomous.py**:
   - Update `AUTONOMOUS_CONFIG` structure
   - Add validation functions
   - Enhance configuration loading/saving

2. **backend/autonomous_trader.py**:
   - Update to use new configuration structure
   - Add support for new status tracking

3. **backend/strategy_defaults.py** (new):
   - Implement optimized strategy parameters
   - Create methods to get parameters based on conditions

4. **backend/autonomous_metrics.py** (new):
   - Implement metrics tracking class
   - Add methods for risk calculation and reporting
   - Implement performance tracking

5. **backend/models/autonomous_config.py** (new, optional):
   - Create formal models for configuration data
   - Type validation and defaults

## Testing Plan

1. **Configuration Validation**:
   - Test validation against valid and invalid configurations
   - Check handling of missing fields and defaults

2. **Storage and Retrieval**:
   - Test saving and loading configuration
   - Verify backward compatibility with older configs

3. **Metrics Calculation**:
   - Test risk calculation methods
   - Verify performance tracking

## Acceptance Criteria

- All new configuration fields can be saved and loaded
- Configuration validation correctly identifies issues
- Strategy default parameters are accessible
- Metrics tracking provides accurate risk and performance data
- Existing functionality continues to work with new data structures
