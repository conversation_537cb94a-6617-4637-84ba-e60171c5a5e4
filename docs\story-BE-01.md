# Story BE-01: Implement Account Balance API (US-3)

## Description
As a trader, I want to view my MT5 account balance so I can monitor my trading capital.

## Acceptance Criteria
1. GET /api/account/balance endpoint returns:
   - Current balance (float)
   - Equity (float)
   - Margin (float)
   - Free margin (float)
   - Margin level (float)
2. Endpoint validates MT5 connection status
3. Returns 401 if credentials are invalid
4. Returns 503 if MT5 server is unreachable
5. Frontend receives updates every 30 seconds via polling

## Technical Implementation
1. Add new route to market_data.py:
```python
@market_data_bp.route('/account/balance', methods=['GET'])
def get_account_balance():
    try:
        mt5 = MT5Integration()
        if not mt5.initialize(settings_path="backend/mt5_settings.json"):
            return jsonify({'error': 'MT5 connection failed'}), 503
            
        balance_info = mt5.get_account_balance()
        return jsonify(balance_info), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
```

2. Extend MT5Integration class with:
```python
def get_account_balance(self):
    """Fetch current account balance information"""
    account_info = mt5.account_info()
    return {
        'balance': account_info.balance,
        'equity': account_info.equity,
        'margin': account_info.margin,
        'free_margin': account_info.margin_free,
        'margin_level': account_info.margin_level
    }
```

## Test Cases
1. Test with valid credentials:
   - Returns 200 with balance data
2. Test with invalid credentials:
   - Returns 401 unauthorized
3. Test when MT5 server is down:
   - Returns 503 service unavailable
4. Test response format:
   - Contains all required fields
   - Numeric values are floats

## Dependencies
1. Requires US-1 (Credential Management) to be completed
2. Uses existing MT5Integration class
3. Frontend depends on ConnectionTab component

## Definition of Done
1. API endpoint implemented and tested
2. Documentation updated in docs/api.md
3. Integration tests added to backend/tests/
4. Frontend team notified for component development
5. Code reviewed and merged to main