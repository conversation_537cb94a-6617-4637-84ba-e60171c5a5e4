<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GarudaAlgo | Advanced MT5 Trading Platform</title>
  <meta name="description" content="GarudaAlgo - Navigate the markets with algorithmic intelligence & strategic precision. Dynamic signals, professional UI, and easy installation.">
  <link rel="icon" href="app_icon.ico">
  <link rel="stylesheet" href="style.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</head>
<body>
  <header class="header">
    <div class="container">
      <nav class="navbar">
        <div class="logo">
          <img src="app_icon.png" alt="GarudaAlgo Logo">
          <h1>GarudaAlgo</h1>
        </div>
        <div class="nav-right">
          <div class="language-selector">
            <button id="lang-en" class="active">EN</button>
            <button id="lang-id">ID</button>
          </div>
          <div class="nav-buttons">
            <a href="login.html" class="btn-secondary" data-lang-key="login-btn">Login</a>
            <a href="register.html" class="btn-secondary" data-lang-key="register-btn">Register</a>
            <a href="#download" class="btn-primary" data-lang-key="download-now">Download Now</a>
          </div>
        </div>
      </nav>
    </div>
  </header>

  <section class="hero">
    <div class="hero-particles">
      <div class="particle"></div>
      <div class="particle"></div>
      <div class="particle"></div>
    </div>
    <div class="container hero-container">
      <div class="hero-content">
        <div class="hero-badge">
          <i class="fas fa-bolt"></i>
          <span data-lang-key="new-version">New Version 2.0</span>
        </div>
        <h1 class="hero-title" data-lang-key="hero-title">Navigate the Markets with <span class="gradient-text">Algorithmic Intelligence</span> & <span class="gradient-text">Strategic Precision</span></h1>
        <p class="hero-subtitle" data-lang-key="hero-subtitle">A sophisticated trading assistant that integrates with MT5, providing advanced market analysis, actionable signals, AI-driven autonomous trading, and comprehensive trade tracking.</p>
        <div class="hero-cta">
          <a href="#download" class="btn-primary" data-lang-key="get-free-trial">Get Free Trial <i class="fas fa-arrow-right btn-icon"></i></a>
          <a href="#features" class="btn-secondary" data-lang-key="explore-features">Explore Features <i class="fas fa-arrow-right btn-icon"></i></a>
        </div>
        <div class="special-offer">
          <div class="offer-badge">
            <span data-lang-key="special-offer">SPECIAL OFFER</span>
          </div>
          <p data-lang-key="limited-offer">Limited-time offer: First 300 users get a free trial!</p>
        </div>
      </div>
      <div class="hero-image">
        <img src="garudaalgosnapshot/garuda-algo-01.png" alt="GarudaAlgo Dashboard" class="main-screenshot">
        <div class="floating-card card-1">
          <i class="fas fa-robot"></i>
          <div class="card-content">
            <h3 data-lang-key="automated-trading">Automated Trading</h3>
            <p data-lang-key="automated-trading-desc">Set & forget</p>
          </div>
        </div>
        <div class="floating-card card-2">
          <i class="fas fa-chart-line"></i>
          <div class="card-content">
            <h3 data-lang-key="dynamic-signals">Dynamic Signals</h3>
            <p data-lang-key="signals-desc">86-95% confidence</p>
          </div>
        </div>
      </div>
    </div>
    <div class="hero-wave">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 220">
        <path fill="#121212" fill-opacity="1" d="M0,96L48,90.7C96,85,192,75,288,90.7C384,107,480,149,576,154.7C672,160,768,128,864,117.3C960,107,1056,117,1152,138.7C1248,160,1344,192,1392,208L1440,224L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
      </svg>
    </div>
  </section>

  <section class="benefits" id="why-algo">
    <div class="container">
      <div class="section-header">
        <div class="section-tag">
          <i class="fas fa-rocket"></i>
          <span data-lang-key="benefits">Benefits</span>
        </div>
        <h2 data-lang-key="why-algo-trading">Why Algorithmic & Autonomous Trading?</h2>
        <p data-lang-key="why-algo-desc">In today's fast-paced markets, manual trading presents several challenges that algorithmic solutions can overcome</p>
      </div>
      <div class="benefits-grid">
        <div class="benefit-card">
          <div class="benefit-icon">
            <i class="fas fa-brain"></i>
          </div>
          <h3 data-lang-key="emotional-control">Emotional Decisions</h3>
          <p data-lang-key="emotional-control-desc">Fear, greed, and other emotions can lead to impulsive and irrational trading choices. Algorithms provide objective analysis free from emotional interference.</p>
        </div>
        <div class="benefit-card">
          <div class="benefit-icon">
            <i class="fas fa-balance-scale"></i>
          </div>
          <h3 data-lang-key="human-bias">Human Bias</h3>
          <p data-lang-key="human-bias-desc">Preconceived notions or recent experiences can cloud judgment. Garuda Algo V2 enables systematic execution and consistent application of your trading strategies.</p>
        </div>
        <div class="benefit-card">
          <div class="benefit-icon">
            <i class="fas fa-bolt"></i>
          </div>
          <h3 data-lang-key="execution-speed">Execution Speed</h3>
          <p data-lang-key="execution-speed-desc">Manual execution can be too slow for fleeting opportunities. Our system reacts to market conditions and executes trades faster than humanly possible.</p>
        </div>
        <div class="benefit-card">
          <div class="benefit-icon">
            <i class="fas fa-clock"></i>
          </div>
          <h3 data-lang-key="time-commitment">Time Commitment</h3>
          <p data-lang-key="time-commitment-desc">Constantly monitoring markets requires significant time. Garuda Algo V2 frees up your time while monitoring markets and managing trades based on your strategies.</p>
        </div>
      </div>
    </div>
  </section>

  <section class="features" id="features">
    <div class="container">
      <div class="section-header">
        <div class="section-tag">
          <i class="fas fa-star"></i>
          <span data-lang-key="features-tag">Features</span>
        </div>
        <h2 data-lang-key="key-features">Key Features</h2>
        <p data-lang-key="what-makes-different">What makes GarudaAlgo different from other trading tools</p>
      </div>
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-img-wrapper">
            <img src="garudaalgosnapshot/garuda-algo-02.png" alt="Easy Installation" class="feature-img">
          </div>
          <div class="feature-content">
            <div class="feature-number">01</div>
            <div class="feature-tag">
              <i class="fas fa-download"></i>
              <span data-lang-key="installation">Installation</span>
            </div>
            <h3 data-lang-key="easy-installation">Easy Installation</h3>
            <p data-lang-key="easy-installation-desc">Download the standalone executable and start trading immediately. No complex setups or plugins required.</p>
          </div>
        </div>
        <div class="feature-card reversed">
          <div class="feature-content">
            <div class="feature-number">02</div>
            <div class="feature-tag">
              <i class="fas fa-chart-pie"></i>
              <span data-lang-key="confidence">Confidence</span>
            </div>
            <h3 data-lang-key="dynamic-confidence">Dynamic Confidence Scoring</h3>
            <p data-lang-key="dynamic-confidence-desc">Our algorithm calculates signal confidence (86-95%) based on real market data, not hardcoded values, giving you reliable insights in any market condition.</p>
            <div class="feature-list">
              <div class="feature-list-item">
                <div class="feature-list-icon"><i class="fas fa-check-circle"></i></div>
                <div class="feature-list-text" data-lang-key="real-time-data">Real-time market data analysis</div>
              </div>
              <div class="feature-list-item">
                <div class="feature-list-icon"><i class="fas fa-check-circle"></i></div>
                <div class="feature-list-text" data-lang-key="adaptive-algo">Adaptive algorithm that learns</div>
              </div>
            </div>
          </div>
          <div class="feature-img-wrapper">
            <img src="garudaalgosnapshot/garuda-algo-03.png" alt="Dynamic Confidence Scoring" class="feature-img">
          </div>
        </div>
        <div class="feature-card">
          <div class="feature-img-wrapper">
            <img src="garudaalgosnapshot/garuda-algo-04.png" alt="Professional UI" class="feature-img">
          </div>
          <div class="feature-content">
            <div class="feature-number">03</div>
            <div class="feature-tag">
              <i class="fas fa-desktop"></i>
              <span data-lang-key="interface">Interface</span>
            </div>
            <h3 data-lang-key="professional-ui">Professional UI/UX</h3>
            <p data-lang-key="professional-ui-desc">Beautiful, animated interface with dark mode, persistent settings, and intuitive navigation for both beginners and experts.</p>
          </div>
        </div>
        <div class="feature-card reversed">
          <div class="feature-content">
            <div class="feature-number">04</div>
            <div class="feature-tag">
              <i class="fas fa-chart-line"></i>
              <span data-lang-key="analysis">Analysis</span>
            </div>
            <h3 data-lang-key="multi-timeframe">Multi-Timeframe Analysis</h3>
            <p data-lang-key="multi-timeframe-desc">Analyze from M1 to MN1 timeframes with perfect reliability—including the notorious monthly (MN1) timeframe that most platforms struggle with.</p>
          </div>
          <div class="feature-img-wrapper">
            <img src="garudaalgosnapshot/garuda-algo-05.png" alt="Multi-Timeframe Analysis" class="feature-img">
          </div>
        </div>
        <div class="feature-card">
          <div class="feature-img-wrapper">
            <img src="garudaalgosnapshot/garuda-algo-06.png" alt="Automated Trading" class="feature-img">
          </div>
          <div class="feature-content">
            <div class="feature-number">05</div>
            <div class="feature-tag">
              <i class="fas fa-robot"></i>
              <span data-lang-key="automation">Automation</span>
            </div>
            <h3 data-lang-key="automated-strategies">Automated Strategies</h3>
            <p data-lang-key="automated-strategies-desc">Set up your strategy once and let GarudaAlgo handle the rest—automated entries, exits, and risk management.</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section class="testimonials">
    <div class="container">
      <div class="section-header">
        <h2 data-lang-key="what-traders-say">What Traders Say</h2>
        <p data-lang-key="testimonials-desc">Hear from traders who've transformed their trading with GarudaAlgo</p>
      </div>
      <div class="testimonial-slider" id="testimonial-slider">
        <div class="testimonial-slide active">
          <div class="testimonial-card">
            <div class="testimonial-rating">
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
            </div>
            <p class="testimonial-text" data-lang-key="testimonial-1">"GarudaAlgo has transformed my trading. Setup took literally 2 minutes, and the dynamic confidence scores have improved my win rate by 32%. Worth every penny!"</p>
            <div class="testimonial-author">
              <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Alex">
              <div>
                <h4>Alex T.</h4>
                <p data-lang-key="forex-trader">Forex Trader</p>
              </div>
            </div>
          </div>
        </div>
        <div class="testimonial-slide">
          <div class="testimonial-card">
            <div class="testimonial-rating">
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
            </div>
            <p class="testimonial-text" data-lang-key="testimonial-2">"As a busy professional, I don't have time to stare at charts all day. GarudaAlgo's automation has been a game-changer. I set my strategies once and check results later."</p>
            <div class="testimonial-author">
              <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Sarah">
              <div>
                <h4>Sarah K.</h4>
                <p data-lang-key="part-time-trader">Part-time Trader</p>
              </div>
            </div>
          </div>
        </div>
        <div class="testimonial-slide">
          <div class="testimonial-card">
            <div class="testimonial-rating">
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
              <i class="fas fa-star"></i>
            </div>
            <p class="testimonial-text" data-lang-key="testimonial-3">"The persistent settings and monthly timeframe analysis sold me. No other tool I've tried handles MN1 charts this well. GarudaAlgo is now my go-to for both short and long-term trading."</p>
            <div class="testimonial-author">
              <img src="https://randomuser.me/api/portraits/men/62.jpg" alt="Michael">
              <div>
                <h4>Michael J.</h4>
                <p data-lang-key="swing-trader">Swing Trader</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="testimonial-controls">
        <button id="prev-testimonial"><i class="fas fa-chevron-left"></i></button>
        <div class="testimonial-dots">
          <span class="dot active" data-index="0"></span>
          <span class="dot" data-index="1"></span>
          <span class="dot" data-index="2"></span>
        </div>
        <button id="next-testimonial"><i class="fas fa-chevron-right"></i></button>
      </div>
    </div>
  </section>

  <section class="comparison">
    <div class="container">
      <div class="section-header">
        <h2 data-lang-key="manual-vs-algo">Manual vs. Algorithmic Trading</h2>
        <p data-lang-key="see-difference">See the difference in real numbers</p>
      </div>
      <div class="comparison-table">
        <table>
          <thead>
            <tr>
              <th data-lang-key="metrics">Metrics</th>
              <th data-lang-key="manual-trading">Manual Trading</th>
              <th data-lang-key="with-garudaalgo">With GarudaAlgo</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td data-lang-key="time-spent">Time Spent Daily</td>
              <td>4-8 hours</td>
              <td><15 minutes</td>
            </tr>
            <tr>
              <td data-lang-key="emotional-decisions">Emotional Decisions</td>
              <td data-lang-key="high">High</td>
              <td data-lang-key="none">None</td>
            </tr>
            <tr>
              <td data-lang-key="missed-opportunities">Missed Opportunities</td>
              <td data-lang-key="frequent">Frequent</td>
              <td data-lang-key="rare">Rare</td>
            </tr>
            <tr>
              <td data-lang-key="analysis-depth">Analysis Depth</td>
              <td data-lang-key="limited-by-time">Limited by time</td>
              <td data-lang-key="comprehensive">Comprehensive</td>
            </tr>
            <tr>
              <td data-lang-key="consistency">Consistency</td>
              <td data-lang-key="variable">Variable</td>
              <td data-lang-key="high-consistency">High</td>
            </tr>
            <tr>
              <td data-lang-key="multi-timeframe-capability">Multi-Timeframe Capability</td>
              <td data-lang-key="challenging">Challenging</td>
              <td data-lang-key="seamless">Seamless</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </section>

  <section class="how-it-works">
    <div class="container">
      <div class="section-header">
        <h2 data-lang-key="how-it-works">How It Works</h2>
        <p data-lang-key="simple-steps">3 simple steps to algorithmic trading mastery</p>
      </div>
      <div class="steps">
        <div class="step">
          <div class="step-number">1</div>
          <div class="step-content">
            <h3 data-lang-key="download-install">Download & Install</h3>
            <p data-lang-key="download-install-desc">Download the standalone executable and launch it. No complex setup required.</p>
          </div>
        </div>
        <div class="step-connector"></div>
        <div class="step">
          <div class="step-number">2</div>
          <div class="step-content">
            <h3 data-lang-key="connect-customize">Connect & Customize</h3>
            <p data-lang-key="connect-customize-desc">Connect to your MT5 account and customize your trading preferences. Your settings are saved automatically.</p>
          </div>
        </div>
        <div class="step-connector"></div>
        <div class="step">
          <div class="step-number">3</div>
          <div class="step-content">
            <h3 data-lang-key="trade-automate">Trade & Automate</h3>
            <p data-lang-key="trade-automate-desc">Start trading with confidence or set up your automated strategies and let GarudaAlgo do the work.</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section class="faq" id="faq">
    <div class="container">
      <div class="section-header">
        <h2 data-lang-key="faq">Frequently Asked Questions</h2>
        <p data-lang-key="faq-desc">Everything you need to know about GarudaAlgo</p>
      </div>
      <div class="faq-grid">
        <div class="faq-item">
          <div class="faq-question">
            <h3 data-lang-key="faq-q1">Do I need coding experience to use GarudaAlgo?</h3>
            <span class="faq-icon"><i class="fas fa-chevron-down"></i></span>
          </div>
          <div class="faq-answer">
            <p data-lang-key="faq-a1">Not at all! GarudaAlgo is designed with a user-friendly interface that requires zero coding knowledge. All trading strategies and settings can be configured through our intuitive UI.</p>
          </div>
        </div>
        <div class="faq-item">
          <div class="faq-question">
            <h3 data-lang-key="faq-q2">How accurate are the trading signals?</h3>
            <span class="faq-icon"><i class="fas fa-chevron-down"></i></span>
          </div>
          <div class="faq-answer">
            <p data-lang-key="faq-a2">GarudaAlgo's dynamic confidence scoring system provides signals with 86-95% confidence based on real-time market data analysis. Each signal comes with a confidence score so you can make informed decisions.</p>
          </div>
        </div>
        <div class="faq-item">
          <div class="faq-question">
            <h3 data-lang-key="faq-q3">Can I use GarudaAlgo with my existing MT5 account?</h3>
            <span class="faq-icon"><i class="fas fa-chevron-down"></i></span>
          </div>
          <div class="faq-answer">
            <p data-lang-key="faq-a3">Yes! GarudaAlgo is fully compatible with any MetaTrader 5 account. Simply connect your existing MT5 account through our app, and you're ready to start using our advanced algorithmic trading features.</p>
          </div>
        </div>
        <div class="faq-item">
          <div class="faq-question">
            <h3 data-lang-key="faq-q4">What currency pairs and timeframes are supported?</h3>
            <span class="faq-icon"><i class="fas fa-chevron-down"></i></span>
          </div>
          <div class="faq-answer">
            <p data-lang-key="faq-a4">GarudaAlgo supports all currency pairs available in your MT5 account. Our multi-timeframe analysis works across all timeframes from M1 (1-minute) to MN1 (monthly), with exceptional reliability even on the challenging monthly timeframe.</p>
          </div>
        </div>
        <div class="faq-item">
          <div class="faq-question">
            <h3 data-lang-key="faq-q5">Is there a free trial available?</h3>
            <span class="faq-icon"><i class="fas fa-chevron-down"></i></span>
          </div>
          <div class="faq-answer">
            <p data-lang-key="faq-a5">Yes! We're currently offering a free trial for the first 300 users who sign up. This gives you full access to all features so you can experience the power of GarudaAlgo before committing to a subscription.</p>
          </div>
        </div>
        <div class="faq-item">
          <div class="faq-question">
            <h3 data-lang-key="faq-q6">What kind of support do you offer?</h3>
            <span class="faq-icon"><i class="fas fa-chevron-down"></i></span>
          </div>
          <div class="faq-answer">
            <p data-lang-key="faq-a6">We offer different levels of support based on your subscription plan. All users receive technical support via email, while Pro and Elite users get priority support with faster response times. Elite users also receive VIP support with direct access to our development team.</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section class="pricing">
    <div class="container">
      <div class="section-header">
        <h2 data-lang-key="pricing">Pricing</h2>
        <p data-lang-key="transparent-pricing">Transparent pricing with no hidden fees</p>
      </div>
      <div class="pricing-cards">
        <div class="pricing-card popular">
          <div class="popular-badge" data-lang-key="most-popular">MOST POPULAR</div>
          <div class="pricing-header">
            <h3 data-lang-key="pro-plan">Pro Plan</h3>
            <div class="pricing-price">
              <span class="currency">$</span>
              <span class="amount">49</span>
              <span class="period" data-lang-key="per-month">/month</span>
            </div>
            <p data-lang-key="billed-monthly">Billed monthly</p>
          </div>
          <div class="pricing-features">
            <ul>
              <li><i class="fas fa-check"></i> <span data-lang-key="feature-dynamic-confidence">Dynamic confidence scoring</span></li>
              <li><i class="fas fa-check"></i> <span data-lang-key="feature-all-timeframes">All timeframes (including MN1)</span></li>
              <li><i class="fas fa-check"></i> <span data-lang-key="feature-automated-trading">Automated trading</span></li>
              <li><i class="fas fa-check"></i> <span data-lang-key="feature-persistent-settings">Persistent settings</span></li>
              <li><i class="fas fa-check"></i> <span data-lang-key="feature-priority-support">Priority support</span></li>
              <li><i class="fas fa-check"></i> <span data-lang-key="feature-unlimited-symbols">Unlimited symbols</span></li>
            </ul>
          </div>
          <a href="#download" class="btn-primary full-width" data-lang-key="get-started">Get Started</a>
        </div>
        <div class="pricing-card">
          <div class="pricing-header">
            <h3 data-lang-key="basic-plan">Basic Plan</h3>
            <div class="pricing-price">
              <span class="currency">$</span>
              <span class="amount">29</span>
              <span class="period" data-lang-key="per-month">/month</span>
            </div>
            <p data-lang-key="billed-monthly">Billed monthly</p>
          </div>
          <div class="pricing-features">
            <ul>
              <li><i class="fas fa-check"></i> <span data-lang-key="feature-dynamic-confidence">Dynamic confidence scoring</span></li>
              <li><i class="fas fa-check"></i> <span data-lang-key="feature-all-timeframes">All timeframes (including MN1)</span></li>
              <li><i class="fas fa-check"></i> <span data-lang-key="feature-manual-trading">Manual trading signals</span></li>
              <li><i class="fas fa-check"></i> <span data-lang-key="feature-persistent-settings">Persistent settings</span></li>
              <li><i class="fas fa-check"></i> <span data-lang-key="feature-standard-support">Standard support</span></li>
              <li><i class="fas fa-check"></i> <span data-lang-key="feature-limited-symbols">Limited symbols (5)</span></li>
            </ul>
          </div>
          <a href="#download" class="btn-secondary full-width" data-lang-key="get-started">Get Started</a>
        </div>
        <div class="pricing-card annual-savings">
          <div class="pricing-header">
            <h3 data-lang-key="elite-plan">Elite Plan</h3>
            <div class="pricing-price">
              <span class="currency">$</span>
              <span class="amount">99</span>
              <span class="period" data-lang-key="per-month">/month</span>
            </div>
            <p data-lang-key="billed-monthly">Billed monthly</p>
          </div>
          <div class="pricing-features">
            <ul>
              <li><i class="fas fa-check"></i> <span data-lang-key="feature-everything-in-pro">Everything in Pro</span></li>
              <li><i class="fas fa-check"></i> <span data-lang-key="feature-advanced-strategies">Advanced strategy templates</span></li>
              <li><i class="fas fa-check"></i> <span data-lang-key="feature-portfolio-management">Portfolio management</span></li>
              <li><i class="fas fa-check"></i> <span data-lang-key="feature-vip-support">VIP support</span></li>
              <li><i class="fas fa-check"></i> <span data-lang-key="feature-strategy-backtesting">Strategy backtesting</span></li>
              <li><i class="fas fa-check"></i> <span data-lang-key="feature-early-updates">Early access to updates</span></li>
            </ul>
          </div>
          <a href="#download" class="btn-secondary full-width" data-lang-key="get-started">Get Started</a>
        </div>
      </div>
    </div>
  </section>

  <section class="cta" id="download">
    <div class="cta-particles">
      <div class="cta-particle"></div>
      <div class="cta-particle"></div>
    </div>
    <div class="container">
      <div class="cta-content">
        <h2 data-lang-key="ready-to-transform">Ready to Transform Your Trading?</h2>
        <p data-lang-key="free-trial-offer">Start your free trial today. Limited to the first 300 users!</p>
        <div class="cta-buttons">
          <a href="#" class="btn-cta" id="download-btn" data-lang-key="download-now">Download Now <i class="fas fa-download btn-icon"></i></a>
          <a href="#faq" class="btn-secondary" data-lang-key="have-questions">Have Questions? <i class="fas fa-question-circle btn-icon"></i></a>
        </div>
        <p class="small-text" data-lang-key="compatibility-note">*Compatible with Windows 7/10/11 with MetaTrader 5 installed</p>
      </div>
    </div>
  </section>

  <footer class="footer">
    <div class="container">
      <div class="footer-grid">
        <div class="footer-col">
          <div class="footer-logo">
            <img src="app_icon.png" alt="GarudaAlgo Logo">
            <h2>GarudaAlgo</h2>
          </div>
          <p data-lang-key="footer-tagline">Navigate the markets with algorithmic intelligence & strategic precision.</p>
        </div>
        <div class="footer-col">
          <h3 data-lang-key="quick-links">Quick Links</h3>
          <ul>
            <li><a href="#features" data-lang-key="features">Features</a></li>
            <li><a href="#why-algo" data-lang-key="why-algo-trading-short">Why Algo Trading</a></li>
            <li><a href="#faq" data-lang-key="faq-link">FAQ</a></li>
            <li><a href="#download" data-lang-key="download">Download</a></li>
            <li><a href="#" data-lang-key="support">Support</a></li>
          </ul>
        </div>
        <div class="footer-col">
          <h3 data-lang-key="legal">Legal</h3>
          <ul>
            <li><a href="#" data-lang-key="terms">Terms & Conditions</a></li>
            <li><a href="#" data-lang-key="privacy">Privacy Policy</a></li>
            <li><a href="#" data-lang-key="refund">Refund Policy</a></li>
          </ul>
        </div>
        <div class="footer-col">
          <h3 data-lang-key="contact">Contact</h3>
          <ul>
            <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
            <li class="social-icons">
              <a href="#"><i class="fab fa-twitter"></i></a>
              <a href="#"><i class="fab fa-facebook"></i></a>
              <a href="#"><i class="fab fa-instagram"></i></a>
              <a href="#"><i class="fab fa-youtube"></i></a>
            </li>
          </ul>
        </div>
      </div>
      <div class="copyright">
        <p>&copy; 2025 GarudaAlgo. <span data-lang-key="all-rights">All rights reserved.</span></p>
      </div>
    </div>
  </footer>

  <div id="download-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal">&times;</span>
      <h2 data-lang-key="download-garudaalgo">Download GarudaAlgo</h2>
      <div class="download-options">
        <a href="#" class="download-button windows">
          <i class="fab fa-windows"></i>
          <span>
            <strong data-lang-key="windows-64">Windows 64-bit</strong>
            <small>GarudaAlgo_Setup.exe</small>
          </span>
        </a>
      </div>
      <div class="download-note">
        <p data-lang-key="after-downloading">After downloading, run the installer and follow the on-screen instructions.</p>
        <p><strong data-lang-key="need-help">Need help?</strong> <a href="#" data-lang-key="installation-guide">View the installation guide</a></p>
      </div>
    </div>
  </div>

  <script src="firebase-config.js"></script>
  <script src="locales.json"></script>
  <script src="script.js"></script>
</body>
</html>
