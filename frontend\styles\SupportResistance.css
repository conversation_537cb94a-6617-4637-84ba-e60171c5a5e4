/* Support/Resistance Specific Styles */

/* Price Bar Styling */
.price-bar-container {
  margin: 15px 0;
  padding: 0 10px;
}

.price-bar-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.price-bar-title {
  font-weight: bold;
  font-size: 0.9rem;
}

.current-price {
  font-size: 0.9rem;
  font-weight: bold;
  color: #ffa500;
}

.price-bar-gradient {
  position: relative;
  height: 10px;
  background-color: #2a2a3a;
  border-radius: 5px;
  margin-bottom: 5px;
  overflow: hidden;
}

.price-bar-gradient-bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, #2a4858 0%, #2a2a3a 50%, #582a2a 100%);
}

.price-bar-indicator {
  position: relative;
  height: 20px;
  background-color: #2a2a3a;
  border-radius: 10px;
  margin-bottom: 15px;
  overflow: hidden;
}

.price-position-indicator {
  position: absolute;
  top: 0;
  width: 4px;
  height: 100%;
  background-color: #ffa500;
  transform: translateX(-50%);
  z-index: 2;
}

.price-bar-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
}

.support-label {
  color: #00cc00;
}

.resistance-label {
  color: #ff4d4d;
  text-align: right;
}

.current-price-label {
  text-align: center;
  color: #ffa500;
}

/* SR Details Styling */
.sr-details-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sr-section {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.sr-section:hover {
  background-color: rgba(0, 0, 0, 0.3);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.sr-section-title {
  display: block;
  margin-bottom: 10px;
  font-weight: 600;
  font-size: 1rem;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sr-section-title.resistance {
  color: #ff4d4d;
}

.sr-section-title.support {
  color: #00cc00;
}

.sr-section-title.cluster {
  color: #e6e600;
}

.sr-section-title.psych {
  color: #00bfff;
}

.sr-section-title.supply {
  color: #ff6b6b;
}

.sr-section-title.demand {
  color: #4ecdc4;
}

.sr-list {
  margin-top: 8px;
  padding-left: 10px;
  max-height: 120px;
  overflow-y: auto;
  list-style-type: none;
}

.sr-list-item {
  margin-bottom: 6px;
  font-size: 0.9rem;
  padding: 4px 6px;
  border-radius: 4px;
  border-bottom: 1px dotted rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sr-list-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.sr-list-item.empty {
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

.sr-price {
  font-weight: 500;
  color: #ffffff;
}

.sr-source {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
}

.sr-zone-range {
  width: 100%;
  text-align: center;
  font-weight: 500;
}

.key-level {
  margin: 8px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.resistance-value {
  color: #ff4d4d;
  font-weight: 500;
}

.support-value {
  color: #00cc00;
  font-weight: 500;
}
