# Story FE-04: Display Account Info on Connection

## Description
Enhance the Connection Tab UI to display MT5 account information (Balance and Equity) when successfully connected. This story builds on the existing Connection Tab UI (FE-02) and integrates with the enhanced connection status API (BE-05) to show real-time account data.

## Technical Context
- Updates existing ConnectionTab component in `frontend/components/ConnectionTab.js`
- Consumes enhanced `/connection_status` endpoint from BE-05
- Part of US1 (Connect to MT5 Account) implementation
- Critical for providing account status visibility to users

## Dependencies
- FE-02 (Connection Tab UI) completed - enhancing existing component
- BE-05 (Enhanced Status API) completed - consuming new account info data

## Technical Details

### API Integration
- Poll `/connection_status` endpoint for account updates
- Expected response structure:
  ```json
  {
    "status": "connected",
    "account_info": {
      "balance": number,
      "equity": number
    }
  }
  ```
- Polling interval: Every 1 second when connected
- Stop polling when disconnected

### UI Requirements
1. **Account Info Display Area**
   - Located below the connection status indicator
   - Only visible when connection status is "connected"
   - Contains two read-only fields:
     * Balance
     * Equity
   - Values should be right-aligned
   - Use appropriate decimal places for currency values
   - Include currency symbol (if available from API)

2. **Visual Requirements**
   - Clear labels for each field
   - Proper spacing between fields
   - Consistent styling with other UI elements
   - Smooth transition when showing/hiding

## Acceptance Criteria

### 1. Data Display & Formatting
- [ ] Balance field displays the correct value from API
- [ ] Equity field displays the correct value from API
- [ ] Numbers formatted with 2 decimal places
- [ ] Values are right-aligned
- [ ] Labels are clear and properly aligned
- [ ] Display updates when values change

### 2. Visibility Control
- [ ] Account info area only visible when status is "connected"
- [ ] Area hidden when disconnected
- [ ] Smooth transition between visible/hidden states
- [ ] No visual glitches during state changes

### 3. API Integration
- [ ] Successfully retrieves data from enhanced status endpoint
- [ ] Handles API errors gracefully
- [ ] Implements proper polling mechanism
- [ ] Stops polling when disconnected
- [ ] Resumes polling when reconnected

### 4. Performance
- [ ] UI remains responsive during polling
- [ ] No visible lag when updating values
- [ ] Efficient DOM updates
- [ ] Proper cleanup when component unmounts

### 5. Error Handling
- [ ] Gracefully handles missing or null values
- [ ] Shows appropriate placeholder if data unavailable
- [ ] Maintains UI stability during API errors
- [ ] Logs errors appropriately

## Implementation Notes
1. Use existing styling patterns from FE-02
2. Implement proper cleanup for polling interval
3. Consider adding loading states for initial data fetch
4. Use appropriate number formatting utilities
5. Follow established error handling patterns

## Testing Requirements
- [ ] Unit tests for component rendering
- [ ] Tests for proper data formatting
- [ ] Tests for visibility toggling
- [ ] Tests for API integration
- [ ] Tests for error handling
- [ ] Update existing ConnectionTab.test.js

## UX Guidelines
- Keep display simple and clean
- Ensure values are easily readable
- Provide visual feedback for updates
- Maintain consistency with existing UI
- Consider color coding for significant changes

## Related Documents
- PRD Section 2.1: MT5 Connection Management
- Architecture Section 4.1: Frontend Components
- US1 from PRD: Connection User Story
- BE-05: Enhanced Status API specification
- FE-02: Connection Tab UI base implementation