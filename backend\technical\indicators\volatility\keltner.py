from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.analysis.technical.indicators.volatility.atr import ATRIndicator # Keltner uses ATR
from src.core.models.market_data import MarketData

class KeltnerChannelsIndicator(BaseIndicator):
    """Keltner Channels indicator."""

    def __init__(self, period: int = 20, atr_period: int = 10, atr_multiplier: float = 2.0,
                 ma_type: str = 'ema', source: str = 'close'):
        """
        Initialize Keltner Channels indicator.

        Args:
            period: The period for the central moving average line.
            atr_period: The period for the ATR calculation.
            atr_multiplier: The multiplier for the ATR value to determine band width.
            ma_type: The type of moving average for the central line ('sma' or 'ema').
            source: The price source for the central MA ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
        """
        super().__init__({
            'period': period,
            'atr_period': atr_period,
            'atr_multiplier': atr_multiplier,
            'ma_type': ma_type,
            'source': source
        })
        # Internal ATR indicator
        self._atr_indicator = ATRIndicator(period=atr_period, ma_type='sma') # Typically SMA for ATR in Keltner

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Keltner Channels values."""
        df = data.to_dataframe()
        if df.empty or len(df) < max(self.params['period'], self.params['atr_period']):
             return {'middle_band': np.array([]), 'upper_band': np.array([]), 'lower_band': np.array([])}

        period = self.params['period']
        atr_multiplier = self.params['atr_multiplier']
        ma_type = self.params['ma_type'].lower()
        source_col = self.params['source'].lower()

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Calculate Middle Band (Moving Average)
        if ma_type == 'sma':
            middle_band = source_data.rolling(window=period).mean()
        else: # ema
            middle_band = source_data.ewm(span=period, adjust=False).mean()

        # Calculate ATR
        atr_result = self._atr_indicator.calculate(data)
        atr = pd.Series(atr_result['atr'], index=df.index) # Ensure index alignment

        # Calculate Upper and Lower Bands
        upper_band = middle_band + (atr_multiplier * atr)
        lower_band = middle_band - (atr_multiplier * atr)

        self._values = {
            'middle_band': middle_band.values,
            'upper_band': upper_band.values,
            'lower_band': lower_band.values,
            'atr': atr.values # Return ATR used for calculation
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        valid_ma_types = ['sma', 'ema']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['ma_type'].lower() not in valid_ma_types:
            raise ValueError(f"MA type must be one of {valid_ma_types}")
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        if self.params['atr_period'] < 1:
            raise ValueError("ATR Period must be greater than 0")
        if self.params['atr_multiplier'] <= 0:
            raise ValueError("ATR Multiplier must be positive")
        return True