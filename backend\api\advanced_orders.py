"""API endpoints for advanced order types like OCO and OTO."""

import logging
from flask import Blueprint, jsonify, request, current_app
import MetaTrader5 as mt5
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

advanced_orders_bp = Blueprint('advanced_orders', __name__, url_prefix='/api/orders/advanced')


@advanced_orders_bp.route('/oco', methods=['POST'])
def place_oco_orders():
    """Place One-Cancels-Other (OCO) orders"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get parameters from request
        data = request.get_json(silent=True) or {}
        
        # Required parameters
        symbol = data.get('symbol')
        order_type = data.get('order_type')
        volume = data.get('volume')
        price1 = data.get('price1')
        price2 = data.get('price2')
        
        # Optional parameters
        sl = data.get('sl')
        tp = data.get('tp')
        comment = data.get('comment', '')
        
        # Validate required parameters
        if not all([symbol, order_type, volume, price1, price2]):
            return jsonify({'error': 'Missing required parameters'}), 400
        
        # Convert order_type string to MT5 constant
        order_type_map = {
            'BUY_LIMIT': mt5.ORDER_TYPE_BUY_LIMIT,
            'SELL_LIMIT': mt5.ORDER_TYPE_SELL_LIMIT,
            'BUY_STOP': mt5.ORDER_TYPE_BUY_STOP,
            'SELL_STOP': mt5.ORDER_TYPE_SELL_STOP
        }
        
        if order_type not in order_type_map:
            return jsonify({'error': f'Invalid order type. Must be one of: {list(order_type_map.keys())}'}), 400
        
        mt5_order_type = order_type_map[order_type]
        
        # Place OCO orders
        result = mt5_conn.trading.place_oco_orders(
            symbol=symbol,
            order_type=mt5_order_type,
            volume=float(volume),
            price1=float(price1),
            price2=float(price2),
            sl=float(sl) if sl is not None else None,
            tp=float(tp) if tp is not None else None,
            comment=comment
        )
        
        if not result['success']:
            return jsonify({'error': result['message']}), 400
        
        return jsonify(result)
    
    except Exception as e:
        logger.exception(f"Error placing OCO orders: {str(e)}")
        return jsonify({'error': str(e)}), 500


@advanced_orders_bp.route('/oto', methods=['POST'])
def place_oto_order():
    """Place One-Triggers-Other (OTO) order"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get parameters from request
        data = request.get_json(silent=True) or {}
        
        # Required parameters
        symbol = data.get('symbol')
        primary_type = data.get('primary_type')
        primary_volume = data.get('primary_volume')
        primary_price = data.get('primary_price')
        secondary_type = data.get('secondary_type')
        secondary_volume = data.get('secondary_volume')
        secondary_price = data.get('secondary_price')
        
        # Optional parameters
        primary_sl = data.get('primary_sl')
        primary_tp = data.get('primary_tp')
        secondary_sl = data.get('secondary_sl')
        secondary_tp = data.get('secondary_tp')
        comment = data.get('comment', '')
        
        # Validate required parameters
        if not all([symbol, primary_type, primary_volume, secondary_type, secondary_volume, secondary_price]):
            return jsonify({'error': 'Missing required parameters'}), 400
        
        # Convert order type strings to MT5 constants
        order_type_map = {
            'BUY': mt5.ORDER_TYPE_BUY,
            'SELL': mt5.ORDER_TYPE_SELL,
            'BUY_LIMIT': mt5.ORDER_TYPE_BUY_LIMIT,
            'SELL_LIMIT': mt5.ORDER_TYPE_SELL_LIMIT,
            'BUY_STOP': mt5.ORDER_TYPE_BUY_STOP,
            'SELL_STOP': mt5.ORDER_TYPE_SELL_STOP
        }
        
        if primary_type not in order_type_map or secondary_type not in order_type_map:
            return jsonify({'error': f'Invalid order type. Must be one of: {list(order_type_map.keys())}'}), 400
        
        mt5_primary_type = order_type_map[primary_type]
        mt5_secondary_type = order_type_map[secondary_type]
        
        # If primary is a market order (BUY/SELL), price can be 0
        if primary_type in ['BUY', 'SELL'] and primary_price is None:
            primary_price = 0.0
        elif primary_price is None:
            return jsonify({'error': 'Primary price is required for pending orders'}), 400
        
        # Place OTO order
        result = mt5_conn.trading.place_oto_order(
            symbol=symbol,
            primary_type=mt5_primary_type,
            primary_volume=float(primary_volume),
            primary_price=float(primary_price),
            secondary_type=mt5_secondary_type,
            secondary_volume=float(secondary_volume),
            secondary_price=float(secondary_price),
            primary_sl=float(primary_sl) if primary_sl is not None else None,
            primary_tp=float(primary_tp) if primary_tp is not None else None,
            secondary_sl=float(secondary_sl) if secondary_sl is not None else None,
            secondary_tp=float(secondary_tp) if secondary_tp is not None else None,
            comment=comment
        )
        
        if not result['success']:
            return jsonify({'error': result['message']}), 400
        
        return jsonify(result)
    
    except Exception as e:
        logger.exception(f"Error placing OTO order: {str(e)}")
        return jsonify({'error': str(e)}), 500
