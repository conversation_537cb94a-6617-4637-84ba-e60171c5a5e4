from typing import Dict, Any, List
import numpy as np
import pandas as pd
from scipy.signal import find_peaks

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class FalseBreakoutPatternIndicator(BaseIndicator):
    """Pattern indicator for detecting false breakouts and distinguishing them from continuations."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize False Breakout pattern indicator.
        
        Args:
            params: Dictionary containing parameters:
                - lookback_period: Period for analyzing price range (default: 20)
                - breakout_threshold: Minimum move for breakout (default: 0.02 or 2%)
                - volume_ratio_threshold: Volume surge threshold (default: 1.5)
                - retracement_threshold: Minimum retracement for false breakout (default: 0.5 or 50%)
                - confirmation_period: Periods to confirm breakout/failure (default: 3)
        """
        super().__init__(params)
        self.lookback_period = self.params.get('lookback_period', 20)
        self.breakout_threshold = self.params.get('breakout_threshold', 0.02)
        self.volume_ratio_threshold = self.params.get('volume_ratio_threshold', 1.5)
        self.retracement_threshold = self.params.get('retracement_threshold', 0.5)
        self.confirmation_period = self.params.get('confirmation_period', 3)

    def _detect_breakout(self, prices: np.ndarray, volumes: np.ndarray,
                        start_idx: int) -> tuple:
        """Detect and classify breakout patterns."""
        if start_idx < self.lookback_period:
            return False, 0, 0, 0
            
        # Calculate price range
        window = slice(start_idx - self.lookback_period, start_idx)
        price_window = prices[window]
        volume_window = volumes[window]
        
        price_high = max(price_window)
        price_low = min(price_window)
        price_range = price_high - price_low
        
        if price_range == 0:
            return False, 0, 0, 0
            
        # Check for breakout
        current_price = prices[start_idx]
        prev_price = prices[start_idx - 1]
        
        # Calculate breakout characteristics
        breakout_size = abs(current_price - prev_price) / prev_price
        if breakout_size < self.breakout_threshold:
            return False, 0, 0, 0
            
        # Check volume surge
        avg_volume = np.mean(volume_window)
        current_volume = volumes[start_idx]
        volume_ratio = current_volume / avg_volume
        
        if volume_ratio < self.volume_ratio_threshold:
            return False, 0, 0, 0
            
        # Determine breakout direction
        if current_price > price_high:
            direction = 1  # Upward breakout
        elif current_price < price_low:
            direction = -1  # Downward breakout
        else:
            return False, 0, 0, 0
            
        return True, direction, breakout_size, volume_ratio

    def _classify_breakout(self, prices: np.ndarray, start_idx: int,
                         direction: int, breakout_size: float) -> tuple:
        """Classify breakout as false or genuine continuation."""
        if start_idx + self.confirmation_period >= len(prices):
            return False, 0, 0
            
        # Analyze price action after breakout
        confirmation_window = slice(start_idx + 1,
                                 start_idx + self.confirmation_period + 1)
        follow_through = prices[confirmation_window]
        
        # Calculate retracement
        if direction > 0:
            max_move = max(follow_through) - prices[start_idx]
            retracement = (max(follow_through) - min(follow_through)) / (max_move + 1e-6)
        else:
            max_move = prices[start_idx] - min(follow_through)
            retracement = (max(follow_through) - min(follow_through)) / (max_move + 1e-6)
        
        # Classify based on retracement and follow-through
        if retracement > self.retracement_threshold:
            # False breakout
            pattern_type = 1  # False breakout
            strength = retracement * breakout_size
        else:
            # Genuine continuation
            pattern_type = -1  # Continuation
            strength = (1 - retracement) * breakout_size
            
        return True, pattern_type, strength

    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """
        Calculate False Breakout pattern values.
        
        Args:
            market_data: Market data containing OHLCV values
            
        Returns:
            Dictionary containing:
                - is_pattern: Boolean array indicating pattern presence
                - pattern_type: Integer array indicating pattern type (1: False Breakout, -1: Continuation)
                - breakout_direction: Integer array indicating breakout direction (1: Upward, -1: Downward)
                - pattern_id: Integer array for pattern instance grouping
                - strength: Float array indicating pattern strength
                - volume_quality: Float array indicating volume confirmation quality
                - trend: Integer array indicating trend context
                - reliability: Float array indicating pattern reliability
        """
        df = market_data.to_dataframe()
        
        close = df['close'].values
        volume = df['volume'].values
        
        is_pattern = np.zeros_like(close)
        pattern_type = np.zeros_like(close)  # 1=False Breakout, -1=Continuation
        breakout_direction = np.zeros_like(close)  # 1=Upward, -1=Downward
        pattern_id = np.zeros_like(close)
        
        # Detect and classify breakouts
        for i in range(self.lookback_period, len(close)):
            is_breakout, direction, size, vol_ratio = self._detect_breakout(close, volume, i)
            
            if is_breakout:
                # Classify the breakout
                can_classify, b_type, strength = self._classify_breakout(close, i, direction, size)
                
                if can_classify:
                    window = slice(i, i + self.confirmation_period + 1)
                    is_pattern[window] = 1
                    pattern_type[window] = b_type
                    breakout_direction[window] = direction
                    pattern_id[window] = i
        
        # Calculate pattern characteristics
        strength = np.zeros_like(close)
        volume_quality = np.zeros_like(close)
        
        for i in range(len(close)):
            if is_pattern[i]:
                # Calculate pattern strength based on:
                # 1. Size of initial breakout
                # 2. Volume confirmation
                # 3. Speed of failure/continuation
                window = slice(i, min(i + self.confirmation_period, len(close)))
                
                price_change = abs(close[window][-1] - close[i]) / close[i]
                avg_volume = np.mean(volume[i-20:i])
                vol_surge = volume[i] / avg_volume
                
                strength[i] = price_change * vol_surge
                volume_quality[i] = vol_surge - 1  # How much above average
        
        # Calculate trend context using 20-period SMA
        sma20 = df['close'].rolling(window=20).mean()
        trend = np.where(df['close'] > sma20, 1, -1)
        
        # Calculate pattern reliability based on future price movement
        reliability = np.zeros_like(close)
        future_window = 20
        
        for i in range(len(close) - future_window):
            if is_pattern[i]:
                future_returns = (df['close'].iloc[i+1:i+future_window+1].values - 
                                df['close'].iloc[i]) / df['close'].iloc[i]
                
                if pattern_type[i] == 1:  # False Breakout
                    # Should reverse
                    max_return = np.max(future_returns)
                    min_return = np.min(future_returns)
                    if breakout_direction[i] > 0:  # Upward breakout
                        reliability[i] = max(0, min(1, -min_return))
                    else:  # Downward breakout
                        reliability[i] = max(0, min(1, max_return))
                else:  # Continuation
                    # Should continue
                    if breakout_direction[i] > 0:  # Upward breakout
                        reliability[i] = max(0, min(1, max_return))
                    else:  # Downward breakout
                        reliability[i] = max(0, min(1, -min_return))
        
        return {
            'is_pattern': is_pattern,
            'pattern_type': pattern_type,
            'breakout_direction': breakout_direction,
            'pattern_id': pattern_id,
            'strength': strength,
            'volume_quality': volume_quality,
            'trend': trend,
            'reliability': reliability
        }
    
    def validate_params(self) -> None:
        """
        Validate indicator parameters.
        
        Raises:
            ValueError: If parameters are invalid
        """
        if self.lookback_period < 10:
            raise ValueError("lookback_period must be at least 10")
        if not 0 < self.breakout_threshold < 1:
            raise ValueError("breakout_threshold must be between 0 and 1")
        if self.volume_ratio_threshold <= 1:
            raise ValueError("volume_ratio_threshold must be greater than 1")
        if not 0 < self.retracement_threshold < 1:
            raise ValueError("retracement_threshold must be between 0 and 1")
        if self.confirmation_period < 2:
            raise ValueError("confirmation_period must be at least 2") 