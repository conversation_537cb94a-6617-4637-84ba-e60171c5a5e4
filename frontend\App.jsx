import React, { useState, useEffect } from 'react';
import { NotificationProvider } from './components/Notification';
import { ConnectionProvider } from './context/ConnectionContext';
import { ModalProvider } from './contexts/ModalContext';
import Header from './components/Header';
import Dashboard from './components/Dashboard';
import AnalysisPage from './pages/AnalysisPage';
import TradeSignalPage from './pages/TradeSignalPage';
// Trade Execution page removed as it's redundant with the Trade Signal page
import AutonomousTradingPage from './pages/AutonomousTradingPage';
import FullAutoModePage from './pages/FullAutoModePage';
import HistoryPage from './pages/HistoryPage';
import SettingsPage from './pages/SettingsPage';
import ProfilePage from './pages/ProfilePage'; // Import ProfilePage
import HelpPage from './pages/HelpPage';       // Import HelpPage
import ConnectionModal from './components/ConnectionModal';
import PageNavigation from './components/PageNavigation';
import './styles/App.css';

const App = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionModalOpen, setConnectionModalOpen] = useState(false);
  const [accountInfo, setAccountInfo] = useState({
    balance: 0,
    equity: 0,
    profit: 0,
    positions: 0,
    accountName: '',
    server: '',
    accountType: '',
    leverage: 0
  });
  const [currentPage, setCurrentPage] = useState('analysis');
  const [selectedSymbol, setSelectedSymbol] = useState('EURUSD');
  const [pageHistory, setPageHistory] = useState(['analysis']);
  const [showPageTransition, setShowPageTransition] = useState(false);
  const [suggestedMT5Path, setSuggestedMT5Path] = useState('');
  const [licenseInfo, setLicenseInfo] = useState({ // New state for license info
    userName: '',
    planType: '',
    planEndDate: '',
    status: ''
  });

  // Initialize theme and fetch suggested MT5 path on app load
  useEffect(() => {
    // Apply saved theme or fallback to dark theme
    const savedTheme = localStorage.getItem('theme') || 'dark';
    applyTheme(savedTheme);

    // Fetch suggested MT5 path
    const fetchSuggestedPath = async () => {
      if (window.api && typeof window.api.getSuggestedMT5Path === 'function') {
        try {
          const path = await window.api.getSuggestedMT5Path();
          if (path) {
            setSuggestedMT5Path(path);
            console.log('App.jsx: Suggested MT5 Path:', path);
          }
        } catch (error) {
          console.error('App.jsx: Error fetching suggested MT5 path:', error);
        }
      }
    };
    fetchSuggestedPath();
  }, []);

  // Function to apply theme
  const applyTheme = (theme) => {
    document.documentElement.setAttribute('data-theme', theme);

    if (theme === 'light') {
      document.documentElement.style.setProperty('--background', '#f3f4f6');
      document.documentElement.style.setProperty('--card', '#ffffff');
      document.documentElement.style.setProperty('--text', '#1f2937');
      document.documentElement.style.setProperty('--text-secondary', '#6b7280');
      document.documentElement.style.setProperty('--border', '#e5e7eb');
      document.documentElement.style.setProperty('--card-hover', '#f9fafb');
      document.documentElement.style.setProperty('--input', '#ffffff');
    } else {
      document.documentElement.style.setProperty('--background', '#111827');
      document.documentElement.style.setProperty('--card', '#1f2937');
      document.documentElement.style.setProperty('--text', '#f9fafb');
      document.documentElement.style.setProperty('--text-secondary', '#9ca3af');
      document.documentElement.style.setProperty('--border', '#374151');
      document.documentElement.style.setProperty('--card-hover', '#2d3748');
      document.documentElement.style.setProperty('--input', '#374151');
    }
  };

  // Check connection status on load
  useEffect(() => {
    const initializeConnection = async () => {
      console.log('App.jsx: Initializing connection state...');
      let autoConnectAttempted = false;
      try {
        // Attempt to load settings that might have been saved by the backend/main process
        // This primarily checks if user_mt5_settings.json has valid data.
        const settingsResponse = await fetch('http://localhost:5001/api/connection/get_current_settings');
      if (settingsResponse.ok) {
        const currentSettings = await settingsResponse.json();
        console.log('App.jsx: Loaded current backend settings:', JSON.stringify(currentSettings)); // Log as string for clarity

        // Detailed check for each property
        const hasLogin = currentSettings && currentSettings.login && currentSettings.login.trim() !== '';
        const hasServer = currentSettings && currentSettings.server && currentSettings.server.trim() !== '';
        const hasPassword = currentSettings && currentSettings.password && currentSettings.password.trim() !== ''; // Password might be empty string intentionally if not set
        const hasPath = currentSettings && currentSettings.path && currentSettings.path.trim() !== '';

        console.log(`App.jsx: Checking settings completeness - Login: ${hasLogin}, Server: ${hasServer}, Password: ${hasPassword}, Path: ${hasPath}`);
        console.log(`App.jsx: Values - Login: '${currentSettings.login}', Server: '${currentSettings.server}', Password: '${currentSettings.password ? '******' : ''}', Path: '${currentSettings.path}'`);


        if (hasLogin && hasServer && hasPassword && hasPath) {
          // If complete settings are found, attempt to check/establish connection
          console.log('App.jsx: Valid and complete settings found, attempting to call /api/connection/reload_settings.');
          // We can call reload_settings which internally uses the saved settings
            // or directly call checkConnectionStatus if reload_settings implies an active attempt.
            // Let's try reload_settings first as it's designed to use existing config.
            const reloadResponse = await fetch('http://localhost:5001/api/connection/reload_settings', { method: 'POST' });
            if (reloadResponse.ok) {
              const reloadData = await reloadResponse.json();
              console.log('App.jsx: Settings reload response:', reloadData);
              if (reloadData.status === 'success' || reloadData.connected === true) {
                 const connectedAfterReload = await checkConnectionStatus(); // This also dispatches mt5:connectionStatusChanged
                 if (connectedAfterReload) {
                    console.log('App.jsx: Auto-connect successful after reload_settings. Dispatching mt5:connectionSucceeded.');
                    // accountInfo state is now updated by checkConnectionStatus
                    window.dispatchEvent(new CustomEvent('mt5:connectionSucceeded', {
                        detail: { connected: true, accountInfo: accountInfo } // Use the latest accountInfo from state
                    }));
                 } else {
                    console.log('App.jsx: Auto-connect via reload_settings reported success, but checkConnectionStatus found not connected.');
                    // checkConnectionStatus already dispatched mt5:connectionStatusChanged with connected: false
                 }
                 autoConnectAttempted = true;
              } else {
                console.log('App.jsx: Reload settings did not result in a connection.', reloadData.message || '');
                // Dispatch status changed if reload failed to connect
                window.dispatchEvent(new CustomEvent('mt5:connectionStatusChanged', {
                    detail: { connected: false, accountInfo: { balance: 0, equity: 0, profit: 0, positions: 0, accountName: '', server: '', accountType: '', leverage: 0 }, error: reloadData.message || 'Reload settings failed' }
                }));
              }
            } else { // reloadResponse not ok
               const errorMsg = `App.jsx: Reload settings call failed with status ${reloadResponse.status}`;
               console.error(errorMsg);
               window.dispatchEvent(new CustomEvent('mt5:connectionStatusChanged', {
                    detail: { connected: false, accountInfo: { balance: 0, equity: 0, profit: 0, positions: 0, accountName: '', server: '', accountType: '', leverage: 0 }, error: errorMsg }
                }));
            }
          } else { // Incomplete settings
            console.log('App.jsx: Incomplete or no backend settings found. Will not auto-connect.');
            // No need to dispatch here, as the final block will handle it if not connected.
          }
        } else { // settingsResponse not ok
          const errorMsg = `App.jsx: Could not fetch current backend settings. Status: ${settingsResponse.status}. Assuming no auto-connect.`;
          console.warn(errorMsg);
           window.dispatchEvent(new CustomEvent('mt5:connectionStatusChanged', {
                detail: { connected: false, accountInfo: { balance: 0, equity: 0, profit: 0, positions: 0, accountName: '', server: '', accountType: '', leverage: 0 }, error: errorMsg }
            }));
        }
      } catch (error) {
        const errorMsg = `App.jsx: Error during initial settings load or connection attempt: ${error.message}`;
        console.error(errorMsg, error);
        window.dispatchEvent(new CustomEvent('mt5:connectionStatusChanged', {
            detail: { connected: false, accountInfo: { balance: 0, equity: 0, profit: 0, positions: 0, accountName: '', server: '', accountType: '', leverage: 0 }, error: errorMsg }
        }));
      }

      if (!autoConnectAttempted && !isConnected) { // Check isConnected state which might have been set by a failed checkConnectionStatus
         console.log('App.jsx: No auto-connect performed or connection failed, ensuring UI is disconnected and dispatching final status.');
         const currentAccInfo = { balance: 0, equity: 0, profit: 0, positions: 0, accountName: '', server: '', accountType: '', leverage: 0 };
         setIsConnected(false);
         setAccountInfo(currentAccInfo);
         // Dispatch status changed if no auto-connect was attempted and we are definitively not connected.
         // This ensures ConnectionContext gets an initial state if nothing else set it.
         window.dispatchEvent(new CustomEvent('mt5:connectionStatusChanged', {
            detail: { connected: false, accountInfo: currentAccInfo, error: null }
         }));
      } else if (autoConnectAttempted && !isConnected) {
        console.log('App.jsx: Auto-connect was attempted but resulted in a disconnected state.');
        // checkConnectionStatus or the error handlers above would have already dispatched the disconnected status.
      }
    };

    initializeConnection();

    // Listen for connection status changes (e.g., after manual connect/disconnect)
    const handleConnectionStatusChanged = (event) => {
      const { connected, accountInfo: newAccountInfo, error } = event.detail;

      setIsConnected(connected);

      if (connected && newAccountInfo) {
        setAccountInfo({
          balance: newAccountInfo.balance || 0,
          equity: newAccountInfo.equity || 0,
          profit: (newAccountInfo.equity - newAccountInfo.balance) || 0,
          positions: newAccountInfo.positions || 0,
          accountType: newAccountInfo.trade_mode === 0 ? 'Demo' : 'Real',
          server: newAccountInfo.server || '',
          leverage: newAccountInfo.leverage || 0,
          accountName: newAccountInfo.name || ''
        });
      } else if (!connected) {
        // Reset account info on disconnect
        setAccountInfo({
          balance: 0,
          equity: 0,
          profit: 0,
          positions: 0,
          accountName: '',
          server: '',
          accountType: '',
          leverage: 0
        });

        // Show error notification if there was an error
        if (error && window.notifications) {
          window.notifications.error('Connection Error', error);
        }
      }
    };

    // Add event listeners
    window.addEventListener('mt5:connectionStatusChanged', handleConnectionStatusChanged);

    // Clean up event listeners on unmount
    return () => {
      window.removeEventListener('mt5:connectionStatusChanged', handleConnectionStatusChanged);
    };
  }, []);

  const checkConnectionStatus = async () => {
    let finalIsConnected = false;
    let finalAccountInfo = { balance: 0, equity: 0, profit: 0, positions: 0, accountName: '', server: '', accountType: '', leverage: 0 };
    let errorDetail = null;

    try {
      console.log('App.jsx: Checking MT5 connection status...');
      const timestamp = new Date().getTime();
      const response = await fetch(`http://localhost:5001/api/connection/connection_status?_=${timestamp}`);
      // console.log('App.jsx: Connection status response status:', response.status); // Less verbose

      if (response.ok) {
        const data = await response.json();
        // console.log('App.jsx: Connection status data:', data); // Less verbose
        finalIsConnected = data.status === 'connected' || data.connected === true;
        // console.log('App.jsx: Connection state from check:', finalIsConnected ? 'Connected' : 'Disconnected');

        if (finalIsConnected && data.account_info) {
          // console.log('App.jsx: Account info received from check:', data.account_info);
          finalAccountInfo = {
            balance: data.account_info.balance || 0,
            equity: data.account_info.equity || 0,
            profit: (data.account_info.equity - data.account_info.balance) || 0,
            positions: data.account_info.positions || 0,
            accountName: data.account_info.name || '',
            server: data.account_info.server || '',
            accountType: data.account_info.trade_mode === 0 ? 'Demo' : 'Real',
            leverage: data.account_info.leverage || 0
          };
        } else if (finalIsConnected) {
          console.warn('App.jsx: Connected but no account info in status check response. Using existing or default.');
          // If already connected and accountInfo has data, keep it, otherwise use default empty.
          finalAccountInfo = accountInfo.accountName ? accountInfo : finalAccountInfo;
        }
        // If not connected, finalAccountInfo remains the default empty object from initialization.
      } else { // response not OK
        errorDetail = `Failed to check connection status, response not OK: ${response.status}`;
        console.error('App.jsx:', errorDetail);
        finalIsConnected = false;
        // finalAccountInfo remains the default empty object.
      }
    } catch (error) {
      errorDetail = `Error checking connection status: ${error.message}`;
      console.error('App.jsx:', errorDetail, error);
      finalIsConnected = false;
      // finalAccountInfo remains the default empty object.
    }

    // Update state
    setIsConnected(finalIsConnected);
    setAccountInfo(finalAccountInfo); // This will be the new or empty info

    // Dispatch status changed event
    console.log('App.jsx: Dispatching mt5:connectionStatusChanged:', { connected: finalIsConnected, accountInfo: finalAccountInfo, error: errorDetail });
    window.dispatchEvent(new CustomEvent('mt5:connectionStatusChanged', {
      detail: { connected: finalIsConnected, accountInfo: finalAccountInfo, error: errorDetail }
    }));

    return finalIsConnected;
  };

  const handleConnect = () => {
    setConnectionModalOpen(true);
  };

  const handleDisconnect = async () => {
    try {
      // Show disconnecting notification
      if (window.notifications) {
        window.notifications.info('Disconnecting', 'Attempting to disconnect from MT5...');
      }

      // Always update UI state immediately to improve responsiveness
      setIsConnected(false);
      setAccountInfo({
        balance: 0,
        equity: 0,
        profit: 0,
        positions: 0,
        accountName: '',
        server: '',
        accountType: '',
        leverage: 0
      });

      // Then send the disconnect request to the backend
      const response = await fetch('http://localhost:5001/api/connection/disconnect', {
        method: 'POST'
      });

      // Handle the response
      if (response.ok) {
        const data = await response.json();
        console.log('Disconnect response:', data);

        if (window.notifications) {
          window.notifications.success('Disconnected', 'Successfully disconnected from MT5.');
        }
      } else {
        const errorMsg = `Disconnect request failed with status: ${response.status}`;
        console.error('App.jsx:', errorMsg);
        // UI state is already disconnected, events dispatched. Notify user of backend issue.
        if (window.notifications) {
          window.notifications.warning('Disconnection Issue', `Disconnected from UI, but server responded with ${response.status}.`);
        }
      }
    } catch (error) {
      const errorMsg = `Error during disconnect process: ${error.message}`;
      console.error('App.jsx:', errorMsg, error);
      // UI state is already disconnected, events dispatched. Notify user of client-side issue.
      if (window.notifications) {
        window.notifications.warning('Disconnection Issue', `Disconnected from UI, but an error occurred: ${error.message}.`);
      }
    }
  };

  const handleConnectionSubmit = async (connectionData) => {
    try {
      // Show connecting notification
      if (window.notifications) {
        window.notifications.info('Connecting', 'Attempting to connect to MT5...');
      }

      // Use the switch_account endpoint for more reliable account switching
      const response = await fetch('http://localhost:5001/api/connection/switch_account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          account: connectionData.login,
          password: connectionData.password,
          server: connectionData.server,
          path: connectionData.path
        })
      });

      // Log the raw response for debugging
      console.log('Connection response status:', response.status);

      // Handle non-OK responses
      if (!response.ok) {
        let errorMessage = `Server error: ${response.status} ${response.statusText}`;

        try {
          // Try to parse the error response as JSON
          const errorData = await response.json();
          if (errorData && errorData.error) {
            errorMessage = errorData.error;
          }
        } catch (parseError) {
          console.error('Could not parse error response:', parseError);
        }

        console.error('Connection failed with error:', errorMessage);

        if (window.notifications) {
          window.notifications.error('Connection Error', errorMessage);
        }

        throw new Error(errorMessage);
      }

      // Parse the response data
      const data = await response.json();
      console.log('Connection response data:', data);

      // Check for both 'connected' and 'success' status for backward compatibility
      if (data.status === 'connected' || data.status === 'success') {
        console.log('Connection successful, updating state');

        // First close the modal to prevent any rendering issues
        setConnectionModalOpen(false);

        let finalAccountInfo = { server: connectionData.server, accountType: connectionData.accountType || 'Demo' };

        if (data.account_info) {
          console.log('Received account info:', data.account_info);
          finalAccountInfo = {
            ...finalAccountInfo,
            balance: data.account_info.balance || 0,
            equity: data.account_info.equity || 0,
            profit: (data.account_info.equity - data.account_info.balance) || 0,
            positions: data.account_info.positions || 0,
            accountName: data.account_info.name || '',
            leverage: data.account_info.leverage || 0
          };
        } else {
          console.warn('No account info received directly in switch_account response. Will rely on subsequent status check if needed.');
          // If no account_info, we might need to call checkConnectionStatus to get it.
          // For now, we'll dispatch with what we have, ConnectionContext might update via its own check.
          // Or, better, call checkConnectionStatus here to ensure full info.
          const statusConnected = await checkConnectionStatus(); // This will set state and dispatch statusChanged
          if (statusConnected) {
             // accountInfo state is now updated by checkConnectionStatus
             // We can use the global accountInfo state for the succeeded event.
             // This ensures the event has the most complete data.
          }
          // finalAccountInfo will be updated by checkConnectionStatus if it ran and got data.
          // The global `accountInfo` state is the source of truth after checkConnectionStatus.
        }

        // Update App.jsx's state
        setIsConnected(true);
        if (data.account_info) { // Only set if directly available, otherwise checkConnectionStatus handles it
            setAccountInfo(finalAccountInfo);
        }
        // else: checkConnectionStatus already updated accountInfo state

        // Dispatch events
        // Use the latest accountInfo from state, which checkConnectionStatus would have updated
        window.dispatchEvent(new CustomEvent('mt5:connectionSucceeded', {
            detail: { connected: true, accountInfo: accountInfo } // Use state `accountInfo`
        }));
        // mt5:connectionStatusChanged is dispatched by checkConnectionStatus if called,
        // or we can dispatch it here if checkConnectionStatus wasn't called and we have direct info.
        if (!data.account_info) { // If checkConnectionStatus was called, it already dispatched.
            // If data.account_info was present, checkConnectionStatus wasn't called, so dispatch statusChanged.
            // This path is less likely if we always call checkConnectionStatus when data.account_info is missing.
        } else {
             window.dispatchEvent(new CustomEvent('mt5:connectionStatusChanged', {
                detail: { connected: true, accountInfo: finalAccountInfo, error: null }
            }));
        }


        if (window.notifications) {
          window.notifications.success('Connection Successful', 'Connected to MT5');
        }
        console.log('Connection process completed successfully');

      } else { // Connection attempt failed (e.g., data.status was 'error')
        const errorMessage = data.error || data.message || 'An error occurred during connection';
        console.error('Connection failed with error:', errorMessage);
        setIsConnected(false); // Ensure App state is disconnected
        const emptyAccountInfo = { balance: 0, equity: 0, profit: 0, positions: 0, accountName: '', server: '', accountType: '', leverage: 0 };
        setAccountInfo(emptyAccountInfo);
        window.dispatchEvent(new CustomEvent('mt5:connectionStatusChanged', {
            detail: { connected: false, accountInfo: emptyAccountInfo, error: errorMessage }
        }));
        if (window.notifications) {
          window.notifications.error('Connection Error', errorMessage);
        }
      }
    } catch (error) {
      console.error('Exception during connection attempt:', error);

      if (window.notifications) {
        window.notifications.error('Connection Error', `Failed to connect: ${error.message}`);
      }
    }
  };

  const handleSymbolSelect = (symbol) => {
    setSelectedSymbol(symbol);
  };

  const navigateToPage = (page) => {
    if (page === currentPage) return;

    // Save current page to history
    setPageHistory(prev => [...prev, page]);

    // Show transition effect
    setShowPageTransition(true);

    // Change page after a short delay for animation
    setTimeout(() => {
      setCurrentPage(page);
      setShowPageTransition(false);
    }, 300);
  };

  const goBack = () => {
    if (pageHistory.length <= 1) return;

    // Remove current page from history
    const newHistory = [...pageHistory];
    newHistory.pop();

    // Get previous page
    const previousPage = newHistory[newHistory.length - 1];

    // Show transition effect
    setShowPageTransition(true);

    // Navigate to previous page
    setTimeout(() => {
      setCurrentPage(previousPage);
      setPageHistory(newHistory);
      setShowPageTransition(false);
    }, 300);
  };

  const navigateToNextStep = () => {
    // Define the natural flow between pages
    const pageFlow = {
      'analysis': 'recommendation',
      'recommendation': 'history' // Skip execution page as it's redundant
    };

    const nextPage = pageFlow[currentPage];
    if (nextPage) {
      navigateToPage(nextPage);
    }
  };

  const handleLicenseVerified = (newLicenseInfo) => {
    console.log('App.jsx: License verified, updating license info:', newLicenseInfo);
    setLicenseInfo(newLicenseInfo);
    // Optionally, save to localStorage if you want it to persist across sessions
    // localStorage.setItem('licenseInfo', JSON.stringify(newLicenseInfo));
  };

  const renderPage = () => {
    switch (currentPage) {
      case 'analysis':
        return <AnalysisPage selectedSymbol={selectedSymbol} onSymbolSelect={handleSymbolSelect} />;
      case 'recommendation':
        return <TradeSignalPage selectedSymbol={selectedSymbol} onSymbolSelect={handleSymbolSelect} />;
      // Execution page removed - trade execution is handled in the Trade Signal page
      case 'autonomous':
        return <AutonomousTradingPage />;
      case 'full-auto':
        return <FullAutoModePage />;
      case 'history':
        return <HistoryPage />;
      case 'settings':
        // Pass licenseInfo to SettingsPage
        return <SettingsPage accountInfo={accountInfo} licenseInfo={licenseInfo} />;
      case 'profile':
        return <ProfilePage licenseInfo={licenseInfo} />; // Pass licenseInfo to ProfilePage
      case 'help':
        return <HelpPage />;    // Add HelpPage case
      default:
        return <AnalysisPage selectedSymbol={selectedSymbol} onSymbolSelect={handleSymbolSelect} />;
    }
  };

  return (
    <NotificationProvider>
      <ConnectionProvider>
        <ModalProvider>
          <div className="app-container">
            <Header
              isConnected={isConnected}
              onConnect={handleConnect}
              onDisconnect={handleDisconnect}
              currentPage={currentPage}
              navigateToPage={navigateToPage}
              accountInfo={accountInfo}
            />
            <div className="app-layout">
              <nav className="sidebar">
                <div className="sidebar-nav">
                  <button
                    className={`sidebar-nav-item ${currentPage === 'analysis' ? 'active' : ''}`}
                    onClick={() => navigateToPage('analysis')}
                  >
                    <span className="nav-icon analysis-icon"></span>
                    <span className="nav-text">Analysis</span>
                  </button>
                  <button
                    className={`sidebar-nav-item ${currentPage === 'recommendation' ? 'active' : ''}`}
                    onClick={() => navigateToPage('recommendation')}
                  >
                    <span className="nav-icon recommendation-icon"></span>
                    <span className="nav-text">Trade Signals</span>
                  </button>
                  {/* Execution button removed as it's redundant with Trade Signals */}
                  <button
                    className={`sidebar-nav-item ${currentPage === 'autonomous' ? 'active' : ''}`}
                    onClick={() => navigateToPage('autonomous')}
                  >
                    <span className="nav-icon autonomous-icon"></span>
                    <span className="nav-text">Autonomous Trading</span>
                  </button>
                  <button
                    className={`sidebar-nav-item ${currentPage === 'full-auto' ? 'active' : ''}`}
                    onClick={() => navigateToPage('full-auto')}
                  >
                    <span className="nav-icon full-auto-icon"></span>
                    <span className="nav-text">🤖 Full Auto Mode</span>
                  </button>
                  <button
                    className={`sidebar-nav-item ${currentPage === 'history' ? 'active' : ''}`}
                    onClick={() => navigateToPage('history')}
                  >
                    <span className="nav-icon history-icon"></span>
                    <span className="nav-text">History</span>
                  </button>
                  <div className="sidebar-divider"></div>
                  <button
                    className={`sidebar-nav-item ${currentPage === 'profile' ? 'active' : ''}`}
                    onClick={() => navigateToPage('profile')}
                  >
                    <span className="nav-icon profile-icon"></span> {/* Assuming a .profile-icon class exists or will be added */}
                    <span className="nav-text">Profile</span>
                  </button>
                  <button
                    className={`sidebar-nav-item ${currentPage === 'settings' ? 'active' : ''}`}
                    onClick={() => navigateToPage('settings')}
                  >
                    <span className="nav-icon settings-icon"></span>
                    <span className="nav-text">Settings</span>
                  </button>
                  <button
                    className={`sidebar-nav-item ${currentPage === 'help' ? 'active' : ''}`}
                    onClick={() => navigateToPage('help')}
                  >
                    <span className="nav-icon help-icon"></span> {/* Assuming a .help-icon class exists or will be added */}
                    <span className="nav-text">Help</span>
                  </button>
                </div>
              </nav>

              <main className={`main-content ${showPageTransition ? 'page-transition' : ''}`}>
                <Dashboard
                  accountInfo={accountInfo}
                  currentPage={currentPage}
                />
                <div className="page-container">
                  <PageNavigation
                    currentPage={currentPage}
                    canGoBack={pageHistory.length > 1}
                    onBack={goBack}
                    onNextStep={navigateToNextStep}
                  />
                  <div className="content-area">
                    {renderPage()}
                  </div>
                </div>
              </main>
            </div>

            <ConnectionModal
              isOpen={connectionModalOpen}
              onClose={() => setConnectionModalOpen(false)}
              onSubmit={handleConnectionSubmit}
              suggestedPath={suggestedMT5Path} // Pass the suggested path
              onLicenseVerified={handleLicenseVerified} // Pass the new callback
            />
          </div>
        </ModalProvider>
      </ConnectionProvider>
    </NotificationProvider>
  );
};

export default App;
