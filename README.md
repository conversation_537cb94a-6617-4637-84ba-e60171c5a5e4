# GarudaAlgo MT5 Trader

An AI-powered trading assistant that connects to MetaTrader 5 for market analysis, trade recommendations, and autonomous trading.

## Features

- **MT5 Integration**: Connect to MetaTrader 5 terminal for real-time market data
- **Market Analysis**: Advanced technical analysis with multi-timeframe support
- **Trade Recommendations**: AI-generated trade signals with entry, stop loss, and take profit levels
- **Autonomous Trading**: Configure and monitor AI-driven trading strategies
- **Modern UI**: Sleek, responsive interface with dark theme and bento-style layout

## Getting Started

### Prerequisites

- Python 3.8+
- Node.js 14+
- MetaTrader 5 terminal installed
- Miniconda (recommended for ta-lib support)

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/garudaalgo.git
   cd garudaalgo
   ```

2. Install backend dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Install frontend dependencies:
   ```
   cd frontend
   npm install
   cd ..
   npm install
   ```

### Running the Application

Start the application with:

```
npm start
```

This will launch both the FastAPI backend and Electron frontend.

## Development

### Backend Development

The backend is built with FastAPI and provides the following endpoints:

- `/connect` - Connect to MT5 terminal
- `/disconnect` - Disconnect from MT5 terminal
- `/connection_status` - Get current connection status
- `/api/symbols` - Get available symbols
- `/api/ohlc` - Get OHLC data for a symbol
- `/api/analysis` - Analyze a symbol
- `/api/recommendations` - Get trade recommendations
- `/api/execute_trade` - Execute a trade
- `/api/autonomous/*` - Autonomous trading endpoints

### Frontend Development

The frontend is built with Electron and React, using Material UI for components.

Key components:
- `App.js` - Main application component
- `ConnectionModal.js` - MT5 connection dialog
- `Dashboard.js` - Main dashboard with account overview
- `MarketAnalysis.js` - Technical analysis component
- `TradeRecommendations.js` - Trade signals component
- `AutonomousTrading.js` - Autonomous trading configuration

## Building and Packaging

### Prerequisites for Building

- Node.js 14+ and npm
- Electron and Electron Builder
- Python 3.8+ with PyInstaller (for backend packaging)
- All dependencies installed via `pip install -r requirements.txt`

### Build Process Overview

The application is built as two separate executables:
1. **Backend Server** (`GarudaAlgo_Backend_Server.exe`) - Standalone FastAPI server
2. **Frontend Application** (`GarudaAlgo_Frontend.exe`) - Electron application that connects to the backend

This approach allows the backend to run independently from the frontend, improving stability and performance.

### Building the Backend

1. Build the backend server executable:
   ```
   pyinstaller garuda_backend.spec
   ```
   
   This creates a standalone executable for the backend server in the `dist` folder named `garuda_backend.exe`.

2. The backend spec file (`garuda_backend.spec`) is configured with:
   - Necessary hidden imports for all backend modules
   - Firebase credentials and other essential files
   - Appropriate runtime hooks for encoding support
   - Console window enabled for debugging

### Building the Frontend

1. First build the React application:
   ```
   npm run build
   ```
   This compiles the React application using Vite and outputs to `frontend/dist`.

2. The frontend wrapper (`frontend_wrapper.py`) is designed to:
   - Check if the backend server is running
   - Launch the backend server if needed
   - Connect the Electron application to the backend

3. Package the complete application:
   ```
   npm run package-win
   ```
   
   The packaged application will be available in the `release-new` directory:
   - Windows installer: `release-new/GarudaAlgo Trader Setup x.x.x.exe`
   - Portable version: `release-new/win-unpacked/GarudaAlgo Trader.exe`

### Packaging Configuration

The packaging is configured in `package.json` under the `build` section:
- `appId`: Application identifier
- `productName`: Display name of the application
- `directories`: Output and resource directories
- `files`: Files to include in the package
- `extraResources`: Additional resources to include (like the backend executable)
- Platform-specific configurations for Windows, macOS, and Linux

### Important Build Notes

1. The backend uses absolute imports to ensure compatibility in the packaged executable (e.g., `from backend.credential_manager` instead of relative imports).

2. The splash screen initiates during application startup while the backend server is being launched.

3. The standalone executables are designed to work without requiring Python or Node.js to be installed on the target system.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
