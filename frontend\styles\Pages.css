/* Common page styles */
.page-container {
  padding: 20px;
}

.page-title {
  font-size: 1.5rem;
  margin-bottom: 20px;
  font-weight: 600;
}

.page-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 1.2rem;
  margin-bottom: 15px;
  font-weight: 500;
}

/* Performance Summary Card */
.performance-summary-card {
  background-color: var(--card);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  padding: 20px;
  margin-bottom: 24px;
}

.performance-summary-card h3 {
  color: var(--text, #ffffff);
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 16px;
  border-bottom: 1px solid var(--border);
  padding-bottom: 10px;
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.performance-metric {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  font-size: 14px;
  color: var(--text-secondary, rgba(255, 255, 255, 0.7));
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text, #ffffff);
}

.metric-value.positive {
  color: var(--success, #10b981);
}

.metric-value.negative {
  color: var(--error, #ef4444);
}

/* Analysis Page */
.analysis-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 20px;
}

.analysis-sidebar {
  background-color: var(--card);
  border-radius: 8px;
  padding: 15px;
}

.analysis-content {
  background-color: var(--card);
  border-radius: 8px;
  padding: 20px;
}

.analysis-form {
  margin-bottom: 20px;
}

.analysis-results {
  margin-top: 20px;
}

.indicator-card {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 10px;
}

.indicator-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.indicator-name {
  font-weight: 500;
}

.indicator-value {
  font-weight: 600;
}

.indicator-value.bullish {
  color: var(--success);
}

.indicator-value.bearish {
  color: var(--error);
}

.indicator-value.neutral {
  color: var(--warning);
}

.indicator-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* Recommendation Page */
.recommendation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

/* Execution Page */
.execution-form {
  max-width: 600px;
  margin: 0 auto;
}

.form-row {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group {
  margin-bottom: 15px;
}

.form-actions {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* History Page */
.history-page {
  background-color: var(--background);
  color: var(--text);
  min-height: calc(100vh - 60px);
  padding: 24px;
}

.history-page h2 {
  color: var(--text, #ffffff);
  margin-bottom: 24px;
}

.history-page h3 {
  color: var(--text, #ffffff);
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
}

.history-page .controls {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
}

.history-page .form-group {
  min-width: 200px;
}

.history-page label {
  display: block;
  margin-bottom: 8px;
  color: var(--text-secondary, rgba(255, 255, 255, 0.7));
}

.history-page select {
  width: 100%;
  padding: 8px 12px;
  background-color: var(--input);
  border: 1px solid var(--border);
  color: var(--text, #ffffff);
  border-radius: 4px;
}

.history-page select option {
  background-color: var(--input);
  color: var(--text, #ffffff);
}

.history-table-container {
  background-color: var(--card);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  padding: 20px;
  margin-top: 24px;
  overflow-x: auto;
}

.history-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  color: var(--text, #ffffff);
}

.history-table th {
  background-color: var(--card-hover);
  padding: 10px;
  text-align: left;
  position: sticky;
  top: 0;
  z-index: 1;
  box-shadow: 0 1px 0 0 var(--border);
  color: var(--text-secondary, rgba(255, 255, 255, 0.7));
  font-weight: 500;
}

.history-table td {
  padding: 10px;
  border-bottom: 1px solid var(--border);
}

.history-table tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.history-table .profit-row {
  background-color: rgba(0, 128, 0, 0.1);
}

.history-table .loss-row {
  background-color: rgba(255, 0, 0, 0.1);
}

.history-table .zero-profit-row {
  background-color: rgba(255, 165, 0, 0.15) !important;
}

.zero-profit-tag {
  display: inline-block;
  margin-left: 5px;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  background-color: #ff9800;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  cursor: help;
}

.zero-profit-details-row {
  background-color: rgba(255, 165, 0, 0.05) !important;
}

.zero-profit-details {
  padding: 10px;
  border-left: 3px solid #ff9800;
  margin: 5px 0;
  font-size: 13px;
}

.zero-profit-details ul {
  list-style-type: none;
  padding-left: 10px;
  margin: 5px 0;
  display: flex;
  flex-wrap: wrap;
}

.zero-profit-details li {
  margin-right: 20px;
  padding: 3px 0;
}

.history-table .positive {
  color: var(--success, #10b981);
}

.history-table .negative {
  color: var(--error, #ef4444);
}

.history-table .buy {
  color: var(--success, #10b981);
  font-weight: 600;
}

.history-table .sell {
  color: var(--error, #ef4444);
  font-weight: 600;
}

.history-page .loading {
  text-align: center;
  padding: 30px;
  color: var(--text-secondary, rgba(255, 255, 255, 0.7));
}

.history-page .no-data {
  text-align: center;
  padding: 30px;
  font-style: italic;
  color: var(--text-secondary, rgba(255, 255, 255, 0.7));
}

/* Pagination styles */
.pagination-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 10px 0;
}

.pagination-info {
  color: var(--text-secondary, rgba(255, 255, 255, 0.7));
  font-size: 14px;
}

.pagination-buttons {
  display: flex;
  gap: 8px;
}

.pagination-button {
  background-color: var(--input);
  color: var(--text, #ffffff);
  border: 1px solid var(--border);
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.pagination-button:hover {
  background-color: var(--card-hover);
}

.pagination-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.pagination-button.active {
  background-color: var(--primary, #3772ff);
  color: white;
  border-color: var(--primary, #3772ff);
}

.rows-per-page {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rows-per-page label {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: 0;
}

.rows-per-page select {
  background-color: var(--input);
  color: var(--text);
  border: 1px solid var(--border);
  border-radius: 4px;
  padding: 5px 8px;
  font-size: 14px;
  width: auto;
}

/* Responsive styles */
@media (max-width: 992px) {
  .analysis-container {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .recommendation-grid {
    grid-template-columns: 1fr;
  }
  
  .history-filters {
    flex-direction: column;
    gap: 10px;
  }
}

/* Dark theme for appropriate pages - only necessary overrides that can't be handled via CSS variables */
.page-container.autonomous-page {
  background-color: var(--background);
  color: var(--text);
  min-height: calc(100vh - 60px); /* Account for header */
  padding: 24px;
  width: 100%;
}

/* Form controls for dark theme */
.autonomous-page select,
.autonomous-page input[type="text"],
.autonomous-page input[type="number"] {
  background-color: var(--input);
  border: 1px solid var(--border);
  color: var(--text);
  border-radius: 4px;
  padding: 8px 12px;
}

.autonomous-page select option {
  background-color: var(--input);
  color: var(--text);
}

.autonomous-page .input-hint {
  color: var(--text-secondary);
  font-size: 13px;
  margin-top: 4px;
}

.autonomous-page .form-group {
  margin-bottom: 16px;
}

.autonomous-page .form-group label {
  display: block;
  margin-bottom: 6px;
  color: var(--text-secondary);
  font-weight: 500;
}

.autonomous-page .button {
  background-color: var(--primary, #3772ff);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.autonomous-page .button:hover {
  background-color: var(--primary-hover, #2a5cdb);
}

.autonomous-page .button.secondary {
  background-color: var(--input);
  border: 1px solid var(--border);
}

.autonomous-page .button.secondary:hover {
  background-color: var(--card-hover);
}

.autonomous-page .button.danger {
  background-color: var(--error, #ff1744);
}

.autonomous-page .button.danger:hover {
  background-color: var(--error-hover, #d1001d);
}

/* Strategy cards */
.strategy-card {
  background-color: var(--card);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid var(--border);
}

.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.strategy-header h4 {
  margin: 0;
  color: var(--text);
  font-size: 16px;
}

.status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status.active {
  background-color: rgba(0, 200, 83, 0.2);
  color: var(--success, #00c853);
}

.status.inactive {
  background-color: rgba(255, 82, 82, 0.2);
  color: var(--error, #ff5252);
}

.strategy-details {
  margin-bottom: 16px;
}

.detail-row {
  display: flex;
  margin-bottom: 8px;
}

.detail-item {
  margin-right: 16px;
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 14px;
  color: var(--text);
}

.detail-value.positive {
  color: var(--success, #00c853);
}

.detail-value.negative {
  color: var(--error, #ff5252);
}

.strategy-actions {
  margin-top: 12px;
}

/* Profile Page Specific Styles */
.profile-page .profile-details-card {
  background-color: var(--card);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* Softer shadow */
  margin-bottom: 20px;
}

.profile-page .profile-details-card h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.2rem; /* Consistent with section-title */
  color: var(--text);
  border-bottom: 1px solid var(--border);
  padding-bottom: 10px;
}

.profile-page .profile-detail-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-light, #374151); /* Lighter border for items */
}
.profile-page .profile-detail-item:last-child {
  border-bottom: none;
}

.profile-page .detail-label {
  font-weight: 500;
  color: var(--text-secondary);
  margin-right: 10px;
}

.profile-page .detail-value {
  color: var(--text);
  text-align: right;
}

.profile-page .status-active {
  color: var(--success, #10b981);
  font-weight: bold;
}
.profile-page .status-inactive,
.profile-page .status-expired,
.profile-page .status-unknown {
  color: var(--error, #ef4444);
}


/* Help Page Specific Styles */
.help-page .page-content {
  max-width: 900px; /* Limit width for readability */
  margin: 0 auto; /* Center content */
}

.help-section {
  background-color: var(--card);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 25px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.help-section h3 {
  font-size: 1.3rem;
  color: var(--text);
  margin-top: 0;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border);
}

.help-section h4 {
  font-size: 1.1rem;
  color: var(--text);
  margin-top: 15px;
  margin-bottom: 10px;
}

.help-section p,
.help-section li {
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: 0.95rem;
}

.help-section ul {
  list-style-type: disc;
  padding-left: 20px;
  margin-bottom: 15px;
}

.help-section ul li {
  margin-bottom: 8px;
}

.help-section strong {
  color: var(--text);
  font-weight: 600;
}

.faq-item {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-light, #374151); /* Lighter border for items */
}

.faq-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.faq-item h4 {
  font-size: 1.1rem; /* Slightly larger for questions */
  color: var(--text);
  margin-bottom: 8px;
}

.faq-item p {
  color: var(--text-secondary);
  margin-top: 0;
  font-size: 0.9rem;
}

.help-page a {
  color: var(--primary, #3772ff);
  text-decoration: none;
}

.help-page a:hover {
  text-decoration: underline;
}

/* General content card style if needed elsewhere */
.content-card {
  background-color: var(--card);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px; /* Default margin */
}

.content-card h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.2rem;
  color: var(--text);
  border-bottom: 1px solid var(--border);
  padding-bottom: 10px;
}
