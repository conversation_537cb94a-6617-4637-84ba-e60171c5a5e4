import React, { useState, useEffect, useRef } from 'react';
import { safeToFixed } from '../../utils/numberUtils';
import { useConnection } from '../../context/ConnectionContext';

/**
 * CurrentPriceDisplay component - Displays the current price prominently
 *
 * @param {Object} analysisData - The analysis data containing current price information
 * @param {string} selectedSymbol - The currently selected symbol
 * @returns {JSX.Element} - The rendered current price display
 */
const CurrentPriceDisplay = ({ analysisData, selectedSymbol }) => {
  const [priceChange, setPriceChange] = useState(null);
  const [previousPrice, setPreviousPrice] = useState(null);
  const [currentPriceData, setCurrentPriceData] = useState(null);
  const refreshTimerRef = useRef(null);
  const { isConnected } = useConnection();

  // Force connected status if we have valid price data from any source
  const effectivelyConnected = isConnected ||
    // Check if we have valid price data from currentPriceData
    (currentPriceData && currentPriceData.bid !== undefined && currentPriceData.bid !== null) ||
    // Check if we have valid price data from analysisData
    (analysisData?.current_price?.bid !== undefined && analysisData?.current_price?.bid !== null) ||
    (analysisData?.support_resistance?.current_price?.bid !== undefined && analysisData?.support_resistance?.current_price?.bid !== null);

  // Get the current price from the analysis data
  const currentPrice = analysisData?.current_price?.bid ||
                      analysisData?.support_resistance?.current_price?.bid ||
                      analysisData?.price_data?.close?.[analysisData.price_data.close.length - 1];

  // Fetch current price periodically
  useEffect(() => {
    // Initialize with data from analysisData
    if (analysisData?.current_price || analysisData?.support_resistance?.current_price) {
      setCurrentPriceData(analysisData?.current_price || analysisData?.support_resistance?.current_price);
    }

    // Track consecutive errors
    let errorCount = 0;
    const MAX_ERRORS = 3;

    // Function to fetch current price
    const fetchCurrentPrice = async () => {
      if (!selectedSymbol) return;

      try {
        const response = await fetch(`http://localhost:5001/api/current_price?symbol=${selectedSymbol}`);

        if (response.ok) {
          const data = await response.json();
          if (data && data.bid !== undefined && data.bid !== null) {
            setCurrentPriceData(data);
            errorCount = 0; // Reset error count on success
          } else if (data && data.error) {
            // We got a response with an error message but in a 200 response
            console.warn(`Price data contains error: ${data.error}`);
            // Still store the data as it contains the error message
            setCurrentPriceData(data);

            // Increment error count but don't stop immediately
            errorCount++;
            if (errorCount >= MAX_ERRORS) {
              console.error('Too many consecutive errors, reducing refresh frequency');
              // Instead of stopping completely, reduce frequency
              if (refreshTimerRef.current) {
                clearInterval(refreshTimerRef.current);
                refreshTimerRef.current = setInterval(fetchCurrentPrice, 15000); // Reduce to every 15 seconds
              }
            }
          }
        } else {
          // Handle HTTP error
          errorCount++;
          console.warn(`Error fetching price (${errorCount}/${MAX_ERRORS}): ${response.status}`);

          // If we've had too many consecutive errors, stop refreshing
          if (errorCount >= MAX_ERRORS) {
            console.error('Too many consecutive errors, stopping price refresh');
            if (refreshTimerRef.current) {
              clearInterval(refreshTimerRef.current);
              refreshTimerRef.current = null;
            }
          }
        }
      } catch (error) {
        errorCount++;
        console.error(`Error fetching current price (${errorCount}/${MAX_ERRORS}):`, error);

        // If we've had too many consecutive errors, stop refreshing
        if (errorCount >= MAX_ERRORS) {
          console.error('Too many consecutive errors, stopping price refresh');
          if (refreshTimerRef.current) {
            clearInterval(refreshTimerRef.current);
            refreshTimerRef.current = null;
          }
        }
      }
    };

    // Set up periodic refresh
    refreshTimerRef.current = setInterval(fetchCurrentPrice, 5000); // Refresh every 5 seconds

    // Clean up on unmount
    return () => {
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
        refreshTimerRef.current = null;
      }
    };
  }, [selectedSymbol, analysisData]);

  // Update price change when current price changes
  useEffect(() => {
    // Use currentPriceData if available, otherwise fall back to analysisData
    const price = currentPriceData?.bid || currentPrice;

    if (price !== null && price !== undefined && previousPrice !== null) {
      const change = price - previousPrice;
      setPriceChange(change);
    }

    if (price !== null && price !== undefined) {
      setPreviousPrice(price);
    }
  }, [currentPriceData, currentPrice]);

  // If no price data is available, show a message
  if ((currentPriceData === null || currentPriceData === undefined) &&
      (currentPrice === null || currentPrice === undefined)) {
    return (
      <div className="current-price-display">
        <div className="price-container">
          <div className="symbol">{selectedSymbol}</div>
          <div className="price">N/A</div>
        </div>
        <div className="price-details">
          <div className="detail">
            <span className="label">Last Update:</span>
            <span className="value">{new Date().toLocaleTimeString()}</span>
          </div>
        </div>
      </div>
    );
  }

  // If we have price data with an error, show the error
  if (currentPriceData && currentPriceData.error && (!currentPriceData.bid || currentPriceData.bid === null)) {
    return (
      <div className="current-price-display">
        <div className="price-container">
          <div className="symbol">{selectedSymbol}</div>
          <div className="price">N/A</div>
          <div className="error-message">{currentPriceData.error}</div>
        </div>
        <div className="price-details">
          <div className="detail">
            <span className="label">Last Update:</span>
            <span className="value">{currentPriceData.time || new Date().toLocaleTimeString()}</span>
          </div>
        </div>
      </div>
    );
  }

  // Get the actual price to display (prefer currentPriceData over analysisData)
  const displayPrice = currentPriceData?.bid || currentPrice;

  // Format the price for display
  const formattedPrice = safeToFixed(displayPrice, 5);

  // Determine the price change direction
  const changeDirection = priceChange > 0 ? 'up' : priceChange < 0 ? 'down' : 'neutral';

  // Format the price change for display
  const formattedChange = priceChange !== null ? safeToFixed(priceChange, 5) : '';
  const formattedChangePercent = priceChange !== null && displayPrice !== 0
    ? safeToFixed((priceChange / displayPrice) * 100, 2) + '%'
    : '';

  return (
    <div className="current-price-display">
      <div className="price-container">
        <div className="symbol">{selectedSymbol}</div>
        <div className={`price ${changeDirection}`}>{formattedPrice}</div>
        {priceChange !== null && (
          <div className={`change ${changeDirection}`}>
            {priceChange > 0 ? '+' : ''}{formattedChange} ({formattedChangePercent})
          </div>
        )}
      </div>
      <div className="price-details">
        <div className="detail">
          <span className="label">Bid:</span>
          <span className="value">{safeToFixed(currentPriceData?.bid || analysisData?.current_price?.bid || analysisData?.support_resistance?.current_price?.bid, 5)}</span>
        </div>
        <div className="detail">
          <span className="label">Ask:</span>
          <span className="value">{safeToFixed(currentPriceData?.ask || analysisData?.current_price?.ask || analysisData?.support_resistance?.current_price?.ask, 5)}</span>
        </div>
        <div className="detail">
          <span className="label">Spread:</span>
          <span className="value">
            {safeToFixed(
              (currentPriceData?.ask || analysisData?.current_price?.ask || analysisData?.support_resistance?.current_price?.ask || 0) -
              (currentPriceData?.bid || analysisData?.current_price?.bid || analysisData?.support_resistance?.current_price?.bid || 0),
              5
            )}
          </span>
        </div>
        <div className="detail">
          <span className="label">Last Update:</span>
          <span className="value">{currentPriceData?.time || analysisData?.current_price?.time || analysisData?.support_resistance?.current_price?.time || 'N/A'}</span>
        </div>
      </div>
    </div>
  );
};

export default CurrentPriceDisplay;
