"""
Firebase interaction logic specifically for the Admin Panel
AND for client-side session/trade recording.
"""
import logging
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional, Tuple, Any

import firebase_admin
from firebase_admin import firestore
from firebase_admin.firestore import SERVER_TIMESTAMP # Import SERVER_TIMESTAMP

# Use the admin auth module for initialization status check and getting the db client
from admin.core import firebase_auth

# --- Global variable to store the current client session document ID ---
# This is managed by create_trading_session and end_trading_session
_current_client_session_id: Optional[str] = None

# --- Authorized Accounts CRUD (Admin Panel Only) ---

def get_authorized_accounts() -> Tuple[List[Dict[str, Any]], Dict[Tuple[str, str], str]]:
    """
    Fetches all documents from the 'authorized_accounts' collection.
    Returns a list of account dicts and a user cache keyed by (account_number, server_name).
    """
    if not firebase_auth._firebase_initialized or not firebase_auth._db:
        logging.error("Firebase not initialized. Cannot fetch accounts.")
        return [], {}
    try:
        accounts_ref = firebase_auth._db.collection('authorized_accounts')
        accounts = []
        users_cache_by_acc_srv: Dict[Tuple[str, str], str] = {}
        for doc in accounts_ref.stream():
            account_data = doc.to_dict()
            if not account_data: continue
            doc_id = doc.id
            user_name = account_data.get('user_name', 'Unknown User')
            acc_num = account_data.get('account_number')
            srv_name = account_data.get('server_name')

            account_data['id'] = doc_id
            account_data['user_name'] = user_name

            if acc_num and srv_name:
                users_cache_by_acc_srv[(str(acc_num), srv_name)] = user_name

            if 'plan_end_date' in account_data and hasattr(account_data['plan_end_date'], 'astimezone'):
                 account_data['plan_end_date_str'] = account_data['plan_end_date'].astimezone(timezone.utc).strftime('%Y-%m-%d')
            else: account_data['plan_end_date_str'] = 'N/A'
            if 'created_at' in account_data and hasattr(account_data['created_at'], 'astimezone'):
                 account_data['created_at_str'] = account_data['created_at'].astimezone(timezone.utc).strftime('%Y-%m-%d %H:%M')
            else: account_data['created_at_str'] = 'N/A'

            accounts.append(account_data)

        logging.info(f"Fetched {len(accounts)} authorized accounts and built user cache from Firebase.")
        return accounts, users_cache_by_acc_srv
    except Exception as e:
        logging.exception(f"Error fetching authorized accounts: {e}")
        return [], {}

def add_authorized_account(account_number: str, server_name: str, user_name: str,
                           plan_type: str, plan_duration_days: int) -> Tuple[bool, str]:
    """Adds a new authorized account to Firestore."""
    if not firebase_auth._firebase_initialized or not firebase_auth._db: return False, "Firebase not initialized."
    try:
        accounts_ref = firebase_auth._db.collection('authorized_accounts')
        query = accounts_ref.where('account_number', '==', str(account_number))\
                           .where('server_name', '==', server_name).limit(1)
        if len(list(query.stream())) > 0: return False, f"Account {account_number} on {server_name} already exists."
        now = datetime.now(timezone.utc)
        end_date = now + timedelta(days=plan_duration_days)
        new_account_data = {
            'account_number': str(account_number), 'server_name': server_name, 'user_name': user_name,
            'status': 'active', 'plan_type': plan_type, 'created_at': SERVER_TIMESTAMP, 'plan_end_date': end_date
        }
        doc_ref = accounts_ref.add(new_account_data)
        logging.info(f"Added authorized account: ID={doc_ref[1].id}, Account={account_number}")
        return True, f"Account {account_number} added successfully."
    except Exception as e:
        logging.exception(f"Error adding authorized account {account_number}: {e}")
        return False, f"Error adding account: {str(e)}"

def update_authorized_account(doc_id: str, updates: Dict[str, Any]) -> Tuple[bool, str]:
    """Updates an existing authorized account document."""
    if not firebase_auth._firebase_initialized or not firebase_auth._db: return False, "Firebase not initialized."
    try:
        if 'plan_duration_days' in updates:
            duration_days = updates.pop('plan_duration_days')
            try:
                # Get current document to read existing end date
                account_ref = firebase_auth._db.collection('authorized_accounts').document(doc_id)
                account_data = account_ref.get().to_dict()
                
                current_end_date = account_data.get('plan_end_date')
                
                # If there's a valid current end date and it's in the future, extend from there
                if current_end_date and hasattr(current_end_date, 'astimezone'):
                    now = datetime.now(timezone.utc)
                    if current_end_date.astimezone(timezone.utc) > now:
                        # Extend from current end date
                        updates['plan_end_date'] = current_end_date + timedelta(days=int(duration_days))
                        logging.info(f"Extending subscription from {current_end_date} by {duration_days} days")
                    else:
                        # End date is in the past, start from now
                        updates['plan_end_date'] = now + timedelta(days=int(duration_days))
                        logging.info(f"Previous subscription expired, setting new end date from today + {duration_days} days")
                else:
                    # No valid end date found, start from now
                    updates['plan_end_date'] = datetime.now(timezone.utc) + timedelta(days=int(duration_days))
                    logging.info(f"No previous end date, setting new end date to today + {duration_days} days")
            except ValueError:
                return False, "Invalid plan duration provided."
            except Exception as e:
                logging.exception(f"Error calculating plan end date: {e}")
                return False, f"Error calculating plan end date: {str(e)}"
        if 'status' in updates: updates['status'] = updates['status'].lower()
        if 'account_number' in updates: updates['account_number'] = str(updates['account_number'])
        account_ref = firebase_auth._db.collection('authorized_accounts').document(doc_id)
        account_ref.update(updates)
        logging.info(f"Updated authorized account: ID={doc_id}, Updates={updates}")
        return True, f"Account {doc_id} updated successfully."
    except Exception as e:
        logging.exception(f"Error updating authorized account {doc_id}: {e}")
        return False, f"Error updating account: {str(e)}"

def delete_authorized_account(doc_id: str) -> Tuple[bool, str]:
    """Deletes an authorized account document."""
    if not firebase_auth._firebase_initialized or not firebase_auth._db: return False, "Firebase not initialized."
    try:
        account_ref = firebase_auth._db.collection('authorized_accounts').document(doc_id)
        account_ref.delete()
        logging.info(f"Deleted authorized account: ID={doc_id}")
        return True, f"Account {doc_id} deleted successfully."
    except Exception as e:
        logging.exception(f"Error deleting authorized account {doc_id}: {e}")
        return False, f"Error deleting account: {str(e)}"

# --- Session Monitoring (Admin Panel Read) ---

def get_trading_sessions(account_filter: Optional[str] = None,
                         server_filter: Optional[str] = None,
                         status_filter: Optional[str] = None,
                         start_date_filter: Optional[datetime] = None,
                         end_date_filter: Optional[datetime] = None,
                         limit: int = 100) -> List[Dict[str, Any]]:
    """Fetches trading session documents with optional filters."""
    if not firebase_auth._firebase_initialized or not firebase_auth._db: return []
    try:
        _, users_cache_by_acc_srv = get_authorized_accounts()
        if not users_cache_by_acc_srv: logging.warning("User cache empty.")

        sessions_ref = firebase_auth._db.collection('trading_sessions')
        query = sessions_ref
        if account_filter: query = query.where('account_number', '==', str(account_filter))
        if server_filter: query = query.where('server_name', '==', server_filter)
        if status_filter and status_filter != 'all': query = query.where('status', '==', status_filter)
        if start_date_filter:
            if start_date_filter.tzinfo is None: start_date_filter = start_date_filter.replace(tzinfo=timezone.utc)
            query = query.where('start_time', '>=', start_date_filter)
        if end_date_filter:
            end_date_inclusive = end_date_filter + timedelta(days=1)
            if end_date_inclusive.tzinfo is None: end_date_inclusive = end_date_inclusive.replace(tzinfo=timezone.utc)
            query = query.where('start_time', '<', end_date_inclusive)
        query = query.order_by('start_time', direction=firestore.Query.DESCENDING).limit(limit)

        sessions = []
        for doc in query.stream():
            session_data = doc.to_dict();
            if not session_data: continue
            session_data['doc_id'] = doc.id
            session_acc_num = session_data.get('account_number')
            session_srv_name = session_data.get('server_name')
            user_name = 'Unknown'
            if session_acc_num and session_srv_name:
                lookup_key = (str(session_acc_num), session_srv_name)
                user_name = users_cache_by_acc_srv.get(lookup_key, 'Unknown')
                if user_name == 'Unknown': logging.warning(f"Session {doc.id}: User name not found for key {lookup_key}.")
            else: logging.warning(f"Session {doc.id} missing account_number/server_name.")
            session_data['user_name'] = user_name
            for field in ['start_time', 'end_time', 'last_update', 'last_trade_time']:
                 ts = session_data.get(field)
                 session_data[f'{field}_str'] = ts.astimezone(timezone.utc).strftime('%Y-%m-%d %H:%M:%S') if ts and hasattr(ts, 'astimezone') else 'N/A'
            sessions.append(session_data)
        logging.info(f"Fetched {len(sessions)} trading sessions matching filters.")
        return sessions
    except Exception as e:
        logging.exception(f"Error fetching trading sessions: {e}")
        return []

# --- Client-Side Session & Trade Recording ---

def create_trading_session(account_fs_id: str, account_number: str, server_name: str,
                           initial_balance: float, initial_equity: float):
    """Creates a new trading session document in Firestore."""
    global _current_client_session_id
    if not firebase_auth._firebase_initialized or not firebase_auth._db:
        logging.error("Firebase not initialized. Cannot create session.")
        return
    if _current_client_session_id:
        logging.warning(f"Attempted to create a new session while one is active ({_current_client_session_id}). Ending previous one first.")
        end_trading_session(0.0, 0.0) # End previous session with placeholder values

    try:
        sessions_ref = firebase_auth._db.collection('trading_sessions')
        session_data = {
            'account_fs_id': account_fs_id, # Link to the authorized account document
            'account_number': str(account_number),
            'server_name': server_name,
            'start_time': SERVER_TIMESTAMP,
            'end_time': None,
            'status': 'active',
            'initial_balance': initial_balance,
            'initial_equity': initial_equity,
            'final_balance': None,
            'final_equity': None,
            'trades_count': 0,
            'last_update': SERVER_TIMESTAMP,
            'last_trade_time': None
        }
        update_time, doc_ref = sessions_ref.add(session_data)
        _current_client_session_id = doc_ref.id
        logging.info(f"Started new trading session: ID={_current_client_session_id} for account {account_number}")
    except Exception as e:
        logging.exception(f"Error creating trading session for account {account_number}: {e}")
        _current_client_session_id = None

def end_trading_session(final_balance: float, final_equity: float):
    """Ends the current trading session, updating final status and values."""
    global _current_client_session_id
    if not _current_client_session_id:
        logging.warning("Attempted to end session, but no active session ID found.")
        return
    if not firebase_auth._firebase_initialized or not firebase_auth._db:
        logging.error("Firebase not initialized. Cannot end session.")
        return

    try:
        session_ref = firebase_auth._db.collection('trading_sessions').document(_current_client_session_id)
        session_ref.update({
            'end_time': SERVER_TIMESTAMP,
            'status': 'completed',
            'final_balance': final_balance,
            'final_equity': final_equity,
            'last_update': SERVER_TIMESTAMP
        })
        logging.info(f"Ended trading session: ID={_current_client_session_id}")
        _current_client_session_id = None # Clear the global ID
    except Exception as e:
        logging.exception(f"Error ending trading session {_current_client_session_id}: {e}")
        # Keep the ID in case we want to retry or handle the error? For now, clear it.
        _current_client_session_id = None

def record_trade(trade_data: Dict[str, Any]):
    """Records a trade action within the current active session's 'trades' subcollection."""
    if not _current_client_session_id:
        logging.warning("Cannot record trade: No active session ID.")
        return
    if not firebase_auth._firebase_initialized or not firebase_auth._db:
        logging.error("Firebase not initialized. Cannot record trade.")
        return

    try:
        session_ref = firebase_auth._db.collection('trading_sessions').document(_current_client_session_id)
        trades_subcollection = session_ref.collection('trades')

        # Prepare trade document data
        # Ensure required fields are present or have defaults
        record = {
            'timestamp': SERVER_TIMESTAMP,
            'action': trade_data.get('action', 'unknown'), # e.g., 'place', 'close'
            'order_type': trade_data.get('type', 'unknown'), # e.g., 'buy', 'sell'
            'symbol': trade_data.get('symbol', 'unknown'),
            'volume': trade_data.get('volume', 0.0),
            'price': trade_data.get('price', 0.0),
            'sl': trade_data.get('sl', 0.0),
            'tp': trade_data.get('tp', 0.0),
            'profit': trade_data.get('profit'), # Can be None until close
            'ticket': trade_data.get('ticket', 0),
            'result_status': trade_data.get('status', 'unknown'), # e.g., 'success', 'failed'
            'result_message': trade_data.get('message', '')
        }
        trades_subcollection.add(record)

        # Increment trades_count in the main session document
        session_ref.update({
            'trades_count': firestore.Increment(1),
            'last_trade_time': SERVER_TIMESTAMP,
            'last_update': SERVER_TIMESTAMP
        })
        logging.info(f"Recorded trade for session {_current_client_session_id}: Ticket {record.get('ticket')}")

    except Exception as e:
        logging.exception(f"Error recording trade for session {_current_client_session_id}: {e}")

def update_trading_session(current_balance: float, current_equity: float):
    """Periodically updates the active session document."""
    if not _current_client_session_id:
        # logging.debug("No active session to update.") # Too noisy for periodic checks
        return
    if not firebase_auth._firebase_initialized or not firebase_auth._db:
        logging.error("Firebase not initialized. Cannot update session.")
        return

    try:
        session_ref = firebase_auth._db.collection('trading_sessions').document(_current_client_session_id)
        session_ref.update({
            'current_balance': current_balance, # Add current balance field
            'current_equity': current_equity,   # Add current equity field
            'last_update': SERVER_TIMESTAMP
        })
        # logging.debug(f"Updated session {_current_client_session_id} with balance/equity.") # Can be noisy
    except Exception as e:
        logging.exception(f"Error updating trading session {_current_client_session_id}: {e}")
