import React, { useState, useEffect } from 'react';
import { useNotification } from './Notification';
import '../styles/entry-spacing.css';

const EntrySpacingSettings = ({ onSettingsChange }) => {
  const [settings, setSettings] = useState({
    enabled: false,
    atr_period: 14,
    atr_multiplier: 2.0,
    min_spacing_points: 10,
    max_spacing_points: 500,
    progressive_spacing_multipliers: [1.0, 1.5, 2.0, 2.5, 3.0, 4.0],
    progressive_volume_multipliers: [1.0, 0.9, 0.8, 0.7, 0.6, 0.5],
    time_based_spacing: {
      enabled: true,
      base_minutes: 5,
      loss_cooldown_multipliers: [1, 2, 3, 4, 5, 6]
    },
    max_consecutive_losses: 5
  });
  
  const [statistics, setStatistics] = useState(null);
  const [loading, setLoading] = useState(false);
  const notify = useNotification();

  // Load current settings on component mount
  useEffect(() => {
    loadSettings();
    loadStatistics();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/entry_spacing/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      }
    } catch (error) {
      console.error('Failed to load entry spacing settings:', error);
    }
  };

  const loadStatistics = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/entry_spacing/statistics');
      if (response.ok) {
        const data = await response.json();
        setStatistics(data);
      }
    } catch (error) {
      console.error('Failed to load entry spacing statistics:', error);
    }
  };

  const saveSettings = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:5001/api/entry_spacing/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
      });

      if (response.ok) {
        notify.success('Success', 'Entry spacing settings saved successfully');
        if (onSettingsChange) {
          onSettingsChange(settings);
        }
        loadStatistics(); // Refresh statistics
      } else {
        const error = await response.json();
        notify.error('Error', `Failed to save settings: ${error.message || 'Unknown error'}`);
      }
    } catch (error) {
      notify.error('Error', 'Failed to save entry spacing settings');
      console.error('Save settings error:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetSettings = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/entry_spacing/reset', {
        method: 'POST'
      });

      if (response.ok) {
        notify.success('Success', 'Entry spacing data reset successfully');
        loadStatistics();
      } else {
        const error = await response.json();
        notify.error('Error', `Failed to reset: ${error.message || 'Unknown error'}`);
      }
    } catch (error) {
      notify.error('Error', 'Failed to reset entry spacing data');
      console.error('Reset error:', error);
    }
  };

  const updateSetting = (path, value) => {
    const newSettings = { ...settings };
    const keys = path.split('.');
    let current = newSettings;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    setSettings(newSettings);
  };

  const updateArraySetting = (path, index, value) => {
    const newSettings = { ...settings };
    const keys = path.split('.');
    let current = newSettings;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]][index] = parseFloat(value);
    setSettings(newSettings);
  };

  return (
    <div className="entry-spacing-settings">
      <div className="settings-header">
        <div className="header-content">
          <h3>📊 Entry Spacing Manager</h3>
          <p className="header-description">
            Intelligent entry timing system that adapts spacing and position sizing based on market volatility (ATR) 
            and recent trading performance to optimize risk management and reduce drawdowns.
          </p>
        </div>
        <div className="settings-actions">
          <button 
            onClick={loadStatistics} 
            className="button secondary"
            title="Refresh Statistics"
          >
            🔄 Refresh
          </button>
          <button 
            onClick={resetSettings} 
            className="button danger"
            title="Reset All Data"
          >
            🗑️ Reset Data
          </button>
          <button 
            onClick={saveSettings} 
            className="button primary"
            disabled={loading}
          >
            {loading ? '💾 Saving...' : '💾 Save Settings'}
          </button>
        </div>
      </div>

      <div className="settings-content">
        <div className="settings-panel">
          {/* Master Control Card */}
          <div className="settings-card master-control">
            <div className="card-header">
              <h4>🎛️ Master Control</h4>
              <div className="card-description">
                Enable or disable the entire Entry Spacing system. When enabled, all trades will be subject to 
                spacing rules based on volatility and performance history.
              </div>
            </div>
            
            <div className="card-content">
              <div className="form-group master-toggle">
                <label className="checkbox-label enhanced">
                  <input
                    type="checkbox"
                    checked={settings.enabled}
                    onChange={(e) => updateSetting('enabled', e.target.checked)}
                  />
                  <span className="toggle-text">
                    {settings.enabled ? '✅ Entry Spacing Manager ENABLED' : '❌ Entry Spacing Manager DISABLED'}
                  </span>
                </label>
                
                <div className="status-container">
                  <div className="status-indicator">
                    <div className={`status-dot ${settings.enabled ? 'active' : 'inactive'}`}></div>
                    <span className="status-text">
                      <strong>{settings.enabled ? 'System Active' : 'System Inactive'}</strong>
                    </span>
                  </div>
                  <div className="status-description">
                    {settings.enabled ? 'Spacing rules will be applied to all trades' : 'Normal trading without spacing restrictions'}
                  </div>
                </div>
              </div>

              <div className="form-group loss-tracking">
                <div className="track-losses-header">
                  <label className="checkbox-label enhanced">
                    <input
                      type="checkbox"
                      checked={true}
                      readOnly
                    />
                    <span className="toggle-text">
                      📊 Max Consecutive Losses to Track
                    </span>
                  </label>
                </div>
                
                <div className="track-losses-input">
                  <input
                    type="number"
                    min="1"
                    max="10"
                    value={settings.max_consecutive_losses}
                    onChange={(e) => updateSetting('max_consecutive_losses', parseInt(e.target.value))}
                  />
                </div>
                
                <div className="input-hint detailed">
                  <strong>Purpose:</strong> Sets how many consecutive losses the system will track and respond to.<br/>
                  <strong>Effect:</strong> After this many losses, the system stops increasing spacing multipliers.<br/>
                  <strong>Recommended:</strong> 5-7 for most strategies to balance protection and opportunity.
                </div>
              </div>
            </div>
          </div>

          {/* ATR-Based Spacing Card */}
          <div className="settings-card atr-spacing">
            <div className="card-header">
              <h4>📐 ATR-Based Spacing</h4>
              <div className="card-description">
                Use Average True Range (ATR) to dynamically adjust spacing between entries based on current market volatility. 
                Higher volatility = wider spacing, lower volatility = tighter spacing.
              </div>
            </div>
            
            <div className="card-content">
              <div className="form-row">
                <div className="form-group">
                  <label>⏱️ ATR Period</label>
                  <input
                    type="number"
                    min="5"
                    max="50"
                    value={settings.atr_period}
                    onChange={(e) => updateSetting('atr_period', parseInt(e.target.value))}
                  />
                  <div className="input-hint detailed">
                    <strong>What it is:</strong> Number of candlesticks used to calculate average volatility.<br/>
                    <strong>Effect:</strong> Shorter = more responsive, Longer = more stable.<br/>
                    <strong>Default:</strong> 14 periods (industry standard).
                  </div>
                </div>

                <div className="form-group">
                  <label>🔢 ATR Multiplier</label>
                  <input
                    type="number"
                    min="0.5"
                    max="5.0"
                    step="0.1"
                    value={settings.atr_multiplier}
                    onChange={(e) => updateSetting('atr_multiplier', parseFloat(e.target.value))}
                  />
                  <div className="input-hint detailed">
                    <strong>What it is:</strong> Multiplies ATR value to determine spacing distance.<br/>
                    <strong>Effect:</strong> Higher = wider spacing, Lower = tighter spacing.<br/>
                    <strong>Example:</strong> ATR=0.001, Multiplier=2.0 → 200 point spacing.
                  </div>
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>📉 Min Spacing (Points)</label>
                  <input
                    type="number"
                    min="1"
                    max="100"
                    value={settings.min_spacing_points}
                    onChange={(e) => updateSetting('min_spacing_points', parseInt(e.target.value))}
                  />
                  <div className="input-hint detailed">
                    <strong>Safety Floor:</strong> Minimum spacing regardless of low volatility.<br/>
                    <strong>Prevents:</strong> Trades too close together in quiet markets.<br/>
                    <strong>Typical:</strong> 10-20 points for major pairs.
                  </div>
                </div>

                <div className="form-group">
                  <label>📈 Max Spacing (Points)</label>
                  <input
                    type="number"
                    min="50"
                    max="1000"
                    value={settings.max_spacing_points}
                    onChange={(e) => updateSetting('max_spacing_points', parseInt(e.target.value))}
                  />
                  <div className="input-hint detailed">
                    <strong>Safety Ceiling:</strong> Maximum spacing to prevent missing opportunities.<br/>
                    <strong>Prevents:</strong> Excessive spacing in very volatile markets.<br/>
                    <strong>Typical:</strong> 200-500 points for major pairs.
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Progressive Scaling Card */}
          <div className="settings-card progressive-scaling">
            <div className="card-header">
              <h4>📊 Progressive Scaling (Anti-Martingale)</h4>
              <div className="card-description">
                Automatically adjust spacing and position sizes after consecutive losses to reduce risk during 
                unfavorable market conditions. Unlike Martingale, this REDUCES risk after losses.
              </div>
            </div>
            
            <div className="card-content">
              <div className="scaling-explanation">
                <div className="explanation-box">
                  <h5>🎯 How Progressive Scaling Works:</h5>
                  <ul>
                    <li><strong>After Loss 1:</strong> Spacing becomes wider, position size may decrease</li>
                    <li><strong>After Loss 2:</strong> Even wider spacing, smaller positions</li>
                    <li><strong>Continues:</strong> Up to max consecutive losses setting</li>
                    <li><strong>After Win:</strong> Resets to normal spacing and sizing</li>
                  </ul>
                </div>
              </div>

              <div className="form-group">
                <label>📏 Spacing Multipliers by Loss Count</label>
                <div className="array-inputs enhanced">
                  {settings.progressive_spacing_multipliers.map((multiplier, index) => (
                    <div key={index} className="array-input-item enhanced">
                      <label>Loss {index + 1}</label>
                      <input
                        type="number"
                        min="0.5"
                        max="10.0"
                        step="0.1"
                        value={multiplier.toFixed(1)}
                        onChange={(e) => updateArraySetting('progressive_spacing_multipliers', index, e.target.value)}
                      />
                      <div className="multiplier-effect">
                        {index === 0 ? 'Normal' : `${(multiplier * 100).toFixed(0)}% wider`}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="input-hint detailed">
                  <strong>Purpose:</strong> Controls how much wider spacing becomes after each consecutive loss.<br/>
                  <strong>Example:</strong> Base spacing 100 points + Loss 1 (1.5x) = 150 points spacing.<br/>
                  <strong>Strategy:</strong> Gradually increase to give market more time to settle.
                </div>
              </div>

              <div className="form-group">
                <label>💰 Volume Multipliers by Loss Count</label>
                <div className="array-inputs enhanced">
                  {settings.progressive_volume_multipliers.map((multiplier, index) => (
                    <div key={index} className="array-input-item enhanced">
                      <label>Loss {index + 1}</label>
                      <input
                        type="number"
                        min="0.1"
                        max="2.0"
                        step="0.1"
                        value={multiplier.toFixed(1)}
                        onChange={(e) => updateArraySetting('progressive_volume_multipliers', index, e.target.value)}
                      />
                      <div className="multiplier-effect">
                        {index === 0 ? 'Normal' : `${(multiplier * 100).toFixed(0)}% size`}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="input-hint detailed">
                  <strong>Risk Management:</strong> Reduces position size after consecutive losses.<br/>
                  <strong>Example:</strong> Normal 0.1 lot + Loss 1 (0.9x) = 0.09 lot position.<br/>
                  <strong>Benefit:</strong> Limits exposure during unfavorable market conditions.
                </div>
              </div>
            </div>
          </div>

          {/* Time-Based Spacing Card */}
          <div className="settings-card time-spacing">
            <div className="card-header">
              <h4>⏰ Time-Based Spacing</h4>
              <div className="card-description">
                Add minimum time delays between entries to prevent overtrading and allow market conditions to change. 
                Especially useful in ranging or choppy markets.
              </div>
            </div>
            
            <div className="card-content">
              <div className="form-group">
                <label className="checkbox-label enhanced">
                  <input
                    type="checkbox"
                    checked={settings.time_based_spacing.enabled}
                    onChange={(e) => updateSetting('time_based_spacing.enabled', e.target.checked)}
                  />
                  <span className="toggle-text">
                    {settings.time_based_spacing.enabled ? '✅ Time-Based Spacing ENABLED' : '❌ Time-Based Spacing DISABLED'}
                  </span>
                </label>
                <div className="input-hint detailed">
                  <strong>Purpose:</strong> Prevents rapid-fire entries that might occur in volatile conditions.<br/>
                  <strong>Benefit:</strong> Gives market time to develop new patterns between trades.
                </div>
              </div>

              <div className="form-group">
                <label>⏱️ Base Time Delay (Minutes)</label>
                <input
                  type="number"
                  min="1"
                  max="60"
                  value={settings.time_based_spacing.base_minutes}
                  onChange={(e) => updateSetting('time_based_spacing.base_minutes', parseInt(e.target.value))}
                  disabled={!settings.time_based_spacing.enabled}
                />
                <div className="input-hint detailed">
                  <strong>Base Cooldown:</strong> Minimum time between any two entries.<br/>
                  <strong>Effect:</strong> All entries must wait at least this long.<br/>
                  <strong>Typical:</strong> 3-10 minutes depending on timeframe and strategy.
                </div>
              </div>

              <div className="form-group">
                <label>🕐 Loss Cooldown Multipliers</label>
                <div className="array-inputs enhanced">
                  {settings.time_based_spacing.loss_cooldown_multipliers.map((multiplier, index) => (
                    <div key={index} className="array-input-item enhanced">
                      <label>Loss {index + 1}:</label>
                      <input
                        type="number"
                        min="1"
                        max="10"
                        value={multiplier}
                        onChange={(e) => updateArraySetting('time_based_spacing.loss_cooldown_multipliers', index, e.target.value)}
                        disabled={!settings.time_based_spacing.enabled}
                      />
                      <div className="multiplier-effect">
                        {`${multiplier * settings.time_based_spacing.base_minutes} min cooldown`}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="input-hint detailed">
                  <strong>Progressive Cooldowns:</strong> Longer waits after consecutive losses.<br/>
                  <strong>Example:</strong> Base 5 min + Loss 1 (2x) = 10 min cooldown.<br/>
                  <strong>Psychology:</strong> Forces patience when strategy isn't working.
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Statistics Panel */}
        <div className="statistics-panel enhanced">
          <div className="stats-header">
            <h4>📈 Entry Spacing Statistics</h4>
            <div className="stats-description">
              Real-time performance metrics and current system status
            </div>
          </div>
          
          {statistics ? (
            <div className="statistics-content">
              <div className="stats-section performance">
                <h5>🎯 Overall Performance</h5>
                <div className="stats-grid">
                  <div className="stat-card">
                    <div className="stat-icon">📊</div>
                    <div className="stat-info">
                      <div className="stat-value">{statistics.total_trades || 0}</div>
                      <div className="stat-label">Total Trades</div>
                    </div>
                  </div>
                  <div className="stat-card">
                    <div className="stat-icon">🎯</div>
                    <div className="stat-info">
                      <div className="stat-value">
                        {statistics.win_rate ? (statistics.win_rate * 100).toFixed(1) + '%' : 'N/A'}
                      </div>
                      <div className="stat-label">Win Rate</div>
                    </div>
                  </div>
                  <div className="stat-card">
                    <div className="stat-icon">💰</div>
                    <div className="stat-info">
                      <div className={`stat-value ${(statistics.total_profit || 0) >= 0 ? 'positive' : 'negative'}`}>
                        ${Math.abs(statistics.total_profit || 0).toFixed(2)}
                      </div>
                      <div className="stat-label">Total P/L</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="stats-section status">
                <h5>🔄 Current Status</h5>
                {Object.entries(statistics.symbol_stats || {}).map(([symbol, stats]) => (
                  <div key={symbol} className="symbol-stats-card">
                    <div className="symbol-header">
                      <h6>{symbol}</h6>
                      <div className={`loss-badge ${stats.consecutive_losses > 0 ? 'active' : ''}`}>
                        {stats.consecutive_losses || 0} losses
                      </div>
                    </div>
                    <div className="symbol-details">
                      <div className="detail-row">
                        <span className="detail-icon">📏</span>
                        <span className="detail-label">Spacing Multiplier:</span>
                        <span className="detail-value">{stats.current_spacing_multiplier?.toFixed(2) || '1.00'}x</span>
                      </div>
                      <div className="detail-row">
                        <span className="detail-icon">💰</span>
                        <span className="detail-label">Volume Multiplier:</span>
                        <span className="detail-value">{stats.current_volume_multiplier?.toFixed(2) || '1.00'}x</span>
                      </div>
                      <div className="detail-row">
                        <span className="detail-icon">⏰</span>
                        <span className="detail-label">Last Entry:</span>
                        <span className="detail-value time">
                          {stats.last_entry_time ? new Date(stats.last_entry_time).toLocaleString() : 'Never'}
                        </span>
                      </div>
                      <div className="detail-row">
                        <span className="detail-icon">🟢</span>
                        <span className="detail-label">Next Entry Allowed:</span>
                        <span className="detail-value time">
                          {stats.next_entry_allowed ? new Date(stats.next_entry_allowed).toLocaleString() : 'Now'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="stats-section analysis">
                <h5>📐 ATR Analysis</h5>
                <div className="atr-grid">
                  {Object.entries(statistics.atr_analysis || {}).map(([symbol, atr]) => (
                    <div key={symbol} className="atr-card">
                      <div className="atr-symbol">{symbol}</div>
                      <div className="atr-details">
                        <div className="atr-row">
                          <span className="atr-label">Current ATR:</span>
                          <span className="atr-value">{atr.toFixed(5)}</span>
                        </div>
                        <div className="atr-row">
                          <span className="atr-label">Spacing Points:</span>
                          <span className="atr-value points">
                            {Math.round(atr * settings.atr_multiplier * Math.pow(10, 5))} pts
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="no-stats enhanced">
              <div className="no-stats-icon">📊</div>
              <h5>No Statistics Available</h5>
              <p>Statistics will appear after the first trade is executed.</p>
              <p>The system is ready to start tracking your trading performance.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EntrySpacingSettings;
