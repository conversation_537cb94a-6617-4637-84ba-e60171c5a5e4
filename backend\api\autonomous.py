from flask import Blueprint, request, jsonify, current_app
import os
import json
import logging
import time
import threading
# Removed requests import
from datetime import datetime, timedelta
from typing import Dict, List, Any
import MetaTrader5 as mt5
from analysis import AnalysisEngine
# Import the signal generation function directly from the signals module
from backend.api.signals import generate_signals_from_analysis
# Import exit strategy configuration
from backend.api.exit_strategy_config import DEFAULT_EXIT_STRATEGIES, EXIT_STRATEGY_SETTINGS
# Import strategy orchestrator
from backend.strategy_orchestrator import StrategyOrchestrator

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
autonomous_bp = Blueprint('autonomous_bp', __name__)

# Global variables
AUTONOMOUS_RUNNING = False
AUTONOMOUS_THREAD = None
# Configure autonomous trading global settings
AUTONOMOUS_CONFIG = {
    "symbols": ["EURUSD", "GBPUSD", "USDJPY", "XAUUSD"],
    "timeframes": ["M1", "M5", "M15", "M30", "H1", "H4", "D1", "W1", "MN1"],
    "scan_interval": 300,
    "min_confidence": 60,
    "max_trades": 5,
    "allowed_styles": ["Scalping", "Short-term", "Swing", "Position"],
    "allow_multiple_positions": False,
    "risk_percent": 1.0,
    "use_exit_strategies": True  # Enable automatic exit strategies
}

def _trading_loop(app):
    """
    Main autonomous trading loop.

    Args:
        app: Flask application instance
    """
    global AUTONOMOUS_RUNNING

    logger.info("Autonomous trading loop started")

    # Track last trade time for each symbol to avoid excessive trading
    last_trade_time = {}

    while AUTONOMOUS_RUNNING:
        try:
            # Use the Flask app context
            with app.app_context():
                # Get MT5 instance
                mt5_instance = app.config.get('MT5_INSTANCE')
                if not mt5_instance or not mt5_instance.is_connected():
                    logger.error("MT5 connection lost or not available")
                    time.sleep(30)  # Wait before retrying
                    continue

                # Log that we're running
                logger.info(f"Autonomous trading running with config: {AUTONOMOUS_CONFIG}")

                # Get current open positions
                positions_result = mt5_instance.get_open_positions()
                if not positions_result.get("success", False):
                    logger.error(f"Failed to get open positions: {positions_result.get('message', 'Unknown error')}")
                    time.sleep(10)
                    continue

                open_positions = positions_result.get("positions", [])
                logger.info(f"Current open positions: {len(open_positions)}")

                # Check if we've reached max trades
                if len(open_positions) >= AUTONOMOUS_CONFIG["max_trades"]:
                    logger.info(f"Maximum number of trades reached ({AUTONOMOUS_CONFIG['max_trades']}). Skipping analysis.")
                    time.sleep(10)
                    continue

                # Process each symbol in the configuration
                for symbol in AUTONOMOUS_CONFIG["symbols"]:
                    # Skip if we've traded this symbol recently (within last 30 seconds)
                    current_time = datetime.now()
                    if symbol in last_trade_time and (current_time - last_trade_time[symbol]).total_seconds() < 30:
                        logger.info(f"Skipping {symbol} - traded recently")
                        continue

                    # Check if we already have a position for this symbol
                    symbol_positions = [p for p in open_positions if p["symbol"] == symbol]
                    if symbol_positions and not AUTONOMOUS_CONFIG["allow_multiple_positions"]:
                        logger.info(f"Skipping {symbol} - already have an open position")
                        continue

                    # Get current price
                    price_data = mt5_instance.get_current_price(symbol)
                    if "error" in price_data:
                        logger.error(f"Failed to get price for {symbol}: {price_data['error']}")
                        continue

                    logger.info(f"Current price for {symbol}: Bid={price_data['bid']}, Ask={price_data['ask']}")

                    # Get the active strategy's timeframe
                    timeframe = AUTONOMOUS_CONFIG["timeframes"][0]
                    min_confidence = AUTONOMOUS_CONFIG["min_confidence"]
                    allowed_styles = AUTONOMOUS_CONFIG["allowed_styles"]

                    logger.info(f"Analyzing {symbol} on {timeframe} timeframe...")

                    # Create analysis engine
                    analyzer = AnalysisEngine(mt5_instance)

                    # Add current price to analyzer
                    analyzer.current_price = price_data

                    # Perform analysis
                    analysis_result = analyzer.get_analysis_for_timeframe(symbol, timeframe)

                    if not analysis_result.get("success"):
                        error_msg = analysis_result.get("message", "Analysis failed")
                        logger.error(f"Analysis failed for {symbol}/{timeframe}: {error_msg}")
                        continue

                    # Extract analysis data
                    if "analysis" in analysis_result:
                        analysis_data = analysis_result["analysis"]
                    elif "data" in analysis_result:
                        analysis_data = analysis_result["data"]
                    else:
                        analysis_data = analysis_result

                    # Ensure we have current price in analysis data
                    analysis_data["current_price"] = price_data

                    # Generate signals using the same function as the trade signal page
                    try:
                        logger.info(f"Generating signals for {symbol} using generate_signals_from_analysis...")
                        signals_result = generate_signals_from_analysis(symbol, timeframe, analysis_data, price_data)
                        logger.info(f"Signal generation complete. Result: {signals_result.keys() if signals_result else 'None'}")

                        # Get all signals
                        all_signals = signals_result.get('signals', [])
                        logger.info(f"Found {len(all_signals)} total signals for {symbol}")

                        # Log all signals for debugging
                        for idx, sig in enumerate(all_signals):
                            logger.info(f"Signal {idx+1}: {sig.get('strategy')} - {sig.get('signalType')} - Confidence: {sig.get('confidence')}%")

                        # Filter signals based on strategy style and confidence
                        valid_signals = []
                        for signal in all_signals:
                            # Check if signal strategy is in allowed styles
                            if signal['strategy'] in [s.lower() for s in allowed_styles]:
                                # For higher timeframes (W1, MN1), we can be more lenient with confidence
                                if timeframe in ['W1', 'MN1'] and signal['confidence'] >= (min_confidence - 10):
                                    valid_signals.append(signal)
                                    logger.info(f"Added {signal['strategy']} signal on higher timeframe {timeframe} with adjusted confidence threshold")
                                # For normal timeframes, use the standard confidence threshold
                                elif signal['confidence'] >= min_confidence:
                                    valid_signals.append(signal)

                        logger.info(f"After filtering: {len(valid_signals)} valid signals for {symbol} (allowed styles: {allowed_styles}, min confidence: {min_confidence})")

                        if not valid_signals:
                            logger.info(f"No valid signals for {symbol} with confidence >= {min_confidence}")
                            continue
                    except Exception as e:
                        logger.exception(f"Error generating signals for {symbol}: {str(e)}")
                        continue

                    # Sort by confidence (highest first)
                    valid_signals.sort(key=lambda x: x['confidence'], reverse=True)

                    # Get the highest confidence signal
                    best_signal = valid_signals[0]
                    signal_type = best_signal['signalType'].lower()

                    logger.info(f"Found {signal_type.upper()} signal for {symbol} with {best_signal['confidence']}% confidence")

                    # Convert to our internal signal format
                    signal = "none"
                    if signal_type == "buy":
                        signal = "buy"
                    elif signal_type == "sell":
                        signal = "sell"

                    if signal == "none":
                        logger.info(f"No actionable trading signal for {symbol}")
                        continue

                    # Use the stop loss from the signal
                    sl_level = best_signal['stopLoss']
                    tp_level = best_signal['takeProfit']

                    # Calculate SL points for lot size calculation
                    point = mt5.symbol_info(symbol).point
                    sl_points = abs(price_data["bid"] - sl_level) / point if signal == "buy" else abs(price_data["ask"] - sl_level) / point

                    # Calculate lot size based on risk percentage and actual stop loss
                    lot_size_result = mt5_instance.calculate_lot_size(symbol, AUTONOMOUS_CONFIG["risk_percent"], sl_points)

                    if not lot_size_result.get("success", False):
                        logger.error(f"Failed to calculate lot size: {lot_size_result.get('message', 'Unknown error')}")
                        continue

                    lot_size = lot_size_result.get("lot_size", 0.01)  # Default to minimum if calculation fails
                    logger.info(f"Calculated lot size for {symbol}: {lot_size}")

                    # Place order based on signal
                    if signal == "buy":
                        try:
                            # Get symbol info for proper point calculation and rounding
                            symbol_info = mt5.symbol_info(symbol)
                            if symbol_info is None:
                                logger.error(f"Failed to get symbol info for {symbol}")
                                continue

                            # Make sure symbol is selected for trading
                            if not symbol_info.visible:
                                if not mt5.symbol_select(symbol, True):
                                    logger.error(f"Failed to select symbol {symbol} for trading")
                                    continue

                            # Get digits for proper rounding
                            digits = symbol_info.digits
                            sl_level = round(sl_level, digits)
                            tp_level = round(tp_level, digits)

                            # Prepare order request dictionary directly
                            order_request = {
                                'action': mt5.TRADE_ACTION_DEAL,
                                'symbol': symbol,
                                'volume': float(lot_size),
                                'type': mt5.ORDER_TYPE_BUY,
                                'price': mt5.symbol_info_tick(symbol).ask, # Use current ask price
                                'sl': sl_level,
                                'tp': tp_level,
                                'deviation': 20,
                                'magic': 12345,
                                'comment': f"Auto_{best_signal['strategy']}",
                                'type_time': mt5.ORDER_TIME_GTC,
                                'type_filling': mt5.ORDER_FILLING_FOK # Use FOK as per original API call
                            }

                            logger.info(f"Attempting direct BUY order send: {order_request}")

                            # Send order directly using mt5 instance
                            result = mt5.order_send(order_request)

                            if result is None:
                                error_code = mt5.last_error()
                                logger.error(f"Direct BUY order send failed for {symbol}. MT5 Error Code: {error_code}")
                                continue

                            # Log the raw result for debugging
                            logger.info(f"MT5 order_send raw result for BUY: {result}")

                            # Check order execution result
                            success_codes = [mt5.TRADE_RETCODE_DONE, mt5.TRADE_RETCODE_PLACED, mt5.TRADE_RETCODE_DONE_PARTIAL]
                            if result.retcode not in success_codes:
                                logger.error(f"BUY order execution failed for {symbol} with retcode: {result.retcode} - {result.comment}")
                                continue

                            logger.info(f"BUY order placed successfully via direct call: Order #{result.order}")
                            position_id = result.order
                            last_trade_time[symbol] = current_time

                            # Apply exit strategies if enabled
                            if EXIT_STRATEGY_SETTINGS.get("enabled", True):
                                try:
                                    # Get the trading style from the signal
                                    trading_style = best_signal['strategy'].lower()

                                    # Get applicable exit strategies for this trading style
                                    applicable_strategies = DEFAULT_EXIT_STRATEGIES.get(trading_style, [])

                                    # Limit the number of strategies per position
                                    max_strategies = EXIT_STRATEGY_SETTINGS.get("max_strategies_per_position", 2)
                                    if len(applicable_strategies) > max_strategies:
                                        applicable_strategies = applicable_strategies[:max_strategies]

                                    if applicable_strategies:
                                        logger.info(f"Applying {len(applicable_strategies)} exit strategies to BUY position {position_id}")

                                        # Apply each configured exit strategy
                                        for strategy_config in applicable_strategies:
                                            # Add symbol to params if not specified
                                            params = strategy_config.get('params', {}).copy()
                                            if 'symbol' not in params:
                                                params['symbol'] = symbol

                                            # Apply the exit strategy
                                            exit_result = mt5_instance.exit_strategies.apply_exit_strategy(
                                                position_id=position_id,
                                                strategy_name=strategy_config['strategy_name'],
                                                params=params,
                                                monitoring_interval=strategy_config.get('monitoring_interval', 60)
                                            )

                                            if exit_result.get('success', False):
                                                logger.info(f"Successfully applied {strategy_config['strategy_name']} exit strategy to position {position_id}")
                                            else:
                                                logger.warning(f"Failed to apply {strategy_config['strategy_name']} exit strategy: {exit_result.get('message', 'Unknown error')}")
                                    else:
                                        logger.info(f"No applicable exit strategies found for trading style: {trading_style}")
                                except Exception as e:
                                    logger.exception(f"Error applying exit strategies to position {position_id}: {str(e)}")
                        except Exception as e:
                            logger.exception(f"Exception placing direct BUY order for {symbol}: {str(e)}")
                            continue

                    elif signal == "sell":
                        try:
                            # Get symbol info for proper point calculation and rounding
                            symbol_info = mt5.symbol_info(symbol)
                            if symbol_info is None:
                                logger.error(f"Failed to get symbol info for {symbol}")
                                continue

                            # Make sure symbol is selected for trading
                            if not symbol_info.visible:
                                if not mt5.symbol_select(symbol, True):
                                    logger.error(f"Failed to select symbol {symbol} for trading")
                                    continue

                            # Get digits for proper rounding
                            digits = symbol_info.digits
                            sl_level = round(sl_level, digits)
                            tp_level = round(tp_level, digits)

                            # Prepare order request dictionary directly
                            order_request = {
                                'action': mt5.TRADE_ACTION_DEAL,
                                'symbol': symbol,
                                'volume': float(lot_size),
                                'type': mt5.ORDER_TYPE_SELL,
                                'price': mt5.symbol_info_tick(symbol).bid, # Use current bid price
                                'sl': sl_level,
                                'tp': tp_level,
                                'deviation': 20,
                                'magic': 12345,
                                'comment': f"Auto_{best_signal['strategy']}",
                                'type_time': mt5.ORDER_TIME_GTC,
                                'type_filling': mt5.ORDER_FILLING_FOK # Use FOK as per original API call
                            }

                            logger.info(f"Attempting direct SELL order send: {order_request}")

                            # Send order directly using mt5 instance
                            result = mt5.order_send(order_request)

                            if result is None:
                                error_code = mt5.last_error()
                                logger.error(f"Direct SELL order send failed for {symbol}. MT5 Error Code: {error_code}")
                                continue

                            # Log the raw result for debugging
                            logger.info(f"MT5 order_send raw result for SELL: {result}")

                            # Check order execution result
                            success_codes = [mt5.TRADE_RETCODE_DONE, mt5.TRADE_RETCODE_PLACED, mt5.TRADE_RETCODE_DONE_PARTIAL]
                            if result.retcode not in success_codes:
                                logger.error(f"SELL order execution failed for {symbol} with retcode: {result.retcode} - {result.comment}")
                                continue

                            logger.info(f"SELL order placed successfully via direct call: Order #{result.order}")
                            position_id = result.order
                            last_trade_time[symbol] = current_time

                            # Apply exit strategies if enabled
                            if EXIT_STRATEGY_SETTINGS.get("enabled", True):
                                try:
                                    # Get the trading style from the signal
                                    trading_style = best_signal['strategy'].lower()

                                    # Get applicable exit strategies for this trading style
                                    applicable_strategies = DEFAULT_EXIT_STRATEGIES.get(trading_style, [])

                                    # Limit the number of strategies per position
                                    max_strategies = EXIT_STRATEGY_SETTINGS.get("max_strategies_per_position", 2)
                                    if len(applicable_strategies) > max_strategies:
                                        applicable_strategies = applicable_strategies[:max_strategies]

                                    if applicable_strategies:
                                        logger.info(f"Applying {len(applicable_strategies)} exit strategies to SELL position {position_id}")

                                        # Apply each configured exit strategy
                                        for strategy_config in applicable_strategies:
                                            # Add symbol to params if not specified
                                            params = strategy_config.get('params', {}).copy()
                                            if 'symbol' not in params:
                                                params['symbol'] = symbol

                                            # Apply the exit strategy
                                            exit_result = mt5_instance.exit_strategies.apply_exit_strategy(
                                                position_id=position_id,
                                                strategy_name=strategy_config['strategy_name'],
                                                params=params,
                                                monitoring_interval=strategy_config.get('monitoring_interval', 60)
                                            )

                                            if exit_result.get('success', False):
                                                logger.info(f"Successfully applied {strategy_config['strategy_name']} exit strategy to position {position_id}")
                                            else:
                                                logger.warning(f"Failed to apply {strategy_config['strategy_name']} exit strategy: {exit_result.get('message', 'Unknown error')}")
                                    else:
                                        logger.info(f"No applicable exit strategies found for trading style: {trading_style}")
                                except Exception as e:
                                    logger.exception(f"Error applying exit strategies to position {position_id}: {str(e)}")
                        except Exception as e:
                            logger.exception(f"Exception placing direct SELL order for {symbol}: {str(e)}")
                            continue

            # Sleep before next scan
            time.sleep(10)  # Check every 10 seconds for testing purposes
        except Exception as e:
            logger.exception(f"Exception in autonomous trading loop: {str(e)}")
            time.sleep(60)  # Wait before retrying

# Define available trading strategies
AVAILABLE_STRATEGIES = [
    {
        "id": "scalping_momentum",
        "name": "Scalping Momentum",
        "description": "Short-term trades based on momentum indicators",
        "timeframes": ["M1", "M5", "M15", "M30"],
        "indicators": ["RSI", "MACD", "Bollinger Bands"],
        "style": "Scalping"
    },
    {
        "id": "intraday_trend",
        "name": "Intraday Trend Following",
        "description": "Intraday trades following established trends",
        "timeframes": ["M15", "M30", "H1", "H4"],
        "indicators": ["Moving Averages", "ADX", "Parabolic SAR"],
        "style": "Short-term"
    },
    {
        "id": "swing_reversal",
        "name": "Swing Reversal",
        "description": "Multi-day trades based on trend reversals",
        "timeframes": ["H1", "H4", "D1", "W1"],
        "indicators": ["RSI", "Stochastic", "Support/Resistance"],
        "style": "Swing"
    },
    {
        "id": "position_trading",
        "name": "Position Trading",
        "description": "Long-term trades based on major market trends",
        "timeframes": ["D1", "W1", "MN1"],
        "indicators": ["Moving Averages", "Fibonacci", "Support/Resistance"],
        "style": "Position"
    }
]

# Active strategies (in-memory storage)
ACTIVE_STRATEGIES = []

# Strategy orchestrator instance
STRATEGY_ORCHESTRATOR = StrategyOrchestrator()

@autonomous_bp.route('/strategies', methods=['GET'])
def get_strategies():
    """
    Get available trading strategies.

    Returns:
        JSON: List of available strategies
    """
    return jsonify(AVAILABLE_STRATEGIES)

@autonomous_bp.route('/active', methods=['GET'])
def get_active_strategies():
    """
    Get active trading strategies.

    Returns:
        JSON: List of active strategies
    """
    # Update active strategies with current status
    for strategy in ACTIVE_STRATEGIES:
        strategy["status"] = "active" if AUTONOMOUS_RUNNING else "inactive"

    return jsonify(ACTIVE_STRATEGIES)

@autonomous_bp.route('/activate', methods=['POST'])
def activate_strategy():
    """
    Activate a trading strategy.

    Request body:
    {
        "strategy_id": "scalping_momentum",
        "symbol": "EURUSD",
        "timeframe": "M15",
        "risk_percent": 1.0,
        "max_trades": 5
    }

    Returns:
        JSON: Activation result
    """
    global AUTONOMOUS_RUNNING, AUTONOMOUS_THREAD, AUTONOMOUS_CONFIG, ACTIVE_STRATEGIES

    # Get request data
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400

    # Validate required fields
    required_fields = ["strategy_id", "symbol", "timeframe", "risk_percent", "max_trades"]
    for field in required_fields:
        if field not in data:
            return jsonify({"error": f"Missing required field: {field}"}), 400

    # Get optional min_confidence parameter
    min_confidence = data.get("min_confidence", 60)  # Default to 60% if not provided

    # Find strategy
    strategy = next((s for s in AVAILABLE_STRATEGIES if s["id"] == data["strategy_id"]), None)
    if not strategy:
        return jsonify({"error": f"Strategy not found: {data['strategy_id']}"}), 404

    # Get MT5 instance
    mt5_instance = current_app.config.get('MT5_INSTANCE')
    if not mt5_instance:
        return jsonify({"error": "MT5 instance not available"}), 500

    # Check connection
    if not mt5_instance.is_connected():
        return jsonify({"error": "Not connected to MT5"}), 500

    # Update configuration
    AUTONOMOUS_CONFIG["symbols"] = [data["symbol"]]
    AUTONOMOUS_CONFIG["timeframes"] = [data["timeframe"]]
    AUTONOMOUS_CONFIG["min_confidence"] = min_confidence  # Use the provided or default confidence threshold
    AUTONOMOUS_CONFIG["max_trades"] = data["max_trades"]
    AUTONOMOUS_CONFIG["allowed_styles"] = [strategy["style"]]
    AUTONOMOUS_CONFIG["risk_percent"] = data["risk_percent"]
    AUTONOMOUS_CONFIG["allow_multiple_positions"] = True # Explicitly set to True on activation

    # Get the current Flask app for the thread
    app = current_app._get_current_object()

    # Start autonomous trading if not already running
    if not AUTONOMOUS_RUNNING:
        AUTONOMOUS_RUNNING = True
        AUTONOMOUS_THREAD = threading.Thread(target=_trading_loop, args=(app,))
        AUTONOMOUS_THREAD.daemon = True
        AUTONOMOUS_THREAD.start()
        logger.info("Autonomous trading started")

    # Add to active strategies
    active_strategy = {
        "id": data["strategy_id"],
        "name": strategy["name"],
        "symbol": data["symbol"],
        "timeframe": data["timeframe"],
        "risk_percent": data["risk_percent"],
        "max_trades": data["max_trades"],
        "min_confidence": min_confidence,
        "status": "active"
    }

    # Check if strategy is already active
    existing_index = next((i for i, s in enumerate(ACTIVE_STRATEGIES) if s["id"] == data["strategy_id"]), None)
    if existing_index is not None:
        ACTIVE_STRATEGIES[existing_index] = active_strategy
    else:
        ACTIVE_STRATEGIES.append(active_strategy)

    # Save configuration to file
    try:
        with open("autonomous_config.json", "w") as f:
            json.dump(AUTONOMOUS_CONFIG, f, indent=4)
    except Exception as e:
        logger.exception(f"Exception saving configuration: {str(e)}")

    return jsonify({
        "success": True,
        "message": f"Strategy activated: {strategy['name']}",
        "strategy_name": strategy["name"],
        "config": AUTONOMOUS_CONFIG
    })

@autonomous_bp.route('/deactivate/<strategy_id>', methods=['POST'])
def deactivate_strategy(strategy_id):
    """
    Deactivate a trading strategy.

    Args:
        strategy_id: ID of the strategy to deactivate

    Returns:
        JSON: Deactivation result
    """
    global AUTONOMOUS_RUNNING, AUTONOMOUS_THREAD, ACTIVE_STRATEGIES

    # Find strategy in active strategies
    strategy_index = next((i for i, s in enumerate(ACTIVE_STRATEGIES) if s["id"] == strategy_id), None)
    if strategy_index is None:
        return jsonify({"error": f"Strategy not active: {strategy_id}"}), 404

    # Remove from active strategies
    ACTIVE_STRATEGIES.pop(strategy_index)

    # If no more active strategies, stop autonomous trading
    if not ACTIVE_STRATEGIES and AUTONOMOUS_RUNNING:
        AUTONOMOUS_RUNNING = False
        if AUTONOMOUS_THREAD:
            AUTONOMOUS_THREAD.join(timeout=5)
            AUTONOMOUS_THREAD = None
        logger.info("Autonomous trading stopped")

    return jsonify({
        "success": True,
        "message": f"Strategy deactivated: {strategy_id}"
    })

@autonomous_bp.route('/status', methods=['GET'])
def get_status():
    """
    Get autonomous trading status.

    Returns:
        JSON: Status information
    """
    return jsonify({
        "running": AUTONOMOUS_RUNNING,
        "config": AUTONOMOUS_CONFIG,
        "active_strategies": ACTIVE_STRATEGIES
    })

@autonomous_bp.route('/debug', methods=['GET'])
def get_debug():
    """
    Get debug information for autonomous trading.

    Returns:
        JSON: Debug information
    """
    # Get MT5 instance
    mt5_instance = current_app.config.get('MT5_INSTANCE')

    # Check connection
    connection_status = "Not available"
    if mt5_instance:
        connection_status = "Connected" if mt5_instance.is_connected() else "Disconnected"

    # Get account info
    account_info = None
    if mt5_instance and mt5_instance.is_connected():
        account_info = mt5.account_info()._asdict() if mt5.account_info() else None

    # Get positions
    positions = []
    if mt5_instance and mt5_instance.is_connected():
        positions_result = mt5_instance.get_open_positions()
        if positions_result.get("success", False):
            positions = positions_result.get("positions", [])

    # Get symbols
    symbols = []
    if mt5_instance and mt5_instance.is_connected():
        for symbol in AUTONOMOUS_CONFIG["symbols"]:
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info:
                symbols.append({
                    "name": symbol,
                    "visible": symbol_info.visible,
                    "trade_mode": symbol_info.trade_mode,
                    "point": symbol_info.point,
                    "digits": symbol_info.digits,
                    "spread": symbol_info.spread,
                    "trade_allowed": mt5.symbol_info_tick(symbol) is not None
                })

    return jsonify({
        "running": AUTONOMOUS_RUNNING,
        "connection_status": connection_status,
        "account_info": account_info,
        "positions": positions,
        "symbols": symbols,
        "active_strategies": ACTIVE_STRATEGIES
    })

@autonomous_bp.route('/settings', methods=['POST'])
def update_settings():
    """
    Update autonomous trading settings.

    Request body:
    {
        "symbols": ["EURUSD", "GBPUSD"],
        "timeframes": ["M15", "H1"],
        "scan_interval": 300,
        "min_confidence": 70,
        "max_trades": 5,
        "allowed_styles": ["Scalping", "Short-term", "Swing"],
        "allow_multiple_positions": false
    }

    Returns:
        JSON: Update result
    """
    global AUTONOMOUS_CONFIG

    # Get request data
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400

    # Validate configuration
    if "symbols" in data and not isinstance(data["symbols"], list):
        return jsonify({"error": "Invalid symbols configuration"}), 400

    if "timeframes" in data and not isinstance(data["timeframes"], list):
        return jsonify({"error": "Invalid timeframes configuration"}), 400

    if "scan_interval" in data and not isinstance(data["scan_interval"], int):
        return jsonify({"error": "Invalid scan interval configuration"}), 400

    if "min_confidence" in data and not isinstance(data["min_confidence"], int):
        return jsonify({"error": "Invalid minimum confidence configuration"}), 400

    if "max_trades" in data and not isinstance(data["max_trades"], int):
        return jsonify({"error": "Invalid maximum trades configuration"}), 400

    if "allowed_styles" in data and not isinstance(data["allowed_styles"], list):
        return jsonify({"error": "Invalid allowed styles configuration"}), 400

    # Update configuration
    for key, value in data.items():
        AUTONOMOUS_CONFIG[key] = value

    # Save configuration to file
    try:
        with open("autonomous_config.json", "w") as f:
            json.dump(AUTONOMOUS_CONFIG, f, indent=4)
    except Exception as e:
        logger.exception(f"Exception saving configuration: {str(e)}")

    return jsonify({
        "success": True,
        "message": "Settings updated",
        "config": AUTONOMOUS_CONFIG
    })

@autonomous_bp.route('/monitor', methods=['GET'])
def get_monitoring_data():
    """
    Get comprehensive monitoring data for autonomous trading.
    Includes performance metrics, active trades, and system health.

    Returns:
        JSON: Monitoring data including performance, trades, and system health
    """
    try:
        # Get MT5 instance
        mt5_instance = current_app.config.get('MT5_INSTANCE')
        if not mt5_instance or not mt5_instance.is_connected():
            return jsonify({
                'success': False,
                'error': 'MT5 not connected',
                'status': 'disconnected'
            }), 503

        # Get active strategies
        active_strategies = ACTIVE_STRATEGIES

        # Performance data
        performance = {
            'overall': {
                'profit_today': 0.0,
                'profit_week': 0.0,
                'total_profit': 0.0,
                'drawdown': 0.0,
                'win_rate': 0.0,
                'trades_count': 0
            },
            'by_strategy': {}
        }

        # Get account info
        account_info = None
        if mt5_instance.is_connected():
            account_info = mt5.account_info()._asdict() if mt5.account_info() else None

        # Get all positions
        positions = []
        autonomous_magic_numbers = [s.get('magic', 12345) for s in active_strategies]

        all_positions = mt5.positions_get()
        if all_positions:
            logger.info(f"Found {len(all_positions)} total positions")
            for position in all_positions:
                pos = position._asdict()
                strategy = next((s for s in active_strategies if s.get('magic') == pos.get('magic')), None)

                # Include all positions for profit calculation, but mark which ones are from our strategies
                # Add strategy name if available
                pos['strategy_name'] = strategy.get('name', 'Unknown') if strategy else 'Unknown'

                # Ensure profit is a float
                if 'profit' in pos:
                    pos['profit'] = float(pos['profit'])

                # Format times for JSON
                pos['time'] = datetime.fromtimestamp(pos['time']).isoformat()
                pos['time_update'] = datetime.fromtimestamp(pos['time_update']).isoformat()

                # Calculate duration
                open_time = datetime.fromtimestamp(position.time)
                duration = datetime.now() - open_time
                pos['duration'] = {
                    'days': duration.days,
                    'hours': duration.seconds // 3600,
                    'minutes': (duration.seconds % 3600) // 60,
                    'seconds': duration.seconds % 60
                }

                positions.append(pos)

        # Get recent history (closed trades from autonomous strategies)
        history = []
        now = datetime.now()
        start_time = now - timedelta(days=7)  # Last 7 days

        # Get history deals
        history_deals = mt5.history_deals_get(start_time, now)
        if history_deals:
            for deal in history_deals:
                d = deal._asdict()
                if d.get('magic') in autonomous_magic_numbers:
                    # Format for frontend
                    d['time'] = datetime.fromtimestamp(d['time']).isoformat()

                    # Find associated strategy
                    strategy = next((s for s in active_strategies if s.get('magic') == d.get('magic')), None)
                    d['strategy_name'] = strategy.get('name', 'Unknown') if strategy else 'Unknown'

                    history.append(d)

        # Calculate performance metrics based on history
        if history:
            # Today's profit
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            today_profit = sum(d['profit'] for d in history if datetime.fromisoformat(d['time']) >= today_start)
            performance['overall']['profit_today'] = today_profit

            # Week's profit
            week_start = now - timedelta(days=now.weekday())
            week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)
            week_profit = sum(d['profit'] for d in history if datetime.fromisoformat(d['time']) >= week_start)
            performance['overall']['profit_week'] = week_profit

            # Total profit
            performance['overall']['total_profit'] = sum(d['profit'] for d in history)

            # Win rate
            profitable_deals = [d for d in history if d['profit'] > 0]
            if history:
                performance['overall']['win_rate'] = len(profitable_deals) / len(history)

            # Trades count
            performance['overall']['trades_count'] = len(history)

            # By strategy
            strategy_profits = {}
            for deal in history:
                strategy_name = deal.get('strategy_name', 'Unknown')
                if strategy_name not in strategy_profits:
                    strategy_profits[strategy_name] = {
                        'total_profit': 0,
                        'trades_count': 0,
                        'profitable_trades': 0
                    }

                strategy_profits[strategy_name]['total_profit'] += deal['profit']
                strategy_profits[strategy_name]['trades_count'] += 1
                if deal['profit'] > 0:
                    strategy_profits[strategy_name]['profitable_trades'] += 1

            # Calculate win rates for each strategy
            for strategy_name, data in strategy_profits.items():
                if data['trades_count'] > 0:
                    win_rate = data['profitable_trades'] / data['trades_count']
                else:
                    win_rate = 0

                performance['by_strategy'][strategy_name] = {
                    'total_profit': data['total_profit'],
                    'trades_count': data['trades_count'],
                    'win_rate': win_rate
                }

        # System health
        system_health = {
            'running': AUTONOMOUS_RUNNING,
            'strategies_count': len(active_strategies),
            'connection_status': 'connected' if mt5_instance and mt5_instance.is_connected() else 'disconnected',
            'account_margin_level': account_info['margin_level'] if account_info else None,
            'last_check_time': datetime.now().isoformat(),
        }

        # Add risk management metrics
        try:
            from .autonomous_fixed import calculate_total_risk_exposure, AUTONOMOUS_CONFIG

            # Calculate risk exposure
            account_balance = account_info['balance'] if account_info else 0
            current_risk_percent = calculate_total_risk_exposure(positions, account_balance)

            # Count positions with break-even and trailing stops
            breakeven_positions = 0
            trailing_positions = 0
            total_profit_loss = 0

            # Debug log to check positions data
            logger.info(f"Positions data for P/L calculation: {len(positions)} positions")

            for position in positions:
                profit = position.get("profit", 0)
                logger.info(f"Position {position.get("ticket", "unknown")} profit: {profit}")

                # Ensure profit is a float
                if isinstance(profit, str):
                    try:
                        profit = float(profit)
                    except ValueError:
                        profit = 0

                total_profit_loss += profit
                entry_price = position.get("price_open", 0)
                sl = position.get("sl", 0)

                if sl != 0:
                    # Check if stop loss is at break-even (within 5 points)
                    symbol_info = mt5.symbol_info(position.get("symbol", ""))
                    if symbol_info:
                        point = symbol_info.point
                        if abs(sl - entry_price) < (5 * point):
                            breakeven_positions += 1
                        # Assume it's a trailing stop if not at entry and not initial stop
                        elif (position.get("type") == 0 and sl > position.get("price_open", 0)) or \
                             (position.get("type") == 1 and sl < position.get("price_open", 0)):
                            trailing_positions += 1

            # Add risk management metrics to system_health
            system_health['current_risk_percent'] = float(current_risk_percent)
            system_health['max_risk_percent'] = float(AUTONOMOUS_CONFIG.get("max_total_risk_percent", 5.0))
            system_health['breakeven_positions'] = breakeven_positions
            system_health['trailing_positions'] = trailing_positions
            system_health['current_profit_loss'] = float(total_profit_loss)
            system_health['max_profit_target'] = float(AUTONOMOUS_CONFIG.get("max_profit_target", 0))
            system_health['max_loss_target'] = float(AUTONOMOUS_CONFIG.get("max_loss_target", 0))
        except Exception as e:
            logger.warning(f"Could not calculate risk management metrics: {str(e)}")


        # Get recent errors or warnings from the autonomous_trader.log file (last 10 lines)
        recent_logs = []
        try:
            log_file_path = os.path.join(os.getcwd(), "autonomous_trader.log")
            if os.path.exists(log_file_path):
                with open(log_file_path, 'r') as f:
                    # Get last 100 lines
                    recent_log_lines = f.readlines()[-100:]

                    # Filter for errors and warnings
                    for line in recent_log_lines:
                        if "ERROR" in line or "WARNING" in line:
                            # Parse log format to extract time and message
                            parts = line.split(' - ', 3)
                            if len(parts) >= 3:
                                timestamp = parts[0]
                                level = parts[1]
                                message = parts[2]

                                recent_logs.append({
                                    'timestamp': timestamp,
                                    'level': level,
                                    'message': message
                                })
        except Exception as e:
            logger.warning(f"Could not read log file: {str(e)}")

        return jsonify({
            'success': True,
            'performance': performance,
            'positions': positions,
            'history': history,
            'system_health': system_health,
            'recent_logs': recent_logs,
            'strategies': [s for s in active_strategies]  # Include strategy configs
        })

    except Exception as e:
        logger.exception(f"Error in monitoring endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@autonomous_bp.route('/logs', methods=['GET'])
def get_logs():
    """
    Get recent log entries from the autonomous trader

    Returns:
        JSON containing log entries
    """
    try:
        # Default to 100 lines if not specified
        lines = request.args.get('lines', 100, type=int)
        # Maximum allowable lines
        if lines > 500:
            lines = 500

        log_entries = []
        log_file_path = os.path.join(os.getcwd(), "autonomous_trader.log")

        if os.path.exists(log_file_path):
            with open(log_file_path, 'r') as f:
                all_lines = f.readlines()
                # Get the most recent n lines
                recent_lines = all_lines[-lines:] if lines < len(all_lines) else all_lines

                for line in recent_lines:
                    # Parse log format (timestamp - level - message)
                    parts = line.split(' - ', 2)
                    if len(parts) >= 3:
                        timestamp = parts[0]
                        level = parts[1]
                        message = parts[2].strip()

                        log_entries.append({
                            'timestamp': timestamp,
                            'level': level,
                            'message': message
                        })

        return jsonify({
            'success': True,
            'logs': log_entries,
            'count': len(log_entries)
        })

    except Exception as e:
        logger.exception(f"Error fetching logs: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@autonomous_bp.route('/strategy-recommendations', methods=['GET'])
def get_strategy_recommendations():
    """
    Get strategy recommendations based on current market conditions.

    Returns:
        JSON: Strategy recommendations for each symbol
    """
    try:
        # Get MT5 instance
        mt5_instance = current_app.config.get('MT5_INSTANCE')
        if not mt5_instance or not mt5_instance.is_connected():
            return jsonify({
                'success': False,
                'error': 'MT5 connection not available'
            }), 400

        # Get market data for analysis
        symbols = AUTONOMOUS_CONFIG.get("symbols", ["EURUSD", "GBPUSD", "USDJPY"])
        market_data = {}

        for symbol in symbols:
            try:
                # Get current price
                price_data = mt5_instance.get_current_price(symbol)
                if "error" not in price_data:
                    # Get analysis data
                    analyzer = AnalysisEngine(mt5_instance)
                    analyzer.current_price = price_data
                    analysis_result = analyzer.get_analysis_for_timeframe(symbol, "H1")

                    if analysis_result.get("success"):
                        analysis_data = analysis_result.get("analysis", analysis_result.get("data", {}))
                        market_data[symbol] = {
                            **price_data,
                            'indicators': analysis_data.get('indicators', {})
                        }
            except Exception as e:
                logger.error(f"Error getting market data for {symbol}: {str(e)}")
                continue

        # Get recommendations from orchestrator
        recommendations = STRATEGY_ORCHESTRATOR.get_strategy_recommendations(market_data)

        return jsonify({
            'success': True,
            'recommendations': recommendations,
            'timestamp': time.time()
        })

    except Exception as e:
        logger.exception(f"Error getting strategy recommendations: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@autonomous_bp.route('/strategy-performance', methods=['GET'])
def get_strategy_performance():
    """
    Get performance metrics for all strategies.

    Returns:
        JSON: Performance data for each strategy
    """
    try:
        performance_data = {}

        for strategy_id, performance in STRATEGY_ORCHESTRATOR.strategy_performance.items():
            performance_data[strategy_id] = {
                'total_trades': performance.total_trades,
                'winning_trades': performance.winning_trades,
                'win_rate': performance.win_rate,
                'total_profit': performance.total_profit,
                'avg_profit': performance.avg_profit,
                'recent_performance': performance.recent_performance,
                'recent_trades_count': len(performance.recent_trades)
            }

        return jsonify({
            'success': True,
            'performance': performance_data,
            'timestamp': time.time()
        })

    except Exception as e:
        logger.exception(f"Error getting strategy performance: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
