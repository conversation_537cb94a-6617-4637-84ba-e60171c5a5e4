# Phase 7: Testing and Validation

## Overview

This phase details the comprehensive testing strategy for all autonomous trading enhancements. A thorough testing plan is essential to ensure the system works reliably in real-world trading scenarios and prevents costly errors.

## Testing Strategy

### 1. Unit Testing

Each component should be tested individually to ensure it functions correctly in isolation:

```python
# Example unit test for TimeFilter class
import unittest
from datetime import datetime
import pytz
from backend.time_filter import TimeFilter

class TestTimeFilter(unittest.TestCase):
    def setUp(self):
        # Create a test configuration
        self.config = {
            "time_filters": {
                "enabled": True,
                "trading_sessions": [
                    {
                        "name": "Test Session",
                        "enabled": True,
                        "start_hour": 9,
                        "start_minute": 0,
                        "end_hour": 17,
                        "end_minute": 0
                    }
                ],
                "time_zone": "UTC",
                "non_trading_days": ["Saturday", "Sunday"]
            }
        }
        self.time_filter = TimeFilter(self.config)
    
    def test_trading_allowed_during_session(self):
        # Mock current time within session (e.g., 10:00 UTC on Monday)
        # This would require patching datetime.now() in actual implementation
        result = self.time_filter.is_trading_allowed()
        self.assertTrue(result)
    
    def test_trading_not_allowed_outside_session(self):
        # Mock current time outside session (e.g., 8:00 UTC on Monday)
        # This would require patching datetime.now() in actual implementation
        result = self.time_filter.is_trading_allowed()
        self.assertFalse(result)
    
    def test_trading_not_allowed_on_weekend(self):
        # Mock current time on weekend (e.g., Saturday)
        # This would require patching datetime.now() in actual implementation
        result = self.time_filter.is_trading_allowed()
        self.assertFalse(result)
```

### 2. Integration Testing

Test interactions between components to ensure they work together correctly:

```python
# Example integration test for TimeFilter with TradingLoop
import unittest
from unittest.mock import patch, MagicMock
from backend.time_filter import TimeFilter
from backend.api.autonomous import _trading_loop

class TestIntegration(unittest.TestCase):
    def setUp(self):
        # Set up mock MT5 instance
        self.mock_mt5 = MagicMock()
        self.mock_mt5.check_connection.return_value = {"connected": True}
        self.mock_mt5.get_account_info.return_value = {
            "success": True,
            "account_info": {
                "balance": 10000,
                "equity": 10000
            }
        }
        self.mock_mt5.get_open_positions.return_value = {
            "success": True,
            "positions": []
        }
        
        # Set up mock app
        self.mock_app = MagicMock()
        self.mock_app.config = {"MT5_INSTANCE": self.mock_mt5}
    
    @patch('backend.api.autonomous.AUTONOMOUS_CONFIG')
    @patch('backend.api.autonomous.TimeFilter')
    @patch('backend.api.autonomous.AUTONOMOUS_RUNNING', True, create=True)
    def test_trading_loop_respects_time_filter(self, mock_time_filter_class, mock_config):
        # Configure mock time filter
        mock_time_filter = MagicMock()
        mock_time_filter_class.return_value = mock_time_filter
        
        # Test when trading is not allowed
        mock_time_filter.is_trading_allowed.return_value = False
        mock_time_filter.get_next_session_start.return_value = "10:00 UTC"
        
        # Override AUTONOMOUS_RUNNING to break the loop after first iteration
        with patch('backend.api.autonomous.AUTONOMOUS_RUNNING', create=True) as mock_running:
            mock_running.__bool__.side_effect = [True, False]  # Run one iteration
            _trading_loop(self.mock_app)
        
        # Verify time filter was checked
        mock_time_filter.is_trading_allowed.assert_called_once()
        
        # Verify no trading operations were attempted
        # (would need to check specific method calls depending on implementation)
```

### 3. Simulated Trading Tests

Test the system with historical data to validate its performance:

```python
# Example simulated trading test
import unittest
from unittest.mock import patch, MagicMock
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

class TestSimulatedTrading(unittest.TestCase):
    def setUp(self):
        # Set up mock MT5 instance with historical data
        self.mock_mt5 = MagicMock()
        
        # Generate sample historical data
        dates = [datetime.now() - timedelta(hours=i) for i in range(100, 0, -1)]
        highs = np.random.normal(1.2000, 0.0050, 100)
        lows = np.random.normal(1.1950, 0.0050, 100)
        opens = np.random.normal(1.1975, 0.0020, 100)
        closes = np.random.normal(1.1980, 0.0020, 100)
        volumes = np.random.normal(100, 20, 100)
        
        self.sample_data = [{
            "time": dates[i],
            "open": opens[i],
            "high": highs[i],
            "low": lows[i],
            "close": closes[i],
            "tick_volume": volumes[i]
        } for i in range(100)]
        
        self.mock_mt5.get_candles.return_value = {
            "success": True,
            "candles": self.sample_data
        }
    
    @patch('backend.multi_timeframe.MultiTimeframeAnalyzer')
    def test_signal_generation_with_historical_data(self, mock_analyzer_class):
        # Create mock analyzer
        mock_analyzer = MagicMock()
        mock_analyzer_class.return_value = mock_analyzer
        
        # Configure mock to return test signals
        mock_analyzer.get_consensus_signal.return_value = {
            "success": True,
            "consensus": "buy",
            "consensus_confidence": 80,
            "consensus_timeframes": 3,
            "entry_signal": {
                "signalType": "buy",
                "confidence": 85,
                "strategy": "test_strategy",
                "stopLoss": 1.1900,
                "takeProfit": 1.2100
            }
        }
        
        # Create simulated trading environment
        # This would be your actual autonomous trading system adapted for simulation
        # For this example, we're just checking the signal generator
        
        # Assert our expectations
        result = mock_analyzer.get_consensus_signal("EURUSD", "H1")
        self.assertEqual(result["consensus"], "buy")
        self.assertGreaterEqual(result["consensus_confidence"], 75)
```

### 4. Market Condition Detection Testing

Test the system's ability to identify different market conditions:

```python
import unittest
from unittest.mock import patch, MagicMock
import numpy as np
from backend.market_condition import MarketConditionDetector, MarketCondition

class TestMarketConditionDetection(unittest.TestCase):
    def setUp(self):
        self.config = {
            "market_condition": {
                "volatility_threshold": 2.0,
                "trend_filter_enabled": True
            }
        }
        self.mock_mt5 = MagicMock()
        self.detector = MarketConditionDetector(self.mock_mt5, self.config)
    
    def test_trending_market_detection(self):
        # Create trending market data
        # Uptrend with higher highs and higher lows
        base = 1.2000
        highs = [base + (i * 0.0010) + (np.random.random() * 0.0005) for i in range(100)]
        lows = [base + (i * 0.0010) - (np.random.random() * 0.0005) for i in range(100)]
        closes = [base + (i * 0.0010) for i in range(100)]
        
        # Set up mock return values
        self.mock_mt5.get_candles.return_value = {
            "success": True,
            "candles": [{
                "high": highs[i],
                "low": lows[i],
                "close": closes[i]
            } for i in range(100)]
        }
        
        # Test detection
        result = self.detector.detect_condition("EURUSD", "H1")
        
        # Assertions
        self.assertTrue(result["success"])
        self.assertEqual(result["condition"], MarketCondition.TRENDING_UP.value)
    
    def test_ranging_market_detection(self):
        # Create ranging market data
        # Price oscillates within a channel
        base = 1.2000
        amplitude = 0.0050
        closes = [base + (amplitude * np.sin(i/10)) for i in range(100)]
        highs = [closes[i] + (np.random.random() * 0.0010) for i in range(100)]
        lows = [closes[i] - (np.random.random() * 0.0010) for i in range(100)]
        
        # Set up mock return values
        self.mock_mt5.get_candles.return_value = {
            "success": True,
            "candles": [{
                "high": highs[i],
                "low": lows[i],
                "close": closes[i]
            } for i in range(100)]
        }
        
        # Test detection
        result = self.detector.detect_condition("EURUSD", "H1")
        
        # Assertions
        self.assertTrue(result["success"])
        self.assertEqual(result["condition"], MarketCondition.RANGING.value)
```

### 5. Risk Management Testing

Test the risk management system's ability to control trade risk:

```python
import unittest
from unittest.mock import patch, MagicMock
from backend.risk_manager import RiskManager

class TestRiskManagement(unittest.TestCase):
    def setUp(self):
        self.config = {
            "risk_management": {
                "max_daily_risk": 5.0,
                "max_risk_per_symbol": 2.0,
                "reduce_risk_after_loss": True,
                "risk_reduction_factor": 0.5,
                "max_consecutive_losses": 3
            }
        }
        self.mock_mt5 = MagicMock()
        
        # Mock account info
        self.mock_mt5.get_account_info.return_value = {
            "success": True,
            "account_info": {
                "balance": 10000,
                "equity": 10000
            }
        }
        
        # Mock symbol info
        self.mock_mt5.get_symbol_info.return_value = {
            "success": True,
            "point": 0.0001,
            "trade_tick_value": 1.0,
            "trade_tick_size": 0.0001
        }
        
        self.risk_manager = RiskManager(self.config, self.mock_mt5)
    
    def test_position_risk_calculation(self):
        result = self.risk_manager.calculate_position_risk("EURUSD", 0.1, 1.2000, 1.1900)
        
        self.assertTrue(result["success"])
        # Expected risk: 100 pips * $1 per pip * 0.1 lots = $10
        # $10 risk on $10,000 balance = 0.1%
        self.assertAlmostEqual(result["monetary_risk"], 10.0, delta=0.1)
        self.assertAlmostEqual(result["percentage_risk"], 0.1, delta=0.01)
    
    def test_daily_risk_limit(self):
        # Register trades up to near the daily limit
        self.risk_manager.daily_risk_used = 4.9  # 4.9% of 5.0% limit used
        
        # Test a trade that would exceed the limit
        result = self.risk_manager.is_trade_allowed("EURUSD", 1.0, 1.2000, 1.1900)
        
        self.assertFalse(result["allowed"])
        self.assertIn("Daily risk limit exceeded", result["reason"])
    
    def test_risk_reduction_after_losses(self):
        # Set consecutive losses
        self.risk_manager.consecutive_losses = 3
        
        # Calculate position size for a trade
        # First get normal risk calculation
        normal_risk = self.risk_manager.calculate_position_risk("EURUSD", 0.1, 1.2000, 1.1900)
        
        # Then check if trade is allowed and get the reduced risk
        result = self.risk_manager.is_trade_allowed("EURUSD", 0.1, 1.2000, 1.1900)
        
        # Risk should be reduced after consecutive losses
        self.assertTrue(result["allowed"])
        reduced_risk = result["risk_info"]["percentage_risk"]
        
        # Expect reduced risk based on the risk reduction factor (0.5)
        self.assertLess(reduced_risk, normal_risk["percentage_risk"])
```

### 6. Frontend Testing

Test the frontend components to ensure they render correctly and interact with the backend properly:

```jsx
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { TimeFilterConfig } from '../components/TimeFilterConfig';

// Mock fetch for API calls
global.fetch = jest.fn();

describe('TimeFilterConfig Component', () => {
  const mockConfig = {
    time_filters: {
      enabled: true,
      trading_sessions: [
        {
          name: 'London',
          enabled: true,
          start_hour: 8,
          start_minute: 0,
          end_hour: 16,
          end_minute: 0
        }
      ],
      time_zone: 'UTC',
      non_trading_days: ['Saturday', 'Sunday']
    }
  };
  
  const mockUpdateConfig = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  test('renders time filter configuration', () => {
    render(
      <TimeFilterConfig 
        config={mockConfig} 
        updateConfig={mockUpdateConfig} 
      />
    );
    
    // Check that component renders correctly
    expect(screen.getByText('Trading Sessions')).toBeInTheDocument();
    expect(screen.getByText('London')).toBeInTheDocument();
    expect(screen.getByLabelText('Enable Time-Based Filters')).toBeChecked();
  });
  
  test('toggles session enabled state', async () => {
    render(
      <TimeFilterConfig 
        config={mockConfig} 
        updateConfig={mockUpdateConfig} 
      />
    );
    
    // Find and click the London session toggle
    const sessionToggle = screen.getAllByRole('checkbox')[1]; // First is main toggle, second is London
    fireEvent.click(sessionToggle);
    
    // Check that updateConfig was called with modified config
    await waitFor(() => {
      expect(mockUpdateConfig).toHaveBeenCalledWith({
        ...mockConfig,
        time_filters: {
          ...mockConfig.time_filters,
          trading_sessions: [
            {
              ...mockConfig.time_filters.trading_sessions[0],
              enabled: false // Should be toggled to false
            }
          ]
        }
      });
    });
  });
});
```

## Testing Environments

### 1. Sandbox Environment

Testing should first be performed in a sandboxed environment that simulates the real trading environment without executing actual trades. This involves:

- Using a simulated MT5 connection
- Processing historical market data
- Validating trade decisions against expected outcomes
- Testing risk management logic with simulated positions

### 2. Demo Account Testing

After sandbox testing, the system should be tested with a live MT5 demo account to validate:

- Connection handling
- Real-time data processing
- Order execution (on demo account)
- System stability over extended periods

### 3. Production Testing

Final validation should be performed with real trading, starting with minimal risk:

- Limited number of symbols
- Lower risk percentage per trade
- Smaller position sizes
- More conservative risk limits

## Test Cases Matrix

| Feature Area | Test Case | Test Type | Environment | Priority |
|--------------|-----------|-----------|-------------|----------|
| Time Filters | Session identification | Unit | Sandbox | High |
| Time Filters | Trading hours enforcement | Integration | Sandbox | High |
| Time Filters | Time zone handling | Unit | Sandbox | Medium |
| Risk Management | Risk calculation | Unit | Sandbox | High |
| Risk Management | Daily risk limit | Integration | Sandbox | High |
| Risk Management | Risk reduction after losses | Integration | Sandbox/Demo | High |
| Risk Management | Position closure on reversal | Integration | Demo | High |
| Market Conditions | Trend detection | Unit | Sandbox | Medium |
| Market Conditions | Volatility detection | Unit | Sandbox | Medium |
| Market Conditions | Parameter adaptation | Integration | Demo | Medium |
| Multi-Timeframe | Timeframe analysis | Unit | Sandbox | Medium |
| Multi-Timeframe | Consensus signal generation | Integration | Demo | High |
| Frontend | Configuration UI | Unit | - | Medium |
| Frontend | Monitoring dashboard | Integration | Demo | Medium |
| System | End-to-end workflow | System | Demo | High |
| System | Extended run stability | System | Demo | High |
| Performance | Backtest against historical data | Performance | Sandbox | Medium |

## Validation Criteria

### 1. Risk Management Validation

- System must never exceed configured risk limits
- Daily risk usage must be accurately tracked and reset
- Position sizing must correctly implement risk parameters
- Early position closure must function as expected

### 2. Trading Logic Validation

- System must only trade during configured sessions
- Market condition detection must be accurate
- Multi-timeframe confirmation must filter false signals
- Position management must work as configured

### 3. System Stability Validation

- System must run continuously for at least 5 market days without issues
- Memory usage must remain stable over time
- Error handling must properly recover from unexpected conditions
- Logging must provide sufficient information for troubleshooting

### 4. Performance Validation

- Backtest performance must show improvement over baseline
- Risk-adjusted returns should show positive expectancy
- System must handle market volatility appropriately
- Win rate and profit factor must meet minimum thresholds

## Test Results Reporting

Test results should be documented in a structured format:

```
Test ID: RM-001
Feature: Risk Management - Daily Risk Limit
Description: Verify the system enforces daily risk limits correctly
Procedure: 
1. Configure daily risk limit of 5%
2. Execute trades consuming 4.9% risk
3. Attempt to execute a trade requiring 0.2% risk
Expected Result: System rejects the trade with a risk limit exceeded message
Actual Result: [To be completed during testing]
Status: [Pass/Fail]
Notes: [Any observations or issues]
```

## Acceptance Criteria

- All high-priority test cases must pass
- No critical or high-severity issues remaining
- System performance meets or exceeds baseline metrics
- Documentation is complete and accurate
- Training has been provided to users
- Rollback plan is in place for emergency situations
