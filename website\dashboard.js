document.addEventListener('DOMContentLoaded', function() {
  const logoutBtn = document.getElementById('logout-btn');
  const downloadAppBtn = document.getElementById('download-app-btn');
  const downloadWindowsBtn = document.getElementById('download-windows-btn');
  
  const userEmailElement = document.getElementById('user-email');
  const userBrokerElement = document.getElementById('user-broker');
  const userAccountElement = document.getElementById('user-account');
  const userAffiliateElement = document.getElementById('user-affiliate');
  
  // Check if user is logged in
  firebase.auth().onAuthStateChanged(user => {
    if (user) {
      // User is logged in, load user data
      loadUserData(user.uid);
    } else {
      // User is not logged in, redirect to login page
      window.location.href = 'login.html';
    }
  });
  
  // Load user data from Firestore
  function loadUserData(userId) {
    firebase.firestore().collection('users').doc(userId).get()
      .then(doc => {
        if (doc.exists) {
          const userData = doc.data();
          
          // Check if user is verified
          if (!userData.verified) {
            // User is not verified, redirect to login page
            alert('Your account is pending verification. We\'ll notify you by email once it\'s approved.');
            firebase.auth().signOut().then(() => {
              window.location.href = 'login.html';
            });
            return;
          }
          
          // Update user info in the dashboard
          userEmailElement.textContent = userData.email || 'N/A';
          userBrokerElement.textContent = userData.broker || 'N/A';
          userAccountElement.textContent = userData.accountNumber || 'N/A';
          userAffiliateElement.textContent = userData.affiliateCode || 'N/A';
        } else {
          console.error('No user data found');
          alert('Error loading user data. Please try again later.');
        }
      })
      .catch(error => {
        console.error('Error getting user data:', error);
        alert('Error loading user data: ' + error.message);
      });
  }
  
  // Logout functionality
  logoutBtn.addEventListener('click', function() {
    firebase.auth().signOut()
      .then(() => {
        window.location.href = 'login.html';
      })
      .catch(error => {
        console.error('Logout error:', error);
        alert('Error logging out: ' + error.message);
      });
  });
  
  // Download app functionality
  function downloadApp() {
    // Open download modal or redirect to download page
    // This is a placeholder - implement actual download logic
    alert('Download functionality will be implemented here.');
  }
  
  downloadAppBtn.addEventListener('click', downloadApp);
  downloadWindowsBtn.addEventListener('click', downloadApp);
});
