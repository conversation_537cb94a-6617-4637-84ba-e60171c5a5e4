from typing import Dict, Any
import numpy as np
import pandas as pd
from scipy import stats

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class LinearRegressionIndicator(BaseIndicator):
    """Linear Regression related calculations indicator."""

    def __init__(self, period: int = 14, source: str = 'close', deviation: float = 2.0):
        """
        Initialize Linear Regression indicator.

        Args:
            period: The lookback period for regression calculation.
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
            deviation: Number of standard errors for channel calculation.
        """
        super().__init__({
            'period': period,
            'source': source,
            'deviation': deviation
        })

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate rolling linear regression values."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period']:
             return {
                 'slope': np.array([]), 'intercept': np.array([]), 'r_squared': np.array([]),
                 'std_error': np.array([]), 'regression_value': np.array([]),
                 'upper_channel': np.array([]), 'lower_channel': np.array([])
             }

        period = self.params['period']
        source_col = self.params['source'].lower()
        deviation = self.params['deviation']

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Initialize result arrays
        n = len(source_data)
        slopes = np.full(n, np.nan)
        intercepts = np.full(n, np.nan)
        r_squared_vals = np.full(n, np.nan)
        std_errors = np.full(n, np.nan)
        regression_values = np.full(n, np.nan)
        upper_channels = np.full(n, np.nan)
        lower_channels = np.full(n, np.nan)

        x = np.arange(period)

        for i in range(period - 1, n):
            y_window = source_data.iloc[i - period + 1 : i + 1].values
            if len(y_window) != period or np.isnan(y_window).any():
                continue # Skip if window is incomplete or contains NaN

            try:
                slope, intercept, r_value, p_value, stderr = stats.linregress(x, y_window)

                # Calculate regression value at the end of the period (LRI)
                regression_value = intercept + slope * (period - 1)

                # Calculate standard error of the estimate
                y_hat = intercept + slope * x
                err = y_window - y_hat
                # Ensure degrees of freedom > 0
                if period > 2:
                    std_err_estimate = np.sqrt(np.sum(err**2) / (period - 2))
                else:
                    std_err_estimate = np.nan # Cannot calculate with period <= 2

                # Calculate channel lines
                upper_channel = regression_value + deviation * std_err_estimate
                lower_channel = regression_value - deviation * std_err_estimate

                # Store results
                slopes[i] = slope
                intercepts[i] = intercept
                r_squared_vals[i] = r_value**2
                std_errors[i] = std_err_estimate # Standard error of the estimate
                regression_values[i] = regression_value
                upper_channels[i] = upper_channel
                lower_channels[i] = lower_channel

            except ValueError: # Handle potential errors in linregress
                continue


        self._values = {
            'slope': slopes,
            'intercept': intercepts,
            'r_squared': r_squared_vals,
            'std_error': std_errors, # Std Error of Estimate
            'regression_value': regression_values, # LRI
            'upper_channel': upper_channels,
            'lower_channel': lower_channels
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 3: # Need at least 3 points for std error calc (period-2 > 0)
            raise ValueError("Period must be at least 3 for Linear Regression calculations")
        if self.params['deviation'] <= 0:
            raise ValueError("Deviation must be positive")
        return True