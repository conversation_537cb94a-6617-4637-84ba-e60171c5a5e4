import React, { useEffect, useState } from 'react';
import notifications from '../notifications';

/**
 * NotificationProvider - Context provider for the notification system
 * This component handles the creation and management of notifications in a React context
 */
export const NotificationContext = React.createContext({
  success: () => {},
  error: () => {},
  warning: () => {},
  info: () => {},
  clear: () => {},
});

export const NotificationProvider = ({ children }) => {
  // Use the context API to make the notification functions available throughout the app
  const notificationAPI = {
    success: (title, message, duration) => notifications.success(title, message, duration),
    error: (title, message, duration) => notifications.error(title, message, duration),
    warning: (title, message, duration) => notifications.warning(title, message, duration),
    info: (title, message, duration) => notifications.info(title, message, duration),
    clear: () => notifications.clear(),
  };

  return (
    <NotificationContext.Provider value={notificationAPI}>
      {children}
    </NotificationContext.Provider>
  );
};

/**
 * useNotification - Custom hook to access the notification API
 */
export const useNotification = () => {
  const context = React.useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

/**
 * Example usage:
 * 
 * // In your app component:
 * import { NotificationProvider } from './components/Notification';
 * 
 * function App() {
 *   return (
 *     <NotificationProvider>
 *       <YourAppContent />
 *     </NotificationProvider>
 *   );
 * }
 * 
 * // In any component:
 * import { useNotification } from './components/Notification';
 * 
 * function YourComponent() {
 *   const notify = useNotification();
 *   
 *   const handleSuccess = () => {
 *     notify.success('Success!', 'Operation completed successfully');
 *   };
 *   
 *   return (
 *     <button onClick={handleSuccess}>
 *       Show Success Notification
 *     </button>
 *   );
 * }
 */ 