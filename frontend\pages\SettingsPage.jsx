import React, { useState, useEffect } from 'react';
import { useNotification } from '../components/Notification';
import '../styles/SettingsPage.css';

// Add licenseInfo to props, with a default value
const SettingsPage = ({ accountInfo = {}, licenseInfo = { userName: '', planType: '', planEndDate: '', status: '' } }) => {
  const [mt5Settings, setMt5Settings] = useState({
    path: '',
    server: '',
    login: '',
    accountType: 'demo'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [testStatus, setTestStatus] = useState('idle'); // idle, testing, success, error
  const [testMessage, setTestMessage] = useState('');
  const [currentTheme, setCurrentTheme] = useState('dark');
  const [refreshInterval, setRefreshInterval] = useState('10');
  const notify = useNotification();

  // Load saved settings on component mount
  useEffect(() => {
    loadSettings();
    
    // Load theme settings
    const savedTheme = localStorage.getItem('theme') || 'dark';
    setCurrentTheme(savedTheme);
    applyTheme(savedTheme);
    
    // Load refresh interval
    const savedInterval = localStorage.getItem('refreshInterval') || '10';
    setRefreshInterval(savedInterval);
  }, []);

  // Update settings when accountInfo changes (if connected)
  useEffect(() => {
    if (accountInfo.server) {
      setMt5Settings(prev => ({
        ...prev,
        server: accountInfo.server || prev.server,
        login: accountInfo.accountName ? String(accountInfo.accountName).split(' ')[0] : prev.login,
        accountType: accountInfo.accountType || prev.accountType
      }));
    }
  }, [accountInfo]);

  const loadSettings = async () => {
    try {
      console.log('Loading MT5 settings...');

      // Try to load from localStorage first
      const savedSettings = localStorage.getItem('mt5Settings');
      if (savedSettings) {
        console.log('Found settings in localStorage');
        try {
          const parsedSettings = JSON.parse(savedSettings);
          console.log('Parsed localStorage settings:', parsedSettings);
          setMt5Settings(prev => ({
            ...prev,
            ...parsedSettings
          }));
        } catch (parseError) {
          console.error('Error parsing localStorage settings:', parseError);
        }
      } else {
        console.log('No settings found in localStorage');
      }

      // If Electron API is available, try to load from there too
      if (window.api && window.api.getSettings) {
        console.log('Attempting to load settings from Electron API');
        try {
          const electronSettings = await window.api.getSettings();
          if (electronSettings) {
            console.log('Loaded settings from Electron API:', electronSettings);
            setMt5Settings(prev => ({
              ...prev,
              path: electronSettings.path || prev.path,
              server: electronSettings.server || prev.server,
              login: electronSettings.login || prev.login,
              accountType: electronSettings.accountType || prev.accountType
            }));
          } else {
            console.log('No settings returned from Electron API');
          }
        } catch (apiError) {
          console.error('Error loading settings from Electron API:', apiError);
        }
      } else {
        console.log('Electron API not available for loading settings');
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
      notify.error('Error', 'Failed to load settings: ' + error.message);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setMt5Settings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const testMT5Path = async () => {
    if (!mt5Settings.path) {
      notify.warning('Validation Error', 'Please enter MT5 terminal path');
      return;
    }

    setIsSubmitting(true);
    setTestStatus('testing');
    setTestMessage('Testing MT5 terminal path...');

    try {
      const response = await fetch('http://localhost:5001/api/connection/test_path', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          path: mt5Settings.path
        })
      });

      const data = await response.json();

      if (response.ok && data.status === 'success') {
        setTestStatus('success');
        setTestMessage('MT5 Terminal found! Path is valid.');
        notify.success('Success', 'MT5 Terminal path is valid');
      } else {
        setTestStatus('error');
        setTestMessage(data.error || 'Failed to validate MT5 Terminal path');
        notify.error('Error', data.error || 'Failed to validate MT5 Terminal path');
      }
    } catch (error) {
      console.error('Path test error:', error);
      setTestStatus('error');
      setTestMessage(error.message || 'Failed to test MT5 Terminal path');
      notify.error('Error', 'Failed to test MT5 Terminal path');
    } finally {
      setIsSubmitting(false);
    }
  };

  const saveSettings = async () => {
    try {
      console.log('Saving MT5 settings:', mt5Settings);

      // Save to localStorage
      localStorage.setItem('mt5Settings', JSON.stringify(mt5Settings));
      console.log('Settings saved to localStorage');

      // If Electron API is available, save there too
      if (window.api && window.api.saveSettings) {
        await window.api.saveSettings(mt5Settings);
        console.log('Settings saved via Electron API');
      }

      notify.success('Success', 'Settings saved successfully');
    } catch (error) {
      console.error('Failed to save settings:', error);
      notify.error('Error', 'Failed to save settings: ' + error.message);
    }
  };
  
  // Function to apply theme to document
  const applyTheme = (theme) => {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
    
    if (theme === 'light') {
      document.documentElement.style.setProperty('--background', '#f3f4f6');
      document.documentElement.style.setProperty('--card', '#ffffff');
      document.documentElement.style.setProperty('--text', '#1f2937');
      document.documentElement.style.setProperty('--text-secondary', '#6b7280');
      document.documentElement.style.setProperty('--border', '#e5e7eb');
      document.documentElement.style.setProperty('--card-hover', '#f9fafb');
      document.documentElement.style.setProperty('--input', '#ffffff');
    } else {
      document.documentElement.style.setProperty('--background', '#111827');
      document.documentElement.style.setProperty('--card', '#1f2937');
      document.documentElement.style.setProperty('--text', '#f9fafb');
      document.documentElement.style.setProperty('--text-secondary', '#9ca3af');
      document.documentElement.style.setProperty('--border', '#374151');
      document.documentElement.style.setProperty('--card-hover', '#2d3748');
      document.documentElement.style.setProperty('--input', '#374151');
    }
  };
  
  // Handle theme change
  const handleThemeChange = (theme) => {
    setCurrentTheme(theme);
    applyTheme(theme);
    notify.success('Theme Updated', `Applied ${theme} theme`);
  };
  
  // Handle refresh interval change
  const handleRefreshIntervalChange = (e) => {
    const value = e.target.value;
    setRefreshInterval(value);
    localStorage.setItem('refreshInterval', value);
    notify.success('Settings Updated', 'Refresh interval updated');
  };

  return (
    <div className="settings-page">
      <div className="settings-section">
        <h2 className="section-title">MT5 Connection Settings</h2>
        <p className="section-description">
          Configure your MetaTrader 5 connection settings. These settings will be used when connecting to MT5.
        </p>

        <div className="settings-form">
          <div className="form-group">
            <label htmlFor="path">MT5 Terminal Path</label>
            <div className="input-with-button">
              <input
                type="text"
                id="path"
                name="path"
                value={mt5Settings.path}
                onChange={handleChange}
                placeholder="C:\\Program Files\\MetaTrader 5\\terminal64.exe"
              />
              <button
                type="button"
                className="button secondary"
                onClick={testMT5Path}
                disabled={isSubmitting}
              >
                Test Path
              </button>
            </div>
            {testStatus !== 'idle' && (
              <div className={`test-message ${testStatus}`}>
                {testMessage}
              </div>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="server">MT5 Server</label>
            <input
              type="text"
              id="server"
              name="server"
              value={mt5Settings.server}
              onChange={handleChange}
              placeholder="e.g., FBS-Demo"
            />
          </div>

          <div className="form-group">
            <label htmlFor="login">MT5 Login ID</label>
            <input
              type="text"
              id="login"
              name="login"
              value={mt5Settings.login}
              onChange={handleChange}
              placeholder="e.g., ********"
            />
          </div>

          <div className="form-group">
            <label htmlFor="accountType">Account Type</label>
            <select
              id="accountType"
              name="accountType"
              value={mt5Settings.accountType}
              onChange={handleChange}
            >
              <option value="demo">Demo Account</option>
              <option value="real">Real Account</option>
            </select>
          </div>

          <div className="form-actions">
            <button
              type="button"
              className="button primary"
              onClick={saveSettings}
              disabled={isSubmitting}
            >
              Save Settings
            </button>
          </div>
        </div>
      </div>

      <div className="settings-section">
        <h2 className="section-title">Application Settings</h2>
        <p className="section-description">
          Configure general application settings.
        </p>

        <div className="settings-form">
          <div className="form-group">
            <label>Theme</label>
            <div className="theme-selector">
              <button 
                className={`theme-option ${currentTheme === 'dark' ? 'active' : ''}`}
                onClick={() => handleThemeChange('dark')}
              >
                <span className="theme-preview dark"></span>
                <span>Dark</span>
              </button>
              <button 
                className={`theme-option ${currentTheme === 'light' ? 'active' : ''}`}
                onClick={() => handleThemeChange('light')}
              >
                <span className="theme-preview light"></span>
                <span>Light</span>
              </button>
            </div>
          </div>

          <div className="form-group">
            <label>Data Refresh Interval</label>
            <select 
              value={refreshInterval}
              onChange={handleRefreshIntervalChange}
            >
              <option value="5">5 seconds</option>
              <option value="10">10 seconds</option>
              <option value="30">30 seconds</option>
              <option value="60">1 minute</option>
            </select>
          </div>
        </div>
      </div>

      {/* New Section for License Information */}
      {licenseInfo && (licenseInfo.userName || licenseInfo.planType || licenseInfo.planEndDate) && (
        <div className="settings-section">
          <h2 className="section-title">Subscription Details</h2>
          <p className="section-description">
            Information about your current account license and subscription.
          </p>
          <div className="settings-form">
            {licenseInfo.userName && (
              <div className="form-group readonly">
                <label>Licensed User</label>
                <input type="text" value={licenseInfo.userName} readOnly />
              </div>
            )}
            {licenseInfo.planType && (
              <div className="form-group readonly">
                <label>Plan Type</label>
                <input type="text" value={licenseInfo.planType} readOnly />
              </div>
            )}
            {licenseInfo.planEndDate && (
              <div className="form-group readonly">
                <label>Plan End Date</label>
                <input type="text" value={new Date(licenseInfo.planEndDate).toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' })} readOnly />
              </div>
            )}
            {licenseInfo.status && (
              <div className="form-group readonly">
                <label>License Status</label>
                <input type="text" value={licenseInfo.status.charAt(0).toUpperCase() + licenseInfo.status.slice(1)} readOnly />
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SettingsPage;
