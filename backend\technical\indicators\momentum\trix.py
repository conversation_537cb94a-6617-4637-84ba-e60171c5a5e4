from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class TRIXIndicator(BaseIndicator):
    """TRIX (Triple Exponential Average) indicator."""

    def __init__(self, period: int = 14, signal_period: int = 9, source: str = 'close'):
        """
        Initialize TRIX indicator.

        Args:
            period: The lookback period for the triple EMA.
            signal_period: The period for the signal line EMA.
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
        """
        super().__init__({
            'period': period,
            'signal_period': signal_period,
            'source': source
        })

    def _ema(self, series: pd.Series, period: int) -> pd.Series:
        """Helper function for EMA calculation."""
        return series.ewm(span=period, adjust=False).mean()

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate TRIX values."""
        df = data.to_dataframe()
        min_len = self.params['period'] + self.params['signal_period']
        if df.empty or len(df) < min_len:
             return {'trix': np.array([]), 'signal': np.array([])}

        period = self.params['period']
        signal_period = self.params['signal_period']
        source_col = self.params['source'].lower()

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Calculate Triple EMA
        ema1 = self._ema(source_data, period)
        ema2 = self._ema(ema1, period)
        ema3 = self._ema(ema2, period)

        # Calculate TRIX (percentage rate of change of triple EMA)
        ema3_shifted = ema3.shift(1)
        # Avoid division by zero
        ema3_shifted_safe = ema3_shifted.replace(0, np.nan)
        trix_values = 100 * (ema3 - ema3_shifted) / ema3_shifted_safe
        trix_values = trix_values.fillna(0) # Fill initial NaNs

        # Calculate Signal Line (EMA of TRIX)
        signal_line = self._ema(trix_values, signal_period)

        self._values = {
            'trix': trix_values.values,
            'signal': signal_line.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        if self.params['signal_period'] < 1:
            raise ValueError("Signal Period must be greater than 0")
        return True