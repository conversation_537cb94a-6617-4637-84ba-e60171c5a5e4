from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class PriceActionPatternIndicator(BaseIndicator):
    """Price Action pattern indicator for Inside/Outside Bars and Tweezers."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - body_ratio: Parameter description (default: 0.5)
                - shadow_ratio: Parameter description (default: 0.2)
                - tweezer_tolerance: Parameter description (default: 0.001)
        """
        default_params = {
            "body_ratio": 0.5,
            "shadow_ratio": 0.2,
            "tweezer_tolerance": 0.001,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)


    def _is_inside_bar(self, curr_high: float, curr_low: float,
                      prev_high: float, prev_low: float) -> bool:
        """Check if current bar is inside previous bar's range."""
        return curr_high < prev_high and curr_low > prev_low

    def _is_outside_bar(self, curr_high: float, curr_low: float,
                       prev_high: float, prev_low: float) -> bool:
        """Check if current bar engulfs previous bar's range."""
        return curr_high > prev_high and curr_low < prev_low

    def _is_strong_bar(self, open_price: float, high: float,
                      low: float, close: float) -> bool:
        """Check if bar has a strong body with small shadows."""
        total_range = high - low
        if total_range == 0:
            return False
            
        body = abs(close - open_price)
        upper_shadow = high - max(open_price, close)
        lower_shadow = min(open_price, close) - low
        
        body_ratio = body / total_range
        shadow_ratio = max(upper_shadow, lower_shadow) / total_range
        
        return (body_ratio >= self.params['body_ratio'] and 
                shadow_ratio <= self.params['shadow_ratio'])

    def _is_tweezer(self, curr_open: float, curr_high: float,
                    curr_low: float, curr_close: float,
                    prev_open: float, prev_high: float,
                    prev_low: float, prev_close: float) -> tuple:
        """Identify Tweezer Top/Bottom patterns."""
        # Check if highs or lows are within tolerance
        tolerance = self.params['tweezer_tolerance']
        high_match = abs(curr_high - prev_high) <= (prev_high * tolerance)
        low_match = abs(curr_low - prev_low) <= (prev_low * tolerance)
        
        if not (high_match or low_match):
            return False, 0
            
        curr_bullish = curr_close > curr_open
        prev_bullish = prev_close > prev_open
        
        # Tweezer Top: Second bar bearish at resistance
        if high_match and prev_bullish and not curr_bullish:
            return True, -1
            
        # Tweezer Bottom: Second bar bullish at support
        if low_match and not prev_bullish and curr_bullish:
            return True, 1
            
        return False, 0

    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        opens = df['open'].values
        highs = df['high'].values
        lows = df['low'].values
        closes = df['close'].values
        
        is_pattern = np.zeros_like(closes)
        pattern_type = np.zeros_like(closes)  # 1=Bullish, -1=Bearish
        pattern_id = np.zeros_like(closes)    # 1=Inside Bar, 2=Outside Bar, 3=Tweezer
        
        # Scan for patterns
        for i in range(1, len(closes)):
            # Check Inside Bar
            if self._is_inside_bar(highs[i], lows[i], highs[i-1], lows[i-1]):
                window = slice(i-1, i+1)
                is_pattern[window] = 1
                pattern_id[window] = 1
                # Determine direction based on trend and strength
                if closes[i] > opens[i] and self._is_strong_bar(opens[i], highs[i],
                                                              lows[i], closes[i]):
                    pattern_type[window] = 1  # Bullish
                elif closes[i] < opens[i] and self._is_strong_bar(opens[i], highs[i],
                                                                lows[i], closes[i]):
                    pattern_type[window] = -1  # Bearish
                continue
                
            # Check Outside Bar
            if self._is_outside_bar(highs[i], lows[i], highs[i-1], lows[i-1]):
                window = slice(i-1, i+1)
                is_pattern[window] = 1
                pattern_id[window] = 2
                # Determine direction based on close vs open
                if closes[i] > opens[i]:
                    pattern_type[window] = 1  # Bullish
                else:
                    pattern_type[window] = -1  # Bearish
                continue
                
            # Check Tweezer patterns
            is_tweezer, tw_type = self._is_tweezer(opens[i], highs[i], lows[i], closes[i],
                                                  opens[i-1], highs[i-1], lows[i-1], closes[i-1])
            if is_tweezer:
                window = slice(i-1, i+1)
                is_pattern[window] = 1
                pattern_type[window] = tw_type
                pattern_id[window] = 3
        
        # Calculate pattern characteristics
        strength = np.zeros_like(closes)
        reliability = np.zeros_like(closes)
        
        for i in range(len(closes)-1):
            if is_pattern[i]:
                # Calculate pattern strength based on:
                # 1. Bar size relative to average
                # 2. Body to range ratio
                # 3. Location within larger trend
                window_start = max(0, i-5)
                window = slice(window_start, i+1)
                
                avg_range = np.mean(highs[window] - lows[window])
                curr_range = highs[i] - lows[i]
                
                if avg_range > 0:
                    range_ratio = curr_range / avg_range
                    body_ratio = abs(closes[i] - opens[i]) / curr_range
                    strength[i] = (range_ratio + body_ratio) / 2
                
                # Calculate reliability based on next bar's movement
                if i < len(closes)-1:
                    future_return = (closes[i+1] - closes[i]) / closes[i]
                    if pattern_type[i] > 0:  # Bullish pattern
                        reliability[i] = 1 if future_return > 0 else -1
                    elif pattern_type[i] < 0:  # Bearish pattern
                        reliability[i] = 1 if future_return < 0 else -1
        
        # Calculate trend context
        trend = np.zeros_like(closes)
        for i in range(20, len(closes)):
            sma = np.mean(closes[i-20:i])
            trend[i] = 1 if closes[i] > sma else -1
        
        return {
            'is_pattern': is_pattern.astype(int),
            'pattern_type': pattern_type,  # 1=Bullish, -1=Bearish
            'pattern_id': pattern_id,      # 1=Inside Bar, 2=Outside Bar, 3=Tweezer
            'strength': strength,
            'trend': trend,
            'reliability': reliability
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if not 0 < self.params['body_ratio'] < 1:
            raise ValueError("Body ratio must be between 0 and 1")
        if not 0 < self.params['shadow_ratio'] < 1:
            raise ValueError("Shadow ratio must be between 0 and 1")
        if not 0 < self.params['tweezer_tolerance'] < 0.1:
            raise ValueError("Tweezer tolerance must be between 0 and 0.1")
        return True 