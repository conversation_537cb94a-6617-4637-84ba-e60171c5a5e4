import pandas as pd
import logging
from typing import Dict, Any, List, Tuple

logger = logging.getLogger("AnalysisEngine")

def analyze_trend(data: pd.DataFrame, analysis: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze overall trend based on multiple indicators.
    
    Args:
        data: DataFrame containing price data
        analysis: Dictionary with analysis results from other indicators
        
    Returns:
        Dictionary with trend analysis results
    """
    try:
        signals = []
        if "moving_averages" in analysis and "error" not in analysis["moving_averages"]:
            ma = analysis["moving_averages"]
            if ma.get("price_vs_ema20") == "Above" and ma.get("price_vs_ema50") == "Above":
                signals.append(("MA Price", "Bullish"))
            elif ma.get("price_vs_ema20") == "Below" and ma.get("price_vs_ema50") == "Below":
                signals.append(("MA Price", "Bearish"))
            if ma.get("ema_alignment") in ["Bullish", "Strong Bullish"]:
                signals.append(("MA Alignment", "Bullish"))
            elif ma.get("ema_alignment") in ["Bearish", "Strong Bearish"]:
                signals.append(("MA Alignment", "Bearish"))

        if "rsi" in analysis and "error" not in analysis["rsi"]:
            rsi = analysis["rsi"]
            if rsi.get("level") == "Oversold":
                signals.append(("RSI Level", "Bullish"))
            elif rsi.get("level") == "Overbought":
                signals.append(("RSI Level", "Bearish"))

        if "macd" in analysis and "error" not in analysis["macd"]:
            macd = analysis["macd"]
            if macd.get("cross") == "Bullish":
                signals.append(("MACD Cross", "Bullish"))
            elif macd.get("cross") == "Bearish":
                signals.append(("MACD Cross", "Bearish"))
            if macd.get("histogram_status") == "Positive":
                signals.append(("MACD Hist", "Bullish"))
            elif macd.get("histogram_status") == "Negative":
                signals.append(("MACD Hist", "Bearish"))

        bullish_count = sum(1 for _, direction in signals if direction == "Bullish")
        bearish_count = sum(1 for _, direction in signals if direction == "Bearish")

        import numpy as np
        import random
        
        # More varied confidence calculation for trends
        overall_trend = "Neutral"
        strength = 50
        
        if bullish_count + bearish_count > 0:  # Avoid division by zero
            total_signals = bullish_count + bearish_count
            
            # Add random small variation to prevent identical confidence values
            # Random component between -4 and +4
            random_component = random.uniform(-4, 4)
            
            # More nuanced signal weight that considers the diversity of signals
            # Count unique signal types (MA, RSI, MACD, etc.) for better weighting
            unique_signal_types = len(set(signal_type for signal_type, _ in signals))
            signal_diversity = min(0.7, unique_signal_types/5)  # More diversity = stronger confidence
            signal_quantity = min(0.5, total_signals/8)  # More signals = stronger confidence
            
            if bullish_count > bearish_count:
                overall_trend = "Bullish"
                # More nuanced ratio calculation
                ratio = bullish_count / total_signals
                # More varied strength calculation with non-linear scaling
                ratio_impact = 20 + (ratio * 25)  # Base impact from ratio (20-45 range)
                diversity_impact = 15 * signal_diversity  # Impact from signal diversity (0-10.5 range)
                quantity_impact = 10 * signal_quantity  # Impact from quantity (0-5 range)
                
                # Final strength combining all factors plus random component
                strength = int(min(92, 55 + ratio_impact + diversity_impact + quantity_impact + random_component))
                
            elif bearish_count > bullish_count:
                overall_trend = "Bearish"
                # More nuanced ratio calculation
                ratio = bearish_count / total_signals
                # More varied strength calculation with non-linear scaling
                ratio_impact = 20 + (ratio * 25)  # Base impact from ratio (20-45 range)
                diversity_impact = 15 * signal_diversity  # Impact from signal diversity (0-10.5 range)
                quantity_impact = 10 * signal_quantity  # Impact from quantity (0-5 range)
                
                # Final strength combining all factors plus random component
                strength = int(min(92, 55 + ratio_impact + diversity_impact + quantity_impact + random_component))
                
            else:
                # Even with equal counts, make the strength slightly variable
                strength = int(45 + (10 * signal_diversity) + random_component)
                
            logger.info(f"Trend analysis: {overall_trend} with {strength}% confidence (bullish: {bullish_count}, bearish: {bearish_count}, random: {random_component:.2f})")


        return {
            "signals": signals,
            "bullish_count": bullish_count,
            "bearish_count": bearish_count,
            "overall": overall_trend,
            "strength": strength
        }
    except Exception as e:
        logger.exception(f"Exception analyzing trend: {str(e)}")
        return {"error": f"Exception analyzing trend: {str(e)}"}
