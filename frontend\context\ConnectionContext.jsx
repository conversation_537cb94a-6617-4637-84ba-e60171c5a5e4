import React, { createContext, useContext, useState, useEffect } from 'react';

// Create the connection context
const ConnectionContext = createContext({
  isConnected: false,
  accountInfo: {
    balance: 0,
    equity: 0,
    profit: 0,
    positions: 0,
    accountName: '',
    server: '',
    accountType: '',
    leverage: 0
  }
});

// Provider component
export const ConnectionProvider = ({ children }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [accountInfo, setAccountInfo] = useState({
    balance: 0,
    equity: 0,
    profit: 0,
    positions: 0,
    accountName: '',
    server: '',
    accountType: '',
    leverage: 0
  });

  // Track disconnection count for auto-reconnect logic
  const [disconnectionCount, setDisconnectionCount] = useState(0);
  const [lastConnectedTime, setLastConnectedTime] = useState(null);
  const [isReconnecting, setIsReconnecting] = useState(false);

  // Function to attempt reconnection
  const attemptReconnection = async () => {
    if (isReconnecting) return; // Prevent multiple simultaneous reconnection attempts

    try {
      setIsReconnecting(true);
      console.log('[ConnectionContext] Attempting to reconnect to MT5...');

      // Use the reload_settings endpoint to reconnect with saved settings
      const response = await fetch('http://localhost:5001/api/connection/reload_settings', {
        method: 'POST'
      });

      if (response.ok) {
        const data = await response.json();
        console.log('[ConnectionContext] Reconnection response:', data);

        if (data.status === 'connected' || data.status === 'success') {
          console.log('[ConnectionContext] Successfully reconnected to MT5');
          setIsConnected(true);
          setDisconnectionCount(0); // Reset disconnection count on successful reconnection

          // Update account info if available
          if (data.account_info) {
            setAccountInfo({
              balance: data.account_info.balance || 0,
              equity: data.account_info.equity || 0,
              profit: (data.account_info.equity - data.account_info.balance) || 0,
              positions: data.account_info.positions || 0,
              accountName: data.account_info.name || '',
              server: data.account_info.server || '',
              accountType: data.account_info.trade_mode === 0 ? 'Demo' : 'Real',
              leverage: data.account_info.leverage || 0
            });
          }
        } else {
          console.error('[ConnectionContext] Reconnection failed:', data.message || 'Unknown error');
        }
      } else {
        console.error('[ConnectionContext] Reconnection request failed:', response.status);
      }
    } catch (error) {
      console.error('[ConnectionContext] Error during reconnection attempt:', error);
    } finally {
      setIsReconnecting(false);
    }
  };

  // Function to disconnect
  const disconnect = async () => {
    try {
      console.log('[ConnectionContext] Disconnecting from MT5...');

      // Update local state immediately for better UI responsiveness
      setIsConnected(false);
      setAccountInfo({
        balance: 0,
        equity: 0,
        profit: 0,
        positions: 0,
        accountName: '',
        server: '',
        accountType: '',
        leverage: 0
      });

      // Then send the disconnect request to the backend
      const response = await fetch('http://localhost:5001/api/connection/disconnect', {
        method: 'POST'
      });

      if (response.ok) {
        const data = await response.json();
        console.log('[ConnectionContext] Disconnect response:', data);
      } else {
        console.error('[ConnectionContext] Disconnect request failed:', response.status);
      }
    } catch (error) {
      console.error('[ConnectionContext] Error during disconnect:', error);
    }
  };

  useEffect(() => {
    let intervalId = null;

    const checkConnectionStatus = async () => {
      // This function is called by the interval IF isConnected was true when interval was set.
      try {
        console.log('[ConnectionContext] Interval: Checking MT5 connection status (because connection was active).');
        const response = await fetch('http://localhost:5001/api/connection/connection_status');

        if (response.ok) {
          const data = await response.json();
          const currentlyConnected = data.connected === true;

          if (currentlyConnected) {
            // Connection is still active or re-established by another means.
            // Ensure local state is up-to-date.
            if (!isConnected) { // This case implies it was set to false then reconnected externally
                 setIsConnected(true); // Bring context state back to true
                 setDisconnectionCount(0); // Reset as connection is now live
                 console.log('[ConnectionContext] Interval: Connection found to be active again.');
            }
            if (data.account_info) {
              setAccountInfo({
                balance: data.account_info.balance || 0,
                equity: data.account_info.equity || 0,
                profit: (data.account_info.equity - data.account_info.balance) || 0,
                positions: data.account_info.positions || 0,
                accountName: data.account_info.name || '',
                server: data.account_info.server || '',
                accountType: data.account_info.trade_mode === 0 ? 'Demo' : 'Real',
                leverage: data.account_info.leverage || 0
              });
            }
          } else { // Connection lost (was true, now false)
            console.log('[ConnectionContext] Interval: Detected connection loss.');
            setIsConnected(false); // This will trigger useEffect to clear the interval.
                                   // And will allow the reconnection logic below to run once.
            const newDisconnectionCount = disconnectionCount + 1;
            setDisconnectionCount(newDisconnectionCount);

            if (newDisconnectionCount <= 3) {
              console.log(`[ConnectionContext] Interval: Attempting auto-reconnect (attempt ${newDisconnectionCount})`);
              // attemptReconnection might set isConnected back to true if successful,
              // which would re-trigger useEffect and restart the interval.
              attemptReconnection();
            } else {
              console.log('[ConnectionContext] Interval: Max auto-reconnect attempts reached. Interval will remain stopped.');
            }
          }
        } else { // response not ok - status check itself failed
          console.error('[ConnectionContext] Interval: Connection status request failed:', response.status);
          setIsConnected(false); // Assume connection is lost, stop interval.
          const newDisconnectionCount = disconnectionCount + 1;
          setDisconnectionCount(newDisconnectionCount);
          if (newDisconnectionCount <= 3) {
            console.log(`[ConnectionContext] Interval: Status check HTTP error. Attempting auto-reconnect (attempt ${newDisconnectionCount})`);
            attemptReconnection();
          } else {
            console.log('[ConnectionContext] Interval: Status check HTTP error. Max attempts. Interval will remain stopped.');
          }
        }
      } catch (error) {
        console.error('[ConnectionContext] Interval: Error during connection status check:', error);
        setIsConnected(false); // Assume connection is lost, stop interval.
        const newDisconnectionCount = disconnectionCount + 1;
        setDisconnectionCount(newDisconnectionCount);
         if (newDisconnectionCount <= 3) {
            console.log(`[ConnectionContext] Interval: Exception in status check. Attempting auto-reconnect (attempt ${newDisconnectionCount})`);
            attemptReconnection();
        } else {
            console.log('[ConnectionContext] Interval: Exception in status check. Max attempts. Interval will remain stopped.');
        }
      }
    };

    if (isConnected) {
      console.log('[ConnectionContext] Connection is active. Starting periodic status check interval.');
      // Perform an immediate check when connection becomes active, then start interval
      // This ensures that if isConnected was set true by an external event, we verify and get latest account info.
      checkConnectionStatus();
      intervalId = setInterval(checkConnectionStatus, 10000);
    } else {
      console.log('[ConnectionContext] Connection is not active. Interval is stopped or not started.');
      // No interval runs if not connected.
    }

    // Event listeners to update this context's isConnected state from external events
    const handleExternalConnectionSuccess = (event) => {
      console.log('[ConnectionContext] Event: mt5:connectionSucceeded received.');
      if (!isConnected) { // Only update if state is changing
        setIsConnected(true);
        setDisconnectionCount(0); // Reset count on fresh external connection
      }
      // Update account info regardless, as it might have changed
      if (event.detail && event.detail.accountInfo) {
        setAccountInfo(event.detail.accountInfo);
      } else if (event.detail && event.detail.connected === true && !event.detail.accountInfo) {
        // If connected but no account info in event, try to fetch it
        // This can happen if App.jsx's initial reload_settings is successful
        console.log('[ConnectionContext] Event: Connected, but no account info in event. Will fetch in next interval or immediate check.');
      }
    };

    const handleExternalDisconnection = (event) => {
      console.log('[ConnectionContext] Event: mt5:disconnected received.');
      if (isConnected) { // Only update if state is changing
         setIsConnected(false);
      }
      // The useEffect reacting to isConnected=false will handle clearing interval
      // and deciding on reconnection attempts if it was an unexpected drop.
    };
    
    // This listener is for status changes that might not be full connect/disconnect events
    // but can update account info or connection state.
    const handleConnectionStatusChanged = (event) => {
        const { connected, accountInfo: newAccountInfo, error } = event.detail;
        console.log('[ConnectionContext] Event: mt5:connectionStatusChanged received.', event.detail);
        if (typeof connected === 'boolean' && connected !== isConnected) {
            setIsConnected(connected);
            if(connected) setDisconnectionCount(0);
        }
        if (connected && newAccountInfo) {
            setAccountInfo(newAccountInfo);
        } else if (!connected && isConnected) { // If event says disconnected, but context thought it was connected
            setIsConnected(false);
        }
        // Error handling for notifications can be done in App.jsx which also listens to this
    };

    window.addEventListener('mt5:connectionSucceeded', handleExternalConnectionSuccess);
    window.addEventListener('mt5:disconnected', handleExternalDisconnection);
    window.addEventListener('mt5:connectionStatusChanged', handleConnectionStatusChanged);

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
        console.log('[ConnectionContext] Cleared interval on unmount or isConnected change.');
      }
      window.removeEventListener('mt5:connectionSucceeded', handleExternalConnectionSuccess);
      window.removeEventListener('mt5:disconnected', handleExternalDisconnection);
      window.removeEventListener('mt5:connectionStatusChanged', handleConnectionStatusChanged);
    };
  }, [isConnected]); // Primary dependency controlling the interval.
                     // disconnectionCount is managed internally by the logic when isConnected is true.

  return (
    <ConnectionContext.Provider value={{
      isConnected,
      accountInfo,
      reconnect: attemptReconnection,
      disconnect,
      isReconnecting
    }}>
      {children}
    </ConnectionContext.Provider>
  );
};

// Custom hook to use the connection context
export const useConnection = () => useContext(ConnectionContext);

export default ConnectionContext;
