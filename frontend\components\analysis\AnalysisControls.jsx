import React from 'react';
import '../../styles/Pages.css';
import '../../styles/BentoComponents.css';

/**
 * AnalysisControls component for symbol and timeframe selection
 * 
 * @param {string} selectedSymbol - Currently selected symbol
 * @param {function} onSymbolChange - <PERSON><PERSON> for symbol change
 * @param {string} timeframe - Currently selected timeframe
 * @param {function} onTimeframeChange - Handler for timeframe change
 * @param {boolean} loading - Whether data is currently loading
 * @param {Array<string>} symbols - List of available symbols
 * @returns {JSX.Element} - The rendered controls
 */
const AnalysisControls = ({ 
  selectedSymbol, 
  onSymbolChange, 
  timeframe, 
  onTimeframeChange, 
  loading, 
  symbols 
}) => {
  return (
    <div className="bento-card analysis-controls-card">
      <div className="analysis-form-group">
        <label htmlFor="symbol">Symbol</label>
        <select
          id="symbol"
          value={selectedSymbol}
          onChange={onSymbolChange}
          disabled={loading || symbols.length === 0}
        >
          {symbols.length === 0 && <option value="">Loading symbols... {loading ? "(in progress)" : "(waiting)"}</option>}
          {Array.isArray(symbols) && symbols.map(symbol => (
            <option key={symbol} value={symbol}>{symbol}</option>
          ))}
        </select>
      </div>

      <div className="analysis-form-group">
        <label htmlFor="timeframe">Timeframe</label>
        <select
          id="timeframe"
          value={timeframe}
          onChange={onTimeframeChange}
          disabled={loading}
        >
          <option value="M1">1 Minute</option>
          <option value="M5">5 Minutes</option>
          <option value="M15">15 Minutes</option>
          <option value="M30">30 Minutes</option>
          <option value="H1">1 Hour</option>
          <option value="H4">4 Hours</option>
          <option value="D1">Daily</option>
          <option value="W1">Weekly</option>
          <option value="MN1">Monthly</option>
        </select>
      </div>
    </div>
  );
};

export default AnalysisControls;
