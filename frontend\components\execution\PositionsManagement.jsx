import React, { useState, useEffect } from 'react';
import { useNotification } from '../Notification';
import { formatDate } from '../../utils/formatters';

/**
 * PositionsManagement component - Displays and manages open positions
 * 
 * @returns {JSX.Element} Rendered component
 */
function PositionsManagement() {
  const [positions, setPositions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedPosition, setSelectedPosition] = useState(null);
  const [showModifyDialog, setShowModifyDialog] = useState(false);
  const [modifySL, setModifySL] = useState(0);
  const [modifyTP, setModifyTP] = useState(0);
  
  const notify = useNotification();
  
  // Fetch open positions
  const fetchPositions = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('http://localhost:5001/api/trade/positions');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch positions: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('Positions received:', data);
      
      // If data is an array, use it directly
      if (Array.isArray(data)) {
        setPositions(data);
      } else {
        // If data contains an error message
        if (data.error) {
          throw new Error(data.error);
        }
        // Empty array if no positions or unknown structure
        setPositions([]);
      }
    } catch (error) {
      console.error('Error fetching positions:', error);
      setError(error.message || 'Failed to load positions');
      notify.error('Error', error.message || 'Failed to load positions');
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch positions on component mount
  useEffect(() => {
    fetchPositions();
    
    // Set up refresh interval (every 10 seconds)
    const intervalId = setInterval(fetchPositions, 10000);
    
    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, []);
  
  // Handle closing a position
  const handleClosePosition = async (positionId) => {
    try {
      // Confirm with user
      if (!window.confirm('Are you sure you want to close this position?')) {
        return;
      }
      
      // Send close request to API
      const response = await fetch('http://localhost:5001/api/trade/position/close', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ position_id: positionId }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to close position');
      }
      
      console.log('Position closed successfully:', data);
      notify.success('Success', 'Position closed successfully');
      
      // Refresh positions
      fetchPositions();
    } catch (error) {
      console.error('Error closing position:', error);
      notify.error('Error', error.message || 'Failed to close position');
    }
  };
  
  // Handle modifying a position
  const openModifyDialog = (position) => {
    setSelectedPosition(position);
    setModifySL(position.sl || 0);
    setModifyTP(position.tp || 0);
    setShowModifyDialog(true);
  };
  
  const handleModifyPosition = async () => {
    if (!selectedPosition) return;
    
    try {
      const response = await fetch('http://localhost:5001/api/trade/position/modify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          position_id: selectedPosition.ticket,
          sl: modifySL,
          tp: modifyTP
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to modify position');
      }
      
      console.log('Position modified successfully:', data);
      notify.success('Success', 'Position modified successfully');
      
      // Close dialog and refresh positions
      setShowModifyDialog(false);
      fetchPositions();
    } catch (error) {
      console.error('Error modifying position:', error);
      notify.error('Error', error.message || 'Failed to modify position');
    }
  };
  
  // Format profit/loss with color
  const formatPL = (pl) => {
    const isProfit = pl >= 0;
    return (
      <span className={isProfit ? 'position-profit' : 'position-loss'}>
        {isProfit ? '+' : ''}{pl.toFixed(2)}
      </span>
    );
  };
  
  // Determine position type (buy/sell)
  const getPositionType = (type) => {
    return type === 0 ? 'BUY' : 'SELL';
  };
  
  // Format position type with styling
  const formatPositionType = (type) => {
    const posType = getPositionType(type);
    const className = posType === 'BUY' ? 'signal-type-badge buy' : 'signal-type-badge sell';
    return <span className={className}>{posType}</span>;
  };
  
  return (
    <div className="positions-container">
      <div className="positions-header">
        <h3 className="positions-title">Open Positions</h3>
        <button 
          className="positions-refresh" 
          onClick={fetchPositions}
          disabled={loading}
        >
          {loading ? 'Refreshing...' : 'Refresh'} 
          <i className="fas fa-sync"></i>
        </button>
      </div>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      {positions.length === 0 && !loading && !error ? (
        <div className="positions-empty">
          No open positions found.
        </div>
      ) : (
        <table className="positions-table">
          <thead>
            <tr>
              <th>Symbol</th>
              <th>Type</th>
              <th>Volume</th>
              <th>Open Price</th>
              <th>SL</th>
              <th>TP</th>
              <th>P/L</th>
              <th>Open Time</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {positions.map(position => (
              <tr key={position.ticket}>
                <td>{position.symbol}</td>
                <td>{formatPositionType(position.type)}</td>
                <td>{position.volume.toFixed(2)}</td>
                <td>{position.price_open.toFixed(5)}</td>
                <td>{position.sl ? position.sl.toFixed(5) : '-'}</td>
                <td>{position.tp ? position.tp.toFixed(5) : '-'}</td>
                <td>{formatPL(position.profit)}</td>
                <td>{formatDate(new Date(position.time))}</td>
                <td className="position-actions">
                  <button 
                    className="position-modify-btn" 
                    onClick={() => openModifyDialog(position)}
                  >
                    Modify
                  </button>
                  <button 
                    className="position-close-btn" 
                    onClick={() => handleClosePosition(position.ticket)}
                  >
                    Close
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
      
      {/* Modify Position Dialog */}
      {showModifyDialog && selectedPosition && (
        <div className="trade-execution-dialog">
          <div className="dialog-content">
            <h3>Modify Position</h3>
            
            <div className="trade-info-group">
              <div className="trade-info-row">
                <span className="label">Symbol:</span>
                <span className="value">{selectedPosition.symbol}</span>
              </div>
              <div className="trade-info-row">
                <span className="label">Type:</span>
                <span className="value">{getPositionType(selectedPosition.type)}</span>
              </div>
              <div className="trade-info-row">
                <span className="label">Open Price:</span>
                <span className="value">{selectedPosition.price_open.toFixed(5)}</span>
              </div>
            </div>
            
            <div className="trade-levels">
              <div className="level-row">
                <span className="level-label">Stop Loss:</span>
                <input 
                  type="number" 
                  value={modifySL}
                  onChange={e => setModifySL(parseFloat(e.target.value) || 0)}
                  step="0.00001"
                />
              </div>
              
              <div className="level-row">
                <span className="level-label">Take Profit:</span>
                <input 
                  type="number" 
                  value={modifyTP}
                  onChange={e => setModifyTP(parseFloat(e.target.value) || 0)}
                  step="0.00001"
                />
              </div>
            </div>
            
            <div className="dialog-actions">
              <button 
                className="cancel-btn" 
                onClick={() => setShowModifyDialog(false)}
              >
                Cancel
              </button>
              <button 
                className="execute-btn" 
                onClick={handleModifyPosition}
              >
                Apply Changes
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default PositionsManagement;
