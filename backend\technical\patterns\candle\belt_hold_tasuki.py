from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class BeltHoldTasukiPatternIndicator(BaseIndicator):
    """Belt Hold and Tasuki Gap pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """Initialize the indicator.
        
        Args:
            params: Dictionary of parameters
                - body_ratio: Minimum ratio of body to total range (default: 0.7)
                - shadow_ratio: Maximum ratio of shadows (default: 0.1)
                - gap_threshold: Minimum price gap (default: 0.01 or 1%)
        """
        default_params = {
            'body_ratio': 0.7,
            'shadow_ratio': 0.1,
            'gap_threshold': 0.01
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)

    def _is_belt_hold(self, open_price: float, high: float,
                     low: float, close: float) -> tuple:
        """Identify Belt Hold Line patterns."""
        total_range = high - low
        if total_range == 0:
            return False, 0
            
        body = abs(close - open_price)
        upper_shadow = high - max(open_price, close)
        lower_shadow = min(open_price, close) - low
        
        body_ratio = body / total_range
        
        # Bullish Belt Hold: Opening at/near low with long body
        if (open_price == low or lower_shadow / total_range <= self.params['shadow_ratio']) and close > open_price:
            if body_ratio >= self.params['body_ratio']:
                return True, 1
                
        # Bearish Belt Hold: Opening at/near high with long body
        if (open_price == high or upper_shadow / total_range <= self.params['shadow_ratio']) and close < open_price:
            if body_ratio >= self.params['body_ratio']:
                return True, -1
                
        return False, 0

    def _is_tasuki_gap(self, opens: np.ndarray, highs: np.ndarray,
                      lows: np.ndarray, closes: np.ndarray, idx: int) -> tuple:
        """Identify Rising/Falling Tasuki Gap patterns."""
        if idx < 2:
            return False, 0
            
        # Check for three consecutive candles
        first_bullish = closes[idx-2] > opens[idx-2]
        second_bullish = closes[idx-1] > opens[idx-1]
        third_bullish = closes[idx] > opens[idx]
        
        # Rising Tasuki Gap
        if (first_bullish and second_bullish and not third_bullish and
            min(opens[idx-1], closes[idx-1]) > max(opens[idx-2], closes[idx-2]) and
            opens[idx] > closes[idx-1] and closes[idx] < opens[idx-1]):
            return True, 1
            
        # Falling Tasuki Gap
        if (not first_bullish and not second_bullish and third_bullish and
            max(opens[idx-1], closes[idx-1]) < min(opens[idx-2], closes[idx-2]) and
            opens[idx] < closes[idx-1] and closes[idx] > opens[idx-1]):
            return True, -1
            
        return False, 0

    def calculate(self, market_data: MarketData) -> Dict[str, Any]:
        """Calculate Belt Hold and Tasuki Gap pattern values.
        
        Args:
            market_data: MarketData object containing OHLCV data
            
        Returns:
            Dictionary containing:
                - is_pattern: Boolean indicating if pattern is present
                - pattern_type: String indicating pattern type ('belt_hold' or 'tasuki')
                - pattern_id: String identifying the pattern
                - strength: Float between 0 and 1 indicating pattern strength
                - trend: Integer (-1 for bearish, 1 for bullish)
                - reliability: Integer (-1 for unreliable, 1 for reliable)
        """
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        opens = df['open'].values
        highs = df['high'].values
        lows = df['low'].values
        closes = df['close'].values
        
        is_pattern = np.zeros(len(df), dtype=bool)
        pattern_type = np.full(len(df), '')
        pattern_id = np.full(len(df), '')
        strength = np.zeros(len(df))
        trend = np.zeros(len(df))
        reliability = np.zeros(len(df))
        
        # Scan for patterns
        for i in range(len(closes)):
            # Check Belt Hold pattern
            is_belt, b_type = self._is_belt_hold(opens[i], highs[i],
                                               lows[i], closes[i])
            if is_belt:
                is_pattern[i] = True
                pattern_type[i] = 'belt_hold'
                pattern_id[i] = f'belt_hold_{"bull" if b_type > 0 else "bear"}_{i}'
                
                # Calculate strength based on body ratio
                total_range = highs[i] - lows[i]
                if total_range > 0:
                    body = abs(closes[i] - opens[i])
                    strength[i] = min(1.0, body / total_range)
                
                trend[i] = b_type
                
                # Calculate reliability based on future price movement
                if i < len(closes)-1:
                    future_return = (closes[i+1] - closes[i]) / closes[i]
                    reliability[i] = 1 if (b_type > 0 and future_return > 0) or (b_type < 0 and future_return < 0) else -1
                continue
                
            # Check Tasuki Gap pattern
            is_tasuki, t_type = self._is_tasuki_gap(opens, highs, lows, closes, i)
            if is_tasuki:
                window = slice(i-2, i+1)
                is_pattern[window] = True
                pattern_type[window] = 'tasuki'
                pattern_id[window] = f'tasuki_{"bull" if t_type > 0 else "bear"}_{i}'
                
                # Calculate strength based on gap size and body ratio
                for j in range(i-2, i+1):
                    total_range = highs[j] - lows[j]
                    if total_range > 0:
                        body = abs(closes[j] - opens[j])
                        body_ratio = body / total_range
                        
                        if j == i:  # Gap candle
                            gap_size = 0
                            if t_type > 0:  # Rising
                                gap_size = opens[j] - closes[j-1]
                            else:  # Falling
                                gap_size = closes[j-1] - opens[j]
                            strength[j] = min(1.0, (body_ratio + (gap_size / total_range)) / 2)
                        else:
                            strength[j] = min(1.0, body_ratio)
                
                trend[window] = t_type
                
                # Calculate reliability based on future price movement
                if i < len(closes)-1:
                    future_return = (closes[i+1] - closes[i]) / closes[i]
                    reliability[window] = 1 if (t_type > 0 and future_return > 0) or (t_type < 0 and future_return < 0) else -1
        
        # Calculate trend context using 20-period SMA
        sma20 = df['close'].rolling(window=20).mean()
        trend = np.where(df['close'] > sma20, 1, -1)
        
        return {
            'is_pattern': is_pattern,
            'pattern_type': pattern_type,
            'pattern_id': pattern_id,
            'strength': strength,
            'trend': trend,
            'reliability': reliability
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters.
        
        Returns:
            Boolean indicating if parameters are valid
        """
        if not self.params:
            return True
            
        body_ratio = self.params.get('body_ratio', 0.7)
        shadow_ratio = self.params.get('shadow_ratio', 0.1)
        gap_threshold = self.params.get('gap_threshold', 0.01)
        
        return (0 < body_ratio <= 1 and
                0 < shadow_ratio <= 1 and
                0 < gap_threshold <= 1) 