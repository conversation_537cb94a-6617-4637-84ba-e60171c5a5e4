/* Tab navigation */
.page-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--border, rgba(255, 255, 255, 0.1));
}

.tab-button {
  padding: 10px 20px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  position: relative;
  color: var(--text-secondary, rgba(255, 255, 255, 0.7));
}

.tab-button.active {
  color: var(--primary, #3772ff);
  font-weight: 600;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--primary, #3772ff);
}

.tab-button:hover {
  background-color: rgba(55, 114, 255, 0.1);
}

/* Autonomous trading page general styles */
.autonomous-page {
  background-color: var(--background, #22303f);
  color: var(--text, #e3e8ef);
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text, #fff);
  margin-bottom: 8px;
}

/* Monitoring dashboard */
.monitoring-container {
  width: 100%;
}

.monitoring-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.monitoring-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.monitoring-header h3 {
  margin: 0;
  color: var(--text, #ffffff);
  font-size: 20px;
  font-weight: 600;
}

.last-updated {
  font-size: 14px;
  color: var(--text-secondary, rgba(255, 255, 255, 0.7));
  display: flex;
  align-items: center;
  gap: 10px;
}

.refresh-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: var(--primary, #3772ff);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
}

.refresh-button:hover {
  background-color: rgba(55, 114, 255, 0.1);
}

.monitoring-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.monitoring-card {
  background-color: var(--card, #1e2a38);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  padding: 20px;
  overflow: hidden;
}

.monitoring-card h4 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text, #ffffff);
}

/* Performance metrics card */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 16px;
}

.metric {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  font-size: 12px;
  color: var(--text-secondary, rgba(255, 255, 255, 0.7));
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text, #ffffff);
}

.metric-value.positive {
  color: var(--success, #10b981);
}

.metric-value.negative {
  color: var(--error, #ef4444);
}

/* Health status card */
.health-status {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.health-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.health-label {
  font-size: 14px;
  color: var(--text-secondary, #8b949e);
}

.health-value {
  font-size: 16px;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  display: inline-flex;
  align-items: center;
  color: var(--text, #fff);
}

.health-value.running {
  background-color: rgba(0, 200, 83, 0.2);
  color: var(--success, #00c853);
}

.health-value.stopped {
  background-color: rgba(255, 82, 82, 0.2);
  color: var(--error, #ff5252);
}

.health-value.connected {
  background-color: rgba(0, 200, 83, 0.2);
  color: var(--success, #00c853);
}

.health-value.disconnected {
  background-color: rgba(255, 82, 82, 0.2);
  color: var(--error, #ff5252);
}

.health-value.good {
  background-color: rgba(0, 200, 83, 0.2);
  color: var(--success, #00c853);
}

.health-value.warning {
  background-color: rgba(255, 193, 7, 0.2);
  color: var(--warning, #ffc107);
}

.health-value.danger {
  background-color: rgba(255, 82, 82, 0.2);
  color: var(--error, #ff5252);
}

/* Positions and history tables */
.table-container {
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
  border-radius: 4px;
}

.positions-table, .history-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  color: var(--text, #ffffff);
}

.positions-table th, .history-table th {
  background-color: var(--card-hover, #172331);
  padding: 10px;
  text-align: left;
  position: sticky;
  top: 0;
  z-index: 1;
  box-shadow: 0 1px 0 0 var(--border, rgba(255, 255, 255, 0.1));
  color: var(--text-secondary, rgba(255, 255, 255, 0.7));
  font-weight: 500;
}

.positions-table td, .history-table td {
  padding: 10px;
  border-bottom: 1px solid var(--border, rgba(255, 255, 255, 0.05));
}

.positions-table tr:hover, .history-table tr:hover {
  background-color: var(--card-hover, rgba(255, 255, 255, 0.05));
}

.positions-table tr.profit, .history-table tr.profit {
  background-color: rgba(0, 200, 83, 0.1);
}

.positions-table tr.loss, .history-table tr.loss {
  background-color: rgba(255, 82, 82, 0.1);
}

.positions-table .positive, .history-table .positive {
  color: var(--success, #10b981);
}

.positions-table .negative, .history-table .negative {
  color: var(--error, #ef4444);
}

.positions-table .buy, .history-table .buy {
  color: var(--success, #10b981);
  font-weight: 600;
}

.positions-table .sell, .history-table .sell {
  color: var(--error, #ef4444);
  font-weight: 600;
}

/* Logs section styles */
.logs-container-wrapper {
  width: 100%;
  background-color: var(--card, #1e2a38);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  padding: 20px;
}

.logs-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.logs-header h3 {
  margin: 0;
  color: var(--text, #fff);
  font-size: 20px;
  font-weight: 600;
}

.logs-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logs-line-selector {
  padding: 6px 10px;
  border-radius: 4px;
  border: 1px solid var(--border, rgba(255, 255, 255, 0.1));
  background-color: var(--input, #172331);
  color: var(--text, #e3e8ef);
  font-size: 14px;
  cursor: pointer;
}

.logs-container {
  max-height: 600px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 13px;
  background-color: var(--input, #172331);
  border-radius: 4px;
  padding: 10px;
  color: var(--text, #e3e8ef);
}

/* Additional logs entry styling */
.log-entry {
  padding: 8px 10px;
  border-bottom: 1px solid var(--border, rgba(255, 255, 255, 0.05));
  white-space: pre-wrap;
  word-break: break-word;
  display: flex;
  flex-wrap: wrap;
}

.log-entry:hover {
  background-color: var(--card-hover, rgba(255, 255, 255, 0.05));
}

.log-entry.INFO {
  color: var(--text, #e3e8ef);
}

.log-entry.DEBUG {
  color: var(--text-secondary, #8b949e);
}

.log-entry.WARNING, .log-entry.WARN {
  color: var(--warning, #ffb74d);
  background-color: rgba(255, 183, 77, 0.1);
}

.log-entry.ERROR {
  color: var(--error, #ff5252);
  background-color: rgba(255, 82, 82, 0.1);
}

.log-entry.CRITICAL {
  color: var(--error, #ff1744);
  background-color: rgba(255, 23, 68, 0.2);
  font-weight: bold;
}

.log-time {
  margin-right: 12px;
  color: var(--text-secondary, #8b949e);
  font-size: 12px;
  min-width: 150px;
}

.log-level {
  font-weight: 600;
  margin-right: 12px;
  min-width: 60px;
}

.log-message {
  flex: 1;
}

/* Loading and error states */
.loading-indicator, .error-message, .no-data {
  text-align: center;
  padding: 20px;
  color: var(--text-secondary, #8b949e);
}

.error-message {
  color: var(--error, #ff5252);
  background-color: rgba(255, 82, 82, 0.1);
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.no-data {
  font-style: italic;
  padding: 15px;
  color: var(--text-secondary, #8b949e);
}

/* Bot status indicator */
.bot-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--text-secondary, #8b949e);
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--error, #ff5252);
}

.bot-status.running .status-indicator {
  background-color: var(--success, #00c853);
}

/* Config panel styling */
.autonomous-info-panel {
  background-color: var(--card, #1e2a38);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.autonomous-info-panel h3 {
  color: var(--text, #fff);
  margin-top: 0;
  margin-bottom: 10px;
}

.autonomous-info-panel p {
  color: var(--text-secondary, #adbac7);
  margin-bottom: 0;
}

.page-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.config-panel, .active-strategies-panel {
  background-color: var(--card, #1e2a38);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.config-panel h3, .active-strategies-panel h3 {
  color: var(--text, #fff);
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 20px;
  border-bottom: 1px solid var(--border, rgba(255, 255, 255, 0.1));
  padding-bottom: 10px;
}

/* Enhanced numeric input styles for better visibility */
.autonomous-page input[type="number"] {
  background-color: var(--input, #172331);
  border: 2px solid var(--border, rgba(255, 255, 255, 0.15));
  color: var(--text, #e3e8ef);
  border-radius: 6px;
  padding: 10px 12px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.autonomous-page input[type="number"]:focus {
  outline: none;
  border-color: var(--primary, #3772ff);
  box-shadow: 0 0 0 3px rgba(55, 114, 255, 0.2), inset 0 1px 3px rgba(0, 0, 0, 0.1);
  background-color: var(--input-focus, #1a2332);
}

.autonomous-page input[type="number"]:hover:not(:focus) {
  border-color: var(--border-hover, rgba(255, 255, 255, 0.25));
  background-color: var(--input-hover, #1a2332);
}

.autonomous-page input[type="number"]::placeholder {
  color: var(--text-secondary, rgba(255, 255, 255, 0.5));
}

/* Light theme overrides for numeric inputs */
@media (prefers-color-scheme: light) {
  .autonomous-page input[type="number"] {
    background-color: #ffffff;
    border-color: #d1d5db;
    color: #1f2937;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .autonomous-page input[type="number"]:focus {
    border-color: var(--primary, #3772ff);
    box-shadow: 0 0 0 3px rgba(55, 114, 255, 0.1), inset 0 1px 2px rgba(0, 0, 0, 0.05);
    background-color: #ffffff;
  }

  .autonomous-page input[type="number"]:hover:not(:focus) {
    border-color: #9ca3af;
    background-color: #f9fafb;
  }

  .autonomous-page input[type="number"]::placeholder {
    color: #6b7280;
  }
}

/* Alternative light theme class-based approach */
.autonomous-page.light-theme input[type="number"] {
  background-color: #ffffff;
  border-color: #d1d5db;
  color: #1f2937;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.autonomous-page.light-theme input[type="number"]:focus {
  border-color: var(--primary, #3772ff);
  box-shadow: 0 0 0 3px rgba(55, 114, 255, 0.1), inset 0 1px 2px rgba(0, 0, 0, 0.05);
  background-color: #ffffff;
}

.autonomous-page.light-theme input[type="number"]:hover:not(:focus) {
  border-color: #9ca3af;
  background-color: #f9fafb;
}

.autonomous-page.light-theme input[type="number"]::placeholder {
  color: #6b7280;
}

/* Additional styling for slider inputs */
.autonomous-page .confidence-slider {
  appearance: none;
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: var(--border, rgba(255, 255, 255, 0.2));
  outline: none;
  border: 1px solid var(--border, rgba(255, 255, 255, 0.15));
}

.autonomous-page .confidence-slider::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary, #3772ff);
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.autonomous-page .confidence-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary, #3772ff);
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Slider container styling */
.slider-container {
  margin: 8px 0;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: var(--text-secondary, rgba(255, 255, 255, 0.7));
}

/* Form section styling improvements */
.risk-management-section {
  background-color: var(--card-secondary, rgba(255, 255, 255, 0.02));
  border: 1px solid var(--border, rgba(255, 255, 255, 0.1));
  border-radius: 8px;
  padding: 20px;
  margin-top: 24px;
}

.risk-management-section h4 {
  color: var(--text, #ffffff);
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  border-bottom: 1px solid var(--border, rgba(255, 255, 255, 0.1));
  padding-bottom: 8px;
}

.form-help {
  display: block;
  margin-top: 4px;
  font-size: 12px;
  color: var(--text-secondary, rgba(255, 255, 255, 0.6));
  font-style: italic;
}

/* Equivalent value display for risk switcher */
.equivalent-value {
  margin-top: 0.75rem;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, rgba(55, 114, 255, 0.1) 0%, rgba(55, 114, 255, 0.05) 100%);
  border-radius: 8px;
  border: 1px solid rgba(55, 114, 255, 0.2);
  text-align: center;
}

.equivalent-label {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary, #3772ff);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.equivalent-label::before {
  content: "💰";
  font-size: 1.1rem;
}

.balance-info {
  margin-top: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-tertiary, #6b7280);
  font-weight: 500;
  padding: 0.5rem;
  background: var(--bg-tertiary, rgba(255, 255, 255, 0.05));
  border-radius: 6px;
  border-left: 3px solid var(--success, #10b981);
}

/* Timeframe recommendation styles */
.timeframe-recommendation {
  margin-top: 1rem;
}

.recommendation-box {
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid;
  margin-top: 0.5rem;
}

.recommendation-box.optimal {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
  border-color: rgba(16, 185, 129, 0.3);
}

.recommendation-box.good {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
  border-color: rgba(59, 130, 246, 0.3);
}

.recommendation-box.avoid {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%);
  border-color: rgba(239, 68, 68, 0.3);
}

.recommendation-box.neutral {
  background: linear-gradient(135deg, rgba(156, 163, 175, 0.1) 0%, rgba(156, 163, 175, 0.05) 100%);
  border-color: rgba(156, 163, 175, 0.3);
}

.recommendation-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.rec-icon {
  font-size: 1.2rem;
}

.rec-title {
  font-weight: 600;
  font-size: 0.95rem;
}

.recommendation-box.optimal .rec-title {
  color: var(--success, #10b981);
}

.recommendation-box.good .rec-title {
  color: var(--primary, #3b82f6);
}

.recommendation-box.avoid .rec-title {
  color: var(--danger, #ef4444);
}

.recommendation-box.neutral .rec-title {
  color: var(--text-secondary, #9ca3af);
}

.recommendation-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.recommendation-details {
  font-size: 0.85rem;
  color: var(--text-tertiary);
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 4px;
  border-left: 3px solid var(--primary, #3b82f6);
}

.recommendation-details strong {
  color: var(--text-primary);
}

/* Timeframe select styling based on recommendation */
.timeframe-optimal {
  border-color: var(--success, #10b981) !important;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2) !important;
}

.timeframe-good {
  border-color: var(--primary, #3b82f6) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

.timeframe-avoid {
  border-color: var(--danger, #ef4444) !important;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2) !important;
}

/* Multi-strategy info section */
.multi-strategy-info {
  margin-top: 1.5rem;
  padding: 1.25rem;
  background: linear-gradient(135deg, rgba(55, 114, 255, 0.1) 0%, rgba(55, 114, 255, 0.05) 100%);
  border-radius: 10px;
  border: 1px solid rgba(55, 114, 255, 0.2);
  border-left: 4px solid var(--primary, #3772ff);
}

.multi-strategy-info h4 {
  margin: 0 0 1rem 0;
  color: var(--primary, #3772ff);
  font-size: 1.1rem;
  font-weight: 600;
}

.multi-strategy-info p {
  margin: 0 0 1rem 0;
  line-height: 1.5;
  color: var(--text-primary);
}

.multi-strategy-info ul {
  margin: 1rem 0;
  padding-left: 1.5rem;
  color: var(--text-secondary);
}

.multi-strategy-info li {
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.multi-strategy-info li strong {
  color: var(--text-primary);
}

.multi-strategy-info p:last-child {
  margin-bottom: 0;
  padding: 0.75rem;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 6px;
  border-left: 3px solid var(--danger, #ef4444);
  color: var(--text-primary);
}

/* Responsive design */
@media (max-width: 768px) {
  .page-content {
    grid-template-columns: 1fr;
  }

  .monitoring-grid {
    grid-template-columns: 1fr;
  }

  .metrics-grid {
    grid-template-columns: 1fr 1fr;
  }

  .health-status {
    grid-template-columns: 1fr;
  }
}
