from typing import Dict, Any, List
import numpy as np
import pandas as pd
from scipy.signal import find_peaks
from scipy.optimize import curve_fit

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class RoundingPatternIndicator(BaseIndicator):
    """Rounding (Saucer) pattern indicator for identifying gradual reversals and consolidations."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize Rounding pattern indicator.
        
        Args:
            params: Dictionary containing parameters:
                - min_points: Minimum number of points for pattern (default: 20)
                - min_width: Minimum width of pattern formation (default: 30)
                - curve_fit_tolerance: Maximum deviation from ideal curve (default: 0.03 or 3%)
                - symmetry_tolerance: Maximum allowed asymmetry ratio (default: 0.3 or 30%)
                - depth_factor: Minimum required depth relative to width (default: 0.1 or 10%)
        """
        super().__init__(params)
        self.min_points = self.params.get('min_points', 20)
        self.min_width = self.params.get('min_width', 30)
        self.curve_fit_tolerance = self.params.get('curve_fit_tolerance', 0.03)
        self.symmetry_tolerance = self.params.get('symmetry_tolerance', 0.3)
        self.depth_factor = self.params.get('depth_factor', 0.1)

    def _parabola(self, x: np.ndarray, a: float, b: float, c: float) -> np.ndarray:
        """Parabolic function for curve fitting."""
        return a * (x - b)**2 + c

    def _is_rounding(self, prices: np.ndarray, start_idx: int, end_idx: int) -> tuple:
        """Identify Rounding patterns and their type."""
        if end_idx - start_idx < self.min_width:
            return False, 0, 0, 0
            
        # Prepare data for curve fitting
        x = np.arange(end_idx - start_idx + 1)
        y = prices[start_idx:end_idx+1]
        
        try:
            # Fit parabolic curve
            popt, _ = curve_fit(self._parabola, x, y)
            a, b, c = popt
            
            # Calculate fitted values
            y_fit = self._parabola(x, a, b, c)
            
            # Check curve fit quality
            residuals = y - y_fit
            fit_error = np.mean(np.abs(residuals / y))
            if fit_error > self.curve_fit_tolerance:
                return False, 0, 0, 0
            
            # Check pattern depth
            pattern_width = end_idx - start_idx
            pattern_depth = abs(max(y) - min(y))
            if pattern_depth < pattern_width * self.depth_factor:
                return False, 0, 0, 0
            
            # Check symmetry
            mid_idx = len(x) // 2
            left_half = y[:mid_idx]
            right_half = y[mid_idx:]
            left_curve = np.mean(np.abs(left_half - y_fit[:mid_idx]))
            right_curve = np.mean(np.abs(right_half - y_fit[mid_idx:]))
            
            if abs(left_curve - right_curve) / max(left_curve, right_curve) > self.symmetry_tolerance:
                return False, 0, 0, 0
            
            # Determine pattern type based on curve direction
            if a > 0:  # Upward facing parabola
                pattern_type = -1  # Rounding Bottom (Saucer)
            else:  # Downward facing parabola
                pattern_type = 1  # Rounding Top
                
            return True, start_idx, end_idx, pattern_type
            
        except RuntimeError:
            return False, 0, 0, 0

    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """
        Calculate Rounding pattern values.
        
        Args:
            market_data: Market data containing OHLCV values
            
        Returns:
            Dictionary containing:
                - is_pattern: Boolean array indicating pattern presence
                - pattern_type: Integer array indicating pattern type (1: Rounding Top, -1: Rounding Bottom)
                - pattern_id: Integer array for pattern instance grouping
                - strength: Float array indicating pattern strength
                - curvature: Float array indicating curve smoothness
                - consolidation_score: Float array indicating quality of consolidation
                - trend: Integer array indicating trend context
                - reliability: Float array indicating pattern reliability
        """
        df = market_data.to_dataframe()
        
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        
        is_pattern = np.zeros_like(close)
        pattern_type = np.zeros_like(close)  # 1=Rounding Top, -1=Rounding Bottom
        pattern_id = np.zeros_like(close)
        
        # Find potential pattern boundaries using peaks and troughs
        peaks, _ = find_peaks(high, distance=self.min_width//4)
        troughs, _ = find_peaks(-low, distance=self.min_width//4)
        
        # Scan for rounding patterns
        for i in range(len(close)):
            # Look for patterns starting at significant points
            if i in peaks or i in troughs:
                for j in range(i + self.min_width, min(len(close), i + self.min_width*2)):
                    is_valid, start, end, r_type = self._is_rounding(close, i, j)
                    if is_valid:
                        is_pattern[start:end+1] = 1
                        pattern_type[start:end+1] = r_type
                        pattern_id[start:end+1] = i
        
        # Calculate pattern characteristics
        strength = np.zeros_like(close)
        curvature = np.zeros_like(close)
        consolidation_score = np.zeros_like(close)
        
        for i in range(len(close)):
            if is_pattern[i]:
                # Find pattern boundaries
                pattern_start = i
                pattern_end = i
                while pattern_end + 1 < len(close) and is_pattern[pattern_end + 1]:
                    pattern_end += 1
                
                # Calculate pattern metrics
                window = slice(pattern_start, pattern_end + 1)
                pattern_prices = close[window]
                
                # Pattern strength based on depth and consistency
                pattern_depth = abs(max(pattern_prices) - min(pattern_prices))
                strength[i] = pattern_depth / close[i]
                
                # Curvature score using price changes
                price_changes = np.diff(pattern_prices)
                curvature[i] = np.std(price_changes) / np.mean(np.abs(price_changes) + 1e-6)
                
                # Consolidation score based on:
                # 1. Consistent curve formation
                # 2. Decreasing volatility
                # 3. Volume pattern (if available)
                volatility = np.std(pattern_prices) / np.mean(pattern_prices)
                curve_consistency = 1 - curvature[i]
                consolidation_score[i] = (curve_consistency + (1 - volatility)) / 2
        
        # Calculate trend context using 20-period SMA
        sma20 = df['close'].rolling(window=20).mean()
        trend = np.where(df['close'] > sma20, 1, -1)
        
        # Calculate reliability based on future price movement
        reliability = np.zeros_like(close)
        future_window = 20
        
        for i in range(len(close) - future_window):
            if is_pattern[i]:
                future_returns = (df['close'].iloc[i+1:i+future_window+1].values - 
                                df['close'].iloc[i]) / df['close'].iloc[i]
                
                if pattern_type[i] == 1:  # Rounding Top
                    max_return = np.max(future_returns)
                    min_return = np.min(future_returns)
                    reliability[i] = max(0, min(1, -min_return))
                else:  # Rounding Bottom
                    max_return = np.max(future_returns)
                    min_return = np.min(future_returns)
                    reliability[i] = max(0, min(1, max_return))
        
        return {
            'is_pattern': is_pattern,
            'pattern_type': pattern_type,
            'pattern_id': pattern_id,
            'strength': strength,
            'curvature': curvature,
            'consolidation_score': consolidation_score,
            'trend': trend,
            'reliability': reliability
        }
    
    def validate_params(self) -> None:
        """
        Validate indicator parameters.
        
        Raises:
            ValueError: If parameters are invalid
        """
        if self.min_points < 10:
            raise ValueError("min_points must be at least 10")
        if self.min_width < 20:
            raise ValueError("min_width must be at least 20 periods")
        if not 0 < self.curve_fit_tolerance < 1:
            raise ValueError("curve_fit_tolerance must be between 0 and 1")
        if not 0 < self.symmetry_tolerance < 1:
            raise ValueError("symmetry_tolerance must be between 0 and 1")
        if not 0 < self.depth_factor < 1:
            raise ValueError("depth_factor must be between 0 and 1") 