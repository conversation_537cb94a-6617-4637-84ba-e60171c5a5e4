/**
 * GarudaAlgo MT5 Trader Theme
 * 
 * This module provides theming functionality for the application.
 */

// Theme Configuration
const themes = {
    light: {
        '--primary': '#2563eb',
        '--primary-hover': '#1d4ed8',
        '--secondary': '#64748b',
        '--secondary-hover': '#475569',
        '--success': '#059669',
        '--error': '#dc2626',
        '--warning': '#d97706',
        '--info': '#0284c7',
        '--background': '#f8fafc',
        '--background-secondary': '#f1f5f9',
        '--text': '#1e293b',
        '--text-secondary': '#64748b',
        '--border': '#cbd5e1',
        '--card': '#ffffff',
        '--card-hover': '#f1f5f9',
        '--input': '#ffffff',
        '--modal-backdrop': 'rgba(15, 23, 42, 0.5)',
        '--shadow': 'rgba(0, 0, 0, 0.1)'
    },
    dark: {
        '--primary': '#3b82f6',
        '--primary-hover': '#2563eb',
        '--secondary': '#94a3b8',
        '--secondary-hover': '#cbd5e1',
        '--success': '#10b981',
        '--error': '#ef4444',
        '--warning': '#f59e0b',
        '--info': '#0ea5e9',
        '--background': '#0f172a',
        '--background-secondary': '#1e293b',
        '--text': '#f1f5f9',
        '--text-secondary': '#94a3b8',
        '--border': '#334155',
        '--card': '#1e293b',
        '--card-hover': '#334155',
        '--input': '#0f172a',
        '--modal-backdrop': 'rgba(0, 0, 0, 0.7)',
        '--shadow': 'rgba(0, 0, 0, 0.3)'
    }
};

// Current theme
let currentTheme = 'light';

// Elements that should be updated when theme changes
const themeableElements = [
    { property: 'background-color', selector: 'body', variable: '--background' },
    { property: 'color', selector: 'body', variable: '--text' },
    { property: 'border-color', selector: '.border', variable: '--border' },
    { property: 'background-color', selector: '.card', variable: '--card' },
    { property: 'background-color', selector: 'input, select, textarea', variable: '--input' },
    { property: 'border-color', selector: 'input, select, textarea', variable: '--border' },
    { property: 'color', selector: '.text-primary', variable: '--primary' },
    { property: 'color', selector: '.text-secondary', variable: '--secondary' },
    { property: 'background-color', selector: '.notification', variable: '--card' },
    { property: 'box-shadow', selector: '.card, .notification', variable: '0 4px 12px var(--shadow)' }
];

/**
 * Get the current theme name
 * @returns {string} The current theme name
 */
function getCurrentTheme() {
    return currentTheme;
}

/**
 * Apply a theme by setting CSS variables
 * @param {string} themeName - Name of the theme to apply ('light' or 'dark')
 */
function applyTheme(themeName) {
    if (!themes[themeName]) {
        console.error(`Theme "${themeName}" not found`);
        return;
    }
    
    // Set current theme
    currentTheme = themeName;
    
    // Apply CSS variables
    const theme = themes[themeName];
    Object.keys(theme).forEach(variable => {
        document.documentElement.style.setProperty(variable, theme[variable]);
    });
    
    // Update themeable elements
    themeableElements.forEach(element => {
        const elements = document.querySelectorAll(element.selector);
        elements.forEach(el => {
            if (element.variable.startsWith('0 ')) {
                // This is a direct value, not a CSS variable
                el.style[element.property] = element.variable;
            } else {
                el.style[element.property] = `var(${element.variable})`;
            }
        });
    });
    
    // Store theme preference
    localStorage.setItem('preferredTheme', themeName);
    
    // Update body class for additional styling
    document.body.classList.remove('light-mode', 'dark-mode');
    document.body.classList.add(`${themeName}-mode`);
    
    // Update theme toggle button if it exists
    const themeToggleButton = document.getElementById('theme-toggle');
    if (themeToggleButton) {
        themeToggleButton.textContent = themeName === 'dark' ? 'Light Mode' : 'Dark Mode';
    }
    
    // Dispatch event for other scripts to react to theme change
    document.dispatchEvent(new CustomEvent('themechange', { detail: { theme: themeName } }));
}

/**
 * Toggle between light and dark themes
 */
function toggleTheme() {
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    applyTheme(newTheme);
    return newTheme;
}

/**
 * Initialize theme based on user preference or system preference
 */
function initTheme() {
    // Check for saved preference
    const savedTheme = localStorage.getItem('preferredTheme');
    
    if (savedTheme) {
        // Use saved preference
        applyTheme(savedTheme);
    } else {
        // Check for system preference
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        applyTheme(prefersDark ? 'dark' : 'light');
    }
    
    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
        if (!localStorage.getItem('preferredTheme')) {
            // Only auto-switch if user hasn't explicitly set a preference
            applyTheme(e.matches ? 'dark' : 'light');
        }
    });
}

// Initialize theme based on user preference
document.addEventListener('DOMContentLoaded', initTheme);

// Make theme functions available to other scripts
window.themeModule = {
    applyTheme,
    toggleTheme,
    getCurrentTheme
}; 