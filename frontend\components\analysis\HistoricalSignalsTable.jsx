import React from 'react';

/**
 * HistoricalSignalsTable - Displays a table of recent buy/sell/neutral signals for the selected symbol and timeframe.
 * @param {Object[]} signals - Array of signal objects with { time, signal, price, outcome }.
 */
function HistoricalSignalsTable({ signals = [] }) {
  // Function to determine signal color based on type
  const getSignalColor = (signal) => {
    if (!signal) return 'var(--text-secondary)';
    
    const signalText = signal.toLowerCase();
    if (signalText.includes('buy') || signalText.includes('long')) return 'var(--success)';
    if (signalText.includes('sell') || signalText.includes('short')) return 'var(--error)';
    return 'var(--warning)'; // Neutral/hold signals
  };
  
  // Function to determine outcome color based on result
  const getOutcomeColor = (outcome) => {
    if (!outcome) return 'var(--text-secondary)';
    
    const outcomeText = outcome.toLowerCase();
    if (outcomeText.includes('profit') || outcomeText.includes('win') || outcomeText.includes('success')) return 'var(--success)';
    if (outcomeText.includes('loss') || outcomeText.includes('fail')) return 'var(--error)';
    return 'var(--warning)'; // Neutral outcomes
  };
  
  // Function to format date/time consistently
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '-';
    
    try {
      // Handle both string timestamps and Date objects
      const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
      return date.toLocaleString(undefined, { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    } catch (e) {
      // If parsing fails, return the original string
      return timestamp;
    }
  };

  return (
    <div className="analysis-card historical-signals-table" style={{ padding: '15px', borderRadius: '8px', background: 'var(--card)', border: '1px solid var(--border)' }}>
      <h3 style={{ color: 'var(--text)' }}>Historical Signals</h3>
      <div style={{ width: '100%', overflow: 'auto' }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ borderBottom: '1px solid var(--border)' }}>
              <th style={{ padding: '8px', textAlign: 'left', color: 'var(--text)' }}>Time</th>
              <th style={{ padding: '8px', textAlign: 'left', color: 'var(--text)' }}>Signal</th>
              <th style={{ padding: '8px', textAlign: 'right', color: 'var(--text)' }}>Price</th>
              <th style={{ padding: '8px', textAlign: 'right', color: 'var(--text)' }}>Outcome</th>
            </tr>
          </thead>
          <tbody>
            {!signals || signals.length === 0 ? (
              <tr>
                <td colSpan={4} style={{ padding: '12px 8px', textAlign: 'center', color: 'var(--text-secondary)' }}>
                  No historical signals found.
                </td>
              </tr>
            ) : (
              signals.map((signal, idx) => {
                // Handle different property names between frontend and API formats
                const time = formatTimestamp(signal.timestamp || signal.time);
                const signalType = signal.signal || signal.type || '-';
                const price = signal.price || signal.entry_price || '-';
                const outcome = signal.outcome || signal.result || '-';
                
                return (
                  <tr key={idx} style={{ borderBottom: '1px solid var(--border-light, rgba(42, 47, 69, 0.5))' }}>
                    <td style={{ padding: '10px 8px', fontSize: '0.9em', color: 'var(--text)' }}>{time}</td>
                    <td style={{ padding: '10px 8px', color: getSignalColor(signalType), fontWeight: 'bold' }}>{signalType}</td>
                    <td style={{ padding: '10px 8px', textAlign: 'right', fontFamily: 'monospace', color: 'var(--text)' }}>{price}</td>
                    <td style={{ padding: '10px 8px', textAlign: 'right', color: getOutcomeColor(outcome) }}>{outcome}</td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default HistoricalSignalsTable;
