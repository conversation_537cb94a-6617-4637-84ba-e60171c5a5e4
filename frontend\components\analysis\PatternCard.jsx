import React, { useState } from 'react';

/**
 * PatternCard - Shows detected chart patterns for the selected symbol/timeframe.
 * @param {Object[]} patterns - Array of pattern objects { name, type, strength, completionTime }
 * @param {Object} analysisData - Full analysis data object to provide context for patterns
 */
function PatternCard({ patterns = [], analysisData = {} }) {
  const [expandedPatternIndex, setExpandedPatternIndex] = useState(null);
  
  // Helper to determine pattern icon based on pattern type
  const getPatternIcon = (patternType) => {
    if (!patternType) return '📊'; // Default
    
    const type = patternType.toLowerCase();
    
    // Reversal patterns
    if (type.includes('double top')) return '🔻';
    if (type.includes('double bottom')) return '🔺';
    if (type.includes('head and shoulder')) return '🔻';
    if (type.includes('inverse head')) return '🔺';
    
    // Continuation patterns
    if (type.includes('triangle')) return '📐';
    if (type.includes('wedge')) return '⊿';
    if (type.includes('flag')) return '⚑';
    if (type.includes('pennant')) return '🚩';
    
    // Candlestick patterns
    if (type.includes('doji')) return '✳️';
    if (type.includes('engulfing')) return '⬛';
    if (type.includes('hammer')) return '🔨';
    if (type.includes('star')) return '⭐';
    
    // General patterns by direction
    if (type.includes('bullish')) return '🔔';
    if (type.includes('bearish')) return '🔕';

    return '📊'; // Default chart icon
  };
  
  // Helper to get pattern significance color
  const getPatternColor = (strength) => {
    if (!strength || typeof strength !== 'number') return 'var(--text-secondary)';
    if (strength > 80) return 'var(--success)'; // Strong
    if (strength > 50) return 'var(--warning)'; // Medium
    return 'var(--text-secondary)'; // Weak
  };
  
  // Helper to get pattern direction
  const getPatternDirection = (name) => {
    if (!name) return '';
    
    const lowercaseName = name.toLowerCase();
    if (lowercaseName.includes('bullish') || 
        lowercaseName.includes('double bottom') || 
        lowercaseName.includes('inverse head') || 
        lowercaseName.includes('morning star') ||
        lowercaseName.includes('hammer')) {
      return 'bullish';
    }
    
    if (lowercaseName.includes('bearish') || 
        lowercaseName.includes('double top') || 
        lowercaseName.includes('head and shoulder') || 
        lowercaseName.includes('evening star') ||
        lowercaseName.includes('shooting star')) {
      return 'bearish';
    }
    
    return 'neutral';
  };

  // Helper to generate signal direction indicator
  const getSignalIndicator = (direction) => {
    const symbol = direction === 'bullish' ? '▲' : direction === 'bearish' ? '▼' : '•';
    
    return (
      <span className={direction} style={{ marginLeft: '8px', fontWeight: 'bold' }}>
        {symbol}
      </span>
    );
  };

  // Generate mini visualization of pattern
  const PatternVisualization = ({ pattern }) => {
    const name = pattern.name || pattern.pattern_type || '';
    const direction = getPatternDirection(name);
    
    // SVG path for different pattern types
    const getPatternPath = () => {
      const width = 80;
      const height = 30;
      const padding = 5;
      
      // Available drawing area
      const drawWidth = width - (padding * 2);
      const drawHeight = height - (padding * 2);
      
      // Starting point
      const startX = padding;
      const startY = direction === 'bullish' ? height - padding : padding;
      
      // Based on pattern type, return different SVG paths
      const nameLower = name.toLowerCase();
      
      if (nameLower.includes('double top')) {
        return `M${startX},${startY + drawHeight/2} 
                Q${startX + drawWidth*0.25},${startY - drawHeight/3} ${startX + drawWidth*0.4},${startY} 
                Q${startX + drawWidth*0.5},${startY + drawHeight/3} ${startX + drawWidth*0.6},${startY} 
                Q${startX + drawWidth*0.75},${startY - drawHeight/3} ${startX + drawWidth},${startY + drawHeight/2}`;
      }
      
      if (nameLower.includes('double bottom')) {
        return `M${startX},${startY - drawHeight/2} 
                Q${startX + drawWidth*0.25},${startY + drawHeight/3} ${startX + drawWidth*0.4},${startY} 
                Q${startX + drawWidth*0.5},${startY - drawHeight/3} ${startX + drawWidth*0.6},${startY} 
                Q${startX + drawWidth*0.75},${startY + drawHeight/3} ${startX + drawWidth},${startY - drawHeight/2}`;
      }

      if (nameLower.includes('triangle')) {
        return `M${startX},${startY} L${startX + drawWidth},${direction === 'bullish' ? startY - drawHeight : startY + drawHeight} 
                M${startX},${startY} L${startX + drawWidth},${startY}`;
      }
      
      // Default pattern: trend line
      return `M${startX},${direction === 'bullish' ? startY : startY - drawHeight} 
              L${startX + drawWidth*0.5},${direction === 'bullish' ? startY - drawHeight*0.5 : startY + drawHeight*0.5} 
              L${startX + drawWidth},${direction === 'bullish' ? startY - drawHeight : startY + drawHeight}`;
    };
    
    return (
      <div className="visualization">
        <svg width="80" height="30" viewBox="0 0 80 30">
          <path
            d={getPatternPath()}
            fill="none"
            stroke={direction === 'bullish' ? 'var(--success)' : 'var(--error)'}
            strokeWidth="2"
            strokeDasharray={name.toLowerCase().includes('support') || name.toLowerCase().includes('resistance') ? "4" : "0"}
          />
        </svg>
      </div>
    );
  };

  // Create empty state component
  const EmptyState = () => (
    <div style={{ 
      textAlign: 'center', 
      padding: '25px 0', 
      color: 'var(--text-secondary)'
    }}>
      <div style={{ fontSize: '1.2em', marginBottom: '10px' }}>
        No patterns detected
      </div>
      <div style={{ fontSize: '0.9em' }}>
        Chart patterns will appear here when<br/>
        technical formations like triangles,<br/>
        head and shoulders, or candlestick<br/>
        patterns are detected.
      </div>
    </div>
  );

  return (
    <div className="analysis-card pattern-card">
      <h3>Detected Patterns</h3>
      {!patterns || patterns.length === 0 ? (
        <EmptyState />
      ) : (
        <ul className="pattern-list">
          {patterns.map((pattern, idx) => {
            // Handle both backend API object format and frontend format
            const name = pattern.name || pattern.pattern_type || 'Unknown Pattern';
            const confidence = pattern.confidence || pattern.strength || null;
            const timestamp = pattern.detectedAt || pattern.completion_time || '';
            const direction = getPatternDirection(name);
            const isExpanded = expandedPatternIndex === idx;
            
            return (
              <li 
                key={idx} 
                className={`pattern-item ${isExpanded ? 'expanded' : ''}`}
                onClick={() => setExpandedPatternIndex(isExpanded ? null : idx)}
              >
                <div className="pattern-header">
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span className="pattern-icon">{getPatternIcon(name)}</span>
                    <div>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <strong style={{ color: getPatternColor(confidence) }}>{name}</strong>
                        {getSignalIndicator(direction)}
                      </div>
                      {confidence != null && (
                        <div style={{ fontSize: '0.9em', color: 'var(--text-secondary)' }}>
                          {Math.round(confidence)}% confidence
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div style={{ fontSize: '1.2em', color: 'var(--text-secondary)' }}>
                    {isExpanded ? '▼' : '▶'}
                  </div>
                </div>
                
                {timestamp && (
                  <div style={{ marginTop: '8px', fontSize: '0.8em', color: 'var(--text-secondary)' }}>
                    Detected: {new Date(timestamp).toLocaleString()}
                  </div>
                )}
                
                {isExpanded && (
                  <div className="pattern-details">
                    <PatternVisualization pattern={pattern} />
                    
                    <div className="detail-row">
                      <div className="detail-item">
                        <div className="detail-label">Signal</div>
                        <strong className={`detail-value ${direction}`}>
                          {direction.toUpperCase() || 'NEUTRAL'}
                        </strong>
                      </div>
                      
                      <div className="detail-item">
                        <div className="detail-label">Reliability</div>
                        <strong className="detail-value" style={{ color: getPatternColor(confidence) }}>
                          {confidence > 80 ? 'HIGH' : confidence > 50 ? 'MEDIUM' : 'LOW'}
                        </strong>
                      </div>
                      
                      <div className="detail-item">
                        <div className="detail-label">Target</div>
                        <strong className="detail-value">
                          {pattern.target_price || pattern.price_target || '—'}
                        </strong>
                      </div>
                    </div>
                  </div>
                )}
              </li>
            );
          })}
        </ul>
      )}
    </div>
  );
}

export default PatternCard;
