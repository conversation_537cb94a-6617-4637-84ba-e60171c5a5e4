from typing import Dict, Any, List
import numpy as np
import pandas as pd

from backend.technical.base_indicator import BaseIndicator

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

class MACDIndicator(BaseIndicator):
    """Moving Average Convergence Divergence indicator."""
    
    def __init__(self, fast_period: int = 12, slow_period: int = 26,
                 signal_period: int = 9, source: str = 'close'):
        """
        Initialize MACD indicator.
        
        Args:
            fast_period: The period for fast EMA
            slow_period: The period for slow EMA
            signal_period: The period for signal line EMA
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4')
        """
        super().__init__({
            'fast_period': fast_period,
            'slow_period': slow_period,
            'signal_period': signal_period,
            'source': source
        })
        self.name = 'MACD' # Store name if needed

    def calculate(self, data: pd.DataFrame) -> Dict[str, np.ndarray]: # Changed type hint
        """Calculate MACD values."""
        # df = data.to_dataframe() # Removed - data is now DataFrame
        df = data
        # Check sufficient data length based on slow period
        if df.empty or self.params['slow_period'] > len(df):
            print(f"MACDIndicator Error: Insufficient data for slow period {self.params['slow_period']}.")
            nan_array = np.full(len(df), np.nan)
            zero_array = np.zeros(len(df), dtype=int)
            return { # Return expected keys with NaNs/zeros
                'macd': nan_array, 'signal': nan_array, 'histogram': nan_array,
                'zero_cross': zero_array, 'signal_cross': zero_array,
                'divergence': zero_array, 'source': nan_array
            }

        # Get source data
        source = self.params['source'].lower()
        if source == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source]
        
        # Calculate fast and slow EMAs
        fast_ema = source_data.ewm(
            span=self.params['fast_period'],
            adjust=False
        ).mean()
        
        slow_ema = source_data.ewm(
            span=self.params['slow_period'],
            adjust=False
        ).mean()
        
        # Calculate MACD line
        macd_line = fast_ema - slow_ema
        
        # Calculate signal line
        signal_line = macd_line.ewm(
            span=self.params['signal_period'],
            adjust=False
        ).mean()
        
        # Calculate histogram
        histogram = macd_line - signal_line
        
        # Calculate zero-line crossovers
        zero_cross = np.where(
            (macd_line > 0) & (macd_line.shift(1) <= 0), 1,
            np.where((macd_line < 0) & (macd_line.shift(1) >= 0), -1, 0)
        )
        
        # Calculate signal line crossovers
        signal_cross = np.where(
            (macd_line > signal_line) & (macd_line.shift(1) <= signal_line.shift(1)), 1,
            np.where((macd_line < signal_line) & (macd_line.shift(1) >= signal_line.shift(1)), -1, 0)
        )
        
        # Calculate divergence (simplified, handle NaNs)
        div_lookback = 5
        divergence = np.zeros(len(df), dtype=int)
        if len(df) > div_lookback:
            price_high = source_data.rolling(window=div_lookback).max()
            price_low = source_data.rolling(window=div_lookback).min()
            macd_high = macd_line.rolling(window=div_lookback).max()
            macd_low = macd_line.rolling(window=div_lookback).min()

            # Ensure indices align and values are not NaN for comparison
            valid_div_indices = price_low.notna() & price_low.shift(1).notna() & \
                                macd_low.notna() & macd_low.shift(1).notna() & \
                                price_high.notna() & price_high.shift(1).notna() & \
                                macd_high.notna() & macd_high.shift(1).notna()

            bullish_div = valid_div_indices & (price_low < price_low.shift(1)) & (macd_low > macd_low.shift(1))
            bearish_div = valid_div_indices & (price_high > price_high.shift(1)) & (macd_high < macd_high.shift(1))

            divergence[bullish_div] = 1
            divergence[bearish_div] = -1

        return {
            'macd': macd_line.values,
            'signal': signal_line.values,
            'histogram': histogram.values,
            'zero_cross': zero_cross,
            'signal_cross': signal_cross,
            'divergence': divergence,
            'source': source_data.values
        }
        return self._values
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['fast_period'] >= self.params['slow_period']:
            raise ValueError("Fast period must be less than slow period")
        if any(p < 1 for p in [self.params['fast_period'],
                              self.params['slow_period'],
                              self.params['signal_period']]):
            raise ValueError("All periods must be greater than 0")
        return True
