import React, { useState } from 'react';
import { Line } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, Filler } from 'chart.js';
import { safeToFixed } from '../../utils/numberUtils';
import { createSafeTooltipCallbacks, createSafeAxisFormatter } from '../../utils/chartUtils';
import { sanitizeChartData, createSafeDataset } from '../../utils/chartSafetyUtils';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

/**
 * AdvancedChart component for displaying price charts
 */
const AdvancedChart = ({ analysisData, selectedSymbol }) => {
  // State for chart options
  const [chartType, setChartType] = useState('line');

  // Extract current price information
  const getCurrentPrice = () => {
    // Try to get price from price_data.close array
    if (analysisData?.price_data?.close && 
        Array.isArray(analysisData.price_data.close) && 
        analysisData.price_data.close.length > 0) {
      const lastPrice = analysisData.price_data.close[analysisData.price_data.close.length - 1];
      if (lastPrice && !isNaN(parseFloat(lastPrice))) {
        return parseFloat(lastPrice);
      }
    }

    // Try alternative sources
    const sources = [
      analysisData?.current_price?.bid,
      analysisData?.current_price?.ask,
      analysisData?.current_price?.last,
      analysisData?.support_resistance?.current_price?.bid,
      analysisData?.support_resistance?.current_price?.ask,
      analysisData?.moving_averages?.ema20,
      analysisData?.bollinger_bands?.middle,
      analysisData?.price_data?.last_price,
      analysisData?.price_data?.current
    ];

    // Return first valid price
    for (const price of sources) {
      if (price && !isNaN(parseFloat(price))) {
        return parseFloat(price);
      }
    }

    // Default fallback
    return 100.0;
  };

  // Generate data if real data is not available
  let priceData = [];
  let timeLabels = [];

  // Helper function to get timeframe from URL
  const getTimeframeFromURL = () => {
    try {
      const url = new URL(window.location.href);
      return url.searchParams.get('timeframe') || 'H1';
    } catch (e) {
      console.log('Error getting timeframe from URL:', e);
      return 'H1';
    }
  };
  
  const timeframe = getTimeframeFromURL();

  // Use real data if available, otherwise generate mock data
  if (analysisData?.price_data?.close && 
      Array.isArray(analysisData.price_data.close) && 
      analysisData.price_data.close.length > 0) {
    // Sanitize all price data arrays for Chart.js
    let sanitizedCloseData = sanitizeChartData(analysisData.price_data.close);
    let sanitizedTimeData = [];
    if (Array.isArray(analysisData.price_data.time)) {
      sanitizedTimeData = sanitizeChartData(analysisData.price_data.time, null);
    }
    
    priceData = sanitizedCloseData;
    timeLabels = sanitizedTimeData;

    // Format time labels from real data
    if (timeLabels.length > 0) {
      timeLabels = timeLabels.map(t => {
        // Parse timestamp
        const timestamp = typeof t === 'string' ? 
          (t.includes('T') ? new Date(t) : new Date(t * 1000)) : 
          new Date(t * 1000);
        if (timeframe === 'W1') {
          // Format week number
          const weekNum = Math.ceil((timestamp.getDate() + new Date(timestamp.getFullYear(), timestamp.getMonth(), 0).getDay()) / 7);
          return `W${weekNum}`;
        } else if (timeframe === 'MN1') {
          // Format month name
          return timestamp.toLocaleDateString([], { month: 'short', year: 'numeric' });
        } else if (timeframe === 'D1') {
          // Daily format
          return timestamp.toLocaleDateString([], { month: 'short', day: 'numeric' });
        } else {
          // Intraday format
          return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        }
      });
    }
    // Fallback if no time data or after mapping yields empty array
    if (!timeLabels || timeLabels.length === 0) {
      // Ensure priceData is an array before mapping for fallback labels
      const baseDataForFallback = Array.isArray(priceData) ? priceData : [];
      timeLabels = baseDataForFallback.map((_, index) => `Point ${index + 1}`);
    }
  } else {
    // Generate mock data
    const currentPrice = getCurrentPrice();
    const dataPoints = 30;
    const volatility = 0.005;
    
    // Generate random walk
    const startPrice = currentPrice * 0.99;
    for (let i = 0; i < dataPoints; i++) {
      if (i === 0) {
        priceData.push(startPrice);
      } else if (i === dataPoints - 1) {
        priceData.push(currentPrice);
      } else {
        const progress = i / (dataPoints - 1);
        const trend = startPrice + (currentPrice - startPrice) * progress;
        const randomFactor = (1 - progress) * volatility * currentPrice;
        const change = (Math.random() - 0.5) * randomFactor;
        priceData.push(trend + change);
      }
    }
    
    // Generate appropriate labels
    const now = new Date();
    if (timeframe === 'W1' || timeframe === 'MN1') {
      timeLabels = Array(dataPoints).fill(0).map((_, i) => {
        const date = new Date();
        
        if (timeframe === 'W1') {
          date.setDate(date.getDate() - (dataPoints - 1 - i) * 7);
          const weekNum = Math.ceil((date.getDate() + new Date(date.getFullYear(), date.getMonth(), 0).getDay()) / 7);
          return `W${weekNum}`;
        } else {
          date.setMonth(date.getMonth() - (dataPoints - 1 - i));
          return date.toLocaleDateString([], { month: 'short', year: 'numeric' });
        }
      });
    } else {
      const intervalMinutes = timeframe === 'M1' ? 1 :
                            timeframe === 'M5' ? 5 :
                            timeframe === 'M15' ? 15 :
                            timeframe === 'M30' ? 30 :
                            timeframe === 'H1' ? 60 :
                            timeframe === 'H4' ? 240 :
                            timeframe === 'D1' ? 1440 : 60;

      timeLabels = Array(dataPoints).fill(0).map((_, i) => {
        const date = new Date(now.getTime() - (dataPoints - 1 - i) * intervalMinutes * 60000);
        
        if (timeframe === 'D1') {
          return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
        }
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      });
    }
  }

  // Configure chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      duration: 800,
      easing: 'easeOutQuart'
    },
    interaction: {
      mode: 'nearest',
      axis: 'x',
      intersect: false
    },
    elements: {
      line: {
        tension: 0.25,
        borderWidth: 2
      },
      point: {
        radius: 0,
        hoverRadius: 5
      }
    },
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        backgroundColor: 'rgba(20, 25, 39, 0.9)',
        titleColor: '#fff',
        bodyColor: '#ccc',
        borderColor: '#2a2f45',
        borderWidth: 1,
        cornerRadius: 4,
        displayColors: true,
        padding: 10,
        bodyFont: { size: 12 },
        titleFont: { size: 14, weight: 'bold' },
        callbacks: createSafeTooltipCallbacks()
      }
    },
    scales: {
      x: {
        grid: {
          display: true,
          color: 'rgba(70, 80, 100, 0.15)',
          borderColor: '#2a2f45'
        },
        ticks: {
          maxTicksLimit: timeframe === 'W1' || timeframe === 'MN1' ? 12 : 10,
          maxRotation: 0,
          color: '#999',
          font: { size: 10 },
          align: timeframe === 'W1' || timeframe === 'MN1' ? 'start' : 'center',
          padding: timeframe === 'W1' || timeframe === 'MN1' ? 5 : 0
        }
      },
      y: {
        position: 'right',
        beginAtZero: false,
        grid: {
          color: 'rgba(160, 174, 192, 0.1)'
        },
        ticks: {
          callback: createSafeAxisFormatter(selectedSymbol)
        }
      }
    }
  };

  return (
    <div className="bento-card bento-span-12 bento-height-3" style={{ padding: '15px', borderRadius: '8px', background: '#1e2130', border: '1px solid #2a2f45', marginBottom: '20px' }}>
      <h3 style={{ marginTop: 0, marginBottom: '15px', fontSize: '18px', fontWeight: '500', color: '#eee' }}>
        {selectedSymbol} Price Chart {timeframe === 'W1' || timeframe === 'MN1' ? `(${timeframe === 'W1' ? 'Weekly' : 'Monthly'})` : ''}
      </h3>
      <div style={{ height: '300px', position: 'relative' }}>
        <Line
          data={{
            labels: timeLabels,
            datasets: [
              {
                label: `${selectedSymbol} Price`,
                data: priceData,
                borderColor: '#4fc3f7',
                backgroundColor: 'rgba(79, 195, 247, 0.1)',
                fill: true,
                borderWidth: 2,
                pointRadius: 0,
                pointHoverRadius: 5
              }
            ]
          }}
          options={chartOptions}
        />
      </div>
    </div>
  );
};

export default AdvancedChart;
