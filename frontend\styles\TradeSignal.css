/* Trade Signal Page Styles */

.trade-signal-page {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.trade-signal-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 12px;
}

/* Signal Controls */
.signal-controls {
  background-color: var(--card);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px var(--shadow);
}

.control-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 12px;
}

.control-row:last-child {
  margin-bottom: 0;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-group label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.control-group select,
.control-group input {
  padding: 8px 12px;
  background-color: var(--background-secondary);
  border: 1px solid var(--border);
  border-radius: 4px;
  color: var(--text);
  min-width: 120px;
}

/* Ensure dropdown options have good contrast in dark mode */
.control-group select option {
  background-color: var(--card);
  color: var(--text);
}

.control-group input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 6px;
  background: var(--background-secondary);
  border-radius: 3px;
  outline: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background: var(--primary);
  border-radius: 50%;
  cursor: pointer;
}

.confidence-filter {
  flex-grow: 1;
  max-width: 400px;
}

.strategy-filter {
  flex-grow: 1;
}

.button-group {
  display: flex;
  gap: 8px;
}

.button-group button {
  padding: 6px 12px;
  background-color: var(--background-secondary);
  border: 1px solid var(--border);
  border-radius: 4px;
  color: var(--text);
  cursor: pointer;
  flex-grow: 1;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.button-group button:hover {
  background-color: var(--card-hover);
}

.button-group button.active {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
}

/* Signal Matrix */
.signal-matrix {
  background-color: var(--card);
  border-radius: 8px;
  box-shadow: 0 2px 4px var(--shadow);
  overflow: hidden;
}

.matrix-header {
  padding: 16px;
  border-bottom: 1px solid var(--border);
}

.matrix-header h3 {
  margin: 0;
  color: var(--text);
  font-size: 1.2rem;
  font-weight: 600;
}

.matrix-content {
  padding: 16px;
}

.no-signals-message {
  padding: 30px;
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
}

.no-signals-message p {
  margin: 8px 0;
}

.signal-group {
  margin-bottom: 24px;
}

.signal-group:last-child {
  margin-bottom: 0;
}

.strategy-heading {
  margin: 0 0 12px 0;
  font-size: 1.1rem;
  color: var(--text);
  font-weight: 500;
  border-bottom: 1px solid var(--border);
  padding-bottom: 8px;
}

.signals-row {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

/* Signal Card */
.signal-card {
  background-color: var(--background-secondary);
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid var(--border);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
  width: 100%;
  max-width: 100%;
}

.signal-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow);
}

.signal-card.selected {
  box-shadow: 0 0 0 2px var(--primary);
}

.signal-card.signal-buy {
  border-top: 3px solid var(--success);
}

.signal-card.signal-sell {
  border-top: 3px solid var(--error);
}

.signal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: var(--background-tertiary, rgba(0, 0, 0, 0.15));
}

.signal-type-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.8rem;
}

.signal-buy .signal-type-badge {
  background-color: rgba(0, 200, 83, 0.2);
  color: var(--success);
}

.signal-sell .signal-type-badge {
  background-color: rgba(255, 61, 113, 0.2);
  color: var(--error);
}

.signal-confidence {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  color: white;
  font-weight: 600;
}

.signal-select input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.signal-body {
  padding: 16px;
  color: var(--text);
}

.signal-info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 0.85rem;
}

.signal-symbol {
  font-weight: bold;
}

.signal-timeframe, 
.signal-strategy {
  color: var(--text-secondary);
}

.signal-levels {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 16px;
  background-color: var(--background-tertiary, rgba(0, 0, 0, 0.1));
  padding: 12px;
  border-radius: 6px;
}

.signal-level {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.level-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.level-value {
  font-family: monospace;
  font-size: 0.9rem;
  font-weight: bold;
}

.level-type,
.level-risk,
.level-profit {
  font-size: 0.75rem;
  margin-left: 8px;
}

.level-type {
  color: var(--text-secondary);
}

.level-risk {
  color: var(--error);
}

.level-profit {
  color: var(--success);
}

.signal-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.signal-timewindow,
.signal-rr {
  display: flex;
  gap: 5px;
}

.meta-label {
  color: var(--text-secondary);
}

.meta-value {
  color: var(--text);
}

.signal-explanation {
  font-size: 0.85rem;
  line-height: 1.4;
  margin-bottom: 16px;
  color: var(--text);
  padding: 10px;
  background-color: var(--background-tertiary, rgba(0, 0, 0, 0.1));
  border-radius: 4px;
}

.signal-actions {
  display: flex;
  justify-content: center;
}

.signal-execute-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  border: none;
  width: 100%;
  transition: background-color 0.2s ease;
}

.buy-btn {
  background-color: var(--success);
  color: white;
}

.buy-btn:hover {
  background-color: var(--success);
  opacity: 0.9;
}

.sell-btn {
  background-color: var(--error);
  color: white;
}

.sell-btn:hover {
  background-color: var(--error);
  opacity: 0.9;
}

/* Execution Queue */
.execution-queue {
  background-color: var(--card);
  border-radius: 8px;
  box-shadow: 0 2px 4px var(--shadow);
  overflow: hidden;
  margin-top: 16px;
  margin-bottom: 24px;
}

.queue-header {
  padding: 16px;
  border-bottom: 1px solid var(--border);
}

.queue-header h3 {
  margin: 0;
  color: var(--text);
  font-size: 1.2rem;
  font-weight: 600;
}

.empty-queue-message {
  padding: 30px;
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
}

.empty-queue-message p {
  margin: 8px 0;
}

.queue-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid var(--border);
  align-items: center;
}

.queue-controls .control-group {
  flex: 1 1 0;
  min-width: 120px;
  max-width: 200px;
}

.execute-all-btn {
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-weight: 500;
  margin-left: auto;
  min-width: 120px;
  transition: background-color 0.2s ease;
}

.execute-all-btn:hover {
  background-color: var(--primary-hover);
}

.execute-all-btn:disabled {
  background-color: var(--background-tertiary, rgba(0, 0, 0, 0.15));
  cursor: not-allowed;
}

.queue-list {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.queue-item {
  background-color: var(--background-secondary);
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid var(--border);
}

.queue-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  background-color: var(--background-tertiary, rgba(0, 0, 0, 0.15));
  font-size: 0.9rem;
}

.queue-item-symbol {
  font-weight: 600;
  color: var(--text);
}

.queue-item-type {
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.75rem;
}

.queue-item-type.buy {
  background-color: rgba(0, 200, 83, 0.2);
  color: var(--success);
}

.queue-item-type.sell {
  background-color: rgba(255, 61, 113, 0.2);
  color: var(--error);
}

.queue-item-strategy {
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.remove-btn {
  background-color: rgba(255, 61, 113, 0.15);
  color: var(--error);
  border: none;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.2s ease;
}

.remove-btn:hover {
  background-color: rgba(255, 61, 113, 0.3);
}

.queue-item-details {
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
}

.queue-item-levels {
  display: flex;
  gap: 12px;
}

.queue-item-levels .level {
  color: var(--text);
}

.queue-item-levels .level.sl {
  color: var(--error);
}

.queue-item-levels .level.tp {
  color: var(--success);
}

.queue-item-sizes {
  display: flex;
  flex-direction: column;
  gap: 6px;
  font-size: 0.8rem;
}

.size-info {
  display: flex;
  justify-content: space-between;
  gap: 8px;
}

.size-label {
  color: var(--text-secondary);
}

.size-value {
  color: var(--text);
  font-weight: 500;
}

.size-info.recommended .size-value {
  color: var(--success);
  font-weight: 600;
}

/* Loading state */
.loading-container {
  padding: 40px;
  text-align: center;
}

.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 3px solid rgba(78, 133, 246, 0.2);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  color: var(--text);
}

.no-data-message {
  padding: 40px;
  text-align: center;
  color: var(--text-secondary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .control-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .control-group,
  .button-group {
    width: 100%;
  }
  
  .signals-row {
    grid-template-columns: 1fr;
  }
  
  .queue-item-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .queue-item-levels,
  .queue-item-sizes {
    width: 100%;
  }
}

@media (min-width: 768px) and (max-width: 1200px) {
  .signals-row {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}
