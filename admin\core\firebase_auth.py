"""Firebase authentication module for admin panel."""

import logging
import os
from typing import Optional

import firebase_admin
from firebase_admin import credentials, firestore

# --- Global variables to track initialization state ---
_firebase_initialized = False
_firebase_app = None
_db = None

def initialize_firebase() -> bool:
    """
    Initializes the Firebase Admin SDK if not already initialized.
    Returns True if initialization is successful or already done, False otherwise.
    """
    global _firebase_initialized, _firebase_app, _db
    
    if _firebase_initialized:
        logging.info("Firebase was already initialized.")
        return True
        
    if firebase_admin._apps: 
        # Firebase app already exists from another import
        _firebase_initialized = True
        _db = firestore.client()
        logging.info("Firebase was already initialized elsewhere.")
        return True
        
    try:
        # Main credentials file is in the project root, fallback to backend folder
        root_cred_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
                              'garuda-algo-firebase-credentials.json')
        backend_cred_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
                                 'backend', 'garuda-algo-firebase-credentials.json')
        
        # Check both paths and use the one that exists
        if os.path.exists(root_cred_path):
            cred_path = root_cred_path
        elif os.path.exists(backend_cred_path):
            cred_path = backend_cred_path
        else:
            logging.error(f"Firebase credentials file not found at {root_cred_path} or {backend_cred_path}")
            return False
            
        logging.info(f"Initializing Firebase with credentials file: {cred_path}")
        cred = credentials.Certificate(cred_path)
        _firebase_app = firebase_admin.initialize_app(cred)
        _db = firestore.client()
        _firebase_initialized = True
        
        project_id = _firebase_app.project_id
        logging.info(f"Firebase Admin SDK initialized successfully. Project ID: {project_id}")
        return True
    except Exception as e:
        logging.exception(f"Failed to initialize Firebase Admin SDK: {e}")
        return False
