from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.analysis.technical.indicators.momentum.rsi import RSIIndicator # Uses RSI
from src.core.models.market_data import MarketData

class StochRSIIndicator(BaseIndicator):
    """Stochastic RSI indicator."""

    def __init__(self, rsi_period: int = 14, stoch_period: int = 14,
                 k_period: int = 3, d_period: int = 3, source: str = 'close'):
        """
        Initialize Stochastic RSI indicator.

        Args:
            rsi_period: The period for the underlying RSI calculation.
            stoch_period: The period for the Stochastic calculation on RSI.
            k_period: The period for the %K line smoothing (often same as d_period).
            d_period: The period for the %D signal line.
            source: The price source for the initial RSI calculation.
        """
        super().__init__({
            'rsi_period': rsi_period,
            'stoch_period': stoch_period,
            'k_period': k_period,
            'd_period': d_period,
            'source': source
        })
        # Internal RSI indicator
        self._rsi_indicator = RSIIndicator(period=rsi_period, source=source)

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Stochastic RSI values."""
        df = data.to_dataframe()
        min_len = max(self.params['rsi_period'], self.params['stoch_period']) + self.params['d_period']
        if df.empty or len(df) < min_len:
             return {'k': np.array([]), 'd': np.array([])}

        rsi_period = self.params['rsi_period']
        stoch_period = self.params['stoch_period']
        k_period = self.params['k_period'] # This is smoothing for %K, often called %K period in StochRSI context
        d_period = self.params['d_period']

        # Calculate RSI first
        rsi_result = self._rsi_indicator.calculate(data)
        rsi = pd.Series(rsi_result['rsi'], index=df.index) # Ensure index alignment

        # Calculate Stochastic of RSI
        rsi_low = rsi.rolling(window=stoch_period).min()
        rsi_high = rsi.rolling(window=stoch_period).max()
        rsi_range = (rsi_high - rsi_low).replace(0, np.nan) # Avoid division by zero

        stoch_rsi_k_raw = 100 * (rsi - rsi_low) / rsi_range
        stoch_rsi_k_raw = stoch_rsi_k_raw.fillna(0) # Fill NaNs

        # Smooth %K line (using SMA as is common for StochRSI %K)
        stoch_rsi_k = stoch_rsi_k_raw.rolling(window=k_period).mean()

        # Calculate %D line (SMA of %K)
        stoch_rsi_d = stoch_rsi_k.rolling(window=d_period).mean()

        self._values = {
            'k': stoch_rsi_k.values,
            'd': stoch_rsi_d.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['rsi_period'] < 1:
            raise ValueError("RSI Period must be greater than 0")
        if self.params['stoch_period'] < 1:
            raise ValueError("Stochastic Period must be greater than 0")
        if self.params['k_period'] < 1:
            raise ValueError("K Period must be greater than 0")
        if self.params['d_period'] < 1:
            raise ValueError("D Period must be greater than 0")
        return True