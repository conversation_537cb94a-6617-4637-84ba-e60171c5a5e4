import React, { useState, useCallback } from 'react';
import { formatDate, formatTime } from '../../utils/formatters';
import { useNotification } from '../../components/Notification';
import { useModal } from '../../contexts/ModalContext';

/**
 * SignalCard component - Displays an individual trading signal with selection capability
 */
function SignalCard({ signal, onSelect, isSelected, currentPrice }) {
  const {
    id,
    symbol,
    timeframe,
    strategy,
    signalType,
    confidence,
    entry,
    stopLoss,
    takeProfit,
    riskReward,
    timeWindow,
    explanation
  } = signal;
  
  // Determine card color based on signal type
  const cardColorClass = signalType === 'BUY' ? 'signal-buy' : 'signal-sell';
  
  // State for trade execution
  const [isSubmitting, setIsSubmitting] = useState(false);
  const notify = useNotification();
  
  // Get modal context functions
  const { openTradeExecutionModal } = useModal();
  
  const formatPrice = (price) => {
    if (price === undefined || price === null) return '-';
    return typeof price === 'number'
      ? price.toFixed(5)
      : price;
  };
  
  // Calculate potential profit/loss
  const calculatePL = () => {
    const entryPrice = entry.price;
    
    if (signalType === 'BUY') {
      const tpProfit = ((takeProfit - entryPrice) / entryPrice) * 100;
      const slLoss = ((entryPrice - stopLoss) / entryPrice) * 100;
      return { profit: tpProfit, loss: slLoss };
    } else {
      const tpProfit = ((entryPrice - takeProfit) / entryPrice) * 100;
      const slLoss = ((stopLoss - entryPrice) / entryPrice) * 100;
      return { profit: tpProfit, loss: slLoss };
    }
  };
  
  const pl = calculatePL();
  
  // Format time window
  const formatTimeWindow = () => {
    if (!timeWindow) return 'Not specified';
    
    const start = new Date(timeWindow.start);
    const end = new Date(timeWindow.end);
    
    return `${formatTime(start)} - ${formatTime(end)}`;
  };
  
  // Handle checkbox change
  const handleSelect = (e) => {
    onSelect(id, e.target.checked);
  };
  
  // Get strategy label
  const getStrategyLabel = () => {
    switch (strategy) {
      case 'scalping': return 'Scalping';
      case 'intraday': return 'Intraday';
      case 'swing': return 'Swing';
      case 'position': return 'Position';
      default: return strategy;
    }
  };
  
  // Get confidence background color
  const getConfidenceColor = () => {
    if (confidence >= 70) return 'var(--success)';
    if (confidence >= 50) return 'var(--warning)';
    return 'var(--error)';
  };
  
  // Trade execution functions
  const handleExecuteSignal = useCallback(() => {
    // Use the modal context to open the execution dialog
    openTradeExecutionModal({
      signalType,
      symbol,
      entry,
      stopLoss,
      takeProfit,
      isProcessing: isSubmitting,
      onExecute: (orderData) => {
        setIsSubmitting(true);
        
        // Get current market price if using market execution
        const useMarketPrice = entry.type === 'Market' || !entry.price;
        const orderPrice = useMarketPrice ? 0 : entry.price;
        
        // Prepare order data for the API
        const apiOrderData = {
          symbol: symbol,
          order_type: signalType === 'BUY' ? 'BUY' : 'SELL', // Ensure proper order type format
          volume: orderData.lotSize,
          price: orderPrice,
          sl: orderData.stopLoss,
          tp: orderData.takeProfit,
          // Add required MT5 fields
          type_time: 'GTC', // Good Till Cancelled
          type_filling: 'FOK', // Fill or Kill
          deviation: 20, // Allow small price deviation
          magic: 12345, // Magic number for identifying
          comment: `GarudaAlgo ${strategy || 'signal'}`
        };
        
        console.log('Sending trade order to API:', apiOrderData);
        
        // Send to API
        fetch('http://localhost:5001/api/trade/place_order', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(apiOrderData),
        })
        .then(response => {
          if (!response.ok) {
            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
              return response.json().then(errorData => {
                throw new Error(errorData.error || `Error ${response.status}: ${response.statusText}`);
              });
            } else {
              throw new Error(`Error ${response.status}: ${response.statusText}`);
            }
          }
          return response.json();
        })
        .then(data => {
          if (data.error) {
            throw new Error(data.error);
          }
          console.log('Order executed successfully:', data);
          notify.success('Trade Executed', `${signalType} order placed successfully for ${symbol}`);
        })
        .catch(error => {
          console.error('Error executing trade:', error);
          notify.error('Trade Execution Failed', error.message || 'Failed to execute trade');
        })
        .finally(() => {
          setIsSubmitting(false);
        });
      }
    });
  }, [symbol, signalType, entry, stopLoss, takeProfit, isSubmitting, strategy, notify, openTradeExecutionModal]);
  
  return (
    <div className={`signal-card ${cardColorClass} ${isSelected ? 'selected' : ''}`}>
      <div className="signal-header">
        <div className="signal-type-badge">{signalType}</div>
        <div className="signal-confidence" style={{ 
          background: getConfidenceColor()
        }}>
          {confidence}%
        </div>
        <div className="signal-select">
          <input 
            type="checkbox" 
            checked={isSelected} 
            onChange={handleSelect} 
            title="Select for execution"
          />
        </div>
      </div>
      
      <div className="signal-body">
        <div className="signal-info-row">
          <div className="signal-symbol">{symbol}</div>
          <div className="signal-timeframe">{timeframe}</div>
          <div className="signal-strategy">{getStrategyLabel()}</div>
        </div>
        
        <div className="signal-levels">
          <div className="signal-level entry">
            <div className="level-label">ENTRY</div>
            <div className="queue-item-details">
              <span className="level-value">{entry && entry.price ? formatPrice(entry.price) : '-'}</span>
              <span className="level-type">{entry && entry.type ? entry.type : '-'}</span>
            </div>
          </div>
          
          <div className="signal-level sl">
            <div className="level-label">STOP LOSS</div>
            <div className="signal-level-sl">
              <span className="level-value">{formatPrice(stopLoss)}</span>
              <span className="level-risk">-{pl && pl.loss ? pl.loss.toFixed(2) : '0.00'}%</span>
            </div>
          </div>
          
          <div className="signal-level tp">
            <div className="level-label">TAKE PROFIT</div>
            <div className="signal-level-profit">
              <span className="level-value">{formatPrice(takeProfit)}</span>
              <span className="level-profit">+{pl && pl.profit ? pl.profit.toFixed(2) : '0.00'}%</span>
            </div>
          </div>
        </div>
        
        <div className="signal-meta">
          <div className="signal-timewindow">
            <span className="meta-label">Execution Window:</span>
            <span className="meta-value">{formatTimeWindow()}</span>
          </div>
          
          <div className="signal-rr">
            <span className="meta-label">Risk/Reward:</span>
            <span className="meta-value">{riskReward}:1</span>
          </div>
        </div>
        
        <div className="signal-explanation">
          {explanation}
        </div>
        
        <div className="signal-actions">
          <button 
            className={`signal-execute-btn ${signalType === 'BUY' ? 'buy-btn' : 'sell-btn'}`}
            onClick={() => handleExecuteSignal()}
          >
            Execute {signalType}
          </button>
        </div>
      </div>
    </div>
  );
}

export default SignalCard;