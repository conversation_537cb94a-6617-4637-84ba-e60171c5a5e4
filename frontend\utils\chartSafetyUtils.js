/**
 * Chart.js Safety Utilities
 * 
 * This file contains utility functions to make Chart.js components more resilient
 * against undefined or null values, especially for monthly timeframe data.
 */

/**
 * Creates safe tooltip callbacks that handle undefined values
 * @param {Object} options - Configuration options
 * @param {number} options.decimals - Number of decimal places to display
 * @param {string} options.prefix - Optional prefix for the value
 * @param {string} options.suffix - Optional suffix for the value
 * @param {string} options.fallback - Fallback text when value is undefined/null
 * @returns {Object} Safe tooltip callbacks object for Chart.js
 */
export function createSafeTooltipCallbacks(options = {}) {
  const {
    decimals = 5,
    prefix = '',
    suffix = '',
    fallback = 'N/A'
  } = options;

  return {
    label: function(context) {
      try {
        let label = context.dataset.label || '';
        if (label) {
          label += ': ';
        }
        
        // Safely handle the value
        if (context.parsed.y !== null && context.parsed.y !== undefined) {
          const value = context.parsed.y;
          if (typeof value === 'number' && !isNaN(value)) {
            label += prefix + value.toFixed(decimals) + suffix;
          } else {
            label += fallback;
          }
        } else {
          label += fallback;
        }
        
        return label;
      } catch (e) {
        console.error('Error in tooltip callback:', e);
        return 'Error';
      }
    }
  };
}

/**
 * Creates a safe axis formatter function for Chart.js
 * @param {Object} options - Configuration options
 * @param {number} options.decimals - Number of decimal places
 * @param {string} options.prefix - Optional prefix for the value
 * @param {string} options.suffix - Optional suffix for the value
 * @param {string} options.fallback - Fallback text when value is undefined/null
 * @returns {Function} Safe formatter function
 */
export function createSafeAxisFormatter(options = {}) {
  const {
    decimals = 5,
    prefix = '',
    suffix = '',
    fallback = 'N/A'
  } = options;

  return function(value) {
    try {
      if (value === null || value === undefined || isNaN(value)) {
        return fallback;
      }
      return prefix + Number(value).toFixed(decimals) + suffix;
    } catch (e) {
      console.error('Error in axis formatter:', e);
      return fallback;
    }
  };
}

/**
 * Safely formats a dataset for Chart.js to ensure it has no undefined values
 * @param {Array} data - The data array to sanitize
 * @param {*} defaultValue - The default value to use for undefined items (default: null)
 * @returns {Array} Sanitized data array
 */
export function sanitizeChartData(data, defaultValue = null) {
  if (!Array.isArray(data)) {
    console.warn('sanitizeChartData received non-array data:', data);
    return [];
  }
  
  return data.map(item => {
    if (item === undefined || item === null || isNaN(item)) {
      return defaultValue;
    }
    return item;
  });
}

/**
 * Creates a safe dataset configuration for Chart.js
 * @param {Object} config - Base configuration
 * @param {Array} data - Data array
 * @returns {Object} Safe dataset configuration
 */
export function createSafeDataset(config, data) {
  return {
    ...config,
    data: sanitizeChartData(data),
  };
}

/**
 * Applies safety patches to Chart.js global defaults
 * Call this once at application startup
 */
export function patchChartJsDefaults() {
  if (typeof window !== 'undefined' && window.Chart) {
    // Patch tooltip defaults
    if (window.Chart.defaults.plugins && window.Chart.defaults.plugins.tooltip) {
      const safeCallbacks = createSafeTooltipCallbacks();
      window.Chart.defaults.plugins.tooltip.callbacks = {
        ...window.Chart.defaults.plugins.tooltip.callbacks,
        ...safeCallbacks
      };
    }
    
    console.log('Chart.js defaults patched for safety');
  } else {
    console.warn('Chart.js not available, skipping safety patches');
  }
}
