import logging
import traceback
from flask import Blueprint, jsonify, request, current_app
import MetaTrader5 as mt5
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)
trade_ops_bp = Blueprint('trade_operations', __name__)

@trade_ops_bp.route('/position/breakeven/all', methods=['POST'])
def set_all_to_breakeven():
    """Set all positions to break-even"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get all open positions
        positions = mt5.positions_get()
        if positions is None:
            error_code = mt5.last_error()
            error_msg = f"Error getting positions: {error_code}"
            return jsonify({'error': error_msg}), 500

        # Set each position to break-even
        results = []
        for position in positions:
            position_dict = position._asdict()
            ticket = position_dict['ticket']
            symbol = position_dict['symbol']
            position_type = position_dict['type']  # 0 for buy, 1 for sell
            price_open = position_dict['price_open']

            # Set SL to entry price (break-even)
            mt5_request = {
                "action": mt5.TRADE_ACTION_SLTP,
                "symbol": symbol,
                "position": ticket,
                "sl": price_open,
                "tp": position_dict['tp']  # Keep existing TP
            }

            result = mt5.order_send(mt5_request)
            results.append({
                'ticket': ticket,
                'symbol': symbol,
                'success': result.retcode == mt5.TRADE_RETCODE_DONE,
                'message': f"Set to break-even: {'Success' if result.retcode == mt5.TRADE_RETCODE_DONE else 'Failed'}"
            })

        return jsonify({
            'success': True,
            'message': f"Processed {len(results)} positions",
            'results': results
        })

    except Exception as e:
        logger.exception(f"Error setting positions to break-even: {str(e)}")
        return jsonify({'error': str(e)}), 500

@trade_ops_bp.route('/position/trailingstop/all', methods=['POST'])
def set_all_to_trailing():
    """Set all positions to trailing stop"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get trailing distance from request
        data = request.get_json(silent=True) or {}
        mode = data.get('mode', 'pips')  # 'percent' or 'pips'

        if mode == 'percent':
            trailing_distance_percent = data.get('trailing_distance_percent', 0.5)  # Default 0.5%
            trailing_distance_points = None  # Will be calculated per position
        else:
            trailing_distance_points = data.get('trailing_distance', 100)  # Default 100 points
            trailing_distance_percent = None

        # Get all open positions
        positions = mt5.positions_get()
        if positions is None:
            error_code = mt5.last_error()
            error_msg = f"Error getting positions: {error_code}"
            return jsonify({'error': error_msg}), 500

        # Set each position to trailing stop
        results = []
        for position in positions:
            position_dict = position._asdict()
            ticket = position_dict['ticket']
            symbol = position_dict['symbol']
            position_type = position_dict['type']  # 0 for buy, 1 for sell
            price_current = position_dict['price_current']

            # Get symbol info to calculate points
            symbol_info = mt5.symbol_info(symbol)
            if not symbol_info:
                results.append({
                    'ticket': ticket,
                    'symbol': symbol,
                    'success': False,
                    'message': "Failed to get symbol info"
                })
                continue

            point = symbol_info.point

            # Calculate distance based on mode
            if mode == 'percent':
                # Calculate distance as percentage of current price
                distance = price_current * (trailing_distance_percent / 100)
                distance_points = int(distance / point)
            else:
                # Use fixed pips distance
                distance = trailing_distance_points * point
                distance_points = trailing_distance_points

            # Calculate SL based on position type
            if position_type == 0:  # BUY
                sl = price_current - distance
            else:  # SELL
                sl = price_current + distance

            # Set trailing stop
            mt5_request = {
                "action": mt5.TRADE_ACTION_SLTP,
                "symbol": symbol,
                "position": ticket,
                "sl": sl,
                "tp": position_dict['tp']  # Keep existing TP
            }

            result = mt5.order_send(mt5_request)

            # Create detailed message based on mode
            if mode == 'percent':
                detail_msg = f"({trailing_distance_percent}% = {distance_points} points)"
            else:
                detail_msg = f"({trailing_distance_points} points)"

            results.append({
                'ticket': ticket,
                'symbol': symbol,
                'success': result.retcode == mt5.TRADE_RETCODE_DONE,
                'message': f"Set trailing stop {detail_msg}: {'Success' if result.retcode == mt5.TRADE_RETCODE_DONE else 'Failed'}",
                'sl_price': sl,
                'distance_points': distance_points
            })

        successful_count = sum(1 for r in results if r['success'])
        mode_text = f"{trailing_distance_percent}% mode" if mode == 'percent' else f"{trailing_distance_points} pips mode"

        return jsonify({
            'success': True,
            'message': f"Processed {len(results)} positions using {mode_text} ({successful_count} successful)",
            'results': results,
            'mode': mode,
            'distance_value': trailing_distance_percent if mode == 'percent' else trailing_distance_points
        })

    except Exception as e:
        logger.exception(f"Error setting trailing stops: {str(e)}")
        return jsonify({'error': str(e)}), 500

@trade_ops_bp.route('/position/close/all', methods=['POST'])
def close_all_positions():
    """Close all open positions"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get all open positions
        positions = mt5.positions_get()
        if positions is None:
            error_code = mt5.last_error()
            error_msg = f"Error getting positions: {error_code}"
            return jsonify({'error': error_msg}), 500

        # Close each position
        results = []
        for position in positions:
            position_dict = position._asdict()
            ticket = position_dict['ticket']
            symbol = position_dict['symbol']
            position_type = position_dict['type']  # 0 for buy, 1 for sell
            volume = position_dict['volume']

            # Determine order type for close operation (opposite of position type)
            order_type = mt5.ORDER_TYPE_SELL if position_type == 0 else mt5.ORDER_TYPE_BUY
            price = mt5.symbol_info_tick(symbol).bid if position_type == 0 else mt5.symbol_info_tick(symbol).ask

            # Close position
            mt5_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": volume,
                "type": order_type,
                "position": ticket,
                "price": price,
                "deviation": 20,
                "magic": 234000,
                "comment": "Close all operation",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            result = mt5.order_send(mt5_request)
            results.append({
                'ticket': ticket,
                'symbol': symbol,
                'success': result.retcode == mt5.TRADE_RETCODE_DONE,
                'message': f"Close position: {'Success' if result.retcode == mt5.TRADE_RETCODE_DONE else 'Failed'}"
            })

        return jsonify({
            'success': True,
            'message': f"Processed {len(results)} positions",
            'results': results
        })

    except Exception as e:
        logger.exception(f"Error closing positions: {str(e)}")
        return jsonify({'error': str(e)}), 500

@trade_ops_bp.route('/position/close/profitable', methods=['POST'])
def close_profitable_positions():
    """Close all profitable positions"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get all open positions
        positions = mt5.positions_get()
        if positions is None:
            error_code = mt5.last_error()
            error_msg = f"Error getting positions: {error_code}"
            return jsonify({'error': error_msg}), 500

        # Filter profitable positions
        profitable_positions = [p for p in positions if p.profit > 0]

        # Close each profitable position
        results = []
        for position in profitable_positions:
            position_dict = position._asdict()
            ticket = position_dict['ticket']
            symbol = position_dict['symbol']
            position_type = position_dict['type']  # 0 for buy, 1 for sell
            volume = position_dict['volume']

            # Determine order type for close operation (opposite of position type)
            order_type = mt5.ORDER_TYPE_SELL if position_type == 0 else mt5.ORDER_TYPE_BUY
            price = mt5.symbol_info_tick(symbol).bid if position_type == 0 else mt5.symbol_info_tick(symbol).ask

            # Close position
            mt5_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": volume,
                "type": order_type,
                "position": ticket,
                "price": price,
                "deviation": 20,
                "magic": 234000,
                "comment": "Close profitable operation",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            result = mt5.order_send(mt5_request)
            results.append({
                'ticket': ticket,
                'symbol': symbol,
                'profit': position_dict['profit'],
                'success': result.retcode == mt5.TRADE_RETCODE_DONE,
                'message': f"Close profitable position: {'Success' if result.retcode == mt5.TRADE_RETCODE_DONE else 'Failed'}"
            })

        return jsonify({
            'success': True,
            'message': f"Processed {len(results)} profitable positions",
            'results': results
        })

    except Exception as e:
        logger.exception(f"Error closing profitable positions: {str(e)}")
        return jsonify({'error': str(e)}), 500

@trade_ops_bp.route('/position/close/losing', methods=['POST'])
def close_losing_positions():
    """Close all losing positions"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get all open positions
        positions = mt5.positions_get()
        if positions is None:
            error_code = mt5.last_error()
            error_msg = f"Error getting positions: {error_code}"
            return jsonify({'error': error_msg}), 500

        # Filter losing positions
        losing_positions = [p for p in positions if p.profit < 0]

        # Close each losing position
        results = []
        for position in losing_positions:
            position_dict = position._asdict()
            ticket = position_dict['ticket']
            symbol = position_dict['symbol']
            position_type = position_dict['type']  # 0 for buy, 1 for sell
            volume = position_dict['volume']

            # Determine order type for close operation (opposite of position type)
            order_type = mt5.ORDER_TYPE_SELL if position_type == 0 else mt5.ORDER_TYPE_BUY
            price = mt5.symbol_info_tick(symbol).bid if position_type == 0 else mt5.symbol_info_tick(symbol).ask

            # Close position
            mt5_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": volume,
                "type": order_type,
                "position": ticket,
                "price": price,
                "deviation": 20,
                "magic": 234000,
                "comment": "Close losing position",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            result = mt5.order_send(mt5_request)
            results.append({
                'ticket': ticket,
                'symbol': symbol,
                'profit': position_dict['profit'],
                'success': result.retcode == mt5.TRADE_RETCODE_DONE,
                'message': f"Close losing position: {'Success' if result.retcode == mt5.TRADE_RETCODE_DONE else 'Failed'}"
            })

        return jsonify({
            'success': True,
            'message': f"Processed {len(results)} losing positions",
            'results': results
        })

    except Exception as e:
        logger.exception(f"Error closing losing positions: {str(e)}")
        return jsonify({'error': str(e)}), 500

@trade_ops_bp.route('/position/close/buy', methods=['POST'])
def close_all_buy_positions():
    """Close all BUY positions"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get all open positions
        positions = mt5.positions_get()
        if positions is None:
            error_code = mt5.last_error()
            error_msg = f"Error getting positions: {error_code}"
            return jsonify({'error': error_msg}), 500

        # Filter BUY positions (type 0 = BUY)
        buy_positions = [p for p in positions if p.type == 0]

        # Close each BUY position
        results = []
        for position in buy_positions:
            position_dict = position._asdict()
            ticket = position_dict['ticket']
            symbol = position_dict['symbol']
            volume = position_dict['volume']

            # For BUY positions, close with SELL
            price = mt5.symbol_info_tick(symbol).bid  # Bid price for selling

            # Close position
            mt5_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": volume,
                "type": mt5.ORDER_TYPE_SELL,  # Sell to close buy positions
                "position": ticket,
                "price": price,
                "deviation": 20,
                "magic": 234000,
                "comment": "Close all BUY positions",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            result = mt5.order_send(mt5_request)
            results.append({
                'ticket': ticket,
                'symbol': symbol,
                'success': result.retcode == mt5.TRADE_RETCODE_DONE,
                'message': f"Close BUY position: {'Success' if result.retcode == mt5.TRADE_RETCODE_DONE else 'Failed'}"
            })

        return jsonify({
            'success': True,
            'message': f"Processed {len(results)} BUY positions",
            'results': results
        })

    except Exception as e:
        logger.exception(f"Error closing BUY positions: {str(e)}")
        return jsonify({'error': str(e)}), 500

@trade_ops_bp.route('/position/close/sell', methods=['POST'])
def close_all_sell_positions():
    """Close all SELL positions"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get all open positions
        positions = mt5.positions_get()
        if positions is None:
            error_code = mt5.last_error()
            error_msg = f"Error getting positions: {error_code}"
            return jsonify({'error': error_msg}), 500

        # Filter SELL positions (type 1 = SELL)
        sell_positions = [p for p in positions if p.type == 1]

        # Close each SELL position
        results = []
        for position in sell_positions:
            position_dict = position._asdict()
            ticket = position_dict['ticket']
            symbol = position_dict['symbol']
            volume = position_dict['volume']

            # For SELL positions, close with BUY
            price = mt5.symbol_info_tick(symbol).ask  # Ask price for buying

            # Close position
            mt5_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": volume,
                "type": mt5.ORDER_TYPE_BUY,  # Buy to close sell positions
                "position": ticket,
                "price": price,
                "deviation": 20,
                "magic": 234000,
                "comment": "Close all SELL positions",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            result = mt5.order_send(mt5_request)
            results.append({
                'ticket': ticket,
                'symbol': symbol,
                'success': result.retcode == mt5.TRADE_RETCODE_DONE,
                'message': f"Close SELL position: {'Success' if result.retcode == mt5.TRADE_RETCODE_DONE else 'Failed'}"
            })

        return jsonify({
            'success': True,
            'message': f"Processed {len(results)} SELL positions",
            'results': results
        })

    except Exception as e:
        logger.exception(f"Error closing SELL positions: {str(e)}")
        return jsonify({'error': str(e)}), 500

@trade_ops_bp.route('/position/modify/bulk', methods=['POST'])
def bulk_modify_positions():
    """Bulk modify SL/TP for all positions or filtered by symbol"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get parameters from request
        data = request.get_json(silent=True) or {}
        symbol = data.get('symbol', None)  # Optional symbol filter

        # Get and convert parameters with proper type handling
        sl_points = data.get('sl_points', None)  # Points for SL from current price
        tp_points = data.get('tp_points', None)  # Points for TP from current price

        # Handle exact prices with proper type conversion
        sl_price_raw = data.get('sl_price', None)  # Exact SL price
        tp_price_raw = data.get('tp_price', None)  # Exact TP price

        # Convert to float if provided (with error handling)
        sl_price = float(sl_price_raw) if sl_price_raw not in [None, ''] else None
        tp_price = float(tp_price_raw) if tp_price_raw not in [None, ''] else None

        logger.info(f"Bulk modify request: sl_points={sl_points}, tp_points={tp_points}, sl_price={sl_price}, tp_price={tp_price}")

        # Ensure at least one of SL or TP parameters is provided
        if sl_points is None and tp_points is None and sl_price is None and tp_price is None:
            return jsonify({
                'success': False,
                'message': "At least one of sl_points, tp_points, sl_price, or tp_price must be provided"
            }), 400

        # Get positions, filtered by symbol if provided
        positions = mt5.positions_get(symbol=symbol) if symbol else mt5.positions_get()
        if positions is None:
            error_code = mt5.last_error()
            error_msg = f"Error getting positions: {error_code}"
            return jsonify({'error': error_msg}), 500

        # Modify each position
        results = []
        for position in positions:
            position_dict = position._asdict()
            ticket = position_dict['ticket']
            position_symbol = position_dict['symbol']
            position_type = position_dict['type']  # 0 for buy, 1 for sell
            current_price = position_dict['price_current']

            # Get symbol info for point conversion if using relative SL/TP
            symbol_info = None
            if sl_points is not None or tp_points is not None:
                symbol_info = mt5.symbol_info(position_symbol)
                if not symbol_info:
                    results.append({
                        'ticket': ticket,
                        'symbol': position_symbol,
                        'success': False,
                        'message': "Failed to get symbol info"
                    })
                    continue

            # Calculate SL/TP based on provided parameters
            new_sl = position_dict['sl']  # Default to current SL
            new_tp = position_dict['tp']  # Default to current TP

            # If points are provided, calculate SL/TP based on current price and position type
            if sl_points is not None and symbol_info:
                point = symbol_info.point
                if position_type == 0:  # BUY
                    new_sl = current_price - (sl_points * point)
                else:  # SELL
                    new_sl = current_price + (sl_points * point)

            if tp_points is not None and symbol_info:
                point = symbol_info.point
                if position_type == 0:  # BUY
                    new_tp = current_price + (tp_points * point)
                else:  # SELL
                    new_tp = current_price - (tp_points * point)

            # If exact prices are provided, use them directly
            if sl_price is not None:
                new_sl = sl_price

            if tp_price is not None:
                new_tp = tp_price

            # Modify position
            mt5_request = {
                "action": mt5.TRADE_ACTION_SLTP,
                "symbol": position_symbol,
                "position": ticket,
                "sl": new_sl,
                "tp": new_tp
            }

            result = mt5.order_send(mt5_request)
            results.append({
                'ticket': ticket,
                'symbol': position_symbol,
                'sl': new_sl,
                'tp': new_tp,
                'success': result.retcode == mt5.TRADE_RETCODE_DONE,
                'message': f"Modify SL/TP: {'Success' if result.retcode == mt5.TRADE_RETCODE_DONE else 'Failed'}"
            })

        return jsonify({
            'success': True,
            'message': f"Processed {len(results)} positions",
            'results': results
        })

    except Exception as e:
        logger.exception(f"Error modifying positions: {str(e)}")
        return jsonify({'error': str(e)}), 500
