from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class ChaikinVolatilityIndicator(BaseIndicator):
    """Chaikin Volatility indicator."""

    def __init__(self, period_hl: int = 10, period_roc: int = 10, ma_type: str = 'ema'):
        """
        Initialize Chaikin Volatility indicator.

        Args:
            period_hl: The period for calculating the High-Low range moving average.
            period_roc: The period for calculating the Rate of Change of the High-Low MA.
            ma_type: The type of moving average ('sma' or 'ema').
        """
        super().__init__({
            'period_hl': period_hl,
            'period_roc': period_roc,
            'ma_type': ma_type
        })

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Chaikin Volatility values."""
        df = data.to_dataframe()
        if df.empty or len(df) < max(self.params['period_hl'], self.params['period_roc']):
             return {'chaikin_volatility': np.array([])}

        period_hl = self.params['period_hl']
        period_roc = self.params['period_roc']
        ma_type = self.params['ma_type'].lower()

        high = df['high']
        low = df['low']

        # Calculate High-Low difference
        high_low_diff = high - low

        # Calculate Moving Average of High-Low difference
        if ma_type == 'sma':
            hl_ma = high_low_diff.rolling(window=period_hl).mean()
        else: # ema
            hl_ma = high_low_diff.ewm(span=period_hl, adjust=False).mean()

        # Calculate Rate of Change (ROC) of the High-Low MA
        roc_hl_ma = (hl_ma / hl_ma.shift(period_roc) - 1) * 100

        self._values = {
            'chaikin_volatility': roc_hl_ma.values,
            'hl_ma': hl_ma.values # Optional: return intermediate calc
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_ma_types = ['sma', 'ema']
        if self.params['ma_type'].lower() not in valid_ma_types:
            raise ValueError(f"MA type must be one of {valid_ma_types}")
        if self.params['period_hl'] < 1:
            raise ValueError("High-Low Period must be greater than 0")
        if self.params['period_roc'] < 1:
            raise ValueError("ROC Period must be greater than 0")
        return True