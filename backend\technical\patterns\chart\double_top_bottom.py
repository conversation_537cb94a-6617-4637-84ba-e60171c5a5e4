from typing import Dict, Any, List
import numpy as np
import pandas as pd
from scipy.signal import find_peaks

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class DoubleTopBottomPatternIndicator(BaseIndicator):
    """Double Top and Double Bottom chart pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - min_peak_distance: Parameter description (default: 10)
                - min_peak_height: Parameter description (default: 0.02)
                - price_tolerance: Parameter description (default: 0.02)
        """
        default_params = {
            "min_peak_distance": 10,
            "min_peak_height": 0.02,
            "price_tolerance": 0.02,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)

    
    def _find_peaks_and_troughs(self, prices: np.ndarray, min_distance: int,
                              min_height: float) -> tuple:
        """Find peaks and troughs in price data."""
        # Find peaks
        peaks, _ = find_peaks(prices, distance=min_distance,
                            height=np.mean(prices) * min_height)
        
        # Find troughs (inverse of peaks)
        troughs, _ = find_peaks(-prices, distance=min_distance,
                              height=np.mean(prices) * min_height)
        
        return peaks, troughs
    
    def _is_double_top(self, prices: np.ndarray, peaks: np.ndarray,
                      troughs: np.ndarray, tolerance: float) -> tuple:
        """Identify Double Top patterns."""
        is_pattern = np.zeros_like(prices)
        pattern_type = np.zeros_like(prices)
        
        if len(peaks) >= 2 and len(troughs) >= 1:
            for i in range(len(peaks)-1):
                # Get two consecutive peaks
                peak1 = peaks[i]
                peak2 = peaks[i+1]
                
                # Get the trough between peaks
                trough = troughs[troughs > peak1]
                trough = trough[trough < peak2]
                
                if len(trough) > 0:
                    trough = trough[0]
                    
                    # Check if it's a valid Double Top pattern
                    price_diff = abs(prices[peak1] - prices[peak2]) / prices[peak1]
                    if (price_diff <= tolerance and
                        prices[trough] < prices[peak1] and
                        prices[trough] < prices[peak2]):
                        
                        # Mark the pattern
                        is_pattern[peak1:peak2+1] = 1
                        pattern_type[peak1:peak2+1] = -1  # Bearish pattern
        
        return is_pattern, pattern_type
    
    def _is_double_bottom(self, prices: np.ndarray, peaks: np.ndarray,
                         troughs: np.ndarray, tolerance: float) -> tuple:
        """Identify Double Bottom patterns."""
        is_pattern = np.zeros_like(prices)
        pattern_type = np.zeros_like(prices)
        
        if len(troughs) >= 2 and len(peaks) >= 1:
            for i in range(len(troughs)-1):
                # Get two consecutive troughs
                trough1 = troughs[i]
                trough2 = troughs[i+1]
                
                # Get the peak between troughs
                peak = peaks[peaks > trough1]
                peak = peak[peak < trough2]
                
                if len(peak) > 0:
                    peak = peak[0]
                    
                    # Check if it's a valid Double Bottom pattern
                    price_diff = abs(prices[trough1] - prices[trough2]) / prices[trough1]
                    if (price_diff <= tolerance and
                        prices[peak] > prices[trough1] and
                        prices[peak] > prices[trough2]):
                        
                        # Mark the pattern
                        is_pattern[trough1:trough2+1] = 1
                        pattern_type[trough1:trough2+1] = 1  # Bullish pattern
        
        return is_pattern, pattern_type
    
    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        close = df['close'].values
        min_distance = self.params['min_peak_distance']
        min_height = self.params['min_peak_height']
        tolerance = self.params['price_tolerance']
        
        # Find peaks and troughs
        peaks, troughs = self._find_peaks_and_troughs(close, min_distance, min_height)
        
        # Identify patterns
        is_dt, dt_type = self._is_double_top(close, peaks, troughs, tolerance)
        is_db, db_type = self._is_double_bottom(close, peaks, troughs, tolerance)
        
        # Combine patterns
        is_pattern = is_dt | is_db
        pattern_type = dt_type + db_type
        
        # Calculate pattern strength
        strength = np.zeros_like(close)
        for i in range(len(close)):
            if is_pattern[i]:
                # Calculate the height of the pattern relative to price
                if pattern_type[i] < 0:  # Double Top
                    height = close[peaks[peaks > i][0]] - close[troughs[troughs > i][0]]
                else:  # Double Bottom
                    height = close[peaks[peaks > i][0]] - close[troughs[troughs > i][0]]
                strength[i] = height / close[i]
        
        # Calculate trend context
        trend = np.zeros_like(close)
        for i in range(1, len(close)):
            if i >= 20:  # Use 20-period SMA for trend
                sma = np.mean(close[i-20:i])
                trend[i] = 1 if close[i] > sma else -1
        
        # Calculate pattern reliability
        reliability = np.zeros_like(close)
        for i in range(1, len(close)):
            if is_pattern[i]:
                # Check if price moved in the expected direction
                if i < len(close)-1:
                    future_return = (close[i+1] - close[i]) / close[i]
                    if pattern_type[i] > 0:  # Bullish pattern
                        reliability[i] = 1 if future_return > 0 else -1
                    else:  # Bearish pattern
                        reliability[i] = 1 if future_return < 0 else -1
        
        return {
            'is_pattern': is_pattern.astype(int),
            'pattern_type': pattern_type,
            'strength': strength,
            'trend': trend,
            'reliability': reliability,
            'peaks': peaks,
            'troughs': troughs
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['min_peak_distance'] < 2:
            raise ValueError("Minimum peak distance must be at least 2")
        if not 0 < self.params['min_peak_height'] < 1:
            raise ValueError("Minimum peak height must be between 0 and 1")
        if not 0 < self.params['price_tolerance'] < 1:
            raise ValueError("Price tolerance must be between 0 and 1")
        return True 