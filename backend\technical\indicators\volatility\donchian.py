from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class DonchianChannelsIndicator(BaseIndicator):
    """Donchian Channels indicator."""

    def __init__(self, period: int = 20):
        """
        Initialize Donchian Channels indicator.

        Args:
            period: The lookback period for calculating the highest high and lowest low.
        """
        super().__init__({'period': period})

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Donchian Channels values."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period']:
             return {'upper_band': np.array([]), 'lower_band': np.array([]), 'middle_band': np.array([])}

        period = self.params['period']

        high = df['high']
        low = df['low']

        # Calculate Upper Band (Highest High over the period)
        upper_band = high.rolling(window=period).max()

        # Calculate Lower Band (Lowest Low over the period)
        lower_band = low.rolling(window=period).min()

        # Calculate Middle Band
        middle_band = (upper_band + lower_band) / 2

        self._values = {
            'upper_band': upper_band.values,
            'lower_band': lower_band.values,
            'middle_band': middle_band.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        return True