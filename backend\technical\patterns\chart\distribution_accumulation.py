from typing import Dict, Any, List
import numpy as np
import pandas as pd
from scipy.signal import find_peaks

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class DistributionAccumulationPatternIndicator(BaseIndicator):
    """Pattern indicator for detecting transitions between consolidation and distribution/accumulation phases."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize Distribution/Accumulation pattern indicator.
        
        Args:
            params: Dictionary containing parameters:
                - lookback_period: Period for analyzing volume and price patterns (default: 20)
                - volume_threshold: Volume increase threshold vs average (default: 1.5)
                - price_threshold: Maximum price movement for consolidation (default: 0.02 or 2%)
                - consolidation_threshold: Maximum volatility for consolidation (default: 0.01 or 1%)
        """
        super().__init__(params)
        self.lookback_period = self.params.get('lookback_period', 20)
        self.volume_threshold = self.params.get('volume_threshold', 1.5)
        self.price_threshold = self.params.get('price_threshold', 0.02)
        self.consolidation_threshold = self.params.get('consolidation_threshold', 0.01)

    def _detect_phase_transition(self, prices: np.ndarray, volumes: np.ndarray,
                               start_idx: int) -> tuple:
        """Detect transitions between consolidation and distribution/accumulation."""
        if start_idx < self.lookback_period:
            return False, 0, 0
            
        window = slice(start_idx - self.lookback_period, start_idx + 1)
        price_window = prices[window]
        volume_window = volumes[window]
        
        # Check for consolidation phase
        price_range = (max(price_window) - min(price_window)) / np.mean(price_window)
        price_volatility = np.std(price_window) / np.mean(price_window)
        
        is_consolidation = (price_range <= self.price_threshold and 
                          price_volatility <= self.consolidation_threshold)
        
        if not is_consolidation:
            return False, 0, 0
            
        # Analyze volume patterns
        avg_volume = np.mean(volume_window[:-5])  # Exclude most recent volumes
        recent_volume = np.mean(volume_window[-5:])  # Last 5 periods
        
        volume_increase = recent_volume > avg_volume * self.volume_threshold
        
        if not volume_increase:
            return False, 0, 0
            
        # Analyze price action for distribution/accumulation
        recent_prices = price_window[-5:]
        price_direction = np.mean(np.diff(recent_prices))
        
        # Distribution: High volume + Price weakness after consolidation
        if price_direction < 0:
            pattern_type = 1  # Distribution
        # Accumulation: High volume + Price strength after consolidation
        else:
            pattern_type = -1  # Accumulation
            
        return True, pattern_type, price_volatility

    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """
        Calculate Distribution/Accumulation pattern values.
        
        Args:
            market_data: Market data containing OHLCV values
            
        Returns:
            Dictionary containing:
                - is_pattern: Boolean array indicating pattern presence
                - pattern_type: Integer array indicating pattern type (1: Distribution, -1: Accumulation)
                - pattern_id: Integer array for pattern instance grouping
                - strength: Float array indicating pattern strength
                - phase_score: Float array indicating quality of phase transition
                - volume_profile: Float array indicating volume characteristics
                - trend: Integer array indicating trend context
                - reliability: Float array indicating pattern reliability
        """
        df = market_data.to_dataframe()
        
        close = df['close'].values
        volume = df['volume'].values
        
        is_pattern = np.zeros_like(close)
        pattern_type = np.zeros_like(close)  # 1=Distribution, -1=Accumulation
        pattern_id = np.zeros_like(close)
        phase_score = np.zeros_like(close)  # Strength of phase transition
        
        # Detect phase transitions
        for i in range(self.lookback_period, len(close)):
            is_valid, p_type, volatility = self._detect_phase_transition(close, volume, i)
            if is_valid:
                window = slice(i-5, i+1)  # Mark the transition period
                is_pattern[window] = 1
                pattern_type[window] = p_type
                pattern_id[window] = i
                
                # Calculate phase transition score
                volume_ratio = np.mean(volume[i-5:i+1]) / np.mean(volume[i-20:i-5])
                price_change = abs(close[i] - close[i-5]) / close[i-5]
                phase_score[window] = (volume_ratio * (1 - volatility) * 
                                    (1 + price_change)) / 3
        
        # Calculate additional characteristics
        strength = np.zeros_like(close)
        volume_profile = np.zeros_like(close)
        
        for i in range(len(close)):
            if is_pattern[i]:
                # Pattern strength based on:
                # 1. Volume surge relative to average
                # 2. Price movement consistency
                # 3. Previous consolidation quality
                window = slice(max(0, i-20), i+1)
                avg_volume = np.mean(volume[window])
                volume_surge = volume[i] / avg_volume
                
                price_changes = np.diff(close[max(0, i-5):i+1])
                price_consistency = np.mean(np.sign(price_changes) == np.sign(pattern_type[i]))
                
                strength[i] = (volume_surge * price_consistency * phase_score[i])
                
                # Volume profile characteristics
                volume_profile[i] = (volume[i] - avg_volume) / avg_volume
        
        # Calculate trend context using 20-period SMA
        sma20 = df['close'].rolling(window=20).mean()
        trend = np.where(df['close'] > sma20, 1, -1)
        
        # Calculate reliability based on future price movement
        reliability = np.zeros_like(close)
        future_window = 20
        
        for i in range(len(close) - future_window):
            if is_pattern[i]:
                future_returns = (df['close'].iloc[i+1:i+future_window+1].values - 
                                df['close'].iloc[i]) / df['close'].iloc[i]
                
                if pattern_type[i] == 1:  # Distribution
                    max_return = np.max(future_returns)
                    min_return = np.min(future_returns)
                    reliability[i] = max(0, min(1, -min_return))
                else:  # Accumulation
                    max_return = np.max(future_returns)
                    min_return = np.min(future_returns)
                    reliability[i] = max(0, min(1, max_return))
        
        return {
            'is_pattern': is_pattern,
            'pattern_type': pattern_type,
            'pattern_id': pattern_id,
            'strength': strength,
            'phase_score': phase_score,
            'volume_profile': volume_profile,
            'trend': trend,
            'reliability': reliability
        }
    
    def validate_params(self) -> None:
        """
        Validate indicator parameters.
        
        Raises:
            ValueError: If parameters are invalid
        """
        if self.lookback_period < 10:
            raise ValueError("lookback_period must be at least 10")
        if self.volume_threshold <= 1:
            raise ValueError("volume_threshold must be greater than 1")
        if not 0 < self.price_threshold < 1:
            raise ValueError("price_threshold must be between 0 and 1")
        if not 0 < self.consolidation_threshold < 1:
            raise ValueError("consolidation_threshold must be between 0 and 1") 