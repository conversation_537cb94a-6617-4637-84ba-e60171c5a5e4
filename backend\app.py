from flask import Flask, jsonify, request
import logging
import os
import time
import json

from datetime import datetime, timezone
from firebase_admin import firestore # For firestore.And
from google.cloud.firestore_v1.base_query import FieldFilter # For FieldFilter

from .api.connection import connection_bp
from .api.market_data import market_data_bp
from .api.signals import signals_bp
from .api.trade_execution import trade_bp
from .api.trade_operations import trade_ops_bp
from .api.trade_management import trade_mgmt_bp  # Advanced trade management
from .api.basket_management import basket_mgmt_bp  # Trade basket management
from .api.advanced_orders import advanced_orders_bp  # Advanced order types
from .api.exit_strategies import exit_strategies_bp  # Algorithmic exit strategies
from .api.autonomous import autonomous_bp
from flask_cors import CORS
from backend.mt5_integration import MT5Integration
# Updated import: get_firestore_client is now the primary way to access the client
from backend.firebase_utils import get_firestore_client
# Import the scheduler for periodic trading operations
from backend.trade_scheduler import TradeScheduler
from backend.api.entry_spacing_api import entry_spacing_bp

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def create_app():
    app = Flask(__name__)

    # Configure CORS
    CORS(app, resources={r"/api/*": {"origins": ["http://localhost:3000", "http://127.0.0.1:3000"]}})

    # Initialize MT5 on startup only in main process
    # Also initialize Firebase here
    if not app.debug or os.environ.get("WERKZEUG_RUN_MAIN") == "true": # Ensure this runs only in the main process
        # Attempt to get Firestore client, which also initializes Firebase if needed
        firestore_client_on_startup = get_firestore_client()
        if not firestore_client_on_startup:
            logger.critical("Firebase Admin SDK could not be initialized on startup. License verification will fail.")
            # Depending on your policy, you might want to exit the app here
            # or allow it to run with license checks failing (which means connections might be denied).
        else:
            logger.info("Firebase Admin SDK initialized (or already was) on startup via get_firestore_client().")

        mt5_instance = None
        # Instantiate MT5Integration but do not attempt to initialize/connect here.
        # The frontend will control connection attempts via API calls.
        mt5_instance = MT5Integration()
        logger.info("MT5Integration instance created. Will connect based on frontend requests or saved settings via API.")

    else:
        # For Werkzeug reloader child processes, also just instantiate.
        mt5_instance = MT5Integration()
        logger.info("Werkzeug reloader process: MT5Integration instance created.")

    # Make MT5 instance available to blueprints
    app.config['MT5_INSTANCE'] = mt5_instance

    # Initialize and start the trade scheduler
    scheduler = TradeScheduler(app)
    # Start the scheduler with a 5-second interval for updating trailing stops
    scheduler.start(interval=5)
    app.config['TRADE_SCHEDULER'] = scheduler

    # Register blueprints
    app.register_blueprint(connection_bp, url_prefix='/api/connection')
    app.register_blueprint(market_data_bp, url_prefix='/api/market')
    app.register_blueprint(signals_bp, url_prefix='/api/signals')
    app.register_blueprint(trade_bp, url_prefix='/api/trade')
    app.register_blueprint(autonomous_bp, url_prefix='/api/autonomous')
    app.register_blueprint(trade_ops_bp, url_prefix='/api/trade-ops')
    app.register_blueprint(trade_mgmt_bp, url_prefix='/api/trade-management')  # Register trade management blueprint
    app.register_blueprint(basket_mgmt_bp)  # Register basket management blueprint
    app.register_blueprint(advanced_orders_bp)  # Register advanced orders blueprint
    app.register_blueprint(exit_strategies_bp)  # Register algorithmic exit strategies blueprint
    app.register_blueprint(entry_spacing_bp)

    # Health check endpoint
    @app.route('/health', methods=['GET'])
    def health_check():
        """Basic health check endpoint"""
        return jsonify({
            "status": "healthy",
            "mt5_initialized": app.config['MT5_INSTANCE'] is not None,
            "mt5_connected": app.config['MT5_INSTANCE'].is_connected() if app.config['MT5_INSTANCE'] else False
        }), 200

    @app.route('/api/verify_account', methods=['POST'])
    def verify_account():
        # Get Firestore client using the new utility function
        current_firestore_client = get_firestore_client()
        if not current_firestore_client:
            logger.warning("Firestore client could not be obtained via get_firestore_client() - skipping account verification.")
            # For dev/testing, you might allow skipping. For prod, this should be an error.
            # Let's keep the skip for now to match previous behavior under this condition.
            return jsonify({"authorized": True, "message": "Verification skipped (Firestore client unavailable)."}), 200

        try:
            data = request.get_json()
            if not data:
                logger.warning("Verification attempt with missing JSON payload.")
                return jsonify({"authorized": False, "reason": "bad_request", "message": "Missing JSON payload."}), 400

            account_number = data.get('account_number')
            server_name = data.get('server_name')

            if not account_number or not server_name:
                logger.warning(f"Verification attempt with missing account_number or server_name. Received: acc_num={account_number}, srv_name={server_name}")
                return jsonify({"authorized": False, "reason": "bad_request", "message": "Missing account_number or server_name."}), 400

            logger.info(f"Verifying account: {account_number} on server: {server_name}")

            query_filters = [
                FieldFilter('account_number', '==', str(account_number)),
                FieldFilter('server_name', '==', server_name.strip()) # Added strip() for consistency
            ]
            docs_stream = current_firestore_client.collection('authorized_accounts') \
                .where(filter=firestore.And(query_filters)) \
                .limit(1) \
                .stream()

            doc_found = False
            for doc in docs_stream:
                doc_found = True
                account_data = doc.to_dict()
                logger.debug(f"Found account data for {account_number}: {account_data}")

                status_field = account_data.get('status')
                is_status_active = (status_field == 'active')

                plan_end_timestamp = account_data.get('plan_end_date') # Firestore Timestamp
                is_plan_valid = False

                if plan_end_timestamp:
                    # Firestore Timestamps are comparable with Python timezone-aware datetimes
                    current_time_utc = datetime.now(timezone.utc)
                    # Ensure plan_end_timestamp is timezone-aware if it's a naive datetime
                    if isinstance(plan_end_timestamp, datetime) and plan_end_timestamp.tzinfo is None:
                        plan_end_timestamp = plan_end_timestamp.replace(tzinfo=timezone.utc)

                    is_plan_valid = plan_end_timestamp > current_time_utc
                    logger.debug(f"Account {account_number} - Plan end date: {plan_end_timestamp}, Current UTC: {current_time_utc}, Is plan valid: {is_plan_valid}")
                else:
                    logger.warning(f"Account {account_number} - Plan end date not found in Firestore document.")

                if is_status_active and is_plan_valid:
                    user_name = account_data.get("user_name", "N/A")
                    plan_type = account_data.get("plan_type", "N/A")
                    logger.info(f"Account {account_number} VERIFIED for user: {user_name}.")
                    return jsonify({
                        "authorized": True,
                        "user_name": user_name,
                        "plan_type": plan_type,
                        "plan_end_date": plan_end_timestamp.isoformat() if plan_end_timestamp else None,
                        "status": status_field,
                        "message": "Account verified."
                    }), 200
                else:
                    reason = "license_inactive_or_expired"
                    message = "Account license is inactive or subscription has expired."
                    if not is_status_active:
                        logger.warning(f"Account {account_number} license is INACTIVE. status: {status_field}")
                    if not is_plan_valid:
                        logger.warning(f"Account {account_number} subscription EXPIRED or invalid. End date: {plan_end_timestamp}")
                    return jsonify({"authorized": False, "reason": reason, "message": message}), 403

            if not doc_found:
                logger.warning(f"Account {account_number} with server {server_name} NOT FOUND in Firestore.")
                return jsonify({"authorized": False, "reason": "not_found", "message": "Account not found or server name mismatch."}), 404

        except Exception as e:
            logger.exception(f"Error during account verification for {account_number}: {e}")
            return jsonify({"authorized": False, "reason": "server_error", "message": "Error verifying account."}), 500

    return app

# Create the application instance
app = create_app()

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=True, use_reloader=True)
