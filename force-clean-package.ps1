# PowerShell script to aggressively clean and package the application

# Set console colors for better readability
$host.UI.RawUI.ForegroundColor = "Cyan"
Write-Output "🚀 GarudaAlgo Force Clean Packaging Script"
$host.UI.RawUI.ForegroundColor = "White"

# Kill any running processes that might lock files
Write-Output "📋 Aggressively stopping any running instances..."
Get-Process -Name "electron*" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Get-Process -Name "node*" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Get-Process -Name "npm*" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Get-Process -Name "garuda*" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue

# Force close any processes that might be locking the app.asar file
Write-Output "📋 Checking for processes locking app.asar..."
$asarPath = "release-new\win-unpacked\resources\app.asar"
if (Test-Path -Path $asarPath) {
    Write-Output "Found app.asar, attempting to force unlock..."
    # This is a more aggressive approach to kill processes locking files
    # It's not ideal, but it might work in this case
    try {
        # Try to rename the file, which will fail if it's locked
        Rename-Item -Path $asarPath -NewName "app.asar.old" -ErrorAction Stop
        # If we get here, the file wasn't locked
        Rename-Item -Path "app.asar.old" -NewName "app.asar" -ErrorAction SilentlyContinue
        Write-Output "File was not locked."
    } catch {
        Write-Output "File is locked. Attempting to force cleanup..."
        # Try to force delete the directory
        Remove-Item -Path "release-new" -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# Clean up previous builds with a more aggressive approach
Write-Output "🧹 Aggressively cleaning previous builds..."
if (Test-Path -Path "release-new") {
    try {
        # Try to force remove the directory
        Remove-Item -Path "release-new" -Recurse -Force -ErrorAction Stop
        Write-Output "Successfully removed release-new directory."
    } catch {
        Write-Output "Failed to remove release-new directory. Creating a new output directory instead."
    }
}

# Create a completely new output directory
$outputDir = "release-fresh-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
Write-Output "📋 Creating new output directory: $outputDir"
New-Item -Path $outputDir -ItemType Directory -Force | Out-Null

# Update package.json to use the new output directory
Write-Output "📋 Updating package.json to use new output directory..."
$packageJson = Get-Content -Path "package.json" -Raw | ConvertFrom-Json
$packageJson.build.directories.output = $outputDir
$packageJson | ConvertTo-Json -Depth 10 | Set-Content -Path "package.json"

# Verify backend executable exists
if (-not (Test-Path -Path "dist\garuda_backend.exe")) {
    $host.UI.RawUI.ForegroundColor = "Red"
    Write-Output "❌ ERROR: Backend executable not found at dist\garuda_backend.exe"
    Write-Output "   Please build it first with: pyinstaller garuda_backend.spec"
    $host.UI.RawUI.ForegroundColor = "White"
    exit 1
}

# Build React frontend
Write-Output "🔨 Building React frontend..."
npm run build

# Copy the backend executable to the project root
Write-Output "📋 Copying backend executable..."
Copy-Item -Path "dist\garuda_backend.exe" -Destination "." -Force

# Run electron-builder with a clean environment
Write-Output "📦 Packaging application with electron-builder..."
npx electron-builder --win --x64 --dir

# Clean up copied files
Write-Output "🧹 Cleaning up..."
Remove-Item -Path "garuda_backend.exe" -Force -ErrorAction SilentlyContinue

# Restore the original output directory in package.json
Write-Output "📋 Restoring package.json..."
$packageJson.build.directories.output = "release-new"
$packageJson | ConvertTo-Json -Depth 10 | Set-Content -Path "package.json"

Write-Output "Build process complete. Check the $outputDir directory for the packaged application."
