"""
Main window for the Garuda Algo Admin Panel.
"""
import logging
from PyQt5.QtWidgets import Q<PERSON>ain<PERSON><PERSON>ow, QTabWidget, QStatusBar, QWidget, QLabel, QVBoxLayout
from PyQt5.QtCore import QSettings, pyqtSlot

# Import admin-specific widgets (to be created)
# from .widgets.account_manager import AccountManagerWidget
# from .widgets.session_monitor import SessionMonitorWidget

# Import shared utilities if needed
# from src.utils import config as app_config

class AdminMainWindow(QMainWindow):
    """Admin Panel Main Window."""

    def __init__(self, settings: QSettings):
        super().__init__()
        self.settings = settings
        logging.info("AdminMainWindow: Initializing...")

        self.setWindowTitle("Garuda Algo - Admin Panel")
        self.setMinimumSize(1000, 700)
        # TODO: Set admin-specific icon?

        # --- Central Widget ---
        self.tabs = QTabWidget()
        self.setCentralWidget(self.tabs)

        # --- Status Bar ---
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Admin Panel Ready.")

        # --- Initialize UI Tabs ---
        self._init_ui_tabs()

        logging.info("AdminMainWindow: Initialization complete.")

    def _init_ui_tabs(self):
        """Create and add the main admin UI tabs."""
        logging.info("Initializing Admin UI tabs...")

        # Placeholder widgets for now
        self.account_manager_tab = QWidget()
        layout1 = QVBoxLayout(self.account_manager_tab)
        layout1.addWidget(QLabel("Account Management Placeholder"))

        self.session_monitor_tab = QWidget()
        layout2 = QVBoxLayout(self.session_monitor_tab)
        layout2.addWidget(QLabel("Session Monitoring Placeholder"))

        # Add tabs
        self.tabs.addTab(self.account_manager_tab, "Account Management")
        self.tabs.addTab(self.session_monitor_tab, "Session Monitoring")

        logging.info("Admin UI tabs initialized.")

    def closeEvent(self, event):
        logging.info("Admin Panel closing.")
        # Add any cleanup specific to the admin panel if needed
        super().closeEvent(event)
