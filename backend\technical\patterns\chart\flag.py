from typing import Dict, Any, List
import numpy as np
import pandas as pd
from scipy.signal import find_peaks

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class FlagPatternIndicator(BaseIndicator):
    """Flag chart pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - min_pole_height: Parameter description (default: 0.1)
                - max_flag_width: Parameter description (default: 20)
                - min_pole_width: Parameter description (default: 5)
                - price_tolerance: Parameter description (default: 0.02)
                - parallel_tolerance: Parameter description (default: 0.1)
        """
        default_params = {
            "min_pole_height": 0.1,
            "max_flag_width": 20,
            "min_pole_width": 5,
            "price_tolerance": 0.02,
            "parallel_tolerance": 0.1,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)


    def _find_pole(self, prices: np.ndarray, start_idx: int, is_bullish: bool) -> tuple:
        """Find potential flag pole."""
        if start_idx < self.params['min_pole_width']:
            return None, None
            
        pole_prices = prices[start_idx-self.params['min_pole_width']:start_idx+1]
        
        if is_bullish:
            pole_start = np.argmin(pole_prices) + (start_idx-self.params['min_pole_width'])
            pole_end = np.argmax(pole_prices) + (start_idx-self.params['min_pole_width'])
        else:
            pole_start = np.argmax(pole_prices) + (start_idx-self.params['min_pole_width'])
            pole_end = np.argmin(pole_prices) + (start_idx-self.params['min_pole_width'])
            
        pole_height = abs(prices[pole_end] - prices[pole_start])
        if pole_height / prices[pole_start] < self.params['min_pole_height']:
            return None, None
            
        return pole_start, pole_end

    def _is_flag(self, prices: np.ndarray, start_idx: int,
                is_bullish: bool = True) -> tuple:
        """Identify Flag patterns."""
        if len(prices) - start_idx < self.params['max_flag_width']:
            return False, 0, 0
            
        # Find flag pole
        pole_start, pole_end = self._find_pole(prices, start_idx, is_bullish)
        if pole_start is None or pole_end is None:
            return False, 0, 0
            
        # Extract potential flag region
        flag_end = min(start_idx + self.params['max_flag_width'], len(prices))
        flag_prices = prices[start_idx:flag_end]
        
        # Find peaks and troughs in flag
        peaks, _ = find_peaks(flag_prices)
        troughs, _ = find_peaks(-flag_prices)
        
        if len(peaks) < 2 or len(troughs) < 2:
            return False, 0, 0
            
        # Calculate trend lines
        peak_x = peaks
        peak_y = flag_prices[peaks]
        peak_slope, peak_intercept = np.polyfit(peak_x, peak_y, 1)
        
        trough_x = troughs
        trough_y = flag_prices[troughs]
        trough_slope, trough_intercept = np.polyfit(trough_x, trough_y, 1)
        
        # Check if trend lines are roughly parallel
        slope_diff = abs(peak_slope - trough_slope)
        avg_slope = (abs(peak_slope) + abs(trough_slope)) / 2
        if slope_diff / avg_slope > self.params['parallel_tolerance']:
            return False, 0, 0
        
        # Check flag direction
        if is_bullish:
            # Bullish flag should slope downward
            if peak_slope >= 0 or trough_slope >= 0:
                return False, 0, 0
        else:
            # Bearish flag should slope upward
            if peak_slope <= 0 or trough_slope <= 0:
                return False, 0, 0
                
        # Check price channel width
        channel_width = abs(peak_intercept - trough_intercept)
        if channel_width / prices[start_idx] > self.params['price_tolerance']:
            return False, 0, 0
            
        return True, pole_start, flag_end

    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        close = df['close'].values
        is_pattern = np.zeros_like(close)
        pattern_type = np.zeros_like(close)
        
        # Scan for bullish flags
        for i in range(len(close)):
            is_valid, start, end = self._is_flag(close, i, is_bullish=True)
            if is_valid:
                is_pattern[start:end] = 1
                pattern_type[start:end] = 1
                
        # Scan for bearish flags
        for i in range(len(close)):
            is_valid, start, end = self._is_flag(close, i, is_bullish=False)
            if is_valid:
                is_pattern[start:end] = 1
                pattern_type[start:end] = -1
        
        # Calculate pattern strength
        strength = np.zeros_like(close)
        for i in range(len(close)):
            if is_pattern[i]:
                # Calculate the height of the pattern relative to price
                if pattern_type[i] > 0:  # Bullish pattern
                    pattern_height = max(close[i:i+self.params['max_flag_width']]) - min(close[i:i+self.params['max_flag_width']])
                else:  # Bearish pattern
                    pattern_height = max(close[i:i+self.params['max_flag_width']]) - min(close[i:i+self.params['max_flag_width']])
                strength[i] = pattern_height / close[i]
        
        # Calculate trend context
        trend = np.zeros_like(close)
        for i in range(1, len(close)):
            if i >= 20:  # Use 20-period SMA for trend
                sma = np.mean(close[i-20:i])
                trend[i] = 1 if close[i] > sma else -1
        
        # Calculate pattern reliability
        reliability = np.zeros_like(close)
        for i in range(1, len(close)):
            if is_pattern[i]:
                # Check if price moved in the expected direction
                if i < len(close)-1:
                    future_return = (close[i+1] - close[i]) / close[i]
                    if pattern_type[i] > 0:  # Bullish pattern
                        reliability[i] = 1 if future_return > 0 else -1
                    else:  # Bearish pattern
                        reliability[i] = 1 if future_return < 0 else -1
        
        return {
            'is_pattern': is_pattern.astype(int),
            'pattern_type': pattern_type,  # 1 for Bullish, -1 for Bearish
            'strength': strength,
            'trend': trend,
            'reliability': reliability
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if not 0 < self.params['min_pole_height'] < 1:
            raise ValueError("Minimum pole height must be between 0 and 1")
        if self.params['max_flag_width'] < 10:
            raise ValueError("Maximum flag width must be at least 10 periods")
        if self.params['min_pole_width'] < 3:
            raise ValueError("Minimum pole width must be at least 3 periods")
        if not 0 < self.params['price_tolerance'] < 1:
            raise ValueError("Price tolerance must be between 0 and 1")
        if not 0 < self.params['parallel_tolerance'] < 1:
            raise ValueError("Parallel tolerance must be between 0 and 1")
        return True 