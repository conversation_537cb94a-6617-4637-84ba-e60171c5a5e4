import React, { useState } from 'react';
import { safeToFixed } from '../../utils/numberUtils';

/**
 * SignalGeneratorCard - Displays current trading signals with entry/exit points
 * 
 * @param {Object} analysisData - The full analysis data object
 * @returns {JSX.Element} - The rendered signal generator card
 */
function SignalGeneratorCard({ analysisData = {} }) {
  const [expanded, setExpanded] = useState(false);
  
  // Extract current price from analysis data
  const currentPrice = 
    analysisData?.current_price?.bid || 
    analysisData?.support_resistance?.current_price?.bid ||
    analysisData?.current_price?.last || 
    analysisData?.support_resistance?.current_price?.last || 0;
  
  // Get signal recommendation data from the API
  // If none is available, use a neutral state without specific values
  const signalData = analysisData?.signal_recommendation || null;
  
  // If no signal data is available, show a message instead of hardcoded values
  
  // Helper to get signal color
  const getSignalColor = (signal) => {
    if (!signal) return 'var(--text-secondary)';
    
    switch(signal.toUpperCase()) {
      case 'STRONG_BUY':
      case 'STRONG BUY':
        return 'var(--success-dark, var(--success))'; // Darker green
      case 'BUY':
        return 'var(--success)';
      case 'NEUTRAL':
        return 'var(--warning)';
      case 'SELL':
        return 'var(--error)';
      case 'STRONG_SELL':
      case 'STRONG SELL':
        return 'var(--error-dark, var(--error))'; // Darker red
      default:
        return 'var(--text-secondary)';
    }
  };
  
  // Format signal text for display
  const formatSignalText = (signal) => {
    if (!signal) return 'NO SIGNAL';
    
    return signal.replace('_', ' ');
  };
  
  // Calculate potential profit/loss
  const calculatePotentialPoints = () => {
    if (!signalData) return { profit: 0, loss: 0 };
    
    const signalType = signalData.signal.toUpperCase();
    let profit = 0;
    let loss = 0;
    
    if (signalType.includes('BUY')) {
      profit = Math.abs(signalData.take_profit - signalData.entry_price);
      loss = Math.abs(signalData.entry_price - signalData.stop_loss);
    } else if (signalType.includes('SELL')) {
      profit = Math.abs(signalData.entry_price - signalData.take_profit);
      loss = Math.abs(signalData.stop_loss - signalData.entry_price);
    }
    
    const instrument = analysisData?.symbol || 'Unknown';
    const isForex = instrument.includes('USD') || 
                   instrument.includes('EUR') || 
                   instrument.includes('GBP') || 
                   instrument.includes('JPY');
    
    // Convert to pips for forex
    if (isForex) {
      const pipMultiplier = instrument.includes('JPY') ? 100 : 10000;
      profit = safeToFixed(profit * pipMultiplier, 1, 'N/A') + ' pips';
      loss = safeToFixed(loss * pipMultiplier, 1, 'N/A') + ' pips';
    } else {
      // For indices and other instruments
      profit = safeToFixed(profit, 2, 'N/A') + ' points';
      loss = safeToFixed(loss, 2, 'N/A') + ' points';
    }
    
    return { profit, loss };
  };
  
  const { profit, loss } = calculatePotentialPoints();
  
  // Helper to get indicator summary
  const getIndicatorSummary = () => {
    if (!signalData?.indicators) return [];
    
    const indicators = [];
    
    if (signalData.indicators.trend) {
      indicators.push({
        name: 'Market Trend',
        value: signalData.indicators.trend.replace('_', ' '),
        color: signalData.indicators.trend.includes('BULL') ? 'var(--success)' : 'var(--error)'
      });
    }
    
    if (signalData.indicators.support_resistance) {
      indicators.push({
        name: 'Price Structure',
        value: signalData.indicators.support_resistance.replace('_', ' '),
        color: signalData.indicators.support_resistance.includes('SUPPORT') ? 'var(--success)' : 'var(--error)'
      });
    }
    
    if (signalData.indicators.rsi) {
      indicators.push({
        name: 'RSI',
        value: signalData.indicators.rsi.replace('_', ' '),
        color: signalData.indicators.rsi.includes('OVERSOLD') ? 'var(--success)' : 
               signalData.indicators.rsi.includes('OVERBOUGHT') ? 'var(--error)' : 'var(--warning)'
      });
    }
    
    if (signalData.indicators.macd) {
      indicators.push({
        name: 'MACD',
        value: signalData.indicators.macd.replace('_', ' '),
        color: signalData.indicators.macd.includes('BULL') ? 'var(--success)' : 'var(--error)'
      });
    }
    
    return indicators;
  };
  
  // Empty state when no signal is available
  if (!signalData || !signalData.signal) {
    return (
      <div className="bento-card signal-generator-card" style={{ padding: '15px', borderRadius: '8px', background: 'var(--card)', border: '1px solid var(--border)' }}>
        <h3>Signal Generator</h3>
        <div style={{ textAlign: 'center', padding: '30px 0', color: 'var(--text-secondary)' }}>
          <div style={{ fontSize: '1.2em', marginBottom: '10px' }}>No trading signals available</div>
          <div style={{ fontSize: '0.9em' }}>Waiting for market conditions to generate signals</div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="bento-card signal-generator-card" 
         style={{ padding: '15px', borderRadius: '8px', background: 'var(--card)', border: '1px solid var(--border)' }}>
      
      {/* Card Header */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
        <h3 style={{ margin: 0, color: 'var(--text)' }}>Signal Generator</h3>
        <div style={{ 
          backgroundColor: 'var(--background-secondary)', 
          borderRadius: '4px', 
          padding: '4px 8px',
          fontSize: '0.8em',
          color: 'var(--text-secondary)'
        }}>
          Last updated: {new Date().toLocaleTimeString()}
        </div>
      </div>
      
      {/* Signal Summary */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        backgroundColor: 'var(--background-secondary)',
        borderRadius: '8px',
        padding: '15px',
        marginBottom: '15px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ 
            fontSize: '2em', 
            fontWeight: 'bold', 
            color: getSignalColor(signalData.signal),
            marginRight: '15px'
          }}>
            {formatSignalText(signalData.signal)}
          </div>
          <div>
            <div style={{ fontSize: '0.9em', color: 'var(--text-secondary)', marginBottom: '5px' }}>Confidence</div>
            <div style={{ position: 'relative', width: '120px', height: '6px', backgroundColor: 'var(--border)', borderRadius: '3px' }}>
              <div style={{ 
                position: 'absolute', 
                top: 0, 
                left: 0, 
                height: '100%', 
                width: `${signalData.confidence}%`,
                backgroundColor: getSignalColor(signalData.signal),
                borderRadius: '3px'
              }}></div>
            </div>
            <div style={{ fontSize: '0.8em', color: 'var(--text-secondary)', marginTop: '5px' }}>{signalData.confidence}% confidence</div>
          </div>
        </div>
        <div style={{ textAlign: 'right' }}>
          <div style={{ fontSize: '0.9em', color: 'var(--text-secondary)', marginBottom: '5px' }}>Risk/Reward</div>
          <div style={{ fontSize: '1.2em', fontWeight: 'bold', color: 'var(--text)' }}>{safeToFixed(signalData.risk_reward, 1)}</div>
          <div style={{ fontSize: '0.8em', color: 'var(--text-secondary)', display: 'flex', justifyContent: 'flex-end', gap: '8px', marginTop: '5px' }}>
            <span style={{ color: 'var(--error)' }}>{loss} risk</span>
            <span>:</span>
            <span style={{ color: 'var(--success)' }}>{profit} reward</span>
          </div>
        </div>
      </div>
      
      {/* Price Levels */}
      <div style={{ 
        display: 'flex',
        gap: '10px',
        marginBottom: expanded ? '20px' : '15px'
      }}>
        <div style={{ flex: '1', backgroundColor: 'var(--background-secondary)', borderRadius: '6px', padding: '10px' }}>
          <div style={{ fontSize: '0.8em', color: 'var(--text-secondary)', marginBottom: '5px' }}>Entry Price</div>
          <div style={{ fontSize: '1.1em', fontWeight: 'bold', color: 'var(--text)' }}>{safeToFixed(signalData.entry_price, 5)}</div>
        </div>
        <div style={{ flex: '1', backgroundColor: 'var(--background-secondary)', borderRadius: '6px', padding: '10px' }}>
          <div style={{ fontSize: '0.8em', color: 'var(--text-secondary)', marginBottom: '5px' }}>Stop Loss</div>
          <div style={{ fontSize: '1.1em', fontWeight: 'bold', color: 'var(--error)' }}>{safeToFixed(signalData.stop_loss, 5)}</div>
        </div>
        <div style={{ flex: '1', backgroundColor: 'var(--background-secondary)', borderRadius: '6px', padding: '10px' }}>
          <div style={{ fontSize: '0.8em', color: 'var(--text-secondary)', marginBottom: '5px' }}>Take Profit</div>
          <div style={{ fontSize: '1.1em', fontWeight: 'bold', color: 'var(--success)' }}>{safeToFixed(signalData.take_profit, 5)}</div>
        </div>
      </div>
      
      {/* Expandable Details Section */}
      <div style={{ cursor: 'pointer', textAlign: 'center' }} onClick={() => setExpanded(!expanded)}>
        <span style={{ fontSize: '0.9em', color: 'var(--text-secondary)' }}>{expanded ? 'Hide details' : 'Show details'}</span>
        <span style={{ marginLeft: '5px', color: 'var(--text-secondary)' }}>{expanded ? '▲' : '▼'}</span>
      </div>
      
      {/* Expanded Content */}
      {expanded && (
        <div style={{ marginTop: '15px', backgroundColor: 'var(--background-secondary)', borderRadius: '6px', padding: '15px' }}>
          <div style={{ marginBottom: '15px' }}>
            <div style={{ fontSize: '0.9em', color: 'var(--text-secondary)', marginBottom: '8px' }}>Indicator Analysis</div>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
              {getIndicatorSummary().map((indicator, index) => (
                <div key={index} style={{ 
                  backgroundColor: 'var(--card-hover)', 
                  borderRadius: '4px', 
                  padding: '6px 10px',
                  fontSize: '0.85em'
                }}>
                  <span style={{ color: 'var(--text)' }}>{indicator.name}: </span>
                  <span style={{ color: indicator.color, fontWeight: 'bold' }}>{indicator.value}</span>
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <div style={{ fontSize: '0.9em', color: 'var(--text-secondary)', marginBottom: '8px' }}>Signal Explanation</div>
            <div style={{ fontSize: '0.9em', lineHeight: '1.5', color: 'var(--text)' }}>
              {signalData.explanation}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default SignalGeneratorCard;
