from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class VortexIndicator(BaseIndicator):
    """Vortex Indicator (VI)."""

    def __init__(self, period: int = 14):
        """
        Initialize Vortex Indicator.

        Args:
            period: The lookback period for calculation.
        """
        super().__init__({'period': period})

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Vortex Indicator (+VI, -VI)."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period'] + 1: # Need shift(1)
             return {'vi_plus': np.array([]), 'vi_minus': np.array([])}

        period = self.params['period']

        high = df['high']
        low = df['low']
        close = df['close'] # Not used directly, but good to have access
        close_prev = close.shift(1)

        # Calculate True Range (TR)
        tr1 = high - low
        tr2 = (high - close_prev).abs()
        tr3 = (low - close_prev).abs()
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

        # Calculate Vortex Movement (+VM, -VM)
        vm_plus = (high - low.shift(1)).abs()
        vm_minus = (low - high.shift(1)).abs()

        # Calculate Sums over the period
        tr_sum = tr.rolling(window=period).sum()
        vm_plus_sum = vm_plus.rolling(window=period).sum()
        vm_minus_sum = vm_minus.rolling(window=period).sum()

        # Calculate Vortex Indicators (+VI, -VI)
        # Avoid division by zero
        tr_sum_safe = tr_sum.replace(0, np.nan)
        vi_plus = vm_plus_sum / tr_sum_safe
        vi_minus = vm_minus_sum / tr_sum_safe

        # Fill initial NaNs
        vi_plus = vi_plus.fillna(0)
        vi_minus = vi_minus.fillna(0)

        self._values = {
            'vi_plus': vi_plus.values,
            'vi_minus': vi_minus.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        return True