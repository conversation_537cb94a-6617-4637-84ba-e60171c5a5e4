"""
Admin Widget - Manage authorized MT5 accounts in Firebase.
"""
import logging
from datetime import datetime
from typing import List, Dict, Any

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QLabel, QTableWidget, QPushButton,
                             QHBoxLayout, QHeaderView, QTableWidgetItem, QMessageBox)
from PyQt5.QtCore import pyqtSlot, Qt, QModelIndex, pyqtSignal
from PyQt5.QtGui import QColor, QBrush

# Import shared base widget for background tasks
from admin.ui.widgets.base_widget import BaseWorkerWidget
# Import the dialog
from admin.ui.dialogs.account_dialog import AccountDialog

class AccountManagerWidget(BaseWorkerWidget):
    accounts_updated = pyqtSignal(list)

    def __init__(self, parent=None):
        super().__init__(parent=parent, mt5_connector=None)
        self.accounts_data = []
        self._init_ui()
        self.task_successful.connect(self._handle_task_result)
        self.task_failed.connect(self._handle_task_error)
        self.refresh_accounts()

    def _init_ui(self):
        layout = QVBoxLayout(self)
        layout.addWidget(QLabel("Authorized Account Management"))

        self.accounts_table = QTableWidget()
        self.accounts_table.setColumnCount(6)
        self.accounts_table.setHorizontalHeaderLabels([
            "Account Number", "Server Name", "User Name", "Status", "Plan Type", "Plan End Date"
        ])
        self.accounts_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeToContents)
        self.accounts_table.horizontalHeader().setStretchLastSection(False)
        self.accounts_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.accounts_table.setSelectionMode(QTableWidget.SingleSelection)
        self.accounts_table.setSortingEnabled(True)
        self.accounts_table.verticalHeader().setVisible(False)
        self.accounts_table.setAlternatingRowColors(True)
        self.accounts_table.doubleClicked.connect(self._edit_account_on_double_click)
        layout.addWidget(self.accounts_table)

        button_layout = QHBoxLayout()
        add_button = QPushButton("Add Account")
        add_button.clicked.connect(self._add_account)
        button_layout.addWidget(add_button)
        edit_button = QPushButton("Edit Selected")
        edit_button.clicked.connect(self._edit_account)
        button_layout.addWidget(edit_button)
        delete_button = QPushButton("Delete Selected")
        delete_button.clicked.connect(self._delete_account)
        button_layout.addWidget(delete_button)
        button_layout.addStretch()
        refresh_button = QPushButton("Refresh List")
        refresh_button.clicked.connect(self.refresh_accounts)
        button_layout.addWidget(refresh_button)
        layout.addLayout(button_layout)

    @pyqtSlot()
    def refresh_accounts(self):
        logging.info("AccountManager: Requesting account list refresh.")
        self.start_task("admin_get_accounts")

    @pyqtSlot(str, object)
    def _handle_task_result(self, task_id: str, result: object):
        """Handle successful task results."""
        logging.debug(f"AccountManager received task_successful: {task_id}")

        if task_id.startswith("admin_get_accounts"):
            # Correctly handle the tuple (list, dict) or error dict
            accounts_list = [] # Default to empty list
            if isinstance(result, tuple) and len(result) == 2 and isinstance(result[0], list):
                accounts_list, _ = result # Unpack, ignore user_cache here
                logging.info(f"AccountManager: Received {len(accounts_list)} accounts.")
                self.accounts_data = accounts_list
                self._update_table(accounts_list)
                self.accounts_updated.emit(accounts_list)
            elif isinstance(result, dict) and "error" in result:
                 self._handle_task_error(task_id, result["error"])
                 self.accounts_updated.emit([])
            else:
                error_msg = "Invalid data format received for accounts"
                logging.error(f"AccountManager: {error_msg} - Type: {type(result)}")
                self._handle_task_error(task_id, error_msg)
                self.accounts_updated.emit([])

        elif task_id.startswith("admin_add_account") or \
             task_id.startswith("admin_update_account") or \
             task_id.startswith("admin_delete_account"):
             # Handle results for Add/Update/Delete
             if isinstance(result, dict) and result.get("success"):
                 QMessageBox.information(self, "Success", result.get("message", "Operation successful."))
                 self.refresh_accounts() # Refresh list after modification
             else:
                 error_msg = result.get("error", "Operation failed") if isinstance(result, dict) else "Unknown error"
                 self._handle_task_error(task_id, error_msg)
        else:
             logging.warning(f"AccountManager: Received result for unhandled task type: {task_id}")


    @pyqtSlot(str, str)
    def _handle_task_error(self, task_id: str, error_msg: str):
        """Handle failed task results."""
        logging.error(f"AccountManager: Task {task_id} failed: {error_msg}")
        QMessageBox.warning(self, "Operation Failed", f"Error performing action:\n{error_msg}")
        if task_id.startswith("admin_get_accounts"):
            self.accounts_table.setRowCount(0)
            self.accounts_updated.emit([])

    def _update_table(self, accounts_data: List[Dict[str, Any]]):
        """Update the table with authorized account data."""
        self.accounts_table.setSortingEnabled(False)
        self.accounts_table.setRowCount(0)
        for acc in accounts_data:
            row = self.accounts_table.rowCount()
            self.accounts_table.insertRow(row)

            acc_num = acc.get('account_number', 'N/A')
            server = acc.get('server_name', 'N/A')
            user = acc.get('user_name', '')
            status = acc.get('status', 'unknown')
            plan = acc.get('plan_type', 'N/A')
            end_date = acc.get('plan_end_date_str', 'N/A')

            item_acc = QTableWidgetItem(acc_num); item_acc.setData(Qt.UserRole, acc.get('id'))
            item_srv = QTableWidgetItem(server)
            item_usr = QTableWidgetItem(user)
            item_stat = QTableWidgetItem(status.capitalize())
            item_plan = QTableWidgetItem(plan)
            item_end = QTableWidgetItem(end_date)

            if status == 'active': item_stat.setForeground(QBrush(QColor("green")))
            elif status == 'inactive' or status == 'expired': item_stat.setForeground(QBrush(QColor("red")))
            else: item_stat.setForeground(QBrush(QColor("orange")))

            self.accounts_table.setItem(row, 0, item_acc)
            self.accounts_table.setItem(row, 1, item_srv)
            self.accounts_table.setItem(row, 2, item_usr)
            self.accounts_table.setItem(row, 3, item_stat)
            self.accounts_table.setItem(row, 4, item_plan)
            self.accounts_table.setItem(row, 5, item_end)

        self.accounts_table.resizeColumnsToContents()
        self.accounts_table.resizeRowsToContents()
        self.accounts_table.setSortingEnabled(True)

    @pyqtSlot()
    def _add_account(self):
        """Show dialog to add a new account."""
        dialog = AccountDialog(parent=self)
        if dialog.exec_():
            new_data = dialog.get_data()
            if new_data:
                logging.info(f"Attempting to add account: {new_data}")
                params_for_add = new_data.copy()
                params_for_add.pop('status', None)
                self.start_task("admin_add_account", params_for_add)

    @pyqtSlot()
    def _edit_account(self):
        """Show dialog to edit the selected account."""
        selected_row = self.accounts_table.currentRow()
        if selected_row < 0:
            QMessageBox.warning(self, "Selection Error", "Please select an account to edit.")
            return
        doc_id_item = self.accounts_table.item(selected_row, 0)
        doc_id = doc_id_item.data(Qt.UserRole) if doc_id_item else None
        if not doc_id:
             QMessageBox.critical(self, "Error", "Could not retrieve account ID for selected row.")
             return
        original_data = next((acc for acc in self.accounts_data if acc.get('id') == doc_id), None)
        if not original_data:
             QMessageBox.critical(self, "Error", "Could not find original data for selected account.")
             return

        dialog = AccountDialog(account_data=original_data, parent=self)
        if dialog.exec_():
            updated_data = dialog.get_data()
            if updated_data:
                logging.info(f"Attempting to update account {doc_id} with: {updated_data}")
                updates_for_firebase = {
                    'account_number': updated_data['account_number'],
                    'server_name': updated_data['server_name'],
                    'user_name': updated_data['user_name'],
                    'status': updated_data['status'],
                    'plan_type': updated_data['plan_type'],
                    'plan_duration_days': updated_data['plan_duration_days']
                }
                self.start_task("admin_update_account", {"doc_id": doc_id, "updates": updates_for_firebase})

    @pyqtSlot()
    def _delete_account(self):
        """Confirm and delete the selected account."""
        selected_row = self.accounts_table.currentRow()
        if selected_row < 0:
            QMessageBox.warning(self, "Selection Error", "Please select an account to delete.")
            return
        doc_id_item = self.accounts_table.item(selected_row, 0)
        acc_num_item = self.accounts_table.item(selected_row, 0)
        doc_id = doc_id_item.data(Qt.UserRole) if doc_id_item else None
        acc_num = acc_num_item.text() if acc_num_item else "Unknown"
        if not doc_id:
             QMessageBox.critical(self, "Error", "Could not retrieve account ID for selected row.")
             return

        reply = QMessageBox.question(self, 'Confirm Delete',
                                     f"Are you sure you want to delete account {acc_num} (ID: {doc_id})?",
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            logging.info(f"Requesting deletion of account ID: {doc_id}")
            self.start_task("admin_delete_account", {"doc_id": doc_id})
        else:
            logging.info("Account deletion cancelled.")

    @pyqtSlot(QModelIndex)
    def _edit_account_on_double_click(self, index: QModelIndex):
         """Handle double-click on a row to edit."""
         if not index.isValid(): return
         self.accounts_table.selectRow(index.row())
         self._edit_account()
