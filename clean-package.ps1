# PowerShell script to clean and package the application

# Set console colors for better readability
$host.UI.RawUI.ForegroundColor = "Cyan"
Write-Output "🚀 GarudaAlgo Clean Packaging Script"
$host.UI.RawUI.ForegroundColor = "White"

# Kill any running processes that might lock files
Write-Output "📋 Stopping any running instances..."
Get-Process -Name "garuda_backend*" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Get-Process -Name "electron*" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Get-Process -Name "node*" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue

# Clean up previous builds
Write-Output "🧹 Cleaning previous builds..."
if (Test-Path -Path release) {
    Remove-Item -Path release -Recurse -Force -ErrorAction SilentlyContinue
}
if (Test-Path -Path release-new) {
    Remove-Item -Path release-new -Recurse -Force -ErrorAction SilentlyContinue
}

# Verify backend executable exists
if (-not (Test-Path -Path "dist\garuda_backend.exe")) {
    $host.UI.RawUI.ForegroundColor = "Red"
    Write-Output "❌ ERROR: Backend executable not found at dist\garuda_backend.exe"
    Write-Output "   Please build it first with: pyinstaller garuda_backend.spec"
    $host.UI.RawUI.ForegroundColor = "White"
    exit 1
}

# Build React frontend
Write-Output "🔨 Building React frontend..."
npm run build

# Copy the backend executable to the project root
Write-Output "📋 Copying backend executable..."
Copy-Item -Path "dist\garuda_backend.exe" -Destination "." -Force

# Run electron-builder with a clean environment
Write-Output "📦 Packaging application with electron-builder..."
npx electron-builder --win --x64 --dir

# Clean up copied files
Write-Output "🧹 Cleaning up..."
Remove-Item -Path "garuda_backend.exe" -Force -ErrorAction SilentlyContinue

Write-Output "Build process complete."
