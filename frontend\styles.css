/* Global styles */
:root {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 16px;
    line-height: 1.5;
    box-sizing: border-box;

    /* Custom properties */
    --primary: #3b82f6;
    --primary-hover: #2563eb;
    --secondary: #4b5563;
    --background: #f3f4f6;
    --card: #ffffff;
    --text: #1f2937;
    --text-secondary: #6b7280;
    --border: #e5e7eb;
    --success: #10b981;
    --error: #ef4444;
    --warning: #f59e0b;
    --info: #3b82f6;
    --card-hover: #f9fafb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius-sm: 0.25rem;
    --radius: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
    :root {
        --primary: #3b82f6;
        --primary-hover: #60a5fa;
        --secondary: #6b7280;
        --background: #111827;
        --card: #1f2937;
        --text: #f9fafb;
        --text-secondary: #9ca3af;
        --border: #374151;
        --card-hover: #2d3748;
        --input: #374151; /* Add input background for dark mode */
    }

    /* Dark mode specific styles for inputs/selects */
    input, select, textarea {
        background-color: var(--input);
        color: var(--text);
        border-color: var(--border);
    }

    select {
        /* Ensure text color contrasts with the dark background */
        color: var(--text);
        /* Optional: Style dropdown arrow if needed */
    }
}

*, *::before, *::after {
    box-sizing: inherit;
    margin: 0;
    padding: 0;
}

body {
    background-color: var(--background);
    color: var(--text);
    transition: background-color 0.3s, color 0.3s;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Layout */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.content {
    flex: 1;
    padding: 1.5rem 0;
}

header {
    background-color: var(--primary);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 4px var(--shadow);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title {
    font-size: 1.5rem;
    font-weight: 600;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--error);
}

.status-indicator.connected {
    background-color: var(--success);
}

/* Cards */
.card {
    background-color: var(--card);
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px var(--shadow);
    padding: 1.25rem;
    margin-bottom: 1.5rem;
    transition: background-color 0.3s, transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px var(--shadow);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
}

.card-body {
    margin-bottom: 1rem;
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

input, select, textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    /* background-color: var(--input); */ /* Base background removed, handled in :root and dark mode */
    color: var(--text);
    font-size: 1rem;
    transition: border-color 0.3s, box-shadow 0.3s, background-color 0.3s, color 0.3s; /* Added transitions */
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1.25rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s, transform 0.1s;
    border: none;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background-color: var(--primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
}

.btn-secondary {
    background-color: var(--secondary);
    color: white;
}

.btn-secondary:hover {
    background-color: var(--secondary-hover);
}

.btn-success {
    background-color: var(--success);
    color: white;
}

.btn-error {
    background-color: var(--error);
    color: white;
}

.btn-warning {
    background-color: var(--warning);
    color: white;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1.5rem;
}

th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border);
}

th {
    background-color: var(--background-secondary);
    font-weight: 600;
}

tr:hover {
    background-color: var(--card-hover);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--modal-backdrop);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: var(--card);
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px var(--shadow);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    transform: translateY(20px);
    transition: transform 0.3s;
}

.modal.show .modal-content {
    transform: translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem;
    border-bottom: 1px solid var(--border);
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
}

.modal-body {
    padding: 1.25rem;
}

.modal-footer {
    padding: 1.25rem;
    border-top: 1px solid var(--border);
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* Notification System */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1100;
    width: 300px;
}

.notification {
    background-color: var(--card);
    color: var(--text);
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px var(--shadow);
    margin-bottom: 10px;
    padding: 15px;
    position: relative;
    animation: slideIn 0.3s forwards;
    border-left: 4px solid var(--primary);
    display: flex;
    align-items: flex-start;
}

.notification.success {
    border-left-color: var(--success);
}

.notification.error {
    border-left-color: var(--error);
}

.notification.warning {
    border-left-color: var(--warning);
}

.notification.info {
    border-left-color: var(--info);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.notification-message {
    font-size: 0.875rem;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 1rem;
    padding: 0;
    margin-left: 10px;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification.hide {
    animation: slideOut 0.3s forwards;
}

/* Tabs */
.tabs {
    display: flex;
    border-bottom: 1px solid var(--border);
    margin-bottom: 1.5rem;
}

.tab {
    padding: 0.75rem 1.25rem;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: color 0.3s, border-color 0.3s;
    font-weight: 500;
}

.tab:hover {
    color: var(--primary);
}

.tab.active {
    color: var(--primary);
    border-bottom-color: var(--primary);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Utilities */
.text-primary { color: var(--primary); }
.text-secondary { color: var(--secondary); }
.text-success { color: var(--success); }
.text-error { color: var(--error); }
.text-warning { color: var(--warning); }
.text-info { color: var(--info); }

.bg-primary { background-color: var(--primary); }
.bg-secondary { background-color: var(--secondary); }
.bg-success { background-color: var(--success); }
.bg-error { background-color: var(--error); }
.bg-warning { background-color: var(--warning); }
.bg-info { background-color: var(--info); }

.border { border: 1px solid var(--border); }
.rounded { border-radius: 0.375rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 2rem; }

.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 1rem; }

.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 1rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 2rem; }

.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }

.d-flex { display: flex; }
.flex-column { flex-direction: column; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.align-center { align-items: center; }
.flex-wrap { flex-wrap: wrap; }
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 1rem; }

.w-full { width: 100%; }
.h-full { height: 100%; }

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 0 0.75rem;
    }

    .card {
        padding: 1rem;
    }

    .header-title {
        font-size: 1.25rem;
    }

    .notification-container {
        width: calc(100% - 40px);
    }
}

/* Loader/Spinner */
.loader {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loader-text {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.loader.dark {
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-top-color: var(--primary);
}

/* Position Section Styles */
.position-card {
    border-left: 4px solid var(--secondary);
    transition: transform 0.2s;
}

.position-card.buy {
    border-left-color: var(--success);
}

.position-card.sell {
    border-left-color: var(--error);
}

.position-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.position-title {
    font-size: 1.1rem;
    font-weight: 600;
}

.position-type {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.position-type.buy {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.position-type.sell {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--error);
}

.position-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.position-detail {
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.detail-value {
    font-weight: 500;
}

.position-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
}
