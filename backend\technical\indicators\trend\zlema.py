from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class ZLEMAIndicator(BaseIndicator):
    """Zero-Lag Exponential Moving Average (ZLEMA) indicator."""

    def __init__(self, period: int = 14, source: str = 'close'):
        """
        Initialize Zero-Lag Exponential Moving Average indicator.

        Args:
            period: The lookback period for the EMA.
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
        """
        super().__init__({
            'period': period,
            'source': source
        })

    def _ema(self, series: pd.Series, period: int) -> pd.Series:
        """Helper function for EMA calculation."""
        return series.ewm(span=period, adjust=False).mean()

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Zero-Lag Exponential Moving Average."""
        df = data.to_dataframe()
        period = self.params['period']
        # Need enough data for the lag calculation
        lag = (period - 1) // 2
        if df.empty or len(df) < period + lag:
             return {'zlema': np.array([])}

        source_col = self.params['source'].lower()

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Calculate zero-lag price
        zero_lag_price = source_data + (source_data - source_data.shift(lag))

        # Calculate ZLEMA
        zlema_values = self._ema(zero_lag_price, period)

        self._values = {
            'zlema': zlema_values.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        return True