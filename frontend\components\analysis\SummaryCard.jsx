import React from 'react';
import TrendArrow from './TrendArrow';
import Sparkline from './Sparkline';
import { getSignalClass, renderIndicatorValue, generateRandomPriceData } from '../../utils/analysisUtils';
import '../../styles/BentoComponents.css';
import '../../styles/AnalysisBento.css';

/**
 * SummaryCard component displays overall analysis summary
 *
 * @param {Object} analysisData - The analysis data
 * @returns {JSX.Element} - The rendered summary card
 */
const SummaryCard = ({ analysisData }) => {
  return (
    <div className="bento-card bento-span-12">
      <div className="bento-card-header">
        <h3 className="bento-card-title">Summary</h3>
        <div className="signal-with-arrow">
          {renderIndicatorValue(analysisData.trend?.overall, 'signal')}
          <TrendArrow direction={analysisData.trend?.overall} animated={true} />
        </div>
      </div>
      <div className="bento-card-content analysis-summary-card">
        <div className="analysis-overall-signal">
          <h4>Overall Signal</h4>
          <div className="analysis-signal-strength">
            <div className="analysis-strength-bar">
              <div
                className={`analysis-strength-value ${getSignalClass(analysisData.trend?.overall)}`}
                style={{ width: `${analysisData.trend?.strength !== undefined && analysisData.trend?.strength !== null ? analysisData.trend?.strength : 50}%` }}
              ></div>
            </div>
            <div className="analysis-strength-label">{analysisData.trend?.strength !== undefined && analysisData.trend?.strength !== null ? analysisData.trend?.strength : 50}% Confidence</div>
          </div>

          {/* Price Movement Sparkline */}
          <div className="mt-2">
            <h4>Price Movement</h4>
            <Sparkline
              data={analysisData.price_data?.close || generateRandomPriceData(20)}
              height={50}
            />
          </div>
        </div>
        <div className="analysis-summary-text">
          <h4>Contributing Signals</h4>
          {analysisData.trend?.signals && analysisData.trend.signals.length > 0 ? (
            <ul>
              {analysisData.trend.signals.map(([indicator, direction], index) => (
                <li key={index}>{indicator}: {renderIndicatorValue(direction, 'signal')}</li>
              ))}
            </ul>
          ) : (
            <p>No specific contributing signals identified.</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default SummaryCard;
