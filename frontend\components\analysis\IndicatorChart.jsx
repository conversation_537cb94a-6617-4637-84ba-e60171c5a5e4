import React, { useState } from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js';
import { Line } from 'react-chartjs-2';

// Register the chart.js components we need (only once)
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

/**
 * IndicatorChart component for displaying technical indicators
 *
 * @param {Object} analysisData - The analysis data containing indicator information
 * @param {string} selectedSymbol - The currently selected symbol
 * @param {string[]} [visibleIndicators] - Array of indicator names to display (e.g. ['rsi', 'macd'])
 * @param {string|number} [height] - Chart height (default: 220)
 * @param {string|number} [width] - Chart width (default: 100%)
 * @param {boolean} [devMode] - If true, allow mock data for development
 * @returns {JSX.Element} - The rendered indicator chart
 */
const IndicatorChart = ({ analysisData, selectedSymbol, visibleIndicators = ['rsi'], height = 220, width = '100%', devMode = false }) => {
  const [activeIndicators, setActiveIndicators] = useState(visibleIndicators);

  // Helper to toggle indicators
  const toggleIndicator = (name) => {
    setActiveIndicators((prev) =>
      prev.includes(name) ? prev.filter((n) => n !== name) : [...prev, name]
    );
  };

  // For debugging - log what data we're receiving
  console.log('[IndicatorChart] analysisData structure:', analysisData);
  console.log('[IndicatorChart] Active indicators:', activeIndicators);
  
  // Check what indicators are available in the data
  if (analysisData) {
    console.log('[IndicatorChart] Available indicators:', Object.keys(analysisData).filter(key => 
      ['rsi', 'macd', 'bollinger_bands', 'atr'].includes(key)));
    
    // Log specific indicator structures if they exist
    if (analysisData.rsi) console.log('[IndicatorChart] RSI structure:', analysisData.rsi);
    if (analysisData.macd) console.log('[IndicatorChart] MACD structure:', analysisData.macd);
  }
  
  // Build chart datasets dynamically
  const datasets = [];
  const labels = [];
  
  // Early return if no data and not in dev mode
  if (!analysisData && !devMode) {
    return (
      <div className="bento-card bento-span-12 bento-height-2">
        <div className="bento-card-header">
          <h3 className="bento-card-title">Technical Indicators</h3>
        </div>
        <div className="bento-card-content" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: height }}>
          <p>No indicator data available</p>
        </div>
      </div>
    );
  }

  // Supported indicators with fallback data handling
  const indicatorMeta = {
    rsi: { 
      label: 'RSI', 
      color: '#3366cc', 
      getDataPoints: (data) => data?.rsi?.value || [],
      useMockData: true
    },
    macd: { 
      label: 'MACD', 
      color: '#ff9900', 
      getDataPoints: (data) => data?.macd?.value || [],
      useMockData: true
    },
    // Add more indicators as needed
  };

  // Process each active indicator
  activeIndicators.forEach((name) => {
    // Get the indicator configuration
    const indicator = indicatorMeta[name];
    if (!indicator) return;

    // Try to get data using the custom accessor function
    let data = indicator.getDataPoints(analysisData);
    
    // Generate mock data if needed and allowed
    if ((!data || !Array.isArray(data) || data.length === 0) && (devMode || indicator.useMockData)) {
      console.log(`[IndicatorChart] Using mock data for ${name}`);
      // Mock data appropriate for the indicator type
      if (name === 'rsi') {
        data = Array(20).fill(0).map(() => 30 + Math.random() * 40); // RSI between 0-100
      } else if (name === 'macd') {
        data = Array(20).fill(0).map(() => -2 + Math.random() * 4); // MACD around zero
      } else {
        data = Array(20).fill(0).map(() => Math.random() * 100);
      }
    }
    
    // Only add to datasets if we have valid data
    if (data && Array.isArray(data) && data.length > 0) {
      datasets.push({
        label: indicator.label,
        data,
        borderColor: indicator.color,
        backgroundColor: 'rgba(0,0,0,0.02)',
        tension: 0.3,
        pointRadius: 0,
      });
      
      // Generate time labels if needed
      if (labels.length === 0) {
        // Try to use timestamps from the data, or create timestamps if not available
        if (analysisData?.price_data?.time && Array.isArray(analysisData.price_data.time)) {
          // Use time from price data
          labels.push(...analysisData.price_data.time.map(t => {
            const date = new Date(t * 1000);
            return date.toLocaleTimeString();
          }));
        } else {
          // Generate mock timestamps
          const now = new Date();
          for (let i = 0; i < data.length; i++) {
            labels.push(new Date(now.getTime() - (data.length - 1 - i) * 5 * 60000).toLocaleTimeString());
          }
        }
      }
    }
  });

  const options = {
    responsive: true,
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
      tooltip: {
        enabled: true,
        mode: 'index',
        intersect: false,
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${context.parsed.y}`;
          },
        },
      },
      title: {
        display: true,
        text: 'Technical Indicators',
      },
    },
    interaction: {
      mode: 'nearest',
      axis: 'x',
      intersect: false,
    },
    scales: {
      y: {
        beginAtZero: false,
      },
    },
  };

  return (
    <div className="bento-card bento-span-12 bento-height-2 indicator-chart">
      <div className="bento-card-header" style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <h3 className="bento-card-title">Technical Indicators</h3>
        <div>
          {Object.keys(indicatorMeta).map((name) => (
            <label key={name} style={{ marginLeft: 10 }}>
              <input
                type="checkbox"
                checked={activeIndicators.includes(name)}
                onChange={() => toggleIndicator(name)}
              />{' '}
              {indicatorMeta[name].label}
            </label>
          ))}
        </div>
      </div>
      <div className="bento-card-content" style={{ height, width }}>
        {datasets.length === 0 ? (
          <p>No indicator data available</p>
        ) : (
          <Line data={{ labels, datasets }} options={options} height={height} />
        )}
      </div>
    </div>
  );
};

export default IndicatorChart;
