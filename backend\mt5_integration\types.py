"""
Type definitions for MT5 integration.
"""

from enum import Enum
from typing import <PERSON><PERSON><PERSON><PERSON>
from datetime import datetime

class ConnectionState(Enum):
    """Enum representing the state of the MT5 connection."""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"

class AccountInfo(NamedTuple):
    """Account information data structure."""
    balance: float
    equity: float
    name: str
    leverage: int
    server: str
    last_updated: datetime

class SymbolInfo(NamedTuple):
    """Symbol information data structure."""
    name: str
    digits: int  # Number of decimal places
    trade_mode: str  # forex, futures, etc.
    currency_base: str
    currency_profit: str
    currency_margin: str
    visible: bool
    custom: bool
    last_updated: datetime
