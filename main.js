const electron = require('electron');
const { app, BrowserWindow, ipcMain, Menu } = electron;
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const Store = require('electron-store');

// Initialize settings store
const store = new Store();

// Global variables
let mainWindow;
let backendProcess;
let isBackendRunning = false; // To track if backend process is active

// Helper function to get the application's user data path for settings
function getAppUserSettingsPath() {
  const appDataRoot = app.getPath('appData'); // Standard app data directory
  if (!appDataRoot) {
    console.error("Could not determine appData path. Falling back to local directory for settings.");
    // Fallback, though app.getPath('appData') should generally work.
    // This fallback is NOT ideal for packaged apps if it resolves to a non-writable location.
    return path.join(__dirname, 'GarudaAlgo', 'user_mt5_settings.json');
  }
  const garudaAlgoDataDir = path.join(appDataRoot, 'GarudaAlgo');

  // Ensure the directory exists
  if (!fs.existsSync(garudaAlgoDataDir)) {
    try {
      fs.mkdirSync(garudaAlgoDataDir, { recursive: true });
    } catch (err) {
      console.error('Failed to create GarudaAlgo appData directory:', garudaAlgoDataDir, err);
    }
  }
  return path.join(garudaAlgoDataDir, 'user_mt5_settings.json');
}

// Create application menu with developer tools toggle
function createApplicationMenu() {
  const isMac = process.platform === 'darwin';
  
  const template = [
    // App menu (macOS only)
    ...(isMac ? [{
      label: app.name,
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    }] : []),
    // File menu
    {
      label: 'File',
      submenu: [
        isMac ? { role: 'close' } : { role: 'quit' }
      ]
    },
    // Edit menu
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' }
      ]
    },
    // View menu with developer tools
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' },
        { type: 'separator' },
        {
          label: 'Toggle Developer Tools',
          accelerator: process.platform === 'darwin' ? 'Alt+Command+I' : 'Ctrl+Shift+I',
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.toggleDevTools();
            }
          }
        }
      ]
    },
    // Help menu
    {
      label: 'Help',
      submenu: [
        {
          label: 'About GarudaAlgo',
          click: async () => {
            const { dialog } = require('electron');
            dialog.showMessageBox({
              title: 'About GarudaAlgo MT5 Trader',
              message: 'GarudaAlgo MT5 Trader',
              detail: `Version: ${app.getVersion()}
Electron: ${process.versions.electron}
Chrome: ${process.versions.chrome}
Node.js: ${process.versions.node}
V8: ${process.versions.v8}`
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// Create the main window
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true, // Enabled for preload script to work with contextBridge
      preload: path.join(__dirname, 'frontend', 'preload.js'), // Added preload script
      webSecurity: true,
      sandbox: true
    },
    title: 'GarudaAlgo MT5 Trader',
    icon: path.join(__dirname, 'assets', 'icon.png'),
  });

  // Set Content Security Policy
  mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          "default-src 'self';",
          "script-src 'self' 'unsafe-eval';", // Added 'unsafe-eval' to allow developer tools
          "style-src 'self' 'unsafe-inline';", // Allow inline styles for now
          "font-src 'self' data:;",
          "img-src 'self' data:;",
          "connect-src 'self' http://localhost:5001;", // Allow API connections
        ].join(' ')
      }
    });
  });

  // Check if we're in development mode
  if (process.env.NODE_ENV === 'development') {
    // In development, load from Vite dev server
    mainWindow.loadURL('http://localhost:3000');
  } else {
    // In production, load the built React app
    mainWindow.loadFile(path.join(__dirname, 'frontend', 'dist', 'react-index.html'));
  }

  // Open DevTools in development mode
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }

  mainWindow.on('closed', () => {
    mainWindow = null;
    stopBackend();
  });
}

// Start the FastAPI backend
function startBackend() {
  if (isBackendRunning) return;

  const isPackaged = app.isPackaged;
  let backendExePath;  if (isPackaged) {
    // For packaged app, use the standalone executable we built with PyInstaller
    const backendExecutableName = process.platform === 'win32' ? 'garuda_backend.exe' : 'garuda_backend';
    
    // First try in the resources directory (primary location)
    // process.resourcesPath points to the resources directory in the packaged app
    let backendExePath = path.join(process.resourcesPath, backendExecutableName);
    console.log('Looking for backend executable in resources directory:', backendExePath);
    
    // Check if the backend exists in the primary location
    if (!fs.existsSync(backendExePath)) {
      console.log('Backend not found in resources directory');
      
      // Try alternative location - same directory as the app executable
      const appRootPath = path.dirname(app.getPath('exe'));
      const altPath = path.join(appRootPath, backendExecutableName);
      console.log('Trying alternative path:', altPath);
      
      if (fs.existsSync(altPath)) {
        console.log('Found backend executable at alternative path');
        backendExePath = altPath;
      } else {
        const errorMessage = `Backend executable not found. Looked in:\n- ${backendExePath}\n- ${altPath}`;
        console.error(errorMessage);
        electron.dialog.showErrorBox('Backend Error', errorMessage);
        return;
      }
    }
    
    backendProcess = spawn(backendExePath, [], { cwd: path.dirname(backendExePath) });

  } else {
    // Development path
    const pythonExecutable = process.platform === 'win32' ? 'python' : 'python3';
    console.log('Starting backend in development mode using:', pythonExecutable, 'backend/run_server.py');
    backendProcess = spawn(pythonExecutable, ['backend/run_server.py'], { cwd: __dirname });
  }
  
  isBackendRunning = true;

  backendProcess.stdout.on('data', (data) => {
    console.log(`Backend stdout: ${data}`);
  });

  backendProcess.stderr.on('data', (data) => {
    console.error(`Backend stderr: ${data}`);
  });

  backendProcess.on('close', (code) => {
    console.log(`Backend process exited with code ${code}`);
    isBackendRunning = false;
  });
}

// Stop the FastAPI backend
function stopBackend() {
  if (!isBackendRunning || !backendProcess) return;

  if (process.platform === 'win32') {
    spawn('taskkill', ['/pid', backendProcess.pid, '/f', '/t']);
  } else {
    backendProcess.kill('SIGINT');
  }

  isBackendRunning = false;
}

// IPC Handlers
ipcMain.handle('get-settings', () => {
  let backendSettings = null;
  const userSettingsFilePath = getAppUserSettingsPath();
  try {
    if (fs.existsSync(userSettingsFilePath)) {
      const settingsJson = fs.readFileSync(userSettingsFilePath, 'utf-8');
      backendSettings = JSON.parse(settingsJson);
      console.log('Main process: Read backend settings from user_mt5_settings.json:', backendSettings);
    } else {
      console.log('Main process: user_mt5_settings.json not found. Returning null.');
    }
  } catch (error) {
    console.error('Main process: Error reading user_mt5_settings.json:', error);
  }

  const formValues = store.get('mt5-form-values');
  if (formValues) {
    console.log('Main process: Read form values from electron-store:', formValues);
  } else {
    console.log('Main process: No form values found in electron-store.');
  }
  
  // Return both sets of settings. The renderer will decide how to merge/prioritize.
  return { backendSettings, formValues };
});

ipcMain.handle('save-settings', (_, settings) => {
  // This handler is called by the renderer to save the *form field values*
  // (typically excluding password) for UI convenience (pre-filling the form next time).
  // It saves to electron-store, NOT to user_mt5_settings.json (backend handles that).
  // The 'settings' object here usually comes from the form and might not include the password.
  store.set('mt5-form-values', settings); // Using a different key to distinguish from backend settings
  console.log('Main process: Saved form values to electron-store:', settings);
  return { success: true };
});

// Removed ipcMain.handle('get-status')
// Removed ipcMain.handle('connect-mt5')
// Removed ipcMain.handle('disconnect-mt5')
// The renderer (renderer.js) now communicates directly with the backend API for these.

// Function to suggest default MT5 terminal path
function getSuggestedMT5Path() {
  switch (process.platform) {
    case 'win32':
      // Check common Program Files locations for MetaTrader 5
      const winPaths = [
        path.join('C:', 'Program Files', 'MetaTrader 5', 'terminal64.exe'),
        path.join('C:', 'Program Files (x86)', 'MetaTrader 5', 'terminal64.exe'),
        path.join('C:', 'Program Files', 'MetaTrader 5', 'terminal.exe'), // Older 32-bit
        path.join('C:', 'Program Files (x86)', 'MetaTrader 5', 'terminal.exe')
      ];
      for (const p of winPaths) {
        if (fs.existsSync(p)) {
          return p;
        }
      }
      // Fallback if none of the common paths exist
      return path.join('C:', 'Program Files', 'MetaTrader 5', 'terminal64.exe');
    case 'darwin': // macOS
      // MT5 on macOS is often via Wine or CrossOver. Direct .app might not be the one used for automation.
      // A common direct install path:
      const macAppPath = '/Applications/MetaTrader 5.app';
      if (fs.existsSync(macAppPath)) {
        // For automation, one might need to point to the executable inside the .app bundle
        // or more likely, a Wine path. This is a simplification.
        // return path.join(macAppPath, 'Contents', 'MacOS', 'MetaTrader 5'); // This is the app runner, not terminal64.exe
        // For now, just suggest the .app path, user might need to adjust for Wine.
        // A more robust solution would involve searching typical Wine prefixes.
        return macAppPath; 
      }
      // A very common Wine path structure (example, highly variable)
      // return path.join(app.getPath('home'), 'Library', 'Application Support', 'MetaTrader 5', 'Bottles', 'metatrader5', 'drive_c', 'Program Files', 'MetaTrader 5', 'terminal64.exe');
      return ''; // Placeholder, as macOS paths are complex with Wine.
    case 'linux':
      // Similar to macOS, often via Wine.
      // return path.join(app.getPath('home'), '.wine', 'drive_c', 'Program Files', 'MetaTrader 5', 'terminal64.exe');
      return ''; // Placeholder
    default:
      return '';
  }
}

ipcMain.handle('get-suggested-mt5-path', () => {
  return getSuggestedMT5Path();
});

// App lifecycle events
app.whenReady().then(() => {
  createApplicationMenu(); // Create the application menu with developer tools toggle
  createWindow();
  startBackend();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('quit', () => {
  stopBackend();
});
