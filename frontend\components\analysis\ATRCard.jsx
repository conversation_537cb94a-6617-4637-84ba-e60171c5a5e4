import React from 'react';
import ATRGauge from './ATRGauge';
import { safeToFixed } from '../../utils/numberUtils';
import '../../styles/BentoComponents.css';
import '../../styles/AnalysisBento.css';

/**
 * ATRCard component displays ATR analysis
 * 
 * @param {Object} atr - The ATR data
 * @returns {JSX.Element} - The rendered ATR card
 */
const ATRCard = ({ atr }) => {
  return (
    <div className="bento-card bento-span-4 bento-card-centered">
      <div className="bento-card-header">
        <h3 className="bento-card-title">ATR</h3>
        <span className="signal neutral">{atr?.volatility ?? 'N/A'}</span>
      </div>
      <div className="bento-card-content">
        <ATRGauge
          value={atr?.value}
          percent={atr?.percent}
        />
        <p className="mt-2"><span>ATR %:</span> <span>{safeToFixed(atr?.percent, 2)}%</span></p>
        <p><span>Volatility:</span> <span>{atr?.volatility ?? 'N/A'}</span></p>
      </div>
    </div>
  );
};

export default ATRCard;
