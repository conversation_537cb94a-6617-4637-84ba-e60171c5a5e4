"""Scheduler for periodic trading operations like updating trailing stops."""

import threading
import time
import logging
from typing import Optional
from flask import Flask

logger = logging.getLogger(__name__)

class TradeScheduler:
    """A scheduler for periodic trading operations."""
    
    def __init__(self, app: Optional[Flask] = None):
        """Initialize the scheduler.
        
        Args:
            app: Optional Flask app to initialize with
        """
        self.app = app
        self.trailing_stop_thread = None
        self.running = False
        self.interval = 5  # Default interval in seconds
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app: Flask):
        """Initialize with a Flask app.
        
        Args:
            app: Flask app to initialize with
        """
        self.app = app
        
        # Add a function to gracefully shut down the scheduler
        # when the Flask app is terminating
        @app.teardown_appcontext
        def shutdown_scheduler(exception=None):
            self.stop()
    
    def start(self, interval: int = 5):
        """Start the scheduler.
        
        Args:
            interval: Interval in seconds between updates
        """
        if self.running:
            logger.warning("Scheduler is already running")
            return
        
        self.interval = interval
        self.running = True
        
        # Start the trailing stop update thread
        self.trailing_stop_thread = threading.Thread(
            target=self._trailing_stop_worker,
            daemon=True  # Daemon thread will be killed when the main thread exits
        )
        self.trailing_stop_thread.start()
        
        logger.info(f"Trade scheduler started with interval of {interval} seconds")
    
    def stop(self):
        """Stop the scheduler."""
        if not self.running:
            logger.warning("Scheduler is not running")
            return
        
        self.running = False
        
        # Wait for the thread to terminate (with timeout)
        if self.trailing_stop_thread and self.trailing_stop_thread.is_alive():
            self.trailing_stop_thread.join(timeout=2.0)
        
        logger.info("Trade scheduler stopped")
    
    def _trailing_stop_worker(self):
        """Worker function to periodically update trailing stops."""
        logger.info("Trailing stop worker thread started")
        
        while self.running:
            try:
                # Get the MT5 instance from the app
                if not self.app:
                    logger.error("Flask app not initialized for scheduler")
                    time.sleep(self.interval)
                    continue
                
                mt5_instance = self.app.config.get('MT5_INSTANCE')
                if not mt5_instance:
                    logger.warning("MT5 instance not available in app config")
                    time.sleep(self.interval)
                    continue
                
                # Check if MT5 is connected
                if not mt5_instance.is_connected():
                    logger.debug("MT5 not connected, skipping trailing stop update")
                    time.sleep(self.interval)
                    continue
                
                # Update trailing stops
                result = mt5_instance.trading.update_trailing_stops()
                
                if result['success']:
                    updated = result.get('updated', 0)
                    if updated > 0:
                        logger.info(f"Updated {updated} trailing stops")
                    else:
                        logger.debug("No trailing stops updated")
                else:
                    logger.warning(f"Failed to update trailing stops: {result.get('message', 'Unknown error')}")
            
            except Exception as e:
                logger.exception(f"Error in trailing stop worker: {str(e)}")
            
            # Sleep for the specified interval
            time.sleep(self.interval)
        
        logger.info("Trailing stop worker thread terminated")
