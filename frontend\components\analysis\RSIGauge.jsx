import React from 'react';
import { isValidNumber, safeToFixed } from '../../utils/numberUtils';
import '../../styles/GaugeIndicator.css';

/**
 * RSIGauge component displays a gauge visualization for RSI values
 * 
 * @param {number} value - The RSI value (0-100)
 * @returns {JSX.Element} - The rendered RSI gauge
 */
const RSIGauge = ({ value }) => {
  // Calculate needle position (0-100 scale to 0-180 degrees)
  const isValid = isValidNumber(value);
  const needleRotation = isValid ? Math.min(Math.max(value, 0), 100) * 1.8 : 90; // Default to middle if no value
  const needleHeight = 50; // Height of the needle

  return (
    <div className="gauge-container">
      <div className="gauge-outer"></div>
      <div className="gauge-inner">{safeToFixed(value, 2)}</div>
      <div
        className="gauge-needle"
        style={{
          height: `${needleHeight}px`,
          transform: `rotate(${needleRotation}deg)`
        }}
      ></div>
      <div className="gauge-labels">
        <span className="gauge-label-oversold">30</span>
        <span className="gauge-label-neutral">50</span>
        <span className="gauge-label-overbought">70</span>
      </div>
    </div>
  );
};

export default RSIGauge;
