"""API endpoints for advanced trade management operations."""

import logging
from flask import Blueprint, jsonify, request, current_app
import MetaTrader5 as mt5
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

trade_mgmt_bp = Blueprint('trade_management', __name__)


@trade_mgmt_bp.route('/partial_close/<int:position_id>', methods=['POST'])
def partial_close(position_id: int):
    """Partially close an open position"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get parameters from request
        data = request.get_json(silent=True) or {}
        volume_percent = data.get('volume_percent', 50)  # Default to 50%
        comment = data.get('comment', '')

        # Validate volume percent
        if not (0 < volume_percent < 100):
            return jsonify({'error': 'Volume percent must be between 0 and 100'}), 400

        # Use the enhanced trading component
        result = mt5_conn.trading.partial_close(position_id, volume_percent, comment)

        if not result['success']:
            return jsonify({'error': result['message']}), 400

        return jsonify(result)

    except Exception as e:
        logger.exception(f"Error partially closing position: {str(e)}")
        return jsonify({'error': str(e)}), 500


@trade_mgmt_bp.route('/trailing_stop/set/<int:position_id>', methods=['POST'])
def set_trailing_stop(position_id: int):
    """Set a trailing stop for a position"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get parameters from request
        data = request.get_json(silent=True) or {}
        distance_points = data.get('distance_points')
        if not distance_points or distance_points <= 0:
            return jsonify({'error': 'Valid distance_points parameter is required'}), 400

        step_points = data.get('step_points', 1)
        activate_profit_points = data.get('activate_profit_points')

        # Set trailing stop
        result = mt5_conn.trading.set_trailing_stop(
            position_id, 
            distance_points, 
            step_points, 
            activate_profit_points
        )

        if not result['success']:
            return jsonify({'error': result['message']}), 400

        return jsonify(result)

    except Exception as e:
        logger.exception(f"Error setting trailing stop: {str(e)}")
        return jsonify({'error': str(e)}), 500


@trade_mgmt_bp.route('/trailing_stop/remove/<int:position_id>', methods=['POST'])
def remove_trailing_stop(position_id: int):
    """Remove a trailing stop for a position"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Remove trailing stop
        result = mt5_conn.trading.remove_trailing_stop(position_id)

        if not result['success']:
            return jsonify({'error': result['message']}), 400

        return jsonify(result)

    except Exception as e:
        logger.exception(f"Error removing trailing stop: {str(e)}")
        return jsonify({'error': str(e)}), 500


@trade_mgmt_bp.route('/trailing_stop/update', methods=['POST'])
def update_trailing_stops():
    """Update all active trailing stops"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Update all trailing stops
        result = mt5_conn.trading.update_trailing_stops()

        if not result['success']:
            return jsonify({'error': result['message']}), 400

        return jsonify(result)

    except Exception as e:
        logger.exception(f"Error updating trailing stops: {str(e)}")
        return jsonify({'error': str(e)}), 500


@trade_mgmt_bp.route('/trailing_stop/set_all', methods=['POST'])
def set_all_trailing_stops():
    """Set trailing stop for all applicable positions"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get parameters from the request
        data = request.get_json(silent=True) or {}
        distance_points = data.get('distance_points')
        if not distance_points or distance_points <= 0:
            return jsonify({'error': 'Valid distance_points parameter is required'}), 400

        step_points = data.get('step_points', 1)
        activate_profit_points = data.get('activate_profit_points')
        symbols = data.get('symbols', [])  # Optional filter by symbols
        types = data.get('types', [])  # Optional filter by position type (0=buy, 1=sell)

        # Get all positions
        positions = mt5_conn.trading.get_open_positions()
        if not positions['success']:
            return jsonify({'error': positions['message']}), 400

        results = {
            'success': True,
            'message': 'Trailing stops set',
            'positions_updated': 0,
            'positions_failed': 0,
            'details': []
        }

        # Set trailing stop for each position that matches criteria
        for position in positions.get('positions', []):
            # Apply filters if provided
            if symbols and position['symbol'] not in symbols:
                continue
            if types and position['type'] not in types:
                continue

            # Set trailing stop
            result = mt5_conn.trading.set_trailing_stop(
                position['ticket'],
                distance_points,
                step_points,
                activate_profit_points
            )

            # Record result
            if result['success']:
                results['positions_updated'] += 1
            else:
                results['positions_failed'] += 1

            results['details'].append({
                'ticket': position['ticket'],
                'symbol': position['symbol'],
                'success': result['success'],
                'message': result['message']
            })

        return jsonify(results)

    except Exception as e:
        logger.exception(f"Error setting all trailing stops: {str(e)}")
        return jsonify({'error': str(e)}), 500


@trade_mgmt_bp.route('/calculate_lot_size', methods=['GET'])
def calculate_lot_size():
    """Calculate optimal lot size based on risk parameters"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get parameters from request
        symbol = request.args.get('symbol')
        if not symbol:
            return jsonify({'error': 'Symbol parameter is required'}), 400

        risk_percent = float(request.args.get('risk_percent', 1.0))
        sl_points = int(request.args.get('sl_points', 0))
        max_risk_percent = float(request.args.get('max_risk_percent', 2.0))
        min_lot = float(request.args.get('min_lot', 0.01)) if request.args.get('min_lot') else None

        if sl_points <= 0:
            return jsonify({'error': 'Valid sl_points parameter is required'}), 400

        # Calculate lot size
        result = mt5_conn.trading.calculate_lot_size(
            symbol, 
            risk_percent, 
            sl_points, 
            max_risk_percent, 
            min_lot
        )

        if not result['success']:
            return jsonify({'error': result['message']}), 400

        return jsonify(result)

    except Exception as e:
        logger.exception(f"Error calculating lot size: {str(e)}")
        return jsonify({'error': str(e)}), 500


@trade_mgmt_bp.route('/set_break_even/<int:position_id>', methods=['POST'])
def set_break_even(position_id: int):
    """Set break even for a position"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get parameters from request
        data = request.get_json(silent=True) or {}
        offset_points = data.get('offset_points', 0)  # Optional offset in points

        # Set break even
        result = mt5_conn.trading.set_break_even(position_id, offset_points)

        if not result['success']:
            return jsonify({'error': result['message']}), 400

        return jsonify(result)

    except Exception as e:
        logger.exception(f"Error setting break even: {str(e)}")
        return jsonify({'error': str(e)}), 500


@trade_mgmt_bp.route('/set_sl/<int:position_id>', methods=['POST'])
def set_sl(position_id: int):
    """Set or modify stop loss for a position"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get parameters from request
        data = request.get_json(silent=True) or {}
        sl_price = data.get('sl_price')
        
        if sl_price is None:
            return jsonify({'error': 'Stop loss price is required'}), 400

        # Set stop loss
        result = mt5_conn.trading.modify_position(
            position_id, 
            sl=float(sl_price),
            tp=None  # Don't modify TP
        )

        if not result['success']:
            return jsonify({'error': result['message']}), 400

        return jsonify(result)

    except Exception as e:
        logger.exception(f"Error setting stop loss: {str(e)}")
        return jsonify({'error': str(e)}), 500


@trade_mgmt_bp.route('/set_tp/<int:position_id>', methods=['POST'])
def set_tp(position_id: int):
    """Set or modify take profit for a position"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get parameters from request
        data = request.get_json(silent=True) or {}
        tp_price = data.get('tp_price')
        
        if tp_price is None:
            return jsonify({'error': 'Take profit price is required'}), 400

        # Set take profit
        result = mt5_conn.trading.modify_position(
            position_id, 
            sl=None,  # Don't modify SL
            tp=float(tp_price)
        )

        if not result['success']:
            return jsonify({'error': result['message']}), 400

        return jsonify(result)

    except Exception as e:
        logger.exception(f"Error setting take profit: {str(e)}")
        return jsonify({'error': str(e)}), 500


@trade_mgmt_bp.route('/modify_position/<int:position_id>', methods=['POST'])
def modify_position(position_id: int):
    """Modify both SL and TP for a position"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get parameters from request
        data = request.get_json(silent=True) or {}
        sl_price = data.get('sl_price')  # Can be None to keep current SL
        tp_price = data.get('tp_price')  # Can be None to keep current TP
        
        if sl_price is None and tp_price is None:
            return jsonify({'error': 'At least one of SL or TP price must be provided'}), 400

        # Convert to float if not None
        sl_price = float(sl_price) if sl_price is not None else None
        tp_price = float(tp_price) if tp_price is not None else None

        # Modify position
        result = mt5_conn.trading.modify_position(position_id, sl=sl_price, tp=tp_price)

        if not result['success']:
            return jsonify({'error': result['message']}), 400

        return jsonify(result)

    except Exception as e:
        logger.exception(f"Error modifying position: {str(e)}")
        return jsonify({'error': str(e)}), 500
