from typing import Dict, Any
import numpy as np
import pandas as pd
import math

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class HMAIndicator(BaseIndicator):
    """Hull Moving Average (HMA) indicator."""

    def __init__(self, period: int = 9, source: str = 'close'):
        """
        Initialize Hull Moving Average indicator.

        Args:
            period: The lookback period for the HMA.
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
        """
        super().__init__({
            'period': period,
            'source': source
        })

    def _wma(self, series: pd.Series, period: int) -> pd.Series:
        """Helper function for WMA calculation."""
        weights = np.arange(1, period + 1)
        # Ensure weights sum is not zero
        weights_sum = weights.sum()
        if weights_sum == 0:
             # Return series of NaNs if weights sum is zero (e.g., period=0, though validation prevents this)
             return pd.Series(np.nan, index=series.index)

        return series.rolling(window=period).apply(
            lambda x: np.sum(weights * x) / weights_sum if len(x) == period else np.nan,
            raw=True
        )

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Hull Moving Average."""
        df = data.to_dataframe()
        period = self.params['period']
        # Ensure period is valid for HMA calculation steps
        if df.empty or len(df) < period or period < 4: # Need enough data for WMA(sqrt(period))
             return {'hma': np.array([])}

        source_col = self.params['source'].lower()
        period_half = int(period / 2)
        period_sqrt = int(math.sqrt(period))

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Calculate WMAs
        wma_half = self._wma(source_data, period_half)
        wma_full = self._wma(source_data, period)

        # Calculate raw HMA
        raw_hma = (2 * wma_half) - wma_full

        # Calculate final HMA (WMA of raw HMA with sqrt(period))
        hma_values = self._wma(raw_hma, period_sqrt)

        self._values = {
            'hma': hma_values.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        # HMA requires period >= 4 for sqrt period to be >= 2 for WMA
        if not isinstance(self.params['period'], int) or self.params['period'] < 4:
            raise ValueError("Period must be an integer >= 4 for HMA")
        return True