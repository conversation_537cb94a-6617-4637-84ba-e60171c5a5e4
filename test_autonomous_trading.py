"""
Test script for autonomous trading functionality.
"""

import logging
import time
import json
import os
from backend.mt5_integration import MT5Integration
from backend.analysis import AnalysisEngine
from backend.recommendation_engine import RecommendationEngine
from backend.autonomous_trader import AutonomousTrader

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_autonomous_trading():
    """Test the autonomous trading functionality."""
    try:
        print("Starting autonomous trading test...")
        
        # Create MT5Integration instance
        mt5 = MT5Integration()
        print("MT5Integration instance created successfully.")
        
        # Initialize MT5 connection
        settings_path = os.path.join(os.getcwd(), "mt5_settings.json")
        if not os.path.exists(settings_path):
            print(f"Settings file not found at {settings_path}")
            return False
        
        print(f"Initializing MT5 with settings from {settings_path}...")
        result = mt5.initialize(settings_path=settings_path)
        if not result.get("success", False):
            print(f"Failed to initialize MT5: {result.get('message', 'Unknown error')}")
            return False
        
        print("MT5 initialized successfully.")
        
        # Check connection
        connection_result = mt5.check_connection()
        if not connection_result.get("connected", False):
            print(f"Not connected to MT5: {connection_result.get('message', 'Unknown error')}")
            return False
        
        print("MT5 connection verified.")
        
        # Create analysis engine
        analysis_engine = AnalysisEngine(mt5)
        print("Analysis engine created successfully.")
        
        # Create recommendation engine
        recommendation_engine = RecommendationEngine(mt5, analysis_engine)
        print("Recommendation engine created successfully.")
        
        # Create autonomous trader
        autonomous_trader = AutonomousTrader(mt5, analysis_engine, recommendation_engine)
        print("Autonomous trader created successfully.")
        
        # Get status
        status = autonomous_trader.get_status()
        print(f"Autonomous trader status: {json.dumps(status, indent=2)}")
        
        # Update configuration
        config = {
            "symbols": ["EURUSD", "GBPUSD"],
            "timeframes": ["M15", "H1"],
            "scan_interval": 300,
            "min_confidence": 70,
            "max_trades": 2,
            "allowed_styles": ["Scalping", "Short-term"],
            "allow_multiple_positions": False,
            "risk_percent": 1.0
        }
        
        print(f"Updating configuration: {json.dumps(config, indent=2)}...")
        update_result = autonomous_trader.update_config(config)
        if not update_result.get("success", False):
            print(f"Failed to update configuration: {update_result.get('message', 'Unknown error')}")
            return False
        
        print("Configuration updated successfully.")
        
        # Start autonomous trading
        print("Starting autonomous trading...")
        start_result = autonomous_trader.start()
        if not start_result.get("success", False):
            print(f"Failed to start autonomous trading: {start_result.get('message', 'Unknown error')}")
            return False
        
        print("Autonomous trading started successfully.")
        
        # Wait for a while
        print("Waiting for 30 seconds...")
        time.sleep(30)
        
        # Stop autonomous trading
        print("Stopping autonomous trading...")
        stop_result = autonomous_trader.stop()
        if not stop_result.get("success", False):
            print(f"Failed to stop autonomous trading: {stop_result.get('message', 'Unknown error')}")
            return False
        
        print("Autonomous trading stopped successfully.")
        
        # Disconnect from MT5
        print("Disconnecting from MT5...")
        mt5.disconnect()
        print("Disconnected from MT5.")
        
        return True
    except Exception as e:
        print(f"Exception in autonomous trading test: {e}")
        return False

if __name__ == "__main__":
    success = test_autonomous_trading()
    print(f"Test {'succeeded' if success else 'failed'}")
