from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator # Corrected import path
from src.core.models.market_data import MarketData # Corrected import path

class ADXIndicator(BaseIndicator):
    """Average Directional Index indicator."""

    def __init__(self, period: int = 14, smoothing: int = 14):
        """
        Initialize ADX indicator.

        Args:
            period: The period for calculating DI+ and DI-
            smoothing: The period for smoothing ADX
        """
        # Corrected super().__init__ call: Only pass the params dictionary
        super().__init__({
            'period': period,
            'smoothing': smoothing
        })
        # Store name separately if needed, but don't pass to base __init__
        self.name = 'ADX'


    def calculate(self, data: MarketData | pd.DataFrame) -> Dict[str, np.ndarray]: # Accept DataFrame too
        """Calculate ADX values."""
        # This method seems to expect a DataFrame, but the type hint is List[MarketData].
        # Assuming _prepare_data handles the conversion or the type hint needs update.
        # For now, focusing on the import fix. Let's assume _prepare_data exists and works.
        # df = self._prepare_data() # Assuming this method exists and returns a DataFrame

        # Placeholder DataFrame creation for calculation logic demonstration if _prepare_data is missing
        if not isinstance(data, pd.DataFrame):
             # Attempt conversion if it's a list of MarketData objects
             if isinstance(data, list) and all(isinstance(item, MarketData) for item in data):
                 df_data = [{'timestamp': md.timestamp, 'open': md.open, 'high': md.high, 'low': md.low, 'close': md.close, 'volume': md.volume} for md in data]
                 df = pd.DataFrame(df_data)
                 # Ensure timestamp is datetime and set as index if needed by calculations
                 if 'timestamp' in df.columns:
                     df['timestamp'] = pd.to_datetime(df['timestamp'])
                     df = df.set_index('timestamp')
             elif isinstance(data, MarketData): # Handle single MarketData object case if needed
                 df_data = [{'timestamp': data.timestamp, 'open': data.open, 'high': data.high, 'low': data.low, 'close': data.close, 'volume': data.volume}]
                 df = pd.DataFrame(df_data)
                 if 'timestamp' in df.columns:
                     df['timestamp'] = pd.to_datetime(df['timestamp'])
                     df = df.set_index('timestamp')
             else:
                 # If it's neither DataFrame nor List[MarketData], cannot proceed
                 print("ADXIndicator Error: Input data format not supported.")
                 return {}
        else:
             df = data # Assume input is already a DataFrame

        # Ensure required columns exist
        required_cols = ['high', 'low', 'close']
        if not all(col in df.columns for col in required_cols):
            print(f"ADXIndicator Error: DataFrame missing required columns: {required_cols}")
            return {}

        period = self.params['period']
        smoothing = self.params['smoothing']

        if df.empty or len(df) < period + smoothing: # Ensure enough data for calculation steps
             print(f"ADXIndicator Error: Insufficient data for calculation (needs >= {period + smoothing} bars).")
             return {}

        high = df['high'].values
        low = df['low'].values
        close = df['close'].values

        # Calculate True Range (TR)
        tr1 = np.abs(high[1:] - low[1:])
        tr2 = np.abs(high[1:] - close[:-1])
        tr3 = np.abs(low[1:] - close[:-1])
        tr = np.maximum.reduce([tr1, tr2, tr3])
        # Handle first bar TR separately
        first_tr = np.abs(high[0] - low[0]) if len(high) > 0 else 0
        tr = np.concatenate(([first_tr], tr)) # Prepend first bar's range

        # Calculate Directional Movement (+DM, -DM)
        move_up = high[1:] - high[:-1]
        move_down = low[:-1] - low[1:]
        pos_dm = np.where((move_up > move_down) & (move_up > 0), move_up, 0)
        neg_dm = np.where((move_down > move_up) & (move_down > 0), move_down, 0)
        pos_dm = np.concatenate(([0], pos_dm))
        neg_dm = np.concatenate(([0], neg_dm))

        # Calculate Smoothed TR, +DM, -DM (using Exponential Moving Average for smoothing)
        # Note: ewm span is related to period by span = 2*period - 1, but alpha=1/period is common for Wilder's smoothing
        atr = pd.Series(tr).ewm(alpha=1/period, adjust=False).mean().values
        smooth_pos_dm = pd.Series(pos_dm).ewm(alpha=1/period, adjust=False).mean().values
        smooth_neg_dm = pd.Series(neg_dm).ewm(alpha=1/period, adjust=False).mean().values

        # Calculate Directional Indicators (+DI, -DI)
        # Avoid division by zero
        atr_safe = np.where(atr == 0, 1e-10, atr) # Replace 0 with a tiny number
        pdi = 100 * smooth_pos_dm / atr_safe
        ndi = 100 * smooth_neg_dm / atr_safe

        # Calculate Directional Index (DX)
        di_sum = pdi + ndi
        di_sum_safe = np.where(di_sum == 0, 1e-10, di_sum) # Avoid division by zero
        dx = 100 * np.abs(pdi - ndi) / di_sum_safe

        # Calculate Average Directional Index (ADX)
        # Apply smoothing using ewm again
        adx = pd.Series(dx).ewm(alpha=1/smoothing, adjust=False).mean().values

        # --- Additional calculations from original file (kept for reference/potential use) ---
        # Calculate trend strength and direction (simplified)
        trend_strength = adx
        trend = np.where(pdi > ndi, 1, -1)

        # Calculate crossovers
        pdi_series = pd.Series(pdi)
        ndi_series = pd.Series(ndi)
        crossover = np.where(
            (pdi_series > ndi_series) & (pdi_series.shift(1) <= ndi_series.shift(1)), 1,
            np.where((pdi_series < ndi_series) & (pdi_series.shift(1) >= ndi_series.shift(1)), -1, 0)
        )

        # Calculate trend phases
        trend_phase = np.zeros_like(adx, dtype=int)
        trend_phase[adx >= 50] = 3    # Strong trend
        trend_phase[(adx >= 25) & (adx < 50)] = 2  # Moderate trend
        trend_phase[(adx >= 0) & (adx < 25)] = 1   # Weak trend or ranging

        # Calculate extreme conditions
        extreme = np.zeros_like(adx, dtype=int)
        extreme[adx >= 75] = 1  # Extremely strong trend
        # --- End of additional calculations ---

        # Pad results with NaNs at the beginning to match original data length
        # The number of NaNs depends on the calculation warmup period
        # A safe estimate might be period + smoothing, but exact depends on ewm implementation details
        # For simplicity, let's return the calculated arrays as is, alignment should happen externally if needed
        # Or, pad based on the length difference
        nan_pad_count = len(df) - len(adx)
        nan_pad = np.full(nan_pad_count, np.nan)

        # Return only the primary ADX value, PDI, and NDI as per typical usage
        return {
            'adx': np.concatenate((nan_pad, adx)),
            'pdi': np.concatenate((nan_pad, pdi[nan_pad_count:])), # Align PDI/NDI
            'ndi': np.concatenate((nan_pad, ndi[nan_pad_count:])),
        }

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        if self.params['smoothing'] < 1:
            raise ValueError("Smoothing period must be greater than 0")
        return True