"""
Admin Widget - Monitor active and past trading sessions from Firebase.
"""
import logging
from datetime import datetime, date
from typing import List, Dict, Any

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QLabel, QTableWidget, QPushButton,
                             QHBoxLayout, QHeaderView, QTableWidgetItem, QLineEdit,
                             QComboBox, QSpacerItem, QSizePolicy, QMessageBox,
                             QDateEdit, QGroupBox)
from PyQt5.QtCore import pyqtSlot, Qt, QDate
from PyQt5.QtGui import QColor, QBrush

# Import shared base widget for background tasks
from admin.ui.widgets.base_widget import BaseWorkerWidget

class SessionMonitorWidget(BaseWorkerWidget):
    def __init__(self, parent=None):
        super().__init__(parent=parent, mt5_connector=None)
        self.sessions_data = []
        self.user_names = {"All Users": None} # Cache user names for filter
        self._init_ui()
        self.task_successful.connect(self._handle_task_result)
        self.task_failed.connect(self._handle_task_error)
        # Initial load triggered by account manager signal connection

    def _init_ui(self):
        layout = QVBoxLayout(self)
        layout.addWidget(QLabel("Trading Session Monitoring"))

        # --- Filter Controls ---
        filter_group = QGroupBox("Filters")
        filter_layout_main = QVBoxLayout(filter_group)
        filter_row1 = QHBoxLayout()
        filter_row2 = QHBoxLayout()

        # Row 1: Account, Server, User (ComboBox)
        filter_row1.addWidget(QLabel("Acc:"))
        self.account_filter_input = QLineEdit()
        self.account_filter_input.setPlaceholderText("Number (Optional)")
        filter_row1.addWidget(self.account_filter_input)

        filter_row1.addWidget(QLabel("Srv:"))
        self.server_filter_input = QLineEdit()
        self.server_filter_input.setPlaceholderText("Server (Optional)")
        filter_row1.addWidget(self.server_filter_input)

        filter_row1.addWidget(QLabel("User:"))
        self.user_filter_combo = QComboBox() # Changed to ComboBox
        self.user_filter_combo.addItem("All Users") # Default option
        filter_row1.addWidget(self.user_filter_combo)

        # Row 2: Status, Dates, Button
        filter_row2.addWidget(QLabel("Status:"))
        self.status_filter_combo = QComboBox()
        self.status_filter_combo.addItems(["All", "active", "completed"])
        filter_row2.addWidget(self.status_filter_combo)

        filter_row2.addWidget(QLabel("From:"))
        self.start_date_filter = QDateEdit()
        self.start_date_filter.setCalendarPopup(True)
        self.start_date_filter.setDate(QDate.currentDate().addMonths(-1))
        self.start_date_filter.setDisplayFormat("yyyy-MM-dd")
        filter_row2.addWidget(self.start_date_filter)

        filter_row2.addWidget(QLabel("To:"))
        self.end_date_filter = QDateEdit()
        self.end_date_filter.setCalendarPopup(True)
        self.end_date_filter.setDate(QDate.currentDate())
        self.end_date_filter.setDisplayFormat("yyyy-MM-dd")
        filter_row2.addWidget(self.end_date_filter)

        filter_row2.addStretch()
        filter_button = QPushButton("Apply Filters / Refresh")
        filter_button.clicked.connect(self.refresh_sessions)
        filter_row2.addWidget(filter_button)

        filter_layout_main.addLayout(filter_row1)
        filter_layout_main.addLayout(filter_row2)
        layout.addWidget(filter_group)

        # --- Sessions Table ---
        self.sessions_table = QTableWidget()
        self.sessions_table.setColumnCount(9) # Added User Name column
        self.sessions_table.setHorizontalHeaderLabels([
            "Account", "Server", "User Name", "Status", "Start Time",
            "End Time", "Initial Bal", "Final Bal", "Trades"
        ])
        self.sessions_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeToContents)
        self.sessions_table.horizontalHeader().setStretchLastSection(True)
        self.sessions_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.sessions_table.setSelectionMode(QTableWidget.SingleSelection)
        self.sessions_table.setSortingEnabled(True)
        self.sessions_table.verticalHeader().setVisible(False)
        self.sessions_table.setAlternatingRowColors(True)
        layout.addWidget(self.sessions_table)

    @pyqtSlot(list)
    def update_user_filter_combo(self, accounts_list: List[Dict[str, Any]]):
        """Populates the user filter combo box based on the account list."""
        logging.info("SessionMonitor: Updating user filter combo box.")
        current_selection = self.user_filter_combo.currentText()
        self.user_filter_combo.clear()
        self.user_names = {"All Users": None} # Reset cache
        self.user_filter_combo.addItem("All Users")

        # Get unique user names, filtering out empty ones
        unique_names = sorted(list(set(acc.get('user_name', '') for acc in accounts_list if acc.get('user_name'))))

        for name in unique_names:
            self.user_names[name] = name # Store name for filtering
            self.user_filter_combo.addItem(name)

        # Restore previous selection if possible
        index = self.user_filter_combo.findText(current_selection)
        if index != -1:
            self.user_filter_combo.setCurrentIndex(index)
        else:
            self.user_filter_combo.setCurrentIndex(0) # Default to "All Users"

        # Trigger initial session load now that users are populated
        self.refresh_sessions()


    @pyqtSlot()
    def refresh_sessions(self):
        """Requests fetching the session list via the worker, applying filters."""
        logging.info("SessionMonitor: Requesting session list refresh.")
        start_dt = datetime.combine(self.start_date_filter.date().toPyDate(), datetime.min.time())
        end_dt = datetime.combine(self.end_date_filter.date().toPyDate(), datetime.max.time()) # Use max time for end date

        # Get selected user name for potential client-side filtering
        selected_user = self.user_filter_combo.currentText()

        filters = {
            "account_filter": self.account_filter_input.text().strip() or None,
            "server_filter": self.server_filter_input.text().strip() or None,
            # "user_name_filter": selected_user if selected_user != "All Users" else None, # Pass to backend if implemented
            "status_filter": self.status_filter_combo.currentText().lower(),
            "start_date_filter": start_dt,
            "end_date_filter": end_dt,
            "limit": 500
        }
        active_filters = {k: v for k, v in filters.items() if v is not None and v != 'all'}
        logging.info(f"Applying filters: {active_filters}")
        # Note: user_name_filter is not sent to backend currently, handled client-side
        self.start_task("admin_get_sessions", active_filters)

    @pyqtSlot(str, object)
    def _handle_task_result(self, task_id: str, result: object):
        """Handle successful task results."""
        logging.debug(f"SessionMonitor received task_successful: {task_id}")
        if task_id.startswith("admin_get_sessions"):
            if isinstance(result, list):
                logging.info(f"SessionMonitor: Received {len(result)} sessions.")
                # Client-side filtering for user name
                selected_user = self.user_filter_combo.currentText()
                if selected_user != "All Users":
                    filtered_result = [
                        s for s in result
                        if s.get('user_name', '') == selected_user
                    ]
                    logging.info(f"Filtered down to {len(filtered_result)} sessions for user '{selected_user}'.")
                else:
                    filtered_result = result

                self.sessions_data = filtered_result
                self._update_table(filtered_result)
            elif isinstance(result, dict) and "error" in result:
                 self._handle_task_error(task_id, result["error"])
            else:
                 logging.error("SessionMonitor: Received invalid data format for get_sessions")
                 self._handle_task_error(task_id, "Invalid data format")

    @pyqtSlot(str, str)
    def _handle_task_error(self, task_id: str, error_msg: str):
        """Handle failed task results."""
        logging.error(f"SessionMonitor: Task {task_id} failed: {error_msg}")
        QMessageBox.warning(self, "Operation Failed", f"Error fetching sessions:\n{error_msg}")
        if task_id.startswith("admin_get_sessions"):
            self.sessions_table.setRowCount(0)

    def _update_table(self, sessions_data: List[Dict[str, Any]]):
        """Update the table with session data."""
        self.sessions_table.setSortingEnabled(False)
        self.sessions_table.setRowCount(0)
        for session in sessions_data:
            row = self.sessions_table.rowCount()
            self.sessions_table.insertRow(row)

            acc_num = session.get('account_number', 'N/A')
            server = session.get('server_name', 'N/A')
            user_name = session.get('user_name', 'N/A') # Should be populated by manager now
            status = session.get('status', 'unknown')
            start_time = session.get('start_time_str', 'N/A')
            end_time = session.get('end_time_str', 'N/A')
            init_bal = f"{session.get('initial_balance', 0.0):,.2f}"
            final_bal = f"{session.get('final_balance', 0.0):,.2f}" if status == 'completed' else "N/A"
            trades = str(session.get('trades_count', 0))

            item_acc = QTableWidgetItem(acc_num); item_acc.setData(Qt.UserRole, session.get('doc_id'))
            item_srv = QTableWidgetItem(server)
            item_user = QTableWidgetItem(user_name)
            item_stat = QTableWidgetItem(status.capitalize())
            item_start = QTableWidgetItem(start_time)
            item_end = QTableWidgetItem(end_time)
            item_init_bal = QTableWidgetItem(init_bal)
            item_final_bal = QTableWidgetItem(final_bal)
            item_trades = QTableWidgetItem(trades)

            if status == 'active': item_stat.setForeground(QBrush(QColor("blue")))
            elif status == 'completed': item_stat.setForeground(QBrush(QColor("green")))
            else: item_stat.setForeground(QBrush(QColor("red")))

            for item in [item_init_bal, item_final_bal, item_trades]:
                 item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)

            self.sessions_table.setItem(row, 0, item_acc)
            self.sessions_table.setItem(row, 1, item_srv)
            self.sessions_table.setItem(row, 2, item_user)
            self.sessions_table.setItem(row, 3, item_stat)
            self.sessions_table.setItem(row, 4, item_start)
            self.sessions_table.setItem(row, 5, item_end)
            self.sessions_table.setItem(row, 6, item_init_bal)
            self.sessions_table.setItem(row, 7, item_final_bal)
            self.sessions_table.setItem(row, 8, item_trades)

        self.sessions_table.resizeColumnsToContents()
        self.sessions_table.resizeRowsToContents()
        self.sessions_table.setSortingEnabled(True)
