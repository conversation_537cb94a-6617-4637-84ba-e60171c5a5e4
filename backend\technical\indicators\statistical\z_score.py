from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class ZScoreIndicator(BaseIndicator):
    """Z-Score (Standard Score) indicator."""

    def __init__(self, period: int = 20, source: str = 'close'):
        """
        Initialize Z-Score indicator.

        Args:
            period: The lookback period for calculating mean and standard deviation.
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
        """
        super().__init__({
            'period': period,
            'source': source
        })

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate rolling Z-Score."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period']:
             return {'z_score': np.array([])}

        period = self.params['period']
        source_col = self.params['source'].lower()

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Calculate rolling mean and standard deviation
        rolling_mean = source_data.rolling(window=period).mean()
        rolling_std = source_data.rolling(window=period).std()

        # Calculate Z-Score, handle potential division by zero
        z_score = (source_data - rolling_mean) / rolling_std.replace(0, np.nan)
        z_score = z_score.fillna(0) # Fill NaNs resulting from division or initial period

        self._values = {
            'z_score': z_score.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        return True