import React, { useState, useEffect } from 'react';
import { useNotification } from '../components/Notification';
import '../styles/Pages.css';
import '../styles/BentoComponents.css';
import '../styles/TradeSignal.css';
import '../styles/TradeExecution.css';

// Import components for Trade Signal page
import SignalControls from '../components/signals/SignalControls';
import SignalMatrix from '../components/signals/SignalMatrix';
import ExecutionQueue from '../components/signals/ExecutionQueue';
import PositionsManagement from '../components/execution/PositionsManagement';

/**
 * TradeSignalPage component - Main page for trade signals and execution
 *
 * @param {string} selectedSymbol - Currently selected symbol
 * @param {function} onSymbolSelect - Handler for symbol selection
 * @returns {JSX.Element} - The rendered trade signal page
 */
function TradeSignalPage({ selectedSymbol, onSymbolSelect }) {
  const [timeframe, setTimeframe] = useState('H1');
  const [signalData, setSignalData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [symbols, setSymbols] = useState([]);
  const [selectedSignals, setSelectedSignals] = useState([]);
  const [strategyFilter, setStrategyFilter] = useState('all'); // 'all', 'scalping', 'intraday', 'swing', 'position'
  const [minimumConfidence, setMinimumConfidence] = useState(0); // Minimum confidence threshold (0-100)
  
  const notify = useNotification();

  // Fetch available symbols
  useEffect(() => {
    const fetchSymbols = async () => {
      try {
        const response = await fetch('http://localhost:5001/api/symbols');
        if (!response.ok) {
          throw new Error(`Failed to fetch symbols: ${response.status}`);
        }

        const data = await response.json();
        setSymbols(data);
      } catch (error) {
        console.error('Error fetching symbols:', error);
        notify.error('Error', 'Failed to load market symbols');
        setSymbols([]);
      }
    };

    fetchSymbols();
  }, [notify]);

  // Fetch signal data when symbol or timeframe changes
  useEffect(() => {
    const fetchSignalData = async () => {
      if (!selectedSymbol) return;

      setLoading(true);
      setSignalData(null);
      
      try {
        console.log(`Fetching signals for ${selectedSymbol} on ${timeframe} timeframe...`);
        // Use the new dedicated signals API endpoint
        const response = await fetch(`http://localhost:5001/api/signals/trade_signals?symbol=${selectedSymbol}&timeframe=${timeframe}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch signal data: ${response.status}`);
        }

        const data = await response.json();
        console.log('Signal data received from backend:', data);
        
        // The backend now handles signal generation completely
        // We just need to use the data as-is
        if (!data || !data.signals) {
          throw new Error('No signals received from backend');
        }
        
        // Directly use the signals from the backend
        setSignalData(data);
      } catch (error) {
        console.error('Error fetching signal data:', error);
        notify.warning('Data Error', error.message || 'Failed to load signal data');
        
        // Fall back to test data only if there's an error
        const testSignals = generateTestSignals(selectedSymbol, timeframe, 0);
        
        // Group the fallback test signals by strategy
        const scalpingSignals = testSignals.filter(s => s.strategy === 'scalping');
        const intradaySignals = testSignals.filter(s => s.strategy === 'intraday');
        const swingSignals = testSignals.filter(s => s.strategy === 'swing');
        const positionSignals = testSignals.filter(s => s.strategy === 'position');
        
        // Set test signals as fallback
        setSignalData({
          signals: testSignals,
          strategySignals: {
            scalping: scalpingSignals,
            intraday: intradaySignals,
            swing: swingSignals,
            position: positionSignals
          },
          baseData: null,
          currentPrice: 0 // Will be replaced with symbol-specific price in generateTestSignals
        });
      } finally {
        setLoading(false);
      }
    };

    fetchSignalData();
  }, [selectedSymbol, timeframe, notify]);

  /**
   * Process raw analysis data into strategy-specific trade signals
   */
  const processSignalData = (data, symbol, timeframe) => {
    if (!data || !data.analysis) {
      // Return properly initialized empty structure when no data
      return { 
        signals: [], 
        strategySignals: {
          scalping: [],
          intraday: [],
          swing: [],
          position: []
        },
        baseData: null,
        currentPrice: 0
      };
    }
    
    const analysisData = data.analysis;
    const baseSignal = analysisData.signal_recommendation || {};
    const currentPrice = analysisData.current_price?.bid || 
                        analysisData.current_price?.last || 
                        analysisData.price_data?.close?.[analysisData.price_data.close.length - 1] || 0;
    
    // Strategy-specific signals
    const strategySignals = {
      scalping: generateScalpingSignals(analysisData, baseSignal, symbol, timeframe, currentPrice),
      intraday: generateIntradaySignals(analysisData, baseSignal, symbol, timeframe, currentPrice),
      swing: generateSwingSignals(analysisData, baseSignal, symbol, timeframe, currentPrice),
      position: generatePositionSignals(analysisData, baseSignal, symbol, timeframe, currentPrice)
    };
    
    // Combine all signals
    let allSignals = [
      ...strategySignals.scalping,
      ...strategySignals.intraday,
      ...strategySignals.swing,
      ...strategySignals.position
    ];
    
    // Always use test data for now - we'll remove this when real signals are working
    console.log('Using test data for signals');
    const testSignals = generateTestSignals(symbol, timeframe, currentPrice || 93500);
    
    // Combine real signals with test signals
    allSignals = [...allSignals, ...testSignals];
    
    // Update strategy signals with test data
    strategySignals.scalping = [...strategySignals.scalping, ...testSignals.filter(s => s.strategy === 'scalping')];
    strategySignals.intraday = [...strategySignals.intraday, ...testSignals.filter(s => s.strategy === 'intraday')];
    strategySignals.swing = [...strategySignals.swing, ...testSignals.filter(s => s.strategy === 'swing')];
    strategySignals.position = [...strategySignals.position, ...testSignals.filter(s => s.strategy === 'position')];
    
    return {
      signals: allSignals,
      strategySignals,
      baseData: analysisData,
      currentPrice
    };
  };
  
  /**
   * Generate test signals for demonstration purposes
   */
  const generateTestSignals = (symbol, timeframe, currentPrice) => {
    // Create test signals for each strategy type
    const now = new Date();
    
    // Test data array
    return [
      // Scalping BUY signal
      {
        id: `test-${symbol}-${timeframe}-scalping-buy`,
        symbol,
        timeframe,
        strategy: 'scalping',
        signalType: 'BUY',
        confidence: 78,
        entry: {
          price: currentPrice,
          type: 'MARKET',
          time: new Date().toISOString()
        },
        stopLoss: currentPrice * 0.995,
        takeProfit: currentPrice * 1.01,
        riskReward: 2.0,
        timeWindow: {
          start: now.toISOString(),
          end: new Date(now.getTime() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours
          expires: new Date(now.getTime() + 4 * 60 * 60 * 1000).toISOString() // 4 hours
        },
        source: {
          trend: 'BULLISH',
          macd: 'BULLISH_CROSSOVER',
          rsi: 'OVERSOLD',
          primaryIndicator: 'rsi'
        },
        explanation: 'Scalping BUY opportunity based on oversold RSI and bullish MACD crossover. Quick reversal expected.'
      },
      
      // Scalping SELL signal
      {
        id: `test-${symbol}-${timeframe}-scalping-sell`,
        symbol,
        timeframe,
        strategy: 'scalping',
        signalType: 'SELL',
        confidence: 65,
        entry: {
          price: currentPrice,
          type: 'MARKET',
          time: new Date().toISOString()
        },
        stopLoss: currentPrice * 1.005,
        takeProfit: currentPrice * 0.99,
        riskReward: 2.0,
        timeWindow: {
          start: now.toISOString(),
          end: new Date(now.getTime() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours
          expires: new Date(now.getTime() + 4 * 60 * 60 * 1000).toISOString() // 4 hours
        },
        source: {
          trend: 'BEARISH',
          macd: 'BEARISH_CROSSOVER',
          rsi: 'OVERBOUGHT',
          primaryIndicator: 'rsi'
        },
        explanation: 'Scalping SELL opportunity based on overbought RSI and bearish MACD crossover. Quick reversal expected.'
      },
      
      // Intraday BUY signal
      {
        id: `test-${symbol}-${timeframe}-intraday-buy`,
        symbol,
        timeframe,
        strategy: 'intraday',
        signalType: 'BUY',
        confidence: 82,
        entry: {
          price: currentPrice,
          type: 'LIMIT',
          time: new Date().toISOString()
        },
        stopLoss: currentPrice * 0.99,
        takeProfit: currentPrice * 1.02,
        riskReward: 2.5,
        timeWindow: {
          start: now.toISOString(),
          end: new Date(now.getTime() + 8 * 60 * 60 * 1000).toISOString(), // 8 hours
          expires: new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
        },
        source: {
          trend: 'BULLISH',
          support_resistance: 'NEAR_SUPPORT',
          primaryIndicator: 'support_resistance'
        },
        explanation: 'Intraday BUY signal detected. Price is near key support level with bullish momentum.'
      },
      
      // Swing SELL signal
      {
        id: `test-${symbol}-${timeframe}-swing-sell`,
        symbol,
        timeframe,
        strategy: 'swing',
        signalType: 'SELL',
        confidence: 88,
        entry: {
          price: currentPrice,
          type: 'STOP',
          time: new Date().toISOString()
        },
        stopLoss: currentPrice * 1.015,
        takeProfit: currentPrice * 0.97,
        riskReward: 3.0,
        timeWindow: {
          start: now.toISOString(),
          end: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days
          expires: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
        },
        source: {
          trend: 'BEARISH',
          macd: 'BEARISH',
          support_resistance: 'NEAR_RESISTANCE',
          primaryIndicator: 'trend'
        },
        explanation: 'Swing SELL opportunity with strong bearish trend near resistance. Looking for multi-day downward movement.'
      },
      
      // Position STRONG BUY signal
      {
        id: `test-${symbol}-${timeframe}-position-buy`,
        symbol,
        timeframe,
        strategy: 'position',
        signalType: 'BUY',
        confidence: 92,
        entry: {
          price: currentPrice,
          type: 'LIMIT',
          time: new Date().toISOString()
        },
        stopLoss: currentPrice * 0.95,
        takeProfit: currentPrice * 1.15,
        riskReward: 3.0,
        timeWindow: {
          start: now.toISOString(),
          end: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
          expires: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
        },
        source: {
          trend: 'STRONG_BULLISH',
          support_resistance: 'MAJOR_SUPPORT',
          primaryIndicator: 'trend'
        },
        explanation: 'Long-term BUY opportunity identified. Strong bullish trend established with price near major support zone. Target holding period: 3-4 weeks minimum.'
      }
    ];
  };

  /**
   * Generate scalping signals (very short term)
   */
  const generateScalpingSignals = (analysisData, baseSignal, symbol, timeframe, currentPrice) => {
    // For testing, return a sample scalping signal
    // In production, this would analyze price action, momentum, and volatility
    // for ultra-short timeframes
    
    // Defensive check for missing or invalid baseSignal
    if (!baseSignal || typeof baseSignal !== 'object' || !baseSignal.signal || baseSignal.signal === 'NEUTRAL') {
      return [];
    }
    
    // Use the main signal but adjust for scalping (tighter stops, smaller targets)
    const direction = baseSignal.signal.includes('BUY') ? 'BUY' : 'SELL';
    const isStrong = baseSignal.signal.includes('STRONG');
    
    // Adjust stop loss and take profit for scalping (tighter ranges)
    const atrValue = analysisData.atr?.value || (currentPrice * 0.001); // 0.1% default
    const scalping_sl = direction === 'BUY' 
      ? currentPrice - (atrValue * 1.0) // Tighter stop for scalping
      : currentPrice + (atrValue * 1.0);
    
    const scalping_tp = direction === 'BUY'
      ? currentPrice + (atrValue * 2.0) // 1:2 risk-reward for scalping
      : currentPrice - (atrValue * 2.0);
    
    // Calculate execution window (shorter for scalping)
    const now = new Date();
    const executionStart = new Date(now.getTime());
    const executionEnd = new Date(now.getTime() + (2 * 60 * 60 * 1000)); // 2 hours

    return [{
      id: `${symbol}-${timeframe}-scalping-${direction}-${Date.now()}`,
      symbol,
      timeframe,
      strategy: 'scalping',
      signalType: direction,
      confidence: isStrong ? baseSignal.confidence : Math.max(40, baseSignal.confidence - 10),
      entry: {
        price: currentPrice,
        type: 'MARKET',
        time: new Date().toISOString()
      },
      stopLoss: parseFloat(scalping_sl.toFixed(5)),
      takeProfit: parseFloat(scalping_tp.toFixed(5)),
      riskReward: parseFloat((Math.abs(currentPrice - scalping_tp) / Math.abs(currentPrice - scalping_sl)).toFixed(2)),
      timeWindow: {
        start: executionStart.toISOString(),
        end: executionEnd.toISOString(),
        expires: executionEnd.toISOString()
      },
      source: {
        ...(baseSignal.indicators || {}),
        primaryIndicator: baseSignal.indicators ? Object.keys(baseSignal.indicators)[0] || 'price' : 'price'
      },
      explanation: `Scalping opportunity based on ${direction} signal with ${isStrong ? 'strong' : 'moderate'} momentum. Tight stop loss for quick in-and-out trade.`
    }];
  };

  /**
   * Generate intraday signals (single session)
   */
  const generateIntradaySignals = (analysisData, baseSignal, symbol, timeframe, currentPrice) => {
    // Defensive check for missing or invalid baseSignal
    if (!baseSignal || typeof baseSignal !== 'object' || !baseSignal.signal || baseSignal.signal === 'NEUTRAL') {
      return [];
    }
    
    // Use the main signal but adjust for intraday trading
    const direction = baseSignal.signal.includes('BUY') ? 'BUY' : 'SELL';
    const isStrong = baseSignal.signal.includes('STRONG');
    
    // Adjust stop loss and take profit for intraday (medium ranges)
    const atrValue = analysisData.atr?.value || (currentPrice * 0.002); // 0.2% default
    const intraday_sl = direction === 'BUY' 
      ? currentPrice - (atrValue * 1.5) 
      : currentPrice + (atrValue * 1.5);
    
    const intraday_tp = direction === 'BUY'
      ? currentPrice + (atrValue * 3.0) // 1:2 risk-reward 
      : currentPrice - (atrValue * 3.0);
    
    // Calculate execution window (current session)
    const now = new Date();
    const executionStart = new Date(now.getTime());
    const executionEnd = new Date(now.getTime() + (8 * 60 * 60 * 1000)); // 8 hours

    return [{
      id: `${symbol}-${timeframe}-intraday-${direction}-${Date.now()}`,
      symbol,
      timeframe,
      strategy: 'intraday',
      signalType: direction,
      confidence: isStrong ? baseSignal.confidence : Math.max(40, baseSignal.confidence - 5),
      entry: {
        price: currentPrice,
        type: 'MARKET',
        time: new Date().toISOString()
      },
      stopLoss: parseFloat(intraday_sl.toFixed(5)),
      takeProfit: parseFloat(intraday_tp.toFixed(5)),
      riskReward: parseFloat((Math.abs(currentPrice - intraday_tp) / Math.abs(currentPrice - intraday_sl)).toFixed(2)),
      timeWindow: {
        start: executionStart.toISOString(),
        end: executionEnd.toISOString(),
        expires: executionEnd.toISOString()
      },
      source: {
        ...(baseSignal.indicators || {}),
        primaryIndicator: baseSignal.indicators ? Object.keys(baseSignal.indicators)[0] || 'price' : 'price'
      },
      explanation: `Intraday opportunity based on ${direction} signal with ${isStrong ? 'strong' : 'moderate'} conviction. Trade has potential for completion within the current trading session.`
    }];
  };

  /**
   * Generate swing trading signals (days to weeks)
   */
  const generateSwingSignals = (analysisData, baseSignal, symbol, timeframe, currentPrice) => {
    // Only generate swing signals on H4 or higher timeframes
    // Defensive check for missing or invalid baseSignal
    if (!baseSignal || typeof baseSignal !== 'object' || !baseSignal.signal || baseSignal.signal === 'NEUTRAL' || 
        !['H4', 'D1', 'W1'].includes(timeframe)) {
      return [];
    }
    
    // Use the main signal for swing trading
    const direction = baseSignal.signal.includes('BUY') ? 'BUY' : 'SELL';
    const isStrong = baseSignal.signal.includes('STRONG');
    
    // Find key support/resistance for swing trading
    let sr_data = analysisData.support_resistance || {};
    let keyLevel = null;
    
    if (direction === 'BUY' && sr_data.support_levels) {
      // For buy signals, find a strong support level below price
      const supportLevels = sr_data.support_levels
        .filter(level => level.price < currentPrice && level.strength >= 6)
        .sort((a, b) => b.price - a.price); // Sort descending (highest support first)
      
      keyLevel = supportLevels[0]?.price;
    } else if (direction === 'SELL' && sr_data.resistance_levels) {
      // For sell signals, find a strong resistance level above price
      const resistanceLevels = sr_data.resistance_levels
        .filter(level => level.price > currentPrice && level.strength >= 6)
        .sort((a, b) => a.price - b.price); // Sort ascending (lowest resistance first)
      
      keyLevel = resistanceLevels[0]?.price;
    }
    
    // Adjust stop loss and take profit for swing trading
    const atrValue = analysisData.atr?.value || (currentPrice * 0.005); // 0.5% default
    
    // Use key levels if available, otherwise use ATR
    const swing_sl = direction === 'BUY' 
      ? (keyLevel ? (keyLevel * 0.999) : (currentPrice - (atrValue * 2.5)))
      : (keyLevel ? (keyLevel * 1.001) : (currentPrice + (atrValue * 2.5)));
    
    const swing_tp = direction === 'BUY'
      ? currentPrice + (Math.abs(currentPrice - swing_sl) * 2.5) // 1:2.5 risk-reward
      : currentPrice - (Math.abs(currentPrice - swing_sl) * 2.5);
    
    // Calculate execution window (longer for swing trades)
    const now = new Date();
    const executionStart = new Date(now.getTime());
    const executionEnd = new Date(now.getTime() + (2 * 24 * 60 * 60 * 1000)); // 2 days
    const expiryDate = new Date(now.getTime() + (14 * 24 * 60 * 60 * 1000)); // 14 days

    return [{
      id: `${symbol}-${timeframe}-swing-${direction}-${Date.now()}`,
      symbol,
      timeframe,
      strategy: 'swing',
      signalType: direction,
      confidence: Math.min(95, isStrong ? baseSignal.confidence + 5 : baseSignal.confidence),
      entry: {
        price: currentPrice,
        type: 'MARKET',
        time: new Date().toISOString()
      },
      stopLoss: parseFloat(swing_sl.toFixed(5)),
      takeProfit: parseFloat(swing_tp.toFixed(5)),
      riskReward: parseFloat((Math.abs(currentPrice - swing_tp) / Math.abs(currentPrice - swing_sl)).toFixed(2)),
      timeWindow: {
        start: executionStart.toISOString(),
        end: executionEnd.toISOString(),
        expires: expiryDate.toISOString()
      },
      source: {
        ...baseSignal.indicators,
        primaryIndicator: Object.keys(baseSignal.indicators || {})[0] || 'price',
        keyLevel: keyLevel ? parseFloat(keyLevel.toFixed(5)) : null
      },
      explanation: `Swing trading opportunity based on ${direction} signal with ${keyLevel ? 'key support/resistance level' : 'technical indicators'}. Target duration: 1-2 weeks.`
    }];
  };

  /**
   * Generate position trading signals (weeks to months)
   */
  const generatePositionSignals = (analysisData, baseSignal, symbol, timeframe, currentPrice) => {
    // Only generate position signals on D1 or higher timeframes with strong signals
    // Defensive check for missing or invalid baseSignal
    if (!baseSignal || typeof baseSignal !== 'object' || !baseSignal.signal || baseSignal.signal === 'NEUTRAL' || 
        !['D1', 'W1', 'MN1'].includes(timeframe) || 
        !baseSignal.signal.includes('STRONG')) {
      return [];
    }
    
    // Use the main signal for position trading
    const direction = baseSignal.signal.includes('BUY') ? 'BUY' : 'SELL';
    
    // Find major support/resistance for position trading
    let sr_data = analysisData.support_resistance || {};
    let majorLevel = null;
    
    if (direction === 'BUY' && sr_data.support_levels) {
      // For buy signals, find a strong support level below price
      const supportLevels = sr_data.support_levels
        .filter(level => level.price < currentPrice && level.strength >= 8) // Higher strength for position trades
        .sort((a, b) => b.price - a.price); 
      
      majorLevel = supportLevels[0]?.price;
    } else if (direction === 'SELL' && sr_data.resistance_levels) {
      // For sell signals, find a strong resistance level above price
      const resistanceLevels = sr_data.resistance_levels
        .filter(level => level.price > currentPrice && level.strength >= 8)
        .sort((a, b) => a.price - b.price);
      
      majorLevel = resistanceLevels[0]?.price;
    }
    
    // Adjust stop loss and take profit for position trading
    const atrValue = analysisData.atr?.value || (currentPrice * 0.01); // 1% default
    
    // Use key levels if available, otherwise use ATR
    const position_sl = direction === 'BUY' 
      ? (majorLevel ? (majorLevel * 0.995) : (currentPrice - (atrValue * 4.0)))
      : (majorLevel ? (majorLevel * 1.005) : (currentPrice + (atrValue * 4.0)));
    
    const position_tp = direction === 'BUY'
      ? currentPrice + (Math.abs(currentPrice - position_sl) * 3.0) // 1:3 risk-reward
      : currentPrice - (Math.abs(currentPrice - position_sl) * 3.0);
    
    // Calculate execution window (much longer for position trades)
    const now = new Date();
    const executionStart = new Date(now.getTime());
    const executionEnd = new Date(now.getTime() + (5 * 24 * 60 * 60 * 1000)); // 5 days
    const expiryDate = new Date(now.getTime() + (60 * 24 * 60 * 60 * 1000)); // 60 days

    return [{
      id: `${symbol}-${timeframe}-position-${direction}-${Date.now()}`,
      symbol,
      timeframe,
      strategy: 'position',
      signalType: direction,
      confidence: Math.min(98, baseSignal.confidence + 8),
      entry: {
        price: currentPrice,
        type: 'MARKET',
        time: new Date().toISOString()
      },
      stopLoss: parseFloat(position_sl.toFixed(5)),
      takeProfit: parseFloat(position_tp.toFixed(5)),
      riskReward: parseFloat((Math.abs(currentPrice - position_tp) / Math.abs(currentPrice - position_sl)).toFixed(2)),
      timeWindow: {
        start: executionStart.toISOString(),
        end: executionEnd.toISOString(),
        expires: expiryDate.toISOString()
      },
      source: {
        ...baseSignal.indicators,
        primaryIndicator: Object.keys(baseSignal.indicators || {})[0] || 'price',
        majorLevel: majorLevel ? parseFloat(majorLevel.toFixed(5)) : null
      },
      explanation: `Position trading opportunity based on strong ${direction} signal with ${majorLevel ? 'major support/resistance level' : 'powerful technical alignment'}. Target duration: 1-3 months.`
    }];
  };

  /**
   * Handle signal selection
   */
  const handleSignalSelect = (signalId, isSelected) => {
    if (isSelected) {
      // Find the signal by ID
      const signal = signalData.signals.find(s => s.id === signalId);
      if (signal) {
        setSelectedSignals(prev => [...prev, signal]);
      }
    } else {
      // Remove the signal from selection
      setSelectedSignals(prev => prev.filter(s => s.id !== signalId));
    }
  };

  /**
   * Handle strategy filter change
   */
  const handleStrategyFilterChange = (strategy) => {
    setStrategyFilter(strategy);
  };

  /**
   * Handle minimum confidence change
   */
  const handleConfidenceChange = (value) => {
    setMinimumConfidence(value);
  };

  /**
   * Handle symbol change
   */
  const handleSymbolChange = (e) => {
    // Clear selected signals when symbol changes
    setSelectedSignals([]);
    onSymbolSelect(e.target.value);
  };

  /**
   * Handle timeframe change
   */
  const handleTimeframeChange = (e) => {
    // Clear selected signals when timeframe changes
    setSelectedSignals([]);
    setTimeframe(e.target.value);
  };

  return (
    <div className="page-container trade-signal-page">
      <h2>Trade Signals</h2>
      
      {/* Controls for symbol, timeframe, filters */}
      <SignalControls
        selectedSymbol={selectedSymbol}
        onSymbolChange={handleSymbolChange}
        timeframe={timeframe}
        onTimeframeChange={handleTimeframeChange}
        strategyFilter={strategyFilter}
        onStrategyFilterChange={handleStrategyFilterChange}
        minimumConfidence={minimumConfidence}
        onConfidenceChange={handleConfidenceChange}
        loading={loading}
        symbols={symbols}
      />

      {loading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <div className="loading-text">Loading signal data...</div>
        </div>
      ) : !signalData ? (
        <div className="no-data-message">
          <p>No signal data available for {selectedSymbol} on {timeframe}.</p>
          <p>Select a symbol and timeframe or check connection.</p>
        </div>
      ) : (
        <div className="trade-signal-content">
          {/* Signal Matrix - displays all available signals */}
          <SignalMatrix
            signalData={signalData}
            strategyFilter={strategyFilter}
            minimumConfidence={minimumConfidence}
            onSignalSelect={handleSignalSelect}
            selectedSignals={selectedSignals.map(s => s.id)}
            currentPrice={signalData.currentPrice}
          />
          
          {/* Execution Queue - displays selected signals */}
          <ExecutionQueue
            selectedSignals={selectedSignals}
            onRemoveSignal={(id) => handleSignalSelect(id, false)}
          />
          
          {/* Positions Management - displays open positions */}
          <PositionsManagement />
        </div>
      )}
    </div>
  );
}

export default TradeSignalPage;
