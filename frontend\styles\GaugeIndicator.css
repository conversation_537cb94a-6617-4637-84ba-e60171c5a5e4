/* Gauge Indicator Styles */
.gauge-container {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto;
}

.gauge-outer {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(
    from 225deg,
    #ff4d4d 0deg 90deg,
    #e6e600 90deg 180deg,
    #00cc00 180deg 270deg
  );
  clip-path: polygon(0 50%, 100% 50%, 100% 100%, 0% 100%);
}

.gauge-inner {
  position: absolute;
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding-bottom: 5px;
  font-weight: bold;
  font-size: 1.2rem;
  color: white;
}

.gauge-needle {
  position: absolute;
  bottom: 10%;
  left: 50%;
  width: 4px;
  background-color: white;
  transform-origin: bottom center;
  border-radius: 2px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.gauge-labels {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  padding: 0 10px;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
}

.gauge-label-oversold {
  text-align: left;
}

.gauge-label-neutral {
  text-align: center;
}

.gauge-label-overbought {
  text-align: right;
}

/* Gauge sizes for different indicators */
.gauge-small {
  width: 80px;
  height: 80px;
}

.gauge-small .gauge-inner {
  font-size: 0.9rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .gauge-container {
    width: 100px;
    height: 100px;
  }
  
  .gauge-small {
    width: 70px;
    height: 70px;
  }
}
