# Risk Management Implementation Plan for Autonomous Trading System

## Overview

This document outlines the implementation plan for adding advanced risk management features to the autonomous trading system, including:

1. Maximum Total Risk
2. Safe Break-Even Calculation
3. Safe Trail Stop Calculation
4. Maximum Profit/Loss Thresholds

## 1. Backend Implementation

### 1.1 Update Configuration Structure

- Modify `AUTONOMOUS_CONFIG` in `autonomous_fixed.py` to include new risk parameters:
  ```python
  AUTONOMOUS_CONFIG = {
      # Existing fields...
      "risk_percent": 1.0,
      # New fields
      "max_total_risk_percent": 5.0,  # Maximum total risk as percentage of balance
      "breakeven_trigger_percent": 0.5,  # Move to breakeven after X% profit reached
      "trailing_stop_trigger_percent": 1.0,  # Start trailing after X% profit
      "trailing_stop_distance_percent": 0.5,  # Trail by X% of current price
      "max_profit_target": 100.0,  # $100 profit to deactivate bot
      "max_loss_target": 50.0,  # $50 loss to deactivate bot
      "active": true  # Flag to enable/disable bot (used when targets are hit)
  }
  ```

### 1.2 Total Risk Calculation

- Add a function to calculate current total risk exposure:
  ```python
  def calculate_total_risk_exposure(positions, account_balance):
      """Calculate the total risk exposure across all open positions."""
      total_risk = 0.0
      for position in positions:
          # Calculate risk for each position based on entry and SL
          if position["sl"] != 0:
              if position["type"] == 0:  # BUY
                  risk_per_position = (position["price_open"] - position["sl"]) * position["volume"] * get_contract_size(position["symbol"])
              else:  # SELL
                  risk_per_position = (position["sl"] - position["price_open"]) * position["volume"] * get_contract_size(position["symbol"])
              total_risk += risk_per_position
      
      # Calculate as a percentage of account balance
      risk_percentage = (total_risk / account_balance) * 100 if account_balance > 0 else 0
      
      return risk_percentage
  ```

### 1.3 Trade Execution Enhancements

- Modify the trading loop to check risk thresholds before placing trades:
  ```python
  # Inside _trading_loop function
  # Check total risk before placing a new trade
  account_info = mt5.account_info()
  if account_info:
      account_balance = account_info.balance
      current_risk = calculate_total_risk_exposure(open_positions, account_balance)
      
      if current_risk >= AUTONOMOUS_CONFIG["max_total_risk_percent"]:
          logger.warning(f"Maximum total risk reached ({current_risk:.2f}%). Skipping trade.")
          continue
  ```

### 1.4 Break-Even and Trailing Stop Functions

- Implement functions to check and adjust stop losses:
  ```python
  def check_and_adjust_stops(positions, mt5_instance):
      """Check all positions and adjust stop losses based on config settings."""
      for position in positions:
          # Break-even check
          adjust_to_breakeven(position, mt5_instance)
          # Trailing stop check
          adjust_trailing_stop(position, mt5_instance)

  def adjust_to_breakeven(position, mt5_instance):
      """Move stop loss to break-even if profit threshold is reached."""
      symbol = position["symbol"]
      position_id = position["ticket"]
      entry_price = position["price_open"]
      current_price = position["price_current"]
      current_sl = position["sl"]
      
      # Skip if no SL or already at/beyond breakeven
      if current_sl == 0 or (position["type"] == 0 and current_sl >= entry_price) or \
         (position["type"] == 1 and current_sl <= entry_price):
          return
          
      # Calculate profit percent
      profit_percent = 0
      if position["type"] == 0:  # BUY
          profit_percent = (current_price - entry_price) / entry_price * 100
      else:  # SELL
          profit_percent = (entry_price - current_price) / entry_price * 100
      
      # Check if profit threshold is reached
      if profit_percent >= AUTONOMOUS_CONFIG["breakeven_trigger_percent"]:
          # Move SL to entry price
          modify_result = mt5_instance.modify_position(
              ticket=position_id,
              sl=entry_price,
              tp=position["tp"]
          )
          
          if modify_result["success"]:
              logger.info(f"Moved stop loss to break-even for {symbol} position {position_id}")
          else:
              logger.error(f"Failed to move stop loss to break-even: {modify_result['message']}")
  ```

### 1.5 Trail Stop Implementation

```python
def adjust_trailing_stop(position, mt5_instance):
    """Adjust trailing stop if profit threshold is reached."""
    symbol = position["symbol"]
    position_id = position["ticket"]
    entry_price = position["price_open"]
    current_price = position["price_current"]
    current_sl = position["sl"]
    
    # Skip if no SL
    if current_sl == 0:
        return
        
    # Calculate profit percent
    profit_percent = 0
    if position["type"] == 0:  # BUY
        profit_percent = (current_price - entry_price) / entry_price * 100
        
        # Check if trailing threshold is reached
        if profit_percent >= AUTONOMOUS_CONFIG["trailing_stop_trigger_percent"]:
            # Calculate new SL based on trailing distance
            trail_distance = current_price * (AUTONOMOUS_CONFIG["trailing_stop_distance_percent"] / 100)
            new_sl = current_price - trail_distance
            
            # Only move SL if it improves the current SL
            if new_sl > current_sl:
                modify_result = mt5_instance.modify_position(
                    ticket=position_id,
                    sl=new_sl,
                    tp=position["tp"]
                )
                
                if modify_result["success"]:
                    logger.info(f"Updated trailing stop for {symbol} position {position_id} to {new_sl}")
                else:
                    logger.error(f"Failed to update trailing stop: {modify_result['message']}")
    
    else:  # SELL
        profit_percent = (entry_price - current_price) / entry_price * 100
        
        # Check if trailing threshold is reached
        if profit_percent >= AUTONOMOUS_CONFIG["trailing_stop_trigger_percent"]:
            # Calculate new SL based on trailing distance
            trail_distance = current_price * (AUTONOMOUS_CONFIG["trailing_stop_distance_percent"] / 100)
            new_sl = current_price + trail_distance
            
            # Only move SL if it improves the current SL
            if new_sl < current_sl or current_sl == 0:
                modify_result = mt5_instance.modify_position(
                    ticket=position_id,
                    sl=new_sl,
                    tp=position["tp"]
                )
                
                if modify_result["success"]:
                    logger.info(f"Updated trailing stop for {symbol} position {position_id} to {new_sl}")
                else:
                    logger.error(f"Failed to update trailing stop: {modify_result['message']}")
```

### 1.6 Max Profit/Loss Monitoring

```python
def check_profit_loss_thresholds(positions, mt5_instance):
    """Check if max profit/loss thresholds have been reached."""
    # Get account information
    account_info = mt5.account_info()
    if not account_info:
        logger.error("Failed to get account information for P/L check")
        return False
    
    # Calculate total profit/loss across all positions
    total_profit_loss = sum(position["profit"] for position in positions)
    
    # Check against thresholds
    max_profit_target = AUTONOMOUS_CONFIG.get("max_profit_target", 0)
    max_loss_target = AUTONOMOUS_CONFIG.get("max_loss_target", 0)
    
    if max_profit_target > 0 and total_profit_loss >= max_profit_target:
        logger.info(f"Maximum profit target reached (${total_profit_loss:.2f}). Deactivating autonomous trading.")
        return True
    
    if max_loss_target > 0 and total_profit_loss <= -max_loss_target:
        logger.info(f"Maximum loss threshold reached (${total_profit_loss:.2f}). Deactivating autonomous trading.")
        return True
    
    return False
```

### 1.7 Modified Trading Loop

```python
# Inside _trading_loop function
try:
    # Use the Flask app context
    with app.app_context():
        # Get MT5 instance
        mt5_instance = app.config.get('MT5_INSTANCE')
        if not mt5_instance or not mt5_instance.is_connected():
            logger.error("MT5 connection lost or not available")
            time.sleep(30)  # Wait before retrying
            continue

        # Get current open positions
        positions_result = mt5_instance.get_open_positions()
        if not positions_result.get("success", False):
            logger.error(f"Failed to get open positions: {positions_result.get('message', 'Unknown error')}")
            time.sleep(10)
            continue
            
        open_positions = positions_result.get("positions", [])
        
        # Check and adjust stop losses
        check_and_adjust_stops(open_positions, mt5_instance)
        
        # Check profit/loss thresholds
        if check_profit_loss_thresholds(open_positions, mt5_instance):
            # If thresholds reached, deactivate bot
            AUTONOMOUS_CONFIG["active"] = False
            AUTONOMOUS_RUNNING = False
            logger.info("Autonomous trading stopped due to profit/loss threshold")
            break
        
        # Continue with normal trading logic only if bot is active
        if not AUTONOMOUS_CONFIG["active"]:
            logger.info("Autonomous trading is deactivated. Stopping trading loop.")
            AUTONOMOUS_RUNNING = False
            break
            
        # Rest of the trading logic...
```

## 2. Frontend Implementation

### 2.1 Update the Risk Settings UI

- Add new risk controls to `AutonomousTradingPage.jsx`:

```jsx
// Add new state variables
const [maxTotalRiskPercent, setMaxTotalRiskPercent] = useState(5.0);
const [breakevenTriggerPercent, setBreakevenTriggerPercent] = useState(0.5);
const [trailingStopTriggerPercent, setTrailingStopTriggerPercent] = useState(1.0);
const [trailingStopDistancePercent, setTrailingStopDistancePercent] = useState(0.5);
const [maxProfitTarget, setMaxProfitTarget] = useState(100.0);
const [maxLossTarget, setMaxLossTarget] = useState(50.0);

// Add to the component's JSX, inside the config-panel
<div className="risk-management-section">
  <h4>Risk Management Settings</h4>
  
  <div className="form-group">
    <label htmlFor="risk-per-trade">Risk Per Trade (%)</label>
    <input
      id="risk-per-trade"
      type="number"
      min="0.1"
      max="10"
      step="0.1"
      value={riskPerTrade}
      onChange={(e) => setRiskPerTrade(e.target.value)}
    />
  </div>
  
  <div className="form-group">
    <label htmlFor="max-total-risk">Max Total Risk (%)</label>
    <input
      id="max-total-risk"
      type="number"
      min="1"
      max="20"
      step="0.5"
      value={maxTotalRiskPercent}
      onChange={(e) => setMaxTotalRiskPercent(e.target.value)}
    />
    <span className="form-help">Maximum total risk across all positions</span>
  </div>
  
  <div className="form-group">
    <label htmlFor="breakeven-trigger">Breakeven Trigger (%)</label>
    <input
      id="breakeven-trigger"
      type="number"
      min="0.1"
      max="5"
      step="0.1"
      value={breakevenTriggerPercent}
      onChange={(e) => setBreakevenTriggerPercent(e.target.value)}
    />
    <span className="form-help">Move SL to breakeven after reaching this profit %</span>
  </div>
  
  <div className="form-group">
    <label htmlFor="trailing-trigger">Trailing Stop Trigger (%)</label>
    <input
      id="trailing-trigger"
      type="number"
      min="0.1"
      max="5"
      step="0.1"
      value={trailingStopTriggerPercent}
      onChange={(e) => setTrailingStopTriggerPercent(e.target.value)}
    />
    <span className="form-help">Start trailing stop after reaching this profit %</span>
  </div>
  
  <div className="form-group">
    <label htmlFor="trailing-distance">Trailing Stop Distance (%)</label>
    <input
      id="trailing-distance"
      type="number"
      min="0.1"
      max="5"
      step="0.1"
      value={trailingStopDistancePercent}
      onChange={(e) => setTrailingStopDistancePercent(e.target.value)}
    />
    <span className="form-help">Distance to maintain for trailing stop</span>
  </div>
  
  <div className="form-group">
    <label htmlFor="max-profit">Max Profit Target ($)</label>
    <input
      id="max-profit"
      type="number"
      min="0"
      step="10"
      value={maxProfitTarget}
      onChange={(e) => setMaxProfitTarget(e.target.value)}
    />
    <span className="form-help">Stop bot after reaching this profit (0 to disable)</span>
  </div>
  
  <div className="form-group">
    <label htmlFor="max-loss">Max Loss Limit ($)</label>
    <input
      id="max-loss"
      type="number"
      min="0"
      step="10"
      value={maxLossTarget}
      onChange={(e) => setMaxLossTarget(e.target.value)}
    />
    <span className="form-help">Stop bot after reaching this loss (0 to disable)</span>
  </div>
</div>
```

### 2.2 Update API Requests

- Modify the `handleActivateStrategy` function to include the new parameters:

```jsx
const handleActivateStrategy = async () => {
  // Validation code...
  
  setIsActivating(true);
  try {
    const response = await fetch('http://localhost:5001/api/autonomous/activate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        strategy_id: selectedStrategy,
        symbol: selectedSymbol,
        timeframe: timeframe,
        risk_percent: parseFloat(riskPerTrade),
        max_trades: parseInt(maxOpenTrades),
        min_confidence: parseInt(minConfidence),
        // New parameters
        max_total_risk_percent: parseFloat(maxTotalRiskPercent),
        breakeven_trigger_percent: parseFloat(breakevenTriggerPercent),
        trailing_stop_trigger_percent: parseFloat(trailingStopTriggerPercent),
        trailing_stop_distance_percent: parseFloat(trailingStopDistancePercent),
        max_profit_target: parseFloat(maxProfitTarget),
        max_loss_target: parseFloat(maxLossTarget)
      })
    });
    
    // Rest of the function...
  }
}
```

### 2.3 Add Visual Indicators for Risk Management

- Create a risk dashboard in the monitoring tab:

```jsx
// Add to renderMonitoringSection function
<div className="monitoring-card risk-card">
  <h4>Risk Management Status</h4>
  <div className="metrics-grid">
    <div className="metric">
      <span className="metric-label">Current Risk Exposure:</span>
      <span className={`metric-value ${system_health.current_risk_percent <= system_health.max_risk_percent ? 'good' : 'warning'}`}>
        {system_health.current_risk_percent?.toFixed(2)}% / {system_health.max_risk_percent?.toFixed(2)}%
      </span>
    </div>
    <div className="metric">
      <span className="metric-label">Break-Even Positions:</span>
      <span className="metric-value">{system_health.breakeven_positions || 0}</span>
    </div>
    <div className="metric">
      <span className="metric-label">Trailing Stop Positions:</span>
      <span className="metric-value">{system_health.trailing_positions || 0}</span>
    </div>
    <div className="metric">
      <span className="metric-label">Current P/L:</span>
      <span className={`metric-value ${performance.overall.total_profit >= 0 ? 'positive' : 'negative'}`}>
        ${Math.abs(performance.overall.total_profit).toFixed(2)}
      </span>
    </div>
    <div className="metric">
      <span className="metric-label">Profit Target:</span>
      <span className="metric-value">${system_health.max_profit_target?.toFixed(2) || 'Disabled'}</span>
    </div>
    <div className="metric">
      <span className="metric-label">Loss Limit:</span>
      <span className="metric-value">${system_health.max_loss_target?.toFixed(2) || 'Disabled'}</span>
    </div>
  </div>
</div>
```

## 3. API Endpoints Implementation

### 3.1 Update Settings Endpoint

- Modify the `update_settings` function in `autonomous_fixed.py` to handle new parameters:

```python
@autonomous_bp.route('/settings', methods=['POST'])
def update_settings():
    global AUTONOMOUS_CONFIG
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
            
        # Original settings
        if "symbols" in data:
            AUTONOMOUS_CONFIG["symbols"] = data["symbols"]
        if "timeframes" in data:
            AUTONOMOUS_CONFIG["timeframes"] = data["timeframes"]
        if "scan_interval" in data:
            AUTONOMOUS_CONFIG["scan_interval"] = int(data["scan_interval"])
        if "min_confidence" in data:
            AUTONOMOUS_CONFIG["min_confidence"] = int(data["min_confidence"])
        if "max_trades" in data:
            AUTONOMOUS_CONFIG["max_trades"] = int(data["max_trades"])
        if "allowed_styles" in data:
            AUTONOMOUS_CONFIG["allowed_styles"] = data["allowed_styles"]
        if "allow_multiple_positions" in data:
            AUTONOMOUS_CONFIG["allow_multiple_positions"] = bool(data["allow_multiple_positions"])
        if "risk_percent" in data:
            AUTONOMOUS_CONFIG["risk_percent"] = float(data["risk_percent"])
        
        # New risk management settings
        if "max_total_risk_percent" in data:
            AUTONOMOUS_CONFIG["max_total_risk_percent"] = float(data["max_total_risk_percent"])
        if "breakeven_trigger_percent" in data:
            AUTONOMOUS_CONFIG["breakeven_trigger_percent"] = float(data["breakeven_trigger_percent"])
        if "trailing_stop_trigger_percent" in data:
            AUTONOMOUS_CONFIG["trailing_stop_trigger_percent"] = float(data["trailing_stop_trigger_percent"])
        if "trailing_stop_distance_percent" in data:
            AUTONOMOUS_CONFIG["trailing_stop_distance_percent"] = float(data["trailing_stop_distance_percent"])
        if "max_profit_target" in data:
            AUTONOMOUS_CONFIG["max_profit_target"] = float(data["max_profit_target"])
        if "max_loss_target" in data:
            AUTONOMOUS_CONFIG["max_loss_target"] = float(data["max_loss_target"])
        
        # Save updated config to file
        with open("autonomous_config.json", "w") as f:
            json.dump(AUTONOMOUS_CONFIG, f, indent=4)
        
        return jsonify({"success": True, "config": AUTONOMOUS_CONFIG})
    
    except Exception as e:
        logger.exception(f"Error updating settings: {str(e)}")
        return jsonify({"error": f"Failed to update settings: {str(e)}"}), 500
```

### 3.2 Enhanced Status API

- Enhance the status endpoint to include risk management metrics:

```python
@autonomous_bp.route('/status', methods=['GET'])
def get_status():
    try:
        # Get open positions to calculate current risk exposure
        mt5_instance = current_app.config.get('MT5_INSTANCE')
        positions_result = mt5_instance.get_open_positions() if mt5_instance else {"success": False}
        open_positions = positions_result.get("positions", []) if positions_result.get("success", False) else []
        
        # Get account info
        account_info = mt5.account_info()
        account_balance = account_info.balance if account_info else 0
        
        # Calculate risk exposure
        current_risk_percent = calculate_total_risk_exposure(open_positions, account_balance)
        
        # Count positions with break-even and trailing stops
        breakeven_positions = 0
        trailing_positions = 0
        total_profit_loss = 0
        
        for position in open_positions:
            total_profit_loss += position.get("profit", 0)
            entry_price = position.get("price_open", 0)
            sl = position.get("sl", 0)
            
            if sl != 0:
                # Check if stop loss is at break-even (within 5 points)
                if abs(sl - entry_price) < (5 * mt5.symbol_info(position.get("symbol", "")).point):
                    breakeven_positions += 1
                # Assume it's a trailing stop if not at entry and not initial stop
                elif (position.get("type") == 0 and sl > position.get("price_open", 0)) or \
                     (position.get("type") == 1 and sl < position.get("price_open", 0)):
                    trailing_positions += 1
        
        return jsonify({
            "running": AUTONOMOUS_RUNNING,
            "active": AUTONOMOUS_CONFIG.get("active", True),
            "strategies_count": len(ACTIVE_STRATEGIES),
            "open_positions": len(open_positions),
            "current_risk_percent": current_risk_percent,
            "max_risk_percent": AUTONOMOUS_CONFIG.get("max_total_risk_percent", 5.0),
            "breakeven_positions": breakeven_positions,
            "trailing_positions": trailing_positions,
            "current_profit_loss": total_profit_loss,
            "max_profit_target": AUTONOMOUS_CONFIG.get("max_profit_target", 0),
            "max_loss_target": AUTONOMOUS_CONFIG.get("max_loss_target", 0),
            "config": AUTONOMOUS_CONFIG
        })
    except Exception as e:
        logger.exception(f"Error getting status: {str(e)}")
        return jsonify({"error": f"Failed to get status: {str(e)}"}), 500
```

## 4. Implementation Steps

1. **Update Backend Configuration**:
   - Add new risk parameters to AUTONOMOUS_CONFIG
   - Implement risk calculation functions
   - Add break-even and trailing stop logic
   - Implement profit/loss threshold monitoring

2. **Enhance Frontend**:
   - Add new UI components for risk settings
   - Update API requests to include new parameters
   - Add visual risk indicators to monitoring dashboard

3. **Testing Strategy**:
   - Test with small risk values first
   - Verify break-even moves stop losses correctly
   - Verify trailing stops follow price movement
   - Test max profit/loss thresholds trigger bot deactivation

## 5. Future Enhancements

- Per-symbol risk management settings
- Time-based risk management (e.g., daily loss limits)
- Risk-based position sizing adjustments
- Email/notification alerts when thresholds are reached
- Position drawdown limits for individual trades
