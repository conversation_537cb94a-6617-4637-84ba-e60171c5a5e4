import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './styles.css';
import { patchNumberPrototype, setupGlobalErrorHandler } from './utils/errorHandling';

// Set up error handling
setupGlobalErrorHandler();

// Patch Number.prototype.toFixed to prevent crashes
patchNumberPrototype();

// Create root element
const root = ReactDOM.createRoot(document.getElementById('root'));

// Render the App component
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
