# Development Guide

## Setup and Installation

### Prerequisites
- Python 3.12+
- Node.js 18+
- MetaTrader 5 platform installed
- Git

### Backend Setup
1. Create a Python virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install required Python packages:
```bash
pip install MetaTrader5 numpy pandas websockets fastapi uvicorn
```

3. Configure MT5 settings in `backend/mt5_settings.json`

### Frontend Setup
1. Install dependencies:
```bash
cd frontend
npm install
```

2. Development mode:
```bash
npm run dev
```

## Development Workflow

### Backend Development
1. Module organization:
   - Keep core trading logic in respective modules
   - Use logging for debugging and monitoring
   - Implement error handling for MT5 operations

2. Testing:
   - Use `test_mt5.py` for MT5 connection testing
   - Implement unit tests for each module
   - Test trading strategies in demo account first

### Frontend Development
1. Component structure:
   - Maintain React components in `frontend/components`
   - Follow atomic design principles
   - Implement responsive design

2. State management:
   - Use React hooks for local state
   - Implement websocket listeners for real-time updates
   - Handle MT5 connection status

## Code Standards

### Python
- Follow PEP 8 style guide
- Use type hints
- Document functions and classes
- Handle exceptions appropriately

### JavaScript/React
- Use ES6+ features
- Follow React best practices
- Implement proper error boundaries
- Use PropTypes for component props

## Git Workflow
1. Feature branches
2. Pull request reviews
3. Merge only after tests pass
4. Keep commits atomic and well-documented

## Deployment

### Development Build
```bash
npm run build:dev
```

### Production Build
```bash
npm run build:prod
python start_app.py
```

## Troubleshooting

### Common Issues
1. MT5 Connection
   - Check MT5 platform is running
   - Verify credentials
   - Check network connectivity

2. Frontend Issues
   - Clear node_modules and reinstall
   - Check for port conflicts
   - Verify websocket connections

3. Backend Issues
   - Check Python environment
   - Verify MT5 terminal access
   - Review log files
