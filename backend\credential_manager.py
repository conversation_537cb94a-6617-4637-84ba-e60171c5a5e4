"""
Secure credential storage implementation supporting OS Keychain and encrypted file fallback.
"""
import os
import json
import logging
import base64
from typing import Dict, Optional
from pathlib import Path
import keyring
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CredentialError(Exception):
    """Base exception for credential-related errors."""
    pass

class KeychainError(CredentialError):
    """Raised when OS keychain operations fail."""
    pass

class EncryptionError(CredentialError):
    """Raised when encryption/decryption operations fail."""
    pass

class ValidationError(CredentialError):
    """Raised when credential validation fails."""
    pass

class CredentialsManager:
    """Manages secure storage and retrieval of MT5 credentials."""
    
    KEYCHAIN_SERVICE = "garudaalgo_mt5"
    KEYCHAIN_USERNAME = "mt5_credentials"
    FALLBACK_FILE = "encrypted_credentials.dat"
    KEY_FILE = "key.dat"
    
    def __init__(self, storage_dir: str = None):
        """Initialize the credentials manager.
        
        Args:
            storage_dir: Optional directory for fallback file storage.
        """
        self.storage_dir = storage_dir or os.path.join(os.path.expanduser("~"), ".garudaalgo")
        os.makedirs(self.storage_dir, mode=0o700, exist_ok=True)
        self._init_encryption()

    def _init_encryption(self):
        """Initialize encryption key for file-based storage."""
        key_path = os.path.join(self.storage_dir, self.KEY_FILE)
        try:
            if os.path.exists(key_path):
                with open(key_path, 'rb') as f:
                    self.key = f.read()
            else:
                self.key = Fernet.generate_key()
                with open(key_path, 'wb') as f:
                    f.write(self.key)
                os.chmod(key_path, 0o600)
            
            self.fernet = Fernet(self.key)
        except Exception as e:
            raise EncryptionError(f"Failed to initialize encryption: {str(e)}")

    def _validate_credentials(self, account: str, password: str, server: str):
        """Validate credential format and content."""
        if not all([account, password, server]):
            raise ValidationError("All credential fields are required")
        
        if not account.isdigit():
            raise ValidationError("Account number must contain only digits")
        
        if len(password) < 8:
            raise ValidationError("Password must be at least 8 characters")
        
        if not server:
            raise ValidationError("Server address is required")

    def save_credentials(self, account: str, password: str, server: str) -> bool:
        """Save MT5 credentials using available storage method."""
        try:
            self._validate_credentials(account, password, server)
            credentials = {
                "account": account,
                "password": password,
                "server": server
            }
            
            # Try OS keychain first
            try:
                keyring.set_password(
                    self.KEYCHAIN_SERVICE,
                    self.KEYCHAIN_USERNAME,
                    json.dumps(credentials)
                )
                logger.info("Credentials saved to OS keychain")
                return True
            except Exception as e:
                logger.warning(f"Keychain storage failed, using encrypted file fallback: {str(e)}")
                return self._save_to_file(credentials)
                
        except Exception as e:
            logger.error(f"Failed to save credentials: {str(e)}")
            raise CredentialError(f"Failed to save credentials: {str(e)}")

    def _save_to_file(self, credentials: Dict[str, str]) -> bool:
        """Save credentials to encrypted file."""
        try:
            encrypted = self.fernet.encrypt(json.dumps(credentials).encode())
            file_path = os.path.join(self.storage_dir, self.FALLBACK_FILE)
            
            with open(file_path, 'wb') as f:
                f.write(encrypted)
            
            os.chmod(file_path, 0o600)
            logger.info("Credentials saved to encrypted file")
            return True
            
        except Exception as e:
            raise EncryptionError(f"Failed to save to encrypted file: {str(e)}")

    def get_credentials(self) -> Dict[str, str]:
        """Retrieve stored credentials."""
        try:
            # Try OS keychain first
            try:
                stored = keyring.get_password(self.KEYCHAIN_SERVICE, self.KEYCHAIN_USERNAME)
                if stored:
                    return json.loads(stored)
            except Exception as e:
                logger.warning(f"Keychain retrieval failed, trying encrypted file: {str(e)}")
            
            # Fall back to encrypted file
            return self._get_from_file()
            
        except Exception as e:
            logger.error(f"Failed to retrieve credentials: {str(e)}")
            raise CredentialError(f"Failed to retrieve credentials: {str(e)}")

    def _get_from_file(self) -> Dict[str, str]:
        """Retrieve credentials from encrypted file."""
        file_path = os.path.join(self.storage_dir, self.FALLBACK_FILE)
        
        if not os.path.exists(file_path):
            raise CredentialError("No stored credentials found")
            
        try:
            with open(file_path, 'rb') as f:
                encrypted = f.read()
            
            decrypted = self.fernet.decrypt(encrypted)
            return json.loads(decrypted)
            
        except Exception as e:
            raise EncryptionError(f"Failed to read from encrypted file: {str(e)}")

    def delete_credentials(self) -> bool:
        """Delete stored credentials from all storage locations."""
        success = False
        
        # Try to delete from keychain
        try:
            keyring.delete_password(self.KEYCHAIN_SERVICE, self.KEYCHAIN_USERNAME)
            success = True
            logger.info("Credentials deleted from OS keychain")
        except Exception as e:
            logger.warning(f"Failed to delete from keychain: {str(e)}")
        
        # Try to delete encrypted file
        file_path = os.path.join(self.storage_dir, self.FALLBACK_FILE)
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                success = True
                logger.info("Credentials deleted from encrypted file")
            except Exception as e:
                logger.warning(f"Failed to delete encrypted file: {str(e)}")
        
        if not success:
            raise CredentialError("Failed to delete credentials from all storage locations")
            
        return True

    def has_credentials(self) -> bool:
        """Check if credentials are stored."""
        try:
            # Check keychain
            if keyring.get_password(self.KEYCHAIN_SERVICE, self.KEYCHAIN_USERNAME):
                return True
        except Exception:
            pass
        
        # Check encrypted file
        file_path = os.path.join(self.storage_dir, self.FALLBACK_FILE)
        return os.path.exists(file_path)