@echo off
echo Building Garuda Algo Admin Panel...

:: Make sure we're in the admin directory
cd %~dp0

:: Create dist directory if it doesn't exist
if not exist dist mkdir dist

:: Activate the virtual environment if it exists
if exist ../.venv/Scripts/activate.bat (
    call ../.venv/Scripts/activate.bat
    echo Virtual environment activated.
) else (
    echo Warning: Virtual environment not found. Make sure requirements are installed globally.
)

:: Copy the credentials file to the local directory for building
echo Checking for credentials file...
set CRED_FILE_COPIED=0

if exist ..\garuda-algo-firebase-credentials.json (
    copy ..\garuda-algo-firebase-credentials.json .\\\n    echo Found and copied credentials from project root.
    set CRED_FILE_COPIED=1
) else if exist ..\backend\garuda-algo-firebase-credentials.json (
    copy ..\backend\garuda-algo-firebase-credentials.json .\\\n    echo Found and copied credentials from backend directory.
    set CRED_FILE_COPIED=1
) else (
    echo WARNING: Could not find credentials file. The executable may not work properly.
)

:: Build using PyInstaller
echo Building executable...
pyinstaller --onedir --clean --name=GarudaAlgo_Admin_Panel main_admin.py

:: Copy the credentials to the dist folder if found
if %CRED_FILE_COPIED%==1 (
    copy garuda-algo-firebase-credentials.json dist\GarudaAlgo_Admin_Panel\\
    echo Copied credentials to output folder.
)

:: Clean up the temporary copied file
if %CRED_FILE_COPIED%==1 (
    del garuda-algo-firebase-credentials.json
)

echo Build complete. Output is in the dist\GarudaAlgo_Admin_Panel folder.
pause
