"""
Test script for the refactored MT5Integration module.
"""

import logging
from mt5_integration import MT5Integration
from mt5_integration.exceptions import MT5Error

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_mt5_integration():
    """Test the MT5Integration module."""
    try:
        print("Starting MT5Integration test...")
        # Create an instance of MT5Integration
        mt5 = MT5Integration()
        print("MT5Integration instance created successfully.")
        logger.info("MT5Integration instance created successfully.")

        # Get connection info (should be disconnected)
        connection_info = mt5.get_connection_info()
        logger.info(f"Connection info: {connection_info}")

        # Try to initialize from settings file
        try:
            init_result = mt5.initialize(settings_path="mt5_settings.json")
            logger.info(f"Initialization result: {init_result}")

            if init_result.get("success", False):
                # Get connection info (should be connected)
                connection_info = mt5.get_connection_info()
                logger.info(f"Connection info after initialization: {connection_info}")

                # Get symbols
                symbols = mt5.get_symbols(use_cache=False)
                logger.info(f"Got {len(symbols)} symbols.")

                # Get current price for a symbol
                price = mt5.get_current_price("EURUSD")
                logger.info(f"Current price for EURUSD: {price}")

                # Disconnect
                disconnect_result = mt5.disconnect()
                logger.info(f"Disconnect result: {disconnect_result}")
            else:
                logger.error(f"Initialization failed: {init_result.get('message', 'Unknown error')}")
        except MT5Error as e:
            logger.error(f"MT5 error during initialization: {e}")

    except Exception as e:
        logger.exception(f"Error in test_mt5_integration: {e}")

if __name__ == "__main__":
    test_mt5_integration()
