import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime, timezone, timedelta
from flask import json
import time
from ..api.connection import connection_bp
from ..mt5_integration import MT5ConnectionError, MT5AuthenticationError
from ..credential_manager import CredentialError

@pytest.fixture
def app():
    """Create test Flask app with connection blueprint"""
    from flask import Flask
    app = Flask(__name__)
    app.register_blueprint(connection_bp)
    return app

@pytest.fixture
def client(app):
    """Create test client"""
    return app.test_client()

def test_connect_success(client):
    """Test successful connection"""
    test_credentials = {
        "account": "12345",
        "password": "test_pass",
        "server": "Test-Server"
    }
    
    with patch('backend.api.connection.mt5_instance') as mock_mt5, \
         patch('backend.api.connection.credentials_manager') as mock_cred:
        
        # Mock successful connection
        mock_mt5.initialize.return_value = {
            'success': True,
            'message': 'Connected successfully'
        }
        mock_mt5.is_connected.return_value = True
        
        # Mock MT5 account info
        mock_account_info = MagicMock()
        mock_account_info.balance = 10000.0
        mock_account_info.equity = 10050.0
        
        with patch('MetaTrader5.account_info', return_value=mock_account_info):
            response = client.post('/connect', 
                                 json=test_credentials,
                                 content_type='application/json')
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['status'] == 'connected'
            assert 'account_info' in data
            assert data['account_info']['balance'] == 10000.0
            assert data['account_info']['equity'] == 10050.0

def test_connect_missing_fields(client):
    """Test connection with missing credentials"""
    incomplete_creds = {
        "account": "12345",
        # Missing password and server
    }
    
    response = client.post('/connect',
                          json=incomplete_creds,
                          content_type='application/json')
    
    assert response.status_code == 400
    data = json.loads(response.data)
    assert data['status'] == 'error'
    assert 'Missing required fields' in data['error']

def test_connect_auth_error(client):
    """Test connection with invalid credentials"""
    test_credentials = {
        "account": "12345",
        "password": "wrong_pass",
        "server": "Test-Server"
    }
    
    with patch('backend.api.connection.mt5_instance') as mock_mt5, \
         patch('backend.api.connection.credentials_manager') as mock_cred:
        
        mock_mt5.initialize.side_effect = MT5AuthenticationError("Invalid credentials")
        
        response = client.post('/connect',
                             json=test_credentials,
                             content_type='application/json')
        
        assert response.status_code == 401
        data = json.loads(response.data)
        assert data['status'] == 'error'
        assert 'Invalid credentials' in data['error']

def test_connect_server_error(client):
    """Test connection when MT5 server is unreachable"""
    test_credentials = {
        "account": "12345",
        "password": "test_pass",
        "server": "Test-Server"
    }
    
    with patch('backend.api.connection.mt5_instance') as mock_mt5, \
         patch('backend.api.connection.credentials_manager') as mock_cred:
        
        mock_mt5.initialize.side_effect = MT5ConnectionError("Server unreachable")
        
        response = client.post('/connect',
                             json=test_credentials,
                             content_type='application/json')
        
        assert response.status_code == 503
        data = json.loads(response.data)
        assert data['status'] == 'error'
        assert 'Server unreachable' in data['error']

def test_disconnect_success(client):
    """Test successful disconnection"""
    with patch('backend.api.connection.mt5_instance') as mock_mt5:
        mock_mt5.is_connected.return_value = True
        mock_mt5.disconnect.return_value = {
            'success': True,
            'message': 'Successfully disconnected'
        }
        
        response = client.post('/disconnect')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == 'disconnected'

def test_disconnect_no_connection(client):
    """Test disconnection when not connected"""
    with patch('backend.api.connection.mt5_instance') as mock_mt5:
        mock_mt5.is_connected.return_value = False
        
        response = client.post('/disconnect')
        
        assert response.status_code == 409
        data = json.loads(response.data)
        assert data['status'] == 'error'
        assert 'No active connection' in data['error']

def test_status_connected(client):
    """Test status check when connected with full account info"""
    current_time = datetime.now(timezone.utc)
    connection_time = current_time - timedelta(minutes=5)
    
    with patch('backend.api.connection.mt5_instance') as mock_mt5:
        mock_mt5.get_connection_info.return_value = {
            'state': 'connected',
            'connected': True,
            'latency_ms': 50,
            'last_error': None,
            'connection_time': connection_time.timestamp(),
            'account_info': {
                'balance': 10000.0,
                'equity': 10050.0,
                'name': 'John Doe',
                'leverage': 100,
                'server': 'MetaQuotes-Demo'
            }
        }
        
        response = client.get('/connection_status')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == 'connected'
        assert data['last_connected'] == connection_time.isoformat()
        assert data['error'] is None
        
        # Verify account info
        account_info = data['account_info']
        assert account_info is not None
        assert account_info['balance'] == 10000.0
        assert account_info['equity'] == 10050.0
        assert account_info['name'] == 'John Doe'
        assert account_info['leverage'] == 100
        assert account_info['server'] == 'MetaQuotes-Demo'
        
        # Verify debug info
        assert '_debug' in data
        assert isinstance(data['_debug']['response_time_ms'], (int, float))
        assert data['_debug']['latency_ms'] == 50

def test_status_disconnected(client):
    """Test status check when disconnected"""
    with patch('backend.api.connection.mt5_instance') as mock_mt5:
        mock_mt5.get_connection_info.return_value = {
            'state': 'disconnected',
            'connected': False,
            'latency_ms': None,
            'last_error': None,
            'connection_time': None
        }
        
        response = client.get('/connection_status')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == 'disconnected'
        assert data['account_info'] is None

def test_status_terminal_unavailable(client):
    """Test status check when MT5 terminal is not running"""
    with patch('backend.api.connection.mt5_instance') as mock_mt5:
        mock_mt5.get_connection_info.return_value = {
            'state': 'error',
            'connected': False,
            'latency_ms': None,
            'last_error': 'MT5 terminal not running',
            'connection_time': None
        }
        
        response = client.get('/connection_status')
        
        assert response.status_code == 503
        data = json.loads(response.data)
        assert data['status'] == 'error'
        assert data['error'] == 'MT5 terminal not running'
        assert data['account_info'] is None

def test_status_internal_error(client):
    """Test status check with internal error"""
    with patch('backend.api.connection.mt5_instance') as mock_mt5:
        mock_mt5.get_connection_info.return_value = {
            'state': 'error',
            'connected': False,
            'latency_ms': None,
            'last_error': 'Internal MT5 error',
            'connection_time': None
        }
        
        response = client.get('/connection_status')
        
        assert response.status_code == 500
        data = json.loads(response.data)
        assert data['status'] == 'error'
        assert data['error'] == 'Internal MT5 error'
        assert data['account_info'] is None

def test_status_performance(client):
    """Test status endpoint performance"""
    with patch('backend.api.connection.mt5_instance') as mock_mt5:
        mock_mt5.get_connection_info.return_value = {
            'state': 'connected',
            'connected': True,
            'latency_ms': 50,
            'last_error': None,
            'connection_time': datetime.now(timezone.utc).timestamp(),
            'account_info': {
                'balance': 10000.0,
                'equity': 10050.0,
                'name': 'John Doe',
                'leverage': 100,
                'server': 'MetaQuotes-Demo'
            }
        }
        
        start_time = time.time()
        response = client.get('/connection_status')
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000  # Convert to milliseconds
        assert response_time < 300, f"Response time {response_time:.2f}ms exceeds 300ms limit"
        
        data = json.loads(response.data)
        assert '_debug' in data
        assert data['_debug']['response_time_ms'] < 300

def test_account_info_precision(client):
    """Test account info numeric precision"""
    with patch('backend.api.connection.mt5_instance') as mock_mt5:
        mock_mt5.get_connection_info.return_value = {
            'state': 'connected',
            'connected': True,
            'latency_ms': 50,
            'last_error': None,
            'connection_time': datetime.now(timezone.utc).timestamp(),
            'account_info': {
                'balance': 10000.123456,  # Test precision handling
                'equity': 10050.987654,
                'name': 'John Doe',
                'leverage': 100,
                'server': 'MetaQuotes-Demo'
            }
        }
        
        response = client.get('/connection_status')
        data = json.loads(response.data)
        
        # Verify that numeric values maintain appropriate precision
        account_info = data['account_info']
        assert isinstance(account_info['balance'], float)
        assert isinstance(account_info['equity'], float)
        # Verify that string representation has at most 2 decimal places
        assert str(account_info['balance']).count('.') == 1
        assert len(str(account_info['balance']).split('.')[1]) <= 2
        assert str(account_info['equity']).count('.') == 1
        assert len(str(account_info['equity']).split('.')[1]) <= 2