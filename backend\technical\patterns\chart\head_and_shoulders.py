from typing import Dict, Any, List
import numpy as np
import pandas as pd
from scipy.signal import find_peaks

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class HeadAndShouldersPatternIndicator(BaseIndicator):
    """Head and Shoulders chart pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize Head and Shoulders pattern indicator.
        
        Args:
            params: Dictionary containing parameters:
                - min_peak_distance: Minimum distance between peaks (default: 10)
                - min_peak_height: Minimum height of peaks relative to price (default: 0.02)
        """
        super().__init__(params)
        self.min_peak_distance = self.params.get('min_peak_distance', 10)
        self.min_peak_height = self.params.get('min_peak_height', 0.02)
    
    def _find_peaks_and_troughs(self, prices: np.ndarray, min_distance: int,
                              min_height: float) -> tuple:
        """Find peaks and troughs in price data."""
        # Find peaks
        peaks, _ = find_peaks(prices, distance=min_distance,
                            height=np.mean(prices) * min_height)
        
        # Find troughs (inverse of peaks)
        troughs, _ = find_peaks(-prices, distance=min_distance,
                              height=np.mean(prices) * min_height)
        
        return peaks, troughs
    
    def _is_head_and_shoulders(self, prices: np.ndarray, peaks: np.ndarray,
                             troughs: np.ndarray) -> tuple:
        """Identify Head and Shoulders patterns."""
        is_pattern = np.zeros_like(prices)
        pattern_type = np.zeros_like(prices)
        pattern_id = np.zeros_like(prices)
        
        if len(peaks) >= 3 and len(troughs) >= 2:
            for i in range(len(peaks)-2):
                # Get three consecutive peaks
                left_shoulder = peaks[i]
                head = peaks[i+1]
                right_shoulder = peaks[i+2]
                
                # Get the troughs between peaks
                left_trough = troughs[troughs > left_shoulder]
                left_trough = left_trough[left_trough < head]
                right_trough = troughs[troughs > head]
                right_trough = right_trough[right_trough < right_shoulder]
                
                if len(left_trough) > 0 and len(right_trough) > 0:
                    left_trough = left_trough[0]
                    right_trough = right_trough[0]
                    
                    # Check if it's a valid Head and Shoulders pattern
                    if (prices[head] > prices[left_shoulder] and
                        prices[head] > prices[right_shoulder] and
                        abs(prices[left_shoulder] - prices[right_shoulder]) / prices[left_shoulder] < 0.02 and
                        prices[left_trough] < prices[left_shoulder] and
                        prices[right_trough] < prices[right_shoulder]):
                        
                        # Mark the pattern
                        is_pattern[left_shoulder:right_shoulder+1] = 1
                        pattern_type[left_shoulder:right_shoulder+1] = -1  # Bearish pattern
                        pattern_id[left_shoulder:right_shoulder+1] = i
        
        return is_pattern, pattern_type, pattern_id
    
    def _is_inverse_head_and_shoulders(self, prices: np.ndarray, peaks: np.ndarray,
                                     troughs: np.ndarray) -> tuple:
        """Identify Inverse Head and Shoulders patterns."""
        is_pattern = np.zeros_like(prices)
        pattern_type = np.zeros_like(prices)
        pattern_id = np.zeros_like(prices)
        
        if len(troughs) >= 3 and len(peaks) >= 2:
            for i in range(len(troughs)-2):
                # Get three consecutive troughs
                left_shoulder = troughs[i]
                head = troughs[i+1]
                right_shoulder = troughs[i+2]
                
                # Get the peaks between troughs
                left_peak = peaks[peaks > left_shoulder]
                left_peak = left_peak[left_peak < head]
                right_peak = peaks[peaks > head]
                right_peak = right_peak[right_peak < right_shoulder]
                
                if len(left_peak) > 0 and len(right_peak) > 0:
                    left_peak = left_peak[0]
                    right_peak = right_peak[0]
                    
                    # Check if it's a valid Inverse Head and Shoulders pattern
                    if (prices[head] < prices[left_shoulder] and
                        prices[head] < prices[right_shoulder] and
                        abs(prices[left_shoulder] - prices[right_shoulder]) / prices[left_shoulder] < 0.02 and
                        prices[left_peak] > prices[left_shoulder] and
                        prices[right_peak] > prices[right_shoulder]):
                        
                        # Mark the pattern
                        is_pattern[left_shoulder:right_shoulder+1] = 1
                        pattern_type[left_shoulder:right_shoulder+1] = 1  # Bullish pattern
                        pattern_id[left_shoulder:right_shoulder+1] = i
        
        return is_pattern, pattern_type, pattern_id
    
    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """
        Calculate Head and Shoulders pattern values.
        
        Args:
            market_data: Market data containing OHLCV values
            
        Returns:
            Dictionary containing:
                - is_pattern: Boolean array indicating pattern presence
                - pattern_type: Integer array indicating pattern type (1: Inverse H&S, -1: H&S)
                - pattern_id: Integer array for pattern instance grouping
                - strength: Float array indicating pattern strength
                - trend: Integer array indicating trend context
                - reliability: Float array indicating pattern reliability
        """
        df = market_data.to_dataframe()
        close = df['close'].values
        
        # Find peaks and troughs
        peaks, troughs = self._find_peaks_and_troughs(close, self.min_peak_distance, self.min_peak_height)
        
        # Identify patterns
        is_hs, hs_type, hs_id = self._is_head_and_shoulders(close, peaks, troughs)
        is_ihs, ihs_type, ihs_id = self._is_inverse_head_and_shoulders(close, peaks, troughs)
        
        # Combine patterns using logical operations
        is_pattern = np.logical_or(is_hs, is_ihs)
        pattern_type = np.where(is_hs, hs_type, ihs_type)
        pattern_id = np.where(is_hs, hs_id, ihs_id)
        
        # Calculate pattern strength
        strength = np.zeros_like(close)
        for i in range(len(close)):
            if is_pattern[i]:
                # Calculate the height of the pattern relative to price
                if pattern_type[i] < 0:  # Bearish pattern
                    height = close[peaks[peaks > i][0]] - close[troughs[troughs > i][0]]
                else:  # Bullish pattern
                    height = close[peaks[peaks > i][0]] - close[troughs[troughs > i][0]]
                strength[i] = min(1.0, height / close[i])
        
        # Calculate trend context using 20-period SMA
        sma20 = df['close'].rolling(window=20).mean()
        trend = np.where(df['close'] > sma20, 1, -1)
        
        # Calculate pattern reliability based on future price movement
        reliability = np.full_like(close, -1, dtype=float)  # Default to -1 for no pattern
        future_window = 20
        
        for i in range(len(close) - future_window):
            if is_pattern[i]:
                future_returns = (df['close'].iloc[i+1:i+future_window+1].values - 
                                df['close'].iloc[i]) / df['close'].iloc[i]
                
                if pattern_type[i] == 1:  # Bullish pattern
                    max_return = np.max(future_returns)
                    reliability[i] = 1 if max_return > 0 else -1
                else:  # Bearish pattern
                    min_return = np.min(future_returns)
                    reliability[i] = 1 if min_return < 0 else -1
        
        return {
            'is_pattern': is_pattern,
            'pattern_type': pattern_type,
            'pattern_id': pattern_id,
            'strength': strength,
            'trend': trend,
            'reliability': reliability
        }
    
    def validate_params(self) -> None:
        """
        Validate indicator parameters.
        
        Raises:
            ValueError: If parameters are invalid
        """
        if self.min_peak_distance < 2:
            raise ValueError("min_peak_distance must be at least 2")
        if not 0 < self.min_peak_height < 1:
            raise ValueError("min_peak_height must be between 0 and 1") 