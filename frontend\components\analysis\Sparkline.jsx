import React from 'react';
import { safeToFixed } from '../../utils/numberUtils';
import '../../styles/Sparkline.css';

/**
 * Sparkline component for price movement visualization
 * 
 * @param {Array<number>} data - Array of price data points
 * @param {number} height - Height of the sparkline in pixels
 * @returns {JSX.Element} - The rendered sparkline
 */
const Sparkline = ({ data, height = 40 }) => {
  // If no data, return empty placeholder
  if (!data || !Array.isArray(data) || data.length === 0 || data.some(val => val === null || val === undefined || isNaN(val))) {
    return (
      <div className="sparkline-container" style={{ height: `${height}px` }}>
        <div className="sparkline">
          {[...Array(10)].map((_, i) => (
            <div
              key={i}
              className="sparkline-bar"
              style={{ height: `${Math.random() * 50 + 10}%` }}
            ></div>
          ))}
        </div>
        <div className="sparkline-label">
          <span>No data</span>
          <span>-</span>
        </div>
      </div>
    );
  }

  // Find min and max for scaling
  const min = Math.min(...data);
  const max = Math.max(...data);
  const range = max - min;

  // Calculate if price went up or down
  const priceChange = data[data.length - 1] - data[0];
  const changeDirection = priceChange > 0 ? 'up' : priceChange < 0 ? 'down' : 'neutral';
  const changePercent = safeToFixed((priceChange / data[0]) * 100, 2);

  return (
    <div className="sparkline-container" style={{ height: `${height}px` }}>
      <div className="sparkline">
        {data.map((value, index) => {
          // Scale the value to fit in the container height
          const scaledHeight = range === 0 ? 50 : ((value - min) / range) * 100;
          const isUp = index > 0 && value > data[index - 1];
          const isDown = index > 0 && value < data[index - 1];

          return (
            <div
              key={index}
              className={`sparkline-bar ${isUp ? 'up' : isDown ? 'down' : ''}`}
              style={{ height: `${scaledHeight}%` }}
            ></div>
          );
        })}
      </div>
      <div className="sparkline-label">
        <span>{safeToFixed(data[0], 5)}</span>
        <span className={`sparkline-value ${changeDirection}`}>
          {priceChange > 0 ? '+' : ''}{changePercent}%
        </span>
        <span>{safeToFixed(data[data.length - 1], 5)}</span>
      </div>
    </div>
  );
};

export default Sparkline;
