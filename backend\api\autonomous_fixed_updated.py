from flask import Blueprint, request, jsonify, current_app
import os
import json
import logging
import time
import threading
import requests
from datetime import datetime
from typing import Dict, List, Any
import MetaTrader5 as mt5

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
autonomous_bp = Blueprint('autonomous_bp', __name__)

# Global variables
AUTONOMOUS_RUNNING = False
AUTONOMOUS_THREAD = None
AUTONOMOUS_CONFIG = {
    "symbols": ["EURUSD", "GBPUSD", "USDJPY", "XAUUSD"],
    "timeframes": ["M5", "M15", "H1"],
    "scan_interval": 300,
    "min_confidence": 60,
    "max_trades": 5,
    "allowed_styles": ["Scalping", "Short-term", "Swing"],
    "allow_multiple_positions": False,
    "risk_percent": 1.0,
    # New risk management parameters
    "max_total_risk_percent": 5.0,  # Maximum total risk as percentage of balance
    "breakeven_trigger_percent": 0.5,  # Move to breakeven after X% profit reached
    "trailing_stop_trigger_percent": 1.0,  # Start trailing after X% profit
    "trailing_stop_distance_percent": 0.5,  # Trail by X% of current price
    "max_profit_target": 100.0,  # $100 profit to deactivate bot
    "max_loss_target": 50.0,  # $50 loss to deactivate bot
    "active": True  # Flag to enable/disable bot (used when targets are hit)
}

def calculate_total_risk_exposure(positions, account_balance):
    """
    Calculate the total risk exposure across all open positions.

    Args:
        positions: List of open positions
        account_balance: Current account balance

    Returns:
        float: Risk percentage of account balance
    """
    total_risk = 0.0
    for position in positions:
        # Calculate risk for each position based on entry and SL
        if position.get("sl", 0) != 0:
            if position["type"] == 0:  # BUY
                risk_per_position = (position["price_open"] - position["sl"]) * position["volume"]
                # Get contract size and convert to account currency
                symbol_info = mt5.symbol_info(position["symbol"])
                if symbol_info:
                    contract_size = symbol_info.trade_contract_size
                    total_risk += risk_per_position * contract_size
            else:  # SELL
                risk_per_position = (position["sl"] - position["price_open"]) * position["volume"]
                # Get contract size and convert to account currency
                symbol_info = mt5.symbol_info(position["symbol"])
                if symbol_info:
                    contract_size = symbol_info.trade_contract_size
                    total_risk += risk_per_position * contract_size

    # Calculate as a percentage of account balance
    risk_percentage = (total_risk / account_balance) * 100 if account_balance > 0 else 0

    return risk_percentage

def check_and_adjust_stops(positions, mt5_instance):
    """
    Check all positions and adjust stop losses based on config settings.

    Args:
        positions: List of open positions
        mt5_instance: MT5 integration instance
    """
    for position in positions:
        # Break-even check
        adjust_to_breakeven(position, mt5_instance)
        # Trailing stop check
        adjust_trailing_stop(position, mt5_instance)

def adjust_to_breakeven(position, mt5_instance):
    """
    Move stop loss to break-even if profit threshold is reached.
    Uses the standardized trade management API.

    Args:
        position: Position data dictionary
        mt5_instance: MT5 integration instance
    """
    symbol = position["symbol"]
    position_id = position["ticket"]
    entry_price = position["price_open"]
    current_price = position["price_current"]
    current_sl = position["sl"]

    # Skip if no SL or already at/beyond breakeven
    if current_sl == 0 or (position["type"] == 0 and current_sl >= entry_price) or \
       (position["type"] == 1 and current_sl <= entry_price):
        return

    # Calculate profit percent
    profit_percent = 0
    if position["type"] == 0:  # BUY
        profit_percent = (current_price - entry_price) / entry_price * 100
    else:  # SELL
        profit_percent = (entry_price - current_price) / entry_price * 100

    # Check if profit threshold is reached
    if profit_percent >= AUTONOMOUS_CONFIG["breakeven_trigger_percent"]:
        try:
            # Use the standardized trade management API endpoint
            response = requests.post(
                f"http://localhost:5001/api/trade_mgmt/set_break_even/{position_id}",
                json={"offset_points": 0},  # Set to exact breakeven
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("success", False):
                    logger.info(f"Moved stop loss to break-even for {symbol} position {position_id} via API")
                else:
                    logger.error(f"Failed to move stop loss to break-even via API: {result.get('message', 'Unknown error')}")
            else:
                logger.error(f"Break-even API request failed with status {response.status_code}: {response.text}")

        except Exception as e:
            logger.error(f"Exception setting break-even via API for {symbol}: {str(e)}")
            # Fallback to direct MT5 call if API fails
            try:
                modify_result = mt5_instance.trading.modify_position(
                    position_id,
                    sl=entry_price,
                    tp=position["tp"]
                )

                if modify_result.get("success", False):
                    logger.info(f"Moved stop loss to break-even for {symbol} position {position_id} (fallback)")
                else:
                    logger.error(f"Failed to move stop loss to break-even (fallback): {modify_result.get('message', 'Unknown error')}")
            except Exception as fallback_e:
                logger.error(f"Fallback break-even also failed: {str(fallback_e)}")

def adjust_trailing_stop(position, mt5_instance):
    """
    Adjust trailing stop if profit threshold is reached.
    Uses the standardized trade management API.

    Args:
        position: Position data dictionary
        mt5_instance: MT5 integration instance
    """
    symbol = position["symbol"]
    position_id = position["ticket"]
    entry_price = position["price_open"]
    current_price = position["price_current"]
    current_sl = position["sl"]

    # Skip if no SL
    if current_sl == 0:
        return

    # Calculate profit percent
    profit_percent = 0
    if position["type"] == 0:  # BUY
        profit_percent = (current_price - entry_price) / entry_price * 100
    else:  # SELL
        profit_percent = (entry_price - current_price) / entry_price * 100

    # Check if trailing threshold is reached
    if profit_percent >= AUTONOMOUS_CONFIG["trailing_stop_trigger_percent"]:
        try:
            # Get symbol info for proper point calculation
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                logger.error(f"Failed to get symbol info for {symbol}")
                return

            # Calculate distance in points based on mode
            trailing_mode = AUTONOMOUS_CONFIG.get("trailing_stop_mode", "percent")

            if trailing_mode == "pips":
                # Use fixed pips distance
                distance_points = int(AUTONOMOUS_CONFIG.get("trailing_stop_distance_pips", 100))
            else:
                # Use percentage-based distance (default)
                trail_distance_percent = AUTONOMOUS_CONFIG.get("trailing_stop_distance_percent", 0.5)
                trail_distance_price = current_price * (trail_distance_percent / 100)
                distance_points = int(trail_distance_price / symbol_info.point)

            # Use the standardized trailing stop API endpoint
            response = requests.post(
                f"http://localhost:5001/api/trade_mgmt/trailing_stop/set/{position_id}",
                json={
                    "distance_points": distance_points,
                    "step_points": max(1, distance_points // 10),  # Step is 10% of distance
                    "activate_profit_points": None  # Already activated by profit check
                },
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("success", False):
                    logger.info(f"Set trailing stop for {symbol} position {position_id} via API (distance: {distance_points} points)")
                else:
                    logger.error(f"Failed to set trailing stop via API: {result.get('message', 'Unknown error')}")
            else:
                logger.error(f"Trailing stop API request failed with status {response.status_code}: {response.text}")

        except Exception as e:
            logger.error(f"Exception setting trailing stop via API for {symbol}: {str(e)}")
            # Fallback to manual trailing stop calculation
            try:
                # Calculate trail distance based on mode
                if trailing_mode == "pips":
                    trail_distance = AUTONOMOUS_CONFIG.get("trailing_stop_distance_pips", 100) * symbol_info.point
                else:
                    trail_distance_percent = AUTONOMOUS_CONFIG.get("trailing_stop_distance_percent", 0.5)
                    trail_distance = current_price * (trail_distance_percent / 100)

                if position["type"] == 0:  # BUY
                    # Calculate new SL based on trailing distance
                    new_sl = round(current_price - trail_distance, symbol_info.digits)

                    # Only move SL if it improves the current SL
                    if new_sl > current_sl:
                        modify_result = mt5_instance.trading.modify_position(
                            position_id,
                            sl=new_sl,
                            tp=position["tp"]
                        )

                        if modify_result.get("success", False):
                            logger.info(f"Updated trailing stop for {symbol} position {position_id} to {new_sl} (fallback)")
                        else:
                            logger.error(f"Failed to update trailing stop (fallback): {modify_result.get('message', 'Unknown error')}")

                else:  # SELL
                    # Calculate new SL based on trailing distance
                    new_sl = round(current_price + trail_distance, symbol_info.digits)

                    # Only move SL if it improves the current SL
                    if new_sl < current_sl or current_sl == 0:
                        modify_result = mt5_instance.trading.modify_position(
                            position_id,
                            sl=new_sl,
                            tp=position["tp"]
                        )

                        if modify_result.get("success", False):
                            logger.info(f"Updated trailing stop for {symbol} position {position_id} to {new_sl} (fallback)")
                        else:
                            logger.error(f"Failed to update trailing stop (fallback): {modify_result.get('message', 'Unknown error')}")

            except Exception as fallback_e:
                logger.error(f"Fallback trailing stop also failed: {str(fallback_e)}")

def check_profit_loss_thresholds(positions, mt5_instance):
    """
    Check if max profit/loss thresholds have been reached.

    Args:
        positions: List of open positions
        mt5_instance: MT5 integration instance

    Returns:
        bool: True if thresholds have been reached, False otherwise
    """
    # Get account information
    account_info = mt5.account_info()
    if not account_info:
        logger.error("Failed to get account information for P/L check")
        return False

    # Calculate total profit/loss across all positions
    total_profit_loss = sum(position.get("profit", 0) for position in positions)

    # Check against thresholds
    max_profit_target = AUTONOMOUS_CONFIG.get("max_profit_target", 0)
    max_loss_target = AUTONOMOUS_CONFIG.get("max_loss_target", 0)

    if max_profit_target > 0 and total_profit_loss >= max_profit_target:
        logger.info(f"Maximum profit target reached (${total_profit_loss:.2f}). Deactivating autonomous trading.")
        return True

    if max_loss_target > 0 and total_profit_loss <= -max_loss_target:
        logger.info(f"Maximum loss threshold reached (${total_profit_loss:.2f}). Deactivating autonomous trading.")
        return True

    return False

def _trading_loop(app):
    """
    Main autonomous trading loop.

    Args:
        app: Flask application instance
    """
    global AUTONOMOUS_RUNNING

    logger.info("Autonomous trading loop started")

    # Track last trade time for each symbol to avoid excessive trading
    last_trade_time = {}

    while AUTONOMOUS_RUNNING:
        try:
            # Use the Flask app context
            with app.app_context():
                # Get MT5 instance
                mt5_instance = app.config.get('MT5_INSTANCE')
                if not mt5_instance or not mt5_instance.is_connected():
                    logger.error("MT5 connection lost or not available")
                    time.sleep(30)  # Wait before retrying
                    continue

                # Check if bot is active (could be deactivated by profit/loss thresholds)
                if not AUTONOMOUS_CONFIG.get("active", True):
                    logger.info("Autonomous trading is deactivated. Stopping trading loop.")
                    AUTONOMOUS_RUNNING = False
                    break

                # Log that we're running
                logger.info(f"Autonomous trading running with config: {AUTONOMOUS_CONFIG}")

                # Get current open positions
                positions_result = mt5_instance.get_open_positions()
                if not positions_result.get("success", False):
                    logger.error(f"Failed to get open positions: {positions_result.get('message', 'Unknown error')}")
                    time.sleep(10)
                    continue

                open_positions = positions_result.get("positions", [])
                logger.info(f"Current open positions: {len(open_positions)}")

                # Check and adjust stop losses for existing positions
                if open_positions:
                    logger.info("Checking and adjusting stop losses for open positions")
                    check_and_adjust_stops(open_positions, mt5_instance)

                    # Check profit/loss thresholds
                    if check_profit_loss_thresholds(open_positions, mt5_instance):
                        # If thresholds reached, deactivate bot
                        AUTONOMOUS_CONFIG["active"] = False
                        logger.info("Autonomous trading deactivated due to profit/loss threshold")
                        # Save updated config to file
                        try:
                            with open("autonomous_config.json", "w") as f:
                                json.dump(AUTONOMOUS_CONFIG, f, indent=4)
                        except Exception as e:
                            logger.error(f"Failed to save config: {str(e)}")
                        continue

                # Check if we've reached max trades
                if len(open_positions) >= AUTONOMOUS_CONFIG["max_trades"]:
                    logger.info(f"Maximum number of trades reached ({AUTONOMOUS_CONFIG['max_trades']}). Skipping analysis.")
                    time.sleep(10)
                    continue

                # Check total risk before placing new trades
                account_info = mt5.account_info()
                if account_info:
                    account_balance = account_info.balance
                    current_risk = calculate_total_risk_exposure(open_positions, account_balance)

                    if current_risk >= AUTONOMOUS_CONFIG["max_total_risk_percent"]:
                        logger.warning(f"Maximum total risk reached ({current_risk:.2f}%). Skipping new trades.")
                        time.sleep(10)
                        continue

                # Process each symbol in the configuration
                for symbol in AUTONOMOUS_CONFIG["symbols"]:
                    # Skip if we've traded this symbol recently (within last 30 seconds)
                    current_time = datetime.now()
                    if symbol in last_trade_time and (current_time - last_trade_time[symbol]).total_seconds() < 30:
                        logger.info(f"Skipping {symbol} - traded recently")
                        continue

                    # Check if we already have a position for this symbol
                    symbol_positions = [p for p in open_positions if p["symbol"] == symbol]
                    if symbol_positions and not AUTONOMOUS_CONFIG["allow_multiple_positions"]:
                        logger.info(f"Skipping {symbol} - already have an open position")
                        continue

                    # Get current price
                    price_data = mt5_instance.get_current_price(symbol)
                    if "error" in price_data:
                        logger.error(f"Failed to get price for {symbol}: {price_data['error']}")
                        continue

                    logger.info(f"Current price for {symbol}: Bid={price_data['bid']}, Ask={price_data['ask']}")

                    # Simple trading logic based on RSI (simulated)
                    # In a real implementation, you would use proper technical analysis
                    import random

                    # Simulate a trading signal (10% chance of no signal, 45% buy, 45% sell)
                    # Very high probability of trading for testing purposes
                    signal = random.choices(["none", "buy", "sell"], weights=[10, 45, 45])[0]

                    if signal == "none":
                        logger.info(f"No trading signal for {symbol}")
                        continue

                    # Calculate lot size based on risk percentage
                    sl_points = 100  # Example: 100 points stop loss
                    lot_size_result = mt5_instance.calculate_lot_size(symbol, AUTONOMOUS_CONFIG["risk_percent"], sl_points)

                    if not lot_size_result.get("success", False):
                        logger.error(f"Failed to calculate lot size: {lot_size_result.get('message', 'Unknown error')}")
                        continue

                    lot_size = lot_size_result.get("lot_size", 0.01)  # Default to minimum if calculation fails
                    logger.info(f"Calculated lot size for {symbol}: {lot_size}")

                    # Place order based on signal
                    if signal == "buy":
                        try:
                            # Use the trade_execution API endpoint directly
                            # This ensures we use the exact same code path as manual execution
                            order_data = {
                                "symbol": symbol,
                                "order_type": "BUY",
                                "volume": float(lot_size),
                                "price": 0.0,  # Use current market price
                                "sl": 0.0,     # Will calculate below
                                "tp": 0.0,     # Will calculate below
                                "deviation": 20,
                                "magic": 12345,
                                "comment": f"Auto: {ACTIVE_STRATEGIES[0]['name'] if ACTIVE_STRATEGIES else 'Strategy'}",
                                "type_time": "GTC",
                                "type_filling": "FOK"
                            }

                            # Get symbol info for proper point calculation
                            symbol_info = mt5.symbol_info(symbol)
                            if symbol_info is None:
                                logger.error(f"Failed to get symbol info for {symbol}")
                                continue

                            # Make sure symbol is selected for trading
                            if not symbol_info.visible:
                                if not mt5.symbol_select(symbol, True):
                                    logger.error(f"Failed to select symbol {symbol} for trading")
                                    continue

                            # Get point value for proper SL/TP calculation
                            point = symbol_info.point
                            digits = symbol_info.digits

                            # Calculate SL and TP levels with proper rounding
                            sl_level = round(price_data["bid"] - (sl_points * point), digits)
                            tp_level = round(price_data["bid"] + (sl_points * 2 * point), digits)  # 2:1 reward:risk

                            # Update order data with SL/TP
                            order_data["sl"] = sl_level
                            order_data["tp"] = tp_level

                            logger.info(f"BUY order for {symbol}: SL={sl_level}, TP={tp_level}, Volume={lot_size}")

                            # Send to API endpoint
                            response = requests.post(
                                "http://localhost:5001/api/trade/place_order",
                                json=order_data
                            )

                            if response.status_code != 200:
                                logger.error(f"BUY order API request failed with status code: {response.status_code}")
                                logger.error(f"Response: {response.text}")
                                continue

                            result = response.json()

                            if not result.get("success", False):
                                logger.error(f"BUY order failed: {result.get('error', 'Unknown error')}")
                                continue

                            logger.info(f"BUY order placed successfully: Order #{result.get('order')}")
                            last_trade_time[symbol] = current_time

                        except Exception as e:
                            logger.exception(f"Exception placing BUY order for {symbol}: {str(e)}")
                            continue

                    elif signal == "sell":
                        try:
                            # Use the trade_execution API endpoint directly
                            # This ensures we use the exact same code path as manual execution
                            order_data = {
                                "symbol": symbol,
                                "order_type": "SELL",
                                "volume": float(lot_size),
                                "price": 0.0,  # Use current market price
                                "sl": 0.0,     # Will calculate below
                                "tp": 0.0,     # Will calculate below
                                "deviation": 20,
                                "magic": 12345,
                                "comment": f"Auto: {ACTIVE_STRATEGIES[0]['name'] if ACTIVE_STRATEGIES else 'Strategy'}",
                                "type_time": "GTC",
                                "type_filling": "FOK"
                            }

                            # Get symbol info for proper point calculation
                            symbol_info = mt5.symbol_info(symbol)
                            if symbol_info is None:
                                logger.error(f"Failed to get symbol info for {symbol}")
                                continue

                            # Make sure symbol is selected for trading
                            if not symbol_info.visible:
                                if not mt5.symbol_select(symbol, True):
                                    logger.error(f"Failed to select symbol {symbol} for trading")
                                    continue

                            # Get point value for proper SL/TP calculation
                            point = symbol_info.point
                            digits = symbol_info.digits

                            # Calculate SL and TP levels with proper rounding
                            sl_level = round(price_data["ask"] + (sl_points * point), digits)
                            tp_level = round(price_data["ask"] - (sl_points * 2 * point), digits)  # 2:1 reward:risk

                            # Update order data with SL/TP
                            order_data["sl"] = sl_level
                            order_data["tp"] = tp_level

                            logger.info(f"SELL order for {symbol}: SL={sl_level}, TP={tp_level}, Volume={lot_size}")

                            # Send to API endpoint
                            response = requests.post(
                                "http://localhost:5001/api/trade/place_order",
                                json=order_data
                            )

                            if response.status_code != 200:
                                logger.error(f"SELL order API request failed with status code: {response.status_code}")
                                logger.error(f"Response: {response.text}")
                                continue

                            result = response.json()

                            if not result.get("success", False):
                                logger.error(f"SELL order failed: {result.get('error', 'Unknown error')}")
                                continue

                            logger.info(f"SELL order placed successfully: Order #{result.get('order')}")
                            last_trade_time[symbol] = current_time

                        except Exception as e:
                            logger.exception(f"Exception placing SELL order for {symbol}: {str(e)}")
                            continue

            # Sleep before next scan
            time.sleep(10)  # Check every 10 seconds for testing purposes
        except Exception as e:
            logger.exception(f"Exception in autonomous trading loop: {str(e)}")
            time.sleep(60)  # Wait before retrying

# Define available trading strategies
AVAILABLE_STRATEGIES = [
    {
        "id": "scalping_momentum",
        "name": "Scalping Momentum",
        "description": "Short-term trades based on momentum indicators",
        "timeframes": ["M5", "M15"],
        "indicators": ["RSI", "MACD", "Bollinger Bands"],
        "style": "Scalping"
    },
    {
        "id": "intraday_trend",
        "name": "Intraday Trend Following",
        "description": "Intraday trades following established trends",
        "timeframes": ["M15", "H1"],
        "indicators": ["Moving Averages", "ADX", "Parabolic SAR"],
        "style": "Short-term"
    },
    {
        "id": "swing_reversal",
        "name": "Swing Reversal",
        "description": "Multi-day trades based on trend reversals",
        "timeframes": ["H1", "H4", "D1"],
        "indicators": ["RSI", "Stochastic", "Support/Resistance"],
        "style": "Swing"
    }
]

# Active strategies (in-memory storage)
ACTIVE_STRATEGIES = []

@autonomous_bp.route('/strategies', methods=['GET'])
def get_strategies():
    """
    Get available trading strategies.

    Returns:
        JSON: List of available strategies
    """
    return jsonify(AVAILABLE_STRATEGIES)

@autonomous_bp.route('/active', methods=['GET'])
def get_active_strategies():
    """
    Get active trading strategies.

    Returns:
        JSON: List of active strategies
    """
    # Update active strategies with current status
    for strategy in ACTIVE_STRATEGIES:
        strategy["status"] = "active" if AUTONOMOUS_RUNNING else "inactive"

    return jsonify(ACTIVE_STRATEGIES)

@autonomous_bp.route('/activate', methods=['POST'])
def activate_strategy():
    """
    Activate a trading strategy.

    Request body:
    {
        "strategy_id": "scalping_momentum",
        "symbol": "EURUSD",
        "timeframe": "M15",
        "risk_percent": 1.0,
        "max_trades": 5
    }

    Returns:
        JSON: Activation result
    """
    global AUTONOMOUS_RUNNING, AUTONOMOUS_THREAD, AUTONOMOUS_CONFIG, ACTIVE_STRATEGIES

    # Get request data
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400

    # Validate required fields
    required_fields = ["strategy_id", "symbol", "timeframe", "risk_percent", "max_trades"]
    for field in required_fields:
        if field not in data:
            return jsonify({"error": f"Missing required field: {field}"}), 400

    # Find strategy
    strategy = next((s for s in AVAILABLE_STRATEGIES if s["id"] == data["strategy_id"]), None)
    if not strategy:
        return jsonify({"error": f"Strategy not found: {data['strategy_id']}"}), 404

    # Get MT5 instance
    mt5_instance = current_app.config.get('MT5_INSTANCE')
    if not mt5_instance:
        return jsonify({"error": "MT5 instance not available"}), 500

    # Check connection
    if not mt5_instance.is_connected():
        return jsonify({"error": "Not connected to MT5"}), 500

    # Update configuration
    AUTONOMOUS_CONFIG["symbols"] = [data["symbol"]]
    AUTONOMOUS_CONFIG["timeframes"] = [data["timeframe"]]
    AUTONOMOUS_CONFIG["min_confidence"] = 60  # Default confidence threshold
    AUTONOMOUS_CONFIG["max_trades"] = data["max_trades"]
    AUTONOMOUS_CONFIG["allowed_styles"] = [strategy["style"]]
    AUTONOMOUS_CONFIG["risk_percent"] = data["risk_percent"]

    # Add risk management parameters if provided
    if "max_total_risk_percent" in data:
        AUTONOMOUS_CONFIG["max_total_risk_percent"] = float(data["max_total_risk_percent"])
    if "breakeven_trigger_percent" in data:
        AUTONOMOUS_CONFIG["breakeven_trigger_percent"] = float(data["breakeven_trigger_percent"])
    if "trailing_stop_trigger_percent" in data:
        AUTONOMOUS_CONFIG["trailing_stop_trigger_percent"] = float(data["trailing_stop_trigger_percent"])

    # Handle trailing stop distance based on mode
    if "trailing_stop_mode" in data:
        AUTONOMOUS_CONFIG["trailing_stop_mode"] = data["trailing_stop_mode"]

        if data["trailing_stop_mode"] == "percent" and "trailing_stop_distance_percent" in data:
            AUTONOMOUS_CONFIG["trailing_stop_distance_percent"] = float(data["trailing_stop_distance_percent"])
            # Clear pips value if switching to percent mode
            AUTONOMOUS_CONFIG.pop("trailing_stop_distance_pips", None)
        elif data["trailing_stop_mode"] == "pips" and "trailing_stop_distance_pips" in data:
            AUTONOMOUS_CONFIG["trailing_stop_distance_pips"] = float(data["trailing_stop_distance_pips"])
            # Clear percent value if switching to pips mode
            AUTONOMOUS_CONFIG.pop("trailing_stop_distance_percent", None)
    else:
        # Fallback to percent mode for backward compatibility
        if "trailing_stop_distance_percent" in data:
            AUTONOMOUS_CONFIG["trailing_stop_distance_percent"] = float(data["trailing_stop_distance_percent"])
            AUTONOMOUS_CONFIG["trailing_stop_mode"] = "percent"

    if "max_profit_target" in data:
        AUTONOMOUS_CONFIG["max_profit_target"] = float(data["max_profit_target"])
    if "max_loss_target" in data:
        AUTONOMOUS_CONFIG["max_loss_target"] = float(data["max_loss_target"])

    # Reset active flag to true when activating a strategy
    AUTONOMOUS_CONFIG["active"] = True

    # Get the current Flask app for the thread
    app = current_app._get_current_object()

    # Start autonomous trading if not already running
    if not AUTONOMOUS_RUNNING:
        AUTONOMOUS_RUNNING = True
        AUTONOMOUS_THREAD = threading.Thread(target=_trading_loop, args=(app,))
        AUTONOMOUS_THREAD.daemon = True
        AUTONOMOUS_THREAD.start()
        logger.info("Autonomous trading started")

    # Add to active strategies
    active_strategy = {
        "id": data["strategy_id"],
        "name": strategy["name"],
        "symbol": data["symbol"],
        "timeframe": data["timeframe"],
        "risk_percent": data["risk_percent"],
        "max_trades": data["max_trades"],
        "status": "active"
    }

    # Check if strategy is already active
    existing_index = next((i for i, s in enumerate(ACTIVE_STRATEGIES) if s["id"] == data["strategy_id"]), None)
    if existing_index is not None:
        ACTIVE_STRATEGIES[existing_index] = active_strategy
    else:
        ACTIVE_STRATEGIES.append(active_strategy)

    # Save configuration to file
    try:
        with open("autonomous_config.json", "w") as f:
            json.dump(AUTONOMOUS_CONFIG, f, indent=4)
    except Exception as e:
        logger.exception(f"Exception saving configuration: {str(e)}")

    return jsonify({
        "success": True,
        "message": f"Strategy activated: {strategy['name']}",
        "strategy_name": strategy["name"],
        "config": AUTONOMOUS_CONFIG
    })

@autonomous_bp.route('/deactivate/<strategy_id>', methods=['POST'])
def deactivate_strategy(strategy_id):
    """
    Deactivate a trading strategy.

    Args:
        strategy_id: ID of the strategy to deactivate

    Returns:
        JSON: Deactivation result
    """
    global AUTONOMOUS_RUNNING, AUTONOMOUS_THREAD, ACTIVE_STRATEGIES

    # Find strategy in active strategies
    strategy_index = next((i for i, s in enumerate(ACTIVE_STRATEGIES) if s["id"] == strategy_id), None)
    if strategy_index is None:
        return jsonify({"error": f"Strategy not active: {strategy_id}"}), 404

    # Remove from active strategies
    ACTIVE_STRATEGIES.pop(strategy_index)

    # If no more active strategies, stop autonomous trading
    if not ACTIVE_STRATEGIES and AUTONOMOUS_RUNNING:
        AUTONOMOUS_RUNNING = False
        if AUTONOMOUS_THREAD:
            AUTONOMOUS_THREAD.join(timeout=5)
            AUTONOMOUS_THREAD = None
        logger.info("Autonomous trading stopped")

    return jsonify({
        "success": True,
        "message": f"Strategy deactivated: {strategy_id}"
    })

@autonomous_bp.route('/status', methods=['GET'])
def get_status():
    """
    Get autonomous trading status.

    Returns:
        JSON: Status information with risk management metrics
    """
    try:
        # Get MT5 instance
        mt5_instance = current_app.config.get('MT5_INSTANCE')

        # Get open positions to calculate current risk exposure
        positions_result = mt5_instance.get_open_positions() if mt5_instance else {"success": False}
        open_positions = positions_result.get("positions", []) if positions_result.get("success", False) else []

        # Get account info
        account_info = mt5.account_info()
        account_balance = account_info.balance if account_info else 0

        # Calculate risk exposure
        current_risk_percent = calculate_total_risk_exposure(open_positions, account_balance)

        # Count positions with break-even and trailing stops
        breakeven_positions = 0
        trailing_positions = 0
        total_profit_loss = 0

        for position in open_positions:
            total_profit_loss += position.get("profit", 0)
            entry_price = position.get("price_open", 0)
            sl = position.get("sl", 0)

            if sl != 0:
                # Check if stop loss is at break-even (within 5 points)
                symbol_info = mt5.symbol_info(position.get("symbol", ""))
                if symbol_info:
                    point = symbol_info.point
                    if abs(sl - entry_price) < (5 * point):
                        breakeven_positions += 1
                    # Assume it's a trailing stop if not at entry and not initial stop
                    elif (position.get("type") == 0 and sl > position.get("price_open", 0)) or \
                         (position.get("type") == 1 and sl < position.get("price_open", 0)):
                        trailing_positions += 1

        # Ensure all risk management parameters are included
        response_data = {
            "running": AUTONOMOUS_RUNNING,
            "active": AUTONOMOUS_CONFIG.get("active", True),
            "strategies_count": len(ACTIVE_STRATEGIES),
            "open_positions": len(open_positions),
            "current_risk_percent": float(current_risk_percent),
            "max_risk_percent": float(AUTONOMOUS_CONFIG.get("max_total_risk_percent", 5.0)),
            "breakeven_positions": breakeven_positions,
            "trailing_positions": trailing_positions,
            "current_profit_loss": float(total_profit_loss),
            "max_profit_target": float(AUTONOMOUS_CONFIG.get("max_profit_target", 0)),
            "max_loss_target": float(AUTONOMOUS_CONFIG.get("max_loss_target", 0)),
            "breakeven_trigger_percent": float(AUTONOMOUS_CONFIG.get("breakeven_trigger_percent", 0.5)),
            "trailing_stop_trigger_percent": float(AUTONOMOUS_CONFIG.get("trailing_stop_trigger_percent", 1.0)),
            "trailing_stop_distance_percent": float(AUTONOMOUS_CONFIG.get("trailing_stop_distance_percent", 0.5)),
            "config": AUTONOMOUS_CONFIG,
            "active_strategies": ACTIVE_STRATEGIES
        }

        return jsonify(response_data)
    except Exception as e:
        logger.exception(f"Error getting status: {str(e)}")
        return jsonify({
            "running": AUTONOMOUS_RUNNING,
            "config": AUTONOMOUS_CONFIG,
            "active_strategies": ACTIVE_STRATEGIES,
            "error": str(e)
        })

@autonomous_bp.route('/debug', methods=['GET'])
def get_debug():
    """
    Get debug information for autonomous trading.

    Returns:
        JSON: Debug information
    """
    # Get MT5 instance
    mt5_instance = current_app.config.get('MT5_INSTANCE')

    # Check connection
    connection_status = "Not available"
    if mt5_instance:
        connection_status = "Connected" if mt5_instance.is_connected() else "Disconnected"

    # Get account info
    account_info = None
    if mt5_instance and mt5_instance.is_connected():
        account_info = mt5.account_info()._asdict() if mt5.account_info() else None

    # Get positions
    positions = []
    if mt5_instance and mt5_instance.is_connected():
        positions_result = mt5_instance.get_open_positions()
        if positions_result.get("success", False):
            positions = positions_result.get("positions", [])

    # Get symbols
    symbols = []
    if mt5_instance and mt5_instance.is_connected():
        for symbol in AUTONOMOUS_CONFIG["symbols"]:
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info:
                symbols.append({
                    "name": symbol,
                    "visible": symbol_info.visible,
                    "trade_mode": symbol_info.trade_mode,
                    "point": symbol_info.point,
                    "digits": symbol_info.digits,
                    "spread": symbol_info.spread,
                    "trade_allowed": mt5.symbol_info_tick(symbol) is not None
                })

    return jsonify({
        "running": AUTONOMOUS_RUNNING,
        "connection_status": connection_status,
        "account_info": account_info,
        "positions": positions,
        "symbols": symbols,
        "active_strategies": ACTIVE_STRATEGIES
    })

@autonomous_bp.route('/settings', methods=['POST'])
def update_settings():
    """
    Update autonomous trading settings.

    Request body:
    {
        "symbols": ["EURUSD", "GBPUSD"],
        "timeframes": ["M15", "H1"],
        "scan_interval": 300,
        "min_confidence": 70,
        "max_trades": 5,
        "allowed_styles": ["Scalping", "Short-term", "Swing"],
        "allow_multiple_positions": false
    }

    Returns:
        JSON: Update result
    """
    global AUTONOMOUS_CONFIG

    # Get request data
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400

    # Validate configuration
    if "symbols" in data and not isinstance(data["symbols"], list):
        return jsonify({"error": "Invalid symbols configuration"}), 400

    if "timeframes" in data and not isinstance(data["timeframes"], list):
        return jsonify({"error": "Invalid timeframes configuration"}), 400

    if "scan_interval" in data and not isinstance(data["scan_interval"], int):
        return jsonify({"error": "Invalid scan interval configuration"}), 400

    if "min_confidence" in data and not isinstance(data["min_confidence"], int):
        return jsonify({"error": "Invalid minimum confidence configuration"}), 400

    if "max_trades" in data and not isinstance(data["max_trades"], int):
        return jsonify({"error": "Invalid maximum trades configuration"}), 400

    if "allowed_styles" in data and not isinstance(data["allowed_styles"], list):
        return jsonify({"error": "Invalid allowed styles configuration"}), 400

    # Update configuration
    for key, value in data.items():
        AUTONOMOUS_CONFIG[key] = value

    # Save configuration to file
    try:
        with open("autonomous_config.json", "w") as f:
            json.dump(AUTONOMOUS_CONFIG, f, indent=4)
    except Exception as e:
        logger.exception(f"Exception saving configuration: {str(e)}")

    return jsonify({
        "success": True,
        "message": "Settings updated",
        "config": AUTONOMOUS_CONFIG
    })
