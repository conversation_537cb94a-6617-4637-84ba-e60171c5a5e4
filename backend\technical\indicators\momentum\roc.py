from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class ROCIndicator(BaseIndicator):
    """Rate of Change (ROC) indicator."""

    def __init__(self, period: int = 14, source: str = 'close'):
        """
        Initialize Rate of Change indicator.

        Args:
            period: The lookback period for calculating the change.
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
        """
        super().__init__({
            'period': period,
            'source': source
        })

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Rate of Change."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period']:
             return {'roc': np.array([])}

        period = self.params['period']
        source_col = self.params['source'].lower()

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Calculate ROC
        shifted_data = source_data.shift(period)
        # Avoid division by zero
        shifted_data_safe = shifted_data.replace(0, np.nan)
        roc_values = 100 * (source_data - shifted_data) / shifted_data_safe
        roc_values = roc_values.fillna(0) # Fill NaNs

        self._values = {
            'roc': roc_values.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        return True