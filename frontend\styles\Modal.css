.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5); /* Explicit background color */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.modal-overlay.open {
  opacity: 1;
  visibility: visible;
}

/* Apply background and visual styles directly to modal-container */
.modal-container {
  background-color: var(--card, #1e293b); /* Use theme variable with fallback */
  color: var(--text, #f1f5f9); /* Use theme variable with fallback */
  border-radius: 8px;
  box-shadow: 0 8px 30px var(--shadow, rgba(0, 0, 0, 0.3));
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  transform: translateY(20px);
  opacity: 0;
  transition: transform 0.3s, opacity 0.3s;
}

/* Target modal-container for the open transition */
.modal-overlay.open .modal-container {
  transform: translateY(0);
  opacity: 1;
}

.modal-header {
  padding: 20px 20px 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 1.5rem;
  padding: 0;
  line-height: 1;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

@media (max-width: 576px) {
  .modal {
    width: 95%;
  }

  .modal-header, .modal-body, .modal-footer {
    padding: 15px;
  }
}
