# GarudaAlgo MT5 Trader

## Overview
GarudaAlgo MT5 Trader is an AI-powered desktop application that integrates with MetaTrader 5 (MT5) to provide advanced trading capabilities. The application combines powerful backend analysis with a modern Electron-based frontend interface, offering both manual and autonomous trading features.

## Project Structure

```
garudaalgo/
├── backend/               # Python-based trading backend
│   ├── mt5_integration.py       # MT5 platform connection handler
│   ├── analysis_engine.py       # Market analysis components
│   ├── recommendation_engine.py # Trading recommendation system
│   ├── autonomous_trader.py     # Automated trading execution
│   ├── main.py                  # Backend orchestration
│   └── run_server.py            # API server for frontend communication
│
├── frontend/              # Electron-based UI
│   ├── index-simple.html        # Simple UI version
│   ├── renderer-simple.js       # Simple UI logic
│   ├── index.html               # Main UI (React version)
│   ├── components/              # React components (advanced UI)
│   └── assets/                  # UI assets and resources
│
├── main.js               # Electron main process
├── start_app.py          # Application launcher
│
└── docs/                 # Project documentation
    ├── architecture.md         # System architecture
    ├── development.md          # Development guide
    ├── components.md           # Component documentation
    ├── api.md                  # API documentation
    ├── roadmap.md              # Development roadmap
    ├── strategies.md           # Trading strategies
    └── troubleshooting.md      # Troubleshooting guide
```

## Key Features

### MT5 Integration
- Seamless connection to MetaTrader 5 platform
- Real-time market data access
- Trade execution capabilities
- Account management

### Market Analysis
- Multi-timeframe technical analysis
- Indicator-based market scanning
- Pattern recognition
- Support and resistance identification

### Trade Recommendations
- AI-powered trading signals
- Entry and exit point calculation
- Risk/reward assessment
- Position sizing recommendations

### Autonomous Trading
- Fully automated trading strategies
- Customizable trading parameters
- Risk management controls
- Performance monitoring

## Quick Start

### Installation
1. Clone the repository
   ```bash
   git clone https://github.com/username/garudaalgo.git
   cd garudaalgo
   ```

2. Install backend dependencies
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. Install frontend dependencies
   ```bash
   npm install
   ```

4. Configure MT5 connection
   - Ensure MT5 terminal is installed and running
   - Use the connection form in the application to enter your credentials

5. Start the application
   ```bash
   python start_app.py
   ```

## Technology Stack
- Backend: Python, FastAPI, MetaTrader 5 API
- Frontend: Electron, HTML/CSS/JavaScript
- Data Analysis: NumPy, Pandas, TA-Lib
- Trading Platform: MetaTrader 5
