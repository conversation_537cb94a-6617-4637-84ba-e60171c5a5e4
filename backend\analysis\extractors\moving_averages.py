import pandas as pd
import logging
from typing import Dict, Any

logger = logging.getLogger("AnalysisEngine")

def extract_moving_averages(data: pd.DataFrame) -> Dict[str, Any]:
    """
    Extract and analyze moving averages.
    
    Args:
        data: DataFrame containing price data and calculated moving averages
        
    Returns:
        Dictionary with moving average analysis results
    """
    try:
        if data.empty or len(data) < 2:
            return {"error": "Insufficient data"}

        latest = data.iloc[-1]
        previous = data.iloc[-2]

        # Determine EMA crossovers
        ema_20_50_cross = "None"
        if pd.notna(latest.get('EMA_20')) and pd.notna(latest.get('EMA_50')) and \
           pd.notna(previous.get('EMA_20')) and pd.notna(previous.get('EMA_50')):
            if previous['EMA_20'] <= previous['EMA_50'] and latest['EMA_20'] > latest['EMA_50']:
                ema_20_50_cross = "Bullish"
            elif previous['EMA_20'] >= previous['EMA_50'] and latest['EMA_20'] < latest['EMA_50']:
                ema_20_50_cross = "Bearish"

        # Determine price relation to EMAs
        price_vs_ema20 = "Above" if pd.notna(latest.get('EMA_20')) and latest['close'] > latest['EMA_20'] else \
                        ("Below" if pd.notna(latest.get('EMA_20')) else "N/A")
        price_vs_ema50 = "Above" if pd.notna(latest.get('EMA_50')) and latest['close'] > latest['EMA_50'] else \
                        ("Below" if pd.notna(latest.get('EMA_50')) else "N/A")
        price_vs_ema100 = "Above" if pd.notna(latest.get('EMA_100')) and latest['close'] > latest['EMA_100'] else \
                         ("Below" if pd.notna(latest.get('EMA_100')) else "N/A")
        price_vs_ema200 = "Above" if pd.notna(latest.get('EMA_200')) and latest['close'] > latest['EMA_200'] else \
                         ("Below" if pd.notna(latest.get('EMA_200')) else "N/A")

        # Determine EMA alignment
        ema_alignment = "None"
        if pd.notna(latest.get('EMA_20')) and pd.notna(latest.get('EMA_50')) and \
           pd.notna(latest.get('EMA_100')) and pd.notna(latest.get('EMA_200')):
            if latest['EMA_20'] > latest['EMA_50'] > latest['EMA_100'] > latest['EMA_200']:
                ema_alignment = "Strong Bullish"
            elif latest['EMA_20'] < latest['EMA_50'] < latest['EMA_100'] < latest['EMA_200']:
                ema_alignment = "Strong Bearish"
            elif latest['EMA_20'] > latest['EMA_50']:
                ema_alignment = "Bullish"
            elif latest['EMA_20'] < latest['EMA_50']:
                ema_alignment = "Bearish"
        elif pd.notna(latest.get('EMA_20')) and pd.notna(latest.get('EMA_50')):
            if latest['EMA_20'] > latest['EMA_50']:
                ema_alignment = "Bullish"
            elif latest['EMA_20'] < latest['EMA_50']:
                ema_alignment = "Bearish"

        return {
            "ema20": latest.get('EMA_20'), "ema50": latest.get('EMA_50'),
            "ema100": latest.get('EMA_100'), "ema200": latest.get('EMA_200'),
            "sma20": latest.get('SMA_20'), "sma50": latest.get('SMA_50'),
            "sma100": latest.get('SMA_100'), "sma200": latest.get('SMA_200'),
            "ema_20_50_cross": ema_20_50_cross,
            "price_vs_ema20": price_vs_ema20, "price_vs_ema50": price_vs_ema50,
            "price_vs_ema100": price_vs_ema100, "price_vs_ema200": price_vs_ema200,
            "ema_alignment": ema_alignment
        }
    except Exception as e:
        logger.exception(f"Exception extracting moving averages: {str(e)}")
        return {"error": f"Exception extracting moving averages: {str(e)}"}
