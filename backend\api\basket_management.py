"""API endpoints for basket trade management operations."""

import logging
from flask import Blueprint, jsonify, request, current_app
import MetaTrader5 as mt5
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

basket_mgmt_bp = Blueprint('basket_management', __name__, url_prefix='/api/basket')


@basket_mgmt_bp.route('/create', methods=['POST'])
def create_basket():
    """Create a new trade basket"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get parameters from request
        data = request.get_json(silent=True) or {}
        name = data.get('name')
        description = data.get('description', '')

        if not name:
            return jsonify({'error': 'Basket name is required'}), 400

        # Create basket
        result = mt5_conn.trading.create_basket(name, description)

        if not result['success']:
            return jsonify({'error': result['message']}), 400

        return jsonify(result)

    except Exception as e:
        logger.exception(f"Error creating basket: {str(e)}")
        return jsonify({'error': str(e)}), 500


@basket_mgmt_bp.route('/<basket_name>/add/<int:position_id>', methods=['POST'])
def add_to_basket(basket_name, position_id):
    """Add a position to a basket"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Add position to basket
        result = mt5_conn.trading.add_to_basket(basket_name, position_id)

        if not result['success']:
            return jsonify({'error': result['message']}), 400

        return jsonify(result)

    except Exception as e:
        logger.exception(f"Error adding to basket: {str(e)}")
        return jsonify({'error': str(e)}), 500


@basket_mgmt_bp.route('/<basket_name>/remove/<int:position_id>', methods=['POST'])
def remove_from_basket(basket_name, position_id):
    """Remove a position from a basket"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Remove position from basket
        result = mt5_conn.trading.remove_from_basket(basket_name, position_id)

        if not result['success']:
            return jsonify({'error': result['message']}), 400

        return jsonify(result)

    except Exception as e:
        logger.exception(f"Error removing from basket: {str(e)}")
        return jsonify({'error': str(e)}), 500


@basket_mgmt_bp.route('/<basket_name>', methods=['GET'])
def get_basket(basket_name):
    """Get basket details"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Get basket
        result = mt5_conn.trading.get_basket(basket_name)

        if not result['success']:
            return jsonify({'error': result['message']}), 400

        return jsonify(result)

    except Exception as e:
        logger.exception(f"Error getting basket: {str(e)}")
        return jsonify({'error': str(e)}), 500


@basket_mgmt_bp.route('/all', methods=['GET'])
def list_baskets():
    """List all baskets"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # List baskets
        result = mt5_conn.trading.list_baskets()

        if not result['success']:
            return jsonify({'error': result['message']}), 400

        return jsonify(result)

    except Exception as e:
        logger.exception(f"Error listing baskets: {str(e)}")
        return jsonify({'error': str(e)}), 500


@basket_mgmt_bp.route('/<basket_name>/close', methods=['POST'])
def close_basket(basket_name):
    """Close all positions in a basket"""
    try:
        # Get MT5 connection
        mt5_conn = current_app.config['MT5_INSTANCE']
        if not mt5_conn:
            return jsonify({'error': 'MT5 connection not initialized'}), 503

        # Check connection status
        if not mt5_conn.is_connected():
            return jsonify({'error': 'MT5 not connected'}), 503

        # Close basket
        result = mt5_conn.trading.close_basket(basket_name)

        if not result['success']:
            return jsonify({'error': result['message']}), 400

        return jsonify(result)

    except Exception as e:
        logger.exception(f"Error closing basket: {str(e)}")
        return jsonify({'error': str(e)}), 500
