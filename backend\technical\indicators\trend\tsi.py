from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class TrendStrengthIndexIndicator(BaseIndicator):
    """Trend Strength Index (TSI) indicator based on directional movement."""

    def __init__(self, period: int = 14):
        """
        Initialize Trend Strength Index indicator.

        Args:
            period: The lookback period for summing directional movement.
        """
        super().__init__({'period': period})

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Trend Strength Index."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period'] + 1: # Need shift(1)
             return {'tsi': np.array([])}

        period = self.params['period']

        high = df['high']
        low = df['low']

        # Calculate directional movement (+DM, -DM)
        move_up = high.diff()
        move_down = low.shift(1) - low # Note: Different from ADX's -DM definition

        plus_dm = np.where((move_up > move_down) & (move_up > 0), move_up, 0)
        minus_dm = np.where((move_down > move_up) & (move_down > 0), move_down, 0)

        plus_dm_series = pd.Series(plus_dm, index=df.index)
        minus_dm_series = pd.Series(minus_dm, index=df.index)

        # Calculate smoothed +DM and -DM (using simple sum over period as per reference code)
        smoothed_plus = plus_dm_series.rolling(window=period).sum()
        smoothed_minus = minus_dm_series.rolling(window=period).sum()

        # Calculate Total Movement
        total_movement = smoothed_plus + smoothed_minus

        # Calculate Trend Strength Index (TSI)
        # Avoid division by zero
        total_movement_safe = total_movement.replace(0, np.nan)
        tsi_values = (smoothed_plus - smoothed_minus) / total_movement_safe
        tsi_values = tsi_values.fillna(0) # Fill initial NaNs

        self._values = {
            'tsi': tsi_values.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        return True