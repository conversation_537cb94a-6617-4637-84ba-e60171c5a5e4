import os
import sys

def find_mt5_terminal():
    """
    Search for MetaTrader 5 terminal in common installation locations.
    """
    print("Searching for MetaTrader 5 terminal...")
    
    # Common installation paths
    common_paths = [
        "C:\\Program Files\\MetaTrader 5",
        "C:\\Program Files (x86)\\MetaTrader 5",
        os.path.join(os.environ.get('LOCALAPPDATA', ''), "Programs\\MetaTrader 5"),
        os.path.join(os.environ.get('APPDATA', ''), "MetaQuotes\\Terminal"),
        "D:\\Program Files\\MetaTrader 5",
    ]
    
    # Files to look for
    target_files = ["terminal64.exe", "terminal.exe", "metatrader.exe"]
    
    found_paths = []
    
    # Search in common paths
    for base_path in common_paths:
        if os.path.exists(base_path):
            print(f"Checking {base_path}...")
            for root, dirs, files in os.walk(base_path):
                for file in files:
                    if file.lower() in target_files:
                        full_path = os.path.join(root, file)
                        found_paths.append(full_path)
                        print(f"Found: {full_path}")
    
    # Search in Program Files directories
    program_files_dirs = ["C:\\Program Files", "C:\\Program Files (x86)", "D:\\Program Files"]
    for program_dir in program_files_dirs:
        if os.path.exists(program_dir):
            print(f"Searching in {program_dir}...")
            for root, dirs, files in os.walk(program_dir):
                # Skip deep directories to avoid long search times
                if root.count(os.sep) - program_dir.count(os.sep) > 3:
                    continue
                
                for file in files:
                    if file.lower() in target_files:
                        full_path = os.path.join(root, file)
                        found_paths.append(full_path)
                        print(f"Found: {full_path}")
    
    return found_paths

if __name__ == "__main__":
    print("MT5 Path Finder")
    print("===============")
    
    paths = find_mt5_terminal()
    
    if paths:
        print("\nFound MetaTrader 5 terminals:")
        for i, path in enumerate(paths, 1):
            print(f"{i}. {path}")
        
        print("\nUse one of these paths when connecting to MT5 in the application.")
    else:
        print("\nNo MetaTrader 5 terminal found in common locations.")
        print("You may need to manually locate the terminal64.exe file in your MT5 installation directory.")
    
    input("\nPress Enter to exit...")
