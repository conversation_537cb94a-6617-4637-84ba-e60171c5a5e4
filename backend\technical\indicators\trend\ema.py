from typing import Dict, Any, List
import numpy as np
import pandas as pd

# Use proper relative import instead of sys.path manipulation
from backend.technical.base_indicator import BaseIndicator

class EMAIndicator(BaseIndicator):
    """Exponential Moving Average indicator."""

    def __init__(self, period: int = 20, source: str = 'close', smoothing: float = 2.0):
        """
        Initialize EMA indicator.

        Args:
            period: The period for calculating EMA
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4')
            smoothing: Smoothing factor (default is 2.0 which is standard EMA)
        """
        super().__init__({
            'period': period,
            'source': source,
            'smoothing': smoothing
        })
        self.name = 'EMA' # Store name if needed

    def calculate(self, data: pd.DataFrame) -> Dict[str, np.ndarray]: # Changed type hint
        """Calculate EMA values."""
        # df = data.to_dataframe() # Removed - data is now DataFrame
        df = data
        if df.empty or self.params['period'] > len(df): # Check period validity
            print(f"EMAIndicator Error: Insufficient data for period {self.params['period']}.")
            nan_array = np.full(len(df), np.nan)
            zero_array = np.zeros(len(df), dtype=int)
            return { # Return expected keys with NaNs/zeros
                'ema': nan_array, 'slope': nan_array, 'distance': nan_array,
                'crossover': zero_array, 'momentum': nan_array, 'source': nan_array
            }

        # Get source data
        source = self.params['source'].lower()
        if source == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source]

        period = self.params['period']

        # Calculate multiplier (standard EMA uses alpha = 2 / (period + 1))
        # Using span directly might be simpler if standard EMA is always desired
        # multiplier = self.params['smoothing'] / (period + 1) # Keep if custom smoothing needed

        # Calculate EMA using span for standard calculation
        ema = source_data.ewm(
            span=period,
            adjust=False # Common practice for financial data
            # alpha=multiplier # Use alpha if custom smoothing is needed
        ).mean()

        # --- Optional calculations (handle potential NaNs) ---
        # Calculate slope for trend direction
        slope = pd.Series(np.gradient(ema.fillna(method='bfill').fillna(method='ffill')), index=ema.index)

        # Calculate distance from price
        ema_safe = ema.replace(0, np.nan) # Avoid division by zero
        distance = ((source_data - ema) / ema_safe) * 100
        distance = distance.fillna(0)

        # Calculate crossovers
        source_shifted = source_data.shift(1)
        ema_shifted = ema.shift(1)
        crossover = np.zeros(len(df), dtype=int)
        valid_indices = source_data.notna() & ema.notna() & source_shifted.notna() & ema_shifted.notna()
        crossover[valid_indices & (source_data > ema) & (source_shifted <= ema_shifted)] = 1
        crossover[valid_indices & (source_data < ema) & (source_shifted >= ema_shifted)] = -1

        # Calculate momentum
        momentum = ema.diff(period)
        # --- End optional calculations ---

        return {
            'ema': ema.values,
            # Include optional values if needed by consuming code
            # 'slope': slope.values,
            # 'distance': distance.values,
            # 'crossover': crossover,
            # 'momentum': momentum.values,
            # 'source': source_data.values
        }

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        if self.params['smoothing'] <= 0:
            raise ValueError("Smoothing factor must be greater than 0")
        return True
