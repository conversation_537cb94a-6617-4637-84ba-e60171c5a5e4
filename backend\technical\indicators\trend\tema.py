from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class TEMAIndicator(BaseIndicator):
    """Triple Exponential Moving Average (TEMA) indicator."""

    def __init__(self, period: int = 9, source: str = 'close'):
        """
        Initialize Triple Exponential Moving Average indicator.

        Args:
            period: The lookback period for the EMAs.
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
        """
        super().__init__({
            'period': period,
            'source': source
        })

    def _ema(self, series: pd.Series, period: int) -> pd.Series:
        """Helper function for EMA calculation."""
        return series.ewm(span=period, adjust=False).mean()

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Triple Exponential Moving Average."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period']:
             return {'tema': np.array([])}

        period = self.params['period']
        source_col = self.params['source'].lower()

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Calculate EMAs
        ema1 = self._ema(source_data, period)
        ema2 = self._ema(ema1, period)
        ema3 = self._ema(ema2, period)

        # Calculate TEMA
        tema_values = (3 * ema1) - (3 * ema2) + ema3

        self._values = {
            'tema': tema_values.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        return True