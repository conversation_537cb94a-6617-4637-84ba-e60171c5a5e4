"""Enhanced trading component for MT5Integration with advanced trade management features."""

import logging
import time
import MetaTrader5 as mt5
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)

class TradingComponent:
    """
    Enhanced component for handling trading operations in MT5.
    Includes advanced trade management features like partial closing,
    per-position trailing stops, and improved risk management.
    """
    
    def __init__(self, connection_component):
        """
        Initialize the trading component.
        
        Args:
            connection_component: The connection component for MT5
        """
        self.connection = connection_component
        # Dictionary to store trailing stop settings per position
        self.trailing_stops = {}
        # Dictionary to store trade history for monitoring
        self.trade_history = {}
        # Dictionary to store trade baskets (groups of related positions)
        self.trade_baskets = {}
        
    def place_market_order(self, symbol: str, order_type: int, volume: float,
                           sl: Optional[float] = None, tp: Optional[float] = None,
                           deviation: int = 10, magic: int = 0,
                           comment: str = "") -> Dict[str, Any]:
        """
        Place a market order.
        
        Args:
            symbol: Symbol name
            order_type: Order type (mt5.ORDER_TYPE_BUY or mt5.ORDER_TYPE_SELL)
            volume: Order volume (lot size)
            sl: Stop loss price
            tp: Take profit price
            deviation: Maximum price deviation
            magic: Magic number (identifier)
            comment: Order comment
            
        Returns:
            Dict with order result
        """
        try:
            # Check connection
            if not self.connection.is_connected():
                return {
                    "success": False,
                    "message": "Not connected to MT5"
                }
            
            # Get symbol info
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                return {
                    "success": False,
                    "message": f"Symbol {symbol} not found"
                }
            
            # Ensure symbol is selected
            if not symbol_info.visible:
                if not mt5.symbol_select(symbol, True):
                    return {
                        "success": False,
                        "message": f"Failed to select symbol {symbol}"
                    }
            
            # Get current price
            if order_type == mt5.ORDER_TYPE_BUY:
                price = mt5.symbol_info_tick(symbol).ask
            else:
                price = mt5.symbol_info_tick(symbol).bid
            
            # Prepare order request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": volume,
                "type": order_type,
                "price": price,
                "deviation": deviation,
                "magic": magic,
                "comment": comment,
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_FOK
            }
            
            # Add SL/TP if provided
            if sl is not None:
                request["sl"] = sl
            if tp is not None:
                request["tp"] = tp
            
            # Send order
            result = mt5.order_send(request)
            if result is None:
                error_code = mt5.last_error()
                return {
                    "success": False,
                    "message": f"Order failed with error code: {error_code}"
                }
            
            # Check result
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                return {
                    "success": False,
                    "message": f"Order failed with retcode: {result.retcode}",
                    "retcode": result.retcode,
                    "result": result._asdict()
                }
            
            # Record the order in trade history for monitoring
            order_info = result._asdict()
            self.trade_history[order_info['order']] = {
                'time': datetime.now().isoformat(),
                'type': 'open',
                'symbol': symbol,
                'volume': volume,
                'price': price,
                'order_type': order_type,
                'sl': sl,
                'tp': tp
            }
            
            # Success
            return {
                "success": True,
                "message": "Order placed successfully",
                "order": result._asdict()
            }
        except Exception as e:
            logger.exception(f"Exception placing market order: {str(e)}")
            return {
                "success": False,
                "message": f"Exception placing market order: {str(e)}"
            }
    
    def get_open_positions(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """
        Get open positions.
        
        Args:
            symbol: Optional symbol filter
            
        Returns:
            Dict with positions
        """
        try:
            # Check connection
            if not self.connection.is_connected():
                return {
                    "success": False,
                    "message": "Not connected to MT5"
                }
            
            # Get positions
            positions = mt5.positions_get(symbol=symbol) if symbol else mt5.positions_get()
            if positions is None:
                error_code = mt5.last_error()
                if error_code == 0:  # No error, just no positions
                    return {
                        "success": True,
                        "positions": []
                    }
                else:
                    return {
                        "success": False,
                        "message": f"Failed to get positions. MT5 error code: {error_code}"
                    }
            
            # Convert positions to dicts
            positions_list = []
            for position in positions:
                position_dict = position._asdict()
                # Convert time fields to datetime strings
                position_dict["time"] = datetime.fromtimestamp(position_dict["time"]).strftime('%Y-%m-%d %H:%M:%S')
                position_dict["time_update"] = datetime.fromtimestamp(position_dict["time_update"]).strftime('%Y-%m-%d %H:%M:%S')
                
                # Add additional information if trailing stop is set
                if position.ticket in self.trailing_stops:
                    position_dict["trailing_stop"] = self.trailing_stops[position.ticket]
                
                positions_list.append(position_dict)
            
            return {
                "success": True,
                "positions": positions_list
            }
        except Exception as e:
            logger.exception(f"Exception getting open positions: {str(e)}")
            return {
                "success": False,
                "message": f"Exception getting open positions: {str(e)}"
            }
    
    def calculate_lot_size(self, symbol: str, risk_percent: float, sl_points: int,
                           max_risk_percent: float = 2.0, min_lot: Optional[float] = None) -> Dict[str, Any]:
        """
        Calculate lot size based on risk percentage and stop loss points with enhanced risk controls.
        
        Args:
            symbol: Symbol name
            risk_percent: Risk percentage (0-100)
            sl_points: Stop loss in points
            max_risk_percent: Maximum allowed risk percentage per trade (default: 2%)
            min_lot: Optional minimum lot size to use (default: use symbol minimum)
            
        Returns:
            Dict with calculated lot size and risk metrics
        """
        try:
            # Check connection
            if not self.connection.is_connected():
                return {
                    "success": False,
                    "message": "Not connected to MT5"
                }
            
            # Get account info
            account_info = mt5.account_info()
            if account_info is None:
                return {
                    "success": False,
                    "message": "Failed to get account info"
                }
            
            # Get symbol info
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                return {
                    "success": False,
                    "message": f"Symbol {symbol} not found"
                }
            
            # Ensure symbol is selected
            if not symbol_info.visible:
                if not mt5.symbol_select(symbol, True):
                    return {
                        "success": False,
                        "message": f"Failed to select symbol {symbol}"
                    }
            
            # Apply risk controls - cap risk percentage at maximum
            applied_risk_percent = min(risk_percent, max_risk_percent)
            if applied_risk_percent != risk_percent:
                logger.warning(f"Risk capped from {risk_percent}% to {applied_risk_percent}% due to max risk limit")
            
            # Calculate risk amount based on balance
            balance = account_info.balance
            risk_amount = balance * (applied_risk_percent / 100)
            
            # Check for open positions to consider portfolio risk
            open_positions = mt5.positions_get()
            total_exposure = 0
            if open_positions and len(open_positions) > 0:
                # Calculate total exposure
                for pos in open_positions:
                    pos_info = pos._asdict()
                    # Consider only same currency positions for risk calculation
                    if symbol_info.currency_profit == mt5.symbol_info(pos_info['symbol']).currency_profit:
                        total_exposure += pos_info['volume'] * pos_info['price_open']
            
            # Calculate point value
            point_value = symbol_info.trade_tick_value * (symbol_info.point / symbol_info.trade_tick_size)
            
            # Calculate lot size
            if sl_points > 0 and point_value > 0:
                lot_size = risk_amount / (sl_points * point_value)
            else:
                lot_size = 0.01  # Default minimum lot size
            
            # Round to nearest valid lot size
            lot_step = symbol_info.volume_step
            lot_size = round(lot_size / lot_step) * lot_step
            
            # Apply minimum lot constraint if provided
            if min_lot is not None:
                lot_size = max(min_lot, lot_size)
            
            # Ensure lot size is within limits
            lot_size = max(symbol_info.volume_min, min(symbol_info.volume_max, lot_size))
            
            # Calculate actual risk based on the final lot size
            actual_risk_amount = lot_size * sl_points * point_value
            actual_risk_percent = (actual_risk_amount / balance) * 100
            
            # Check account margin level to prevent over-trading
            margin_level = 0
            if account_info.margin != 0:
                margin_level = (account_info.equity / account_info.margin) * 100
            
            return {
                "success": True,
                "lot_size": lot_size,
                "risk_amount": risk_amount,
                "actual_risk_amount": actual_risk_amount,
                "actual_risk_percent": actual_risk_percent,
                "balance": balance,
                "margin_level": margin_level,
                "total_exposure": total_exposure,
                "portfolio_info": {
                    "open_positions": len(open_positions) if open_positions else 0,
                    "equity": account_info.equity,
                    "margin": account_info.margin
                }
            }
        except Exception as e:
            logger.exception(f"Exception calculating lot size: {str(e)}")
            return {
                "success": False,
                "message": f"Exception calculating lot size: {str(e)}"
            }
    
    def close_position(self, ticket: int, retry_count: int = 3, retry_delay: float = 0.5) -> Dict[str, Any]:
        """
        Close an open position with retry mechanism.
        
        Args:
            ticket: Position ticket
            retry_count: Number of retries if operation fails
            retry_delay: Delay between retries in seconds
            
        Returns:
            Dict with close result
        """
        for attempt in range(retry_count):
            try:
                # Check connection
                if not self.connection.is_connected():
                    return {
                        "success": False,
                        "message": "Not connected to MT5"
                    }
                
                # Get position
                position = mt5.positions_get(ticket=ticket)
                if position is None or len(position) == 0:
                    return {
                        "success": False,
                        "message": f"Position {ticket} not found"
                    }
                
                position = position[0]
                
                # Determine order type for closing
                close_type = mt5.ORDER_TYPE_SELL if position.type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY
                
                # Get price for closing
                symbol = position.symbol
                price = mt5.symbol_info_tick(symbol).ask if close_type == mt5.ORDER_TYPE_BUY else mt5.symbol_info_tick(symbol).bid
                
                # Prepare close request
                request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": symbol,
                    "volume": position.volume,
                    "type": close_type,
                    "position": ticket,
                    "price": price,
                    "deviation": 10,
                    "magic": position.magic,
                    "comment": f"Close position #{ticket}",
                    "type_time": mt5.ORDER_TIME_GTC,
                    "type_filling": mt5.ORDER_FILLING_FOK
                }
                
                logger.info(f"Closing position {ticket} of {symbol} with volume {position.volume}")
                
                # Send close request
                result = mt5.order_send(request)
                if result is None:
                    error_code = mt5.last_error()
                    logger.error(f"Close failed with error code: {error_code}")
                    if attempt < retry_count - 1:
                        logger.info(f"Retrying... (attempt {attempt+1}/{retry_count})")
                        time.sleep(retry_delay)
                        continue
                    return {
                        "success": False,
                        "message": f"Close failed with error code: {error_code}"
                    }
                
                # Check result
                if result.retcode != mt5.TRADE_RETCODE_DONE:
                    logger.error(f"Close failed with retcode: {result.retcode}")
                    if attempt < retry_count - 1 and result.retcode in [10004, 10018, 10019, 10021]:
                        # Retry for server busy, market closed, not enough money, or invalid volume
                        logger.info(f"Retrying... (attempt {attempt+1}/{retry_count})")
                        time.sleep(retry_delay)
                        continue
                    return {
                        "success": False,
                        "message": f"Close failed with retcode: {result.retcode}",
                        "retcode": result.retcode,
                        "result": result._asdict()
                    }
                
                # Success - remove from trailing stops if present
                if ticket in self.trailing_stops:
                    del self.trailing_stops[ticket]
                
                # Update trade history
                self.trade_history[ticket] = {
                    'time': datetime.now().isoformat(),
                    'type': 'close',
                    'symbol': symbol,
                    'volume': position.volume,
                    'close_price': price,
                    'profit': position.profit
                }
                
                return {
                    "success": True,
                    "message": f"Position {ticket} closed successfully",
                    "result": result._asdict(),
                    "profit": position.profit
                }
            except Exception as e:
                logger.exception(f"Exception closing position: {str(e)}")
                if attempt < retry_count - 1:
                    logger.info(f"Retrying after error... (attempt {attempt+1}/{retry_count})")
                    time.sleep(retry_delay)
                    continue
                return {
                    "success": False,
                    "message": f"Exception closing position: {str(e)}"
                }
        
        # Should never reach here but added for completeness
        return {
            "success": False,
            "message": f"Failed to close position after {retry_count} attempts"
        }
        
    def modify_position(self, ticket: int, sl: Optional[float] = None, tp: Optional[float] = None, 
                         retry_count: int = 3, retry_delay: float = 0.5) -> Dict[str, Any]:
        """
        Modify stop loss and/or take profit of an open position.
        
        Args:
            ticket: Position ticket
            sl: New stop loss price (None to keep current)
            tp: New take profit price (None to keep current)
            retry_count: Number of retries if operation fails
            retry_delay: Delay between retries in seconds
            
        Returns:
            Dict with modify result
        """
        for attempt in range(retry_count):
            try:
                # Check connection
                if not self.connection.is_connected():
                    return {
                        "success": False,
                        "message": "Not connected to MT5"
                    }
                
                # Get position
                position = mt5.positions_get(ticket=ticket)
                if position is None or len(position) == 0:
                    return {
                        "success": False,
                        "message": f"Position {ticket} not found"
                    }
                
                position = position[0]
                
                # If SL or TP not provided, use existing values
                if sl is None:
                    sl = position.sl
                
                if tp is None:
                    tp = position.tp
                
                # Prepare modify request
                request = {
                    "action": mt5.TRADE_ACTION_SLTP,
                    "symbol": position.symbol,
                    "position": ticket,
                    "sl": sl,
                    "tp": tp
                }
                
                logger.info(f"Modifying position {ticket}: SL={sl}, TP={tp}")
                
                # Send modify request
                result = mt5.order_send(request)
                if result is None:
                    error_code = mt5.last_error()
                    logger.error(f"Position modify failed with error code: {error_code}")
                    if attempt < retry_count - 1:
                        logger.info(f"Retrying... (attempt {attempt+1}/{retry_count})")
                        time.sleep(retry_delay)
                        continue
                    return {
                        "success": False,
                        "message": f"Modify failed with error code: {error_code}"
                    }
                
                # Check result
                if result.retcode != mt5.TRADE_RETCODE_DONE:
                    logger.error(f"Position modify failed with retcode: {result.retcode}")
                    if attempt < retry_count - 1 and result.retcode in [10004, 10018, 10019, 10021]:
                        # Retry for server busy, market closed, not enough money, or invalid volume
                        logger.info(f"Retrying... (attempt {attempt+1}/{retry_count})")
                        time.sleep(retry_delay)
                        continue
                    return {
                        "success": False,
                        "message": f"Modify failed with retcode: {result.retcode}",
                        "retcode": result.retcode,
                        "result": result._asdict()
                    }
                
                # Success
                logger.info(f"Position {ticket} modified successfully")
                return {
                    "success": True,
                    "message": f"Position {ticket} modified successfully",
                    "sl": sl,
                    "tp": tp
                }
            except Exception as e:
                logger.exception(f"Exception modifying position: {str(e)}")
                if attempt < retry_count - 1:
                    logger.info(f"Retrying after error... (attempt {attempt+1}/{retry_count})")
                    time.sleep(retry_delay)
                    continue
                return {
                    "success": False,
                    "message": f"Exception modifying position: {str(e)}"
                }
    
    def partial_close(self, ticket: int, volume_percent: float, comment: str = "") -> Dict[str, Any]:
        """
        Partially close an open position.
        
        Args:
            ticket: Position ticket
            volume_percent: Percentage of position volume to close (0-100)
            comment: Comment for the close operation
            
        Returns:
            Dict with close result
        """
        try:
            # Check connection
            if not self.connection.is_connected():
                return {
                    "success": False,
                    "message": "Not connected to MT5"
                }
            
            # Get position
            position = mt5.positions_get(ticket=ticket)
            if position is None or len(position) == 0:
                return {
                    "success": False,
                    "message": f"Position {ticket} not found"
                }
            
            position = position[0]
            original_volume = position.volume
            
            # Calculate volume to close
            close_volume = round(original_volume * (volume_percent / 100), 2)
            
            # Ensure minimum volume step is respected
            symbol_info = mt5.symbol_info(position.symbol)
            if symbol_info is None:
                return {
                    "success": False,
                    "message": f"Symbol {position.symbol} info not available"
                }
                
            volume_step = symbol_info.volume_step
            close_volume = round(close_volume / volume_step) * volume_step
            
            # Ensure we're closing a valid amount
            if close_volume <= 0 or close_volume > original_volume:
                return {
                    "success": False,
                    "message": f"Invalid close volume: {close_volume}. Original volume: {original_volume}"
                }
            
            # Determine order type for closing
            close_type = mt5.ORDER_TYPE_SELL if position.type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY
            
            # Get price for closing
            symbol = position.symbol
            price = mt5.symbol_info_tick(symbol).ask if close_type == mt5.ORDER_TYPE_BUY else mt5.symbol_info_tick(symbol).bid
            
            # Prepare close request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": close_volume,
                "type": close_type,
                "position": ticket,
                "price": price,
                "deviation": 10,
                "magic": position.magic,
                "comment": comment or f"Partial close {volume_percent}% of #{ticket}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_FOK
            }
            
            # Send close request
            result = mt5.order_send(request)
            if result is None:
                error_code = mt5.last_error()
                return {
                    "success": False,
                    "message": f"Partial close failed with error code: {error_code}"
                }
            
            # Check result
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                return {
                    "success": False,
                    "message": f"Partial close failed with retcode: {result.retcode}",
                    "retcode": result.retcode,
                    "result": result._asdict()
                }
            
            # Success
            remaining_volume = original_volume - close_volume
            
            # Update trade history
            self.trade_history[f"{ticket}_partial_{datetime.now().strftime('%Y%m%d%H%M%S')}"] = {
                'time': datetime.now().isoformat(),
                'type': 'partial_close',
                'symbol': symbol,
                'volume_closed': close_volume,
                'volume_remaining': remaining_volume,
                'close_price': price,
                'original_ticket': ticket
            }
            
            return {
                "success": True,
                "message": f"Position {ticket} partially closed successfully",
                "closed_volume": close_volume,
                "remaining_volume": remaining_volume,
                "closed_percent": volume_percent,
                "result": result._asdict()
            }
        except Exception as e:
            logger.exception(f"Exception partially closing position: {str(e)}")
            return {
                "success": False,
                "message": f"Exception partially closing position: {str(e)}"
            }
            
    def set_trailing_stop(self, ticket: int, distance_points: int, step_points: int = 1,
                          activate_profit_points: Optional[int] = None) -> Dict[str, Any]:
        """
        Set a trailing stop for a specific position. This requires periodic checking via update_trailing_stops().
        
        Args:
            ticket: Position ticket
            distance_points: Distance in points to maintain from the current price
            step_points: Minimum price movement in points to trigger a trailing stop adjustment
            activate_profit_points: Only activate trailing stop after position reaches this profit in points (optional)
            
        Returns:
            Dict with trailing stop setup result
        """
        try:
            # Check connection
            if not self.connection.is_connected():
                return {
                    "success": False,
                    "message": "Not connected to MT5"
                }
            
            # Get position
            position = mt5.positions_get(ticket=ticket)
            if position is None or len(position) == 0:
                return {
                    "success": False,
                    "message": f"Position {ticket} not found"
                }
            
            position = position[0]
            position_info = position._asdict()
            symbol = position_info['symbol']
            symbol_info = mt5.symbol_info(symbol)
            
            if symbol_info is None:
                return {
                    "success": False,
                    "message": f"Symbol {symbol} info not available"
                }
            
            # Calculate the trailing stop price based on current position and price
            current_price = mt5.symbol_info_tick(symbol).bid if position_info['type'] == mt5.ORDER_TYPE_BUY else mt5.symbol_info_tick(symbol).ask
            
            # Store trailing stop information
            self.trailing_stops[ticket] = {
                'symbol': symbol,
                'type': position_info['type'],  # 0 for buy, 1 for sell
                'distance_points': distance_points,
                'step_points': step_points,
                'activate_profit_points': activate_profit_points,
                'price_open': position_info['price_open'],
                'last_price': current_price,
                'initial_sl': position_info['sl'],
                'created_at': datetime.now().isoformat(),
                'active': False if activate_profit_points else True  # Only active if no activation profit defined
            }
            
            logger.info(f"Trailing stop set for position {ticket} with distance {distance_points} points")
            
            # Try to set initial SL if none exists and trailing stop is active
            if position_info['sl'] == 0 and self.trailing_stops[ticket]['active']:
                # Calculate SL based on position type and distance
                if position_info['type'] == mt5.ORDER_TYPE_BUY:
                    sl_price = current_price - (distance_points * symbol_info.point)
                else:  # SELL position
                    sl_price = current_price + (distance_points * symbol_info.point)
                    
                # Set the initial SL
                modify_result = self.modify_position(ticket, sl=sl_price)
                if not modify_result['success']:
                    logger.warning(f"Failed to set initial SL for trailing stop on position {ticket}: {modify_result['message']}")
            
            return {
                "success": True,
                "message": f"Trailing stop set for position {ticket}",
                "trailing_stop": self.trailing_stops[ticket]
            }
        except Exception as e:
            logger.exception(f"Exception setting trailing stop: {str(e)}")
            return {
                "success": False,
                "message": f"Exception setting trailing stop: {str(e)}"
            }
            
    def update_trailing_stops(self) -> Dict[str, Any]:
        """
        Update all trailing stops based on current market prices.
        Should be called periodically to update all trailing stops.
        
        Returns:
            Dict with update results
        """
        if not self.trailing_stops:
            return {
                "success": True,
                "message": "No trailing stops to update",
                "updated": 0
            }
            
        try:
            # Check connection
            if not self.connection.is_connected():
                return {
                    "success": False,
                    "message": "Not connected to MT5"
                }
                
            updated_positions = []
            tickets_to_remove = []
            
            # Process each position with a trailing stop
            for ticket, ts_info in self.trailing_stops.items():
                try:
                    # Get current position
                    position = mt5.positions_get(ticket=ticket)
                    if position is None or len(position) == 0:
                        # Position no longer exists, mark for removal
                        tickets_to_remove.append(ticket)
                        continue
                        
                    position = position[0]
                    position_info = position._asdict()
                    symbol = position_info['symbol']
                    
                    # Get current price
                    current_bid = mt5.symbol_info_tick(symbol).bid
                    current_ask = mt5.symbol_info_tick(symbol).ask
                    
                    # Price for calculation depends on position type
                    current_price = current_bid if position_info['type'] == mt5.ORDER_TYPE_BUY else current_ask
                    
                    # Get symbol info for point value
                    symbol_info = mt5.symbol_info(symbol)
                    if symbol_info is None:
                        logger.warning(f"Cannot update trailing stop for {ticket}: Symbol info not available")
                        continue
                    
                    # Check if trailing stop should be activated based on profit
                    if not ts_info['active'] and ts_info['activate_profit_points'] is not None:
                        # Calculate current profit in points
                        if position_info['type'] == mt5.ORDER_TYPE_BUY:
                            profit_points = (current_bid - position_info['price_open']) / symbol_info.point
                        else:  # SELL position
                            profit_points = (position_info['price_open'] - current_ask) / symbol_info.point
                            
                        # Activate if profit threshold reached
                        if profit_points >= ts_info['activate_profit_points']:
                            logger.info(f"Activating trailing stop for position {ticket} - profit {profit_points} points exceeded threshold {ts_info['activate_profit_points']}")
                            self.trailing_stops[ticket]['active'] = True
                            # Set initial SL on activation
                            if position_info['type'] == mt5.ORDER_TYPE_BUY:
                                sl_price = current_bid - (ts_info['distance_points'] * symbol_info.point)
                            else:  # SELL position
                                sl_price = current_ask + (ts_info['distance_points'] * symbol_info.point)
                                
                            modify_result = self.modify_position(ticket, sl=sl_price)
                            if modify_result['success']:
                                logger.info(f"Initial trailing stop set for position {ticket} at {sl_price}")
                                # Update the last price
                                self.trailing_stops[ticket]['last_price'] = current_price
                            else:
                                logger.warning(f"Failed to set initial trailing stop for position {ticket}: {modify_result['message']}")
                    
                    # Skip if not active yet
                    if not self.trailing_stops[ticket]['active']:
                        continue
                        
                    # Check if price moved enough to update the SL
                    last_price = ts_info['last_price']
                    price_movement = 0
                    
                    if position_info['type'] == mt5.ORDER_TYPE_BUY:
                        # For BUY positions, only move SL up when price increases
                        if current_bid > last_price:
                            price_movement = (current_bid - last_price) / symbol_info.point
                            
                            # If price moved enough steps, update SL
                            if price_movement >= ts_info['step_points']:
                                new_sl = current_bid - (ts_info['distance_points'] * symbol_info.point)
                                
                                # Only update if new SL is higher than current SL
                                if new_sl > position_info['sl'] or position_info['sl'] == 0:
                                    modify_result = self.modify_position(ticket, sl=new_sl)
                                    if modify_result['success']:
                                        logger.info(f"Updated trailing stop for BUY position {ticket} to {new_sl}")
                                        # Update the last price
                                        self.trailing_stops[ticket]['last_price'] = current_bid
                                        updated_positions.append(ticket)
                                    else:
                                        logger.warning(f"Failed to update trailing stop for position {ticket}: {modify_result['message']}")
                    else:  # SELL position
                        # For SELL positions, only move SL down when price decreases
                        if current_ask < last_price:
                            price_movement = (last_price - current_ask) / symbol_info.point
                            
                            # If price moved enough steps, update SL
                            if price_movement >= ts_info['step_points']:
                                new_sl = current_ask + (ts_info['distance_points'] * symbol_info.point)
                                
                                # Only update if new SL is lower than current SL or SL not set
                                if new_sl < position_info['sl'] or position_info['sl'] == 0:
                                    modify_result = self.modify_position(ticket, sl=new_sl)
                                    if modify_result['success']:
                                        logger.info(f"Updated trailing stop for SELL position {ticket} to {new_sl}")
                                        # Update the last price
                                        self.trailing_stops[ticket]['last_price'] = current_ask
                                        updated_positions.append(ticket)
                                    else:
                                        logger.warning(f"Failed to update trailing stop for position {ticket}: {modify_result['message']}")
                                        
                except Exception as pos_e:
                    logger.exception(f"Error updating trailing stop for position {ticket}: {str(pos_e)}")
            
            # Remove trailing stops for closed positions
            for ticket in tickets_to_remove:
                if ticket in self.trailing_stops:
                    del self.trailing_stops[ticket]
                    logger.info(f"Removed trailing stop for closed position {ticket}")
            
            return {
                "success": True,
                "message": f"Updated {len(updated_positions)} trailing stops",
                "updated": len(updated_positions),
                "updated_positions": updated_positions,
                "removed": len(tickets_to_remove),
                "active_trailing_stops": len(self.trailing_stops)
            }
            
        except Exception as e:
            logger.exception(f"Exception updating trailing stops: {str(e)}")
            return {
                "success": False,
                "message": f"Exception updating trailing stops: {str(e)}"
            }
    
    def modify_position(self, ticket: int, sl: Optional[float] = None, tp: Optional[float] = None, 
                         retry_count: int = 3, retry_delay: float = 0.5) -> Dict[str, Any]:
        """
        Modify stop loss and/or take profit of an open position.
        
        Args:
            ticket: Position ticket
            sl: New stop loss price (None to keep current)
            tp: New take profit price (None to keep current)
            retry_count: Number of retries if operation fails
            retry_delay: Delay between retries in seconds
            
        Returns:
            Dict with modify result
        """
        try:
            # Check connection
            if not self.connection.is_connected():
                return {
                    "success": False,
                    "message": "Not connected to MT5"
                }
            
            # Get position
            position = mt5.positions_get(ticket=ticket)
            if position is None or len(position) == 0:
                return {
                    "success": False,
                    "message": f"Position {ticket} not found"
                }
            
            position = position[0]
            position_info = position._asdict()
            
            # Prepare modify request
            request = {
                "action": mt5.TRADE_ACTION_SLTP,
                "symbol": position_info['symbol'],
                "position": ticket,
                "magic": position_info['magic']
            }
            
            # Set SL and TP only if provided, otherwise keep current
            if sl is not None:
                request["sl"] = sl
            else:
                request["sl"] = position_info['sl']  # Keep current SL
                
            if tp is not None:
                request["tp"] = tp
            else:
                request["tp"] = position_info['tp']  # Keep current TP
            
            # Retry loop
            for attempt in range(retry_count):
                result = mt5.order_send(request)
                
                if result is None:
                    error_code = mt5.last_error()
                    logger.warning(f"Modify position attempt {attempt+1} failed with error code: {error_code}")
                    if attempt < retry_count - 1:
                        time.sleep(retry_delay)
                        continue
                    else:
                        return {
                            "success": False,
                            "message": f"Modify position failed with error code: {error_code}"
                        }
                
                # Check result
                if result.retcode != mt5.TRADE_RETCODE_DONE:
                    logger.warning(f"Modify position attempt {attempt+1} failed with retcode: {result.retcode}")
                    if attempt < retry_count - 1:
                        time.sleep(retry_delay)
                        continue
                    else:
                        return {
                            "success": False,
                            "message": f"Modify position failed with retcode: {result.retcode}",
                            "retcode": result.retcode,
                            "result": result._asdict()
                        }
                
                # Success
                return {
                    "success": True,
                    "message": f"Position {ticket} modified successfully",
                    "sl": sl,
                    "tp": tp,
                    "result": result._asdict()
                }
            
            # If we get here, all retries failed
            return {
                "success": False,
                "message": f"Failed to modify position {ticket} after {retry_count} attempts"
            }
            
        except Exception as e:
            logger.exception(f"Exception modifying position: {str(e)}")
            return {
                "success": False,
                "message": f"Exception modifying position: {str(e)}"
            }
            
    def set_break_even(self, ticket: int, offset_points: int = 0) -> Dict[str, Any]:
        """
        Set break even for a position - move stop loss to entry price (plus optional offset).
        
        Args:
            ticket: Position ticket
            offset_points: Optional offset in points (positive value means more favorable to position)
            
        Returns:
            Dict with break even result
        """
        try:
            # Check connection
            if not self.connection.is_connected():
                return {
                    "success": False,
                    "message": "Not connected to MT5"
                }
            
            # Get position
            position = mt5.positions_get(ticket=ticket)
            if position is None or len(position) == 0:
                return {
                    "success": False,
                    "message": f"Position {ticket} not found"
                }
            
            position = position[0]
            position_info = position._asdict()
            
            # Get symbol info for point calculation
            symbol = position_info['symbol']
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                return {
                    "success": False,
                    "message": f"Symbol {symbol} not found"
                }
            
            # Calculate break even price with offset
            entry_price = position_info['price_open']
            point_size = symbol_info.point
            
            if position_info['type'] == mt5.POSITION_TYPE_BUY:
                # For long positions, add offset to entry price (higher SL is better)
                break_even_price = entry_price + (offset_points * point_size)
            else:  # SELL position
                # For short positions, subtract offset from entry price (lower SL is better)
                break_even_price = entry_price - (offset_points * point_size)
            
            # Round to appropriate number of digits
            digits = symbol_info.digits
            break_even_price = round(break_even_price, digits)
            
            # Check if the break-even level would trigger immediate close
            current_bid = mt5.symbol_info_tick(symbol).bid
            current_ask = mt5.symbol_info_tick(symbol).ask
            
            if (position_info['type'] == mt5.POSITION_TYPE_BUY and break_even_price >= current_bid) or \
               (position_info['type'] == mt5.POSITION_TYPE_SELL and break_even_price <= current_ask):
                return {
                    "success": False,
                    "message": f"Break even price would trigger immediate close. Current price: {current_bid if position_info['type'] == mt5.POSITION_TYPE_BUY else current_ask}"
                }
            
            # Set the stop loss to break even
            result = self.modify_position(ticket, sl=break_even_price)
            
            if result['success']:
                return {
                    "success": True,
                    "message": f"Break even set for position {ticket} at {break_even_price}",
                    "break_even_price": break_even_price,
                    "entry_price": entry_price,
                    "offset_points": offset_points
                }
            else:
                return {
                    "success": False,
                    "message": f"Failed to set break even: {result['message']}"
                }
        
        except Exception as e:
            logger.exception(f"Exception setting break even: {str(e)}")
            return {
                "success": False,
                "message": f"Exception setting break even: {str(e)}"
            }
    
    def remove_trailing_stop(self, ticket: int) -> Dict[str, Any]:
        """
        Remove trailing stop for a specific position.
        
        Args:
            ticket: Position ticket
            
        Returns:
            Dict with removal result
        """
        try:
            if ticket in self.trailing_stops:
                del self.trailing_stops[ticket]
                logger.info(f"Removed trailing stop for position {ticket}")
                return {
                    "success": True,
                    "message": f"Trailing stop removed for position {ticket}"
                }
            else:
                return {
                    "success": False,
                    "message": f"No trailing stop found for position {ticket}"
                }
        except Exception as e:
            logger.exception(f"Exception removing trailing stop: {str(e)}")
            return {
                "success": False,
                "message": f"Exception removing trailing stop: {str(e)}"
            }
            
    #============================================================================
    # Basket Trade Management Functions
    #============================================================================
    
    def create_basket(self, name: str, description: str = "") -> Dict[str, Any]:
        """
        Create a new trade basket for managing groups of positions.
        
        Args:
            name: Name of the basket
            description: Optional description of the basket
            
        Returns:
            Dict with creation result
        """
        try:
            # Check if basket with this name already exists
            if name in self.trade_baskets:
                return {
                    "success": False,
                    "message": f"Basket with name '{name}' already exists"
                }
            
            # Create new basket
            basket_id = f"basket_{datetime.now().strftime('%Y%m%d%H%M%S')}_{name}"
            self.trade_baskets[name] = {
                'id': basket_id,
                'name': name,
                'description': description,
                'created_at': datetime.now().isoformat(),
                'positions': [],
                'status': 'active',
                'risk_level': None,
                'profit': 0,
                'metadata': {}
            }
            
            logger.info(f"Created trade basket '{name}' with ID {basket_id}")
            
            return {
                "success": True,
                "message": f"Basket '{name}' created successfully",
                "basket": self.trade_baskets[name]
            }
        except Exception as e:
            logger.exception(f"Exception creating trade basket: {str(e)}")
            return {
                "success": False,
                "message": f"Exception creating trade basket: {str(e)}"
            }
    
    def add_to_basket(self, basket_name: str, ticket: int) -> Dict[str, Any]:
        """
        Add a position to a trade basket.
        
        Args:
            basket_name: Name of the basket
            ticket: Position ticket to add
            
        Returns:
            Dict with addition result
        """
        try:
            # Check if basket exists
            if basket_name not in self.trade_baskets:
                return {
                    "success": False,
                    "message": f"Basket '{basket_name}' not found"
                }
            
            basket = self.trade_baskets[basket_name]
            
            # Check if position already in basket
            if ticket in basket['positions']:
                return {
                    "success": False,
                    "message": f"Position {ticket} already in basket '{basket_name}'"
                }
            
            # Check if position exists
            position = mt5.positions_get(ticket=ticket)
            if position is None or len(position) == 0:
                return {
                    "success": False,
                    "message": f"Position {ticket} not found"
                }
            
            # Add position to basket
            basket['positions'].append(ticket)
            
            # Update timestamp
            basket['updated_at'] = datetime.now().isoformat()
            
            logger.info(f"Added position {ticket} to basket '{basket_name}'")
            
            return {
                "success": True,
                "message": f"Position {ticket} added to basket '{basket_name}'",
                "basket": basket
            }
        except Exception as e:
            logger.exception(f"Exception adding to trade basket: {str(e)}")
            return {
                "success": False,
                "message": f"Exception adding to trade basket: {str(e)}"
            }
    
    def remove_from_basket(self, basket_name: str, ticket: int) -> Dict[str, Any]:
        """
        Remove a position from a trade basket.
        
        Args:
            basket_name: Name of the basket
            ticket: Position ticket to remove
            
        Returns:
            Dict with removal result
        """
        try:
            # Check if basket exists
            if basket_name not in self.trade_baskets:
                return {
                    "success": False,
                    "message": f"Basket '{basket_name}' not found"
                }
            
            basket = self.trade_baskets[basket_name]
            
            # Check if position in basket
            if ticket not in basket['positions']:
                return {
                    "success": False,
                    "message": f"Position {ticket} not in basket '{basket_name}'"
                }
            
            # Remove position from basket
            basket['positions'].remove(ticket)
            
            # Update timestamp
            basket['updated_at'] = datetime.now().isoformat()
            
            logger.info(f"Removed position {ticket} from basket '{basket_name}'")
            
            return {
                "success": True,
                "message": f"Position {ticket} removed from basket '{basket_name}'",
                "basket": basket
            }
        except Exception as e:
            logger.exception(f"Exception removing from trade basket: {str(e)}")
            return {
                "success": False,
                "message": f"Exception removing from trade basket: {str(e)}"
            }
    
    def get_basket(self, basket_name: str) -> Dict[str, Any]:
        """
        Get information about a trade basket including current position details.
        
        Args:
            basket_name: Name of the basket
            
        Returns:
            Dict with basket details
        """
        try:
            # Check if basket exists
            if basket_name not in self.trade_baskets:
                return {
                    "success": False,
                    "message": f"Basket '{basket_name}' not found"
                }
            
            basket = self.trade_baskets[basket_name]
            
            # Get details of positions in the basket
            positions_details = []
            total_profit = 0
            
            for ticket in basket['positions']:
                position = mt5.positions_get(ticket=ticket)
                if position is not None and len(position) > 0:
                    position_dict = position[0]._asdict()
                    positions_details.append(position_dict)
                    total_profit += position_dict['profit']
            
            # Update basket profit
            basket['profit'] = total_profit
            
            return {
                "success": True,
                "message": f"Retrieved basket '{basket_name}'",
                "basket": basket,
                "positions": positions_details,
                "total_profit": total_profit,
                "position_count": len(positions_details)
            }
        except Exception as e:
            logger.exception(f"Exception getting trade basket: {str(e)}")
            return {
                "success": False,
                "message": f"Exception getting trade basket: {str(e)}"
            }
    
    def list_baskets(self) -> Dict[str, Any]:
        """
        List all trade baskets.
        
        Returns:
            Dict with list of baskets
        """
        try:
            baskets_list = []
            
            for name, basket in self.trade_baskets.items():
                # Get current profit for each basket
                total_profit = 0
                active_positions = 0
                
                for ticket in basket['positions']:
                    position = mt5.positions_get(ticket=ticket)
                    if position is not None and len(position) > 0:
                        position_dict = position[0]._asdict()
                        total_profit += position_dict['profit']
                        active_positions += 1
                
                # Update basket profit
                basket['profit'] = total_profit
                basket['active_positions'] = active_positions
                
                baskets_list.append(basket)
            
            return {
                "success": True,
                "message": f"Retrieved {len(baskets_list)} baskets",
                "baskets": baskets_list
            }
        except Exception as e:
            logger.exception(f"Exception listing trade baskets: {str(e)}")
            return {
                "success": False,
                "message": f"Exception listing trade baskets: {str(e)}"
            }
    
    def close_basket(self, basket_name: str) -> Dict[str, Any]:
        """
        Close all positions in a trade basket.
        
        Args:
            basket_name: Name of the basket
            
        Returns:
            Dict with closure result
        """
        try:
            # Check if basket exists
            if basket_name not in self.trade_baskets:
                return {
                    "success": False,
                    "message": f"Basket '{basket_name}' not found"
                }
            
            basket = self.trade_baskets[basket_name]
            
            # Close all positions in the basket
            results = []
            success_count = 0
            total_profit = 0
            
            for ticket in basket['positions']:
                position = mt5.positions_get(ticket=ticket)
                if position is not None and len(position) > 0:
                    result = self.close_position(ticket)
                    
                    results.append({
                        'ticket': ticket,
                        'success': result['success'],
                        'message': result['message']
                    })
                    
                    if result['success']:
                        success_count += 1
                        total_profit += result.get('profit', 0)
            
            # Update basket status
            basket['status'] = 'closed'
            basket['closed_at'] = datetime.now().isoformat()
            basket['final_profit'] = total_profit
            
            logger.info(f"Closed basket '{basket_name}' with profit {total_profit}")
            
            return {
                "success": True,
                "message": f"Closed {success_count} of {len(basket['positions'])} positions in basket '{basket_name}'",
                "basket": basket,
                "results": results,
                "total_profit": total_profit
            }
        except Exception as e:
            logger.exception(f"Exception closing trade basket: {str(e)}")
            return {
                "success": False,
                "message": f"Exception closing trade basket: {str(e)}"
            }
            
    #============================================================================
    # OCO/OTO Order Functions
    #============================================================================
    
    def place_oco_orders(self, symbol: str, order_type: int, volume: float,
                         price1: float, price2: float, sl: Optional[float] = None,
                         tp: Optional[float] = None, comment: str = "") -> Dict[str, Any]:
        """
        Place One-Cancels-Other (OCO) orders - when one order is filled, the other is cancelled.
        
        Args:
            symbol: Symbol name
            order_type: Order type (mt5.ORDER_TYPE_BUY_LIMIT, mt5.ORDER_TYPE_SELL_LIMIT, etc.)
            volume: Order volume (lot size)
            price1: Price for first order
            price2: Price for second order
            sl: Stop loss price (optional)
            tp: Take profit price (optional)
            comment: Order comment (optional)
            
        Returns:
            Dict with order result
        """
        try:
            # Check connection
            if not self.connection.is_connected():
                return {
                    "success": False,
                    "message": "Not connected to MT5"
                }
            
            # Check symbol info
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                return {
                    "success": False,
                    "message": f"Symbol {symbol} not found"
                }
            
            if not symbol_info.visible:
                if not mt5.symbol_select(symbol, True):
                    return {
                        "success": False,
                        "message": f"Failed to select symbol {symbol}"
                    }
            
            # Generate a unique magic number to identify this OCO pair
            magic = int(time.time()) % 1000000
            
            # Prepare first order
            request1 = {
                "action": mt5.TRADE_ACTION_PENDING,
                "symbol": symbol,
                "volume": volume,
                "type": order_type,
                "price": price1,
                "deviation": 10,
                "magic": magic,
                "comment": f"OCO-1: {comment}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_FOK
            }
            
            # Add SL/TP if provided
            if sl is not None:
                request1["sl"] = sl
            if tp is not None:
                request1["tp"] = tp
            
            # Place first order
            result1 = mt5.order_send(request1)
            if result1 is None or result1.retcode != mt5.TRADE_RETCODE_DONE:
                return {
                    "success": False,
                    "message": f"Failed to place first OCO order: {result1.retcode if result1 else 'Unknown error'}",
                    "result1": result1._asdict() if result1 else None
                }
            
            # Prepare second order
            request2 = {
                "action": mt5.TRADE_ACTION_PENDING,
                "symbol": symbol,
                "volume": volume,
                "type": order_type,
                "price": price2,
                "deviation": 10,
                "magic": magic,  # Same magic to identify the pair
                "comment": f"OCO-2: {comment}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_FOK
            }
            
            # Add SL/TP if provided
            if sl is not None:
                request2["sl"] = sl
            if tp is not None:
                request2["tp"] = tp
            
            # Place second order
            result2 = mt5.order_send(request2)
            if result2 is None or result2.retcode != mt5.TRADE_RETCODE_DONE:
                # If second order fails, cancel the first one
                self._cancel_order(result1.order)
                return {
                    "success": False,
                    "message": f"Failed to place second OCO order: {result2.retcode if result2 else 'Unknown error'}",
                    "result1": result1._asdict(),
                    "result2": result2._asdict() if result2 else None
                }
            
            # Store the OCO relationship for later management
            self._store_oco_pair(result1.order, result2.order, symbol, magic)
            
            return {
                "success": True,
                "message": "OCO orders placed successfully",
                "order1": result1._asdict(),
                "order2": result2._asdict(),
                "magic": magic
            }
        except Exception as e:
            logger.exception(f"Exception placing OCO orders: {str(e)}")
            return {
                "success": False,
                "message": f"Exception placing OCO orders: {str(e)}"
            }
    
    def place_oto_order(self, symbol: str, primary_type: int, primary_volume: float,
                        primary_price: float, secondary_type: int, secondary_volume: float,
                        secondary_price: float, primary_sl: Optional[float] = None,
                        primary_tp: Optional[float] = None, secondary_sl: Optional[float] = None,
                        secondary_tp: Optional[float] = None, comment: str = "") -> Dict[str, Any]:
        """
        Place One-Triggers-Other (OTO) orders - when primary order is filled, the secondary order is placed.
        
        Args:
            symbol: Symbol name
            primary_type: Primary order type (mt5.ORDER_TYPE_BUY, mt5.ORDER_TYPE_SELL, etc.)
            primary_volume: Primary order volume
            primary_price: Primary order price (0 for market orders)
            secondary_type: Secondary order type
            secondary_volume: Secondary order volume
            secondary_price: Secondary order price
            primary_sl: Primary order stop loss (optional)
            primary_tp: Primary order take profit (optional)
            secondary_sl: Secondary order stop loss (optional)
            secondary_tp: Secondary order take profit (optional)
            comment: Order comment (optional)
            
        Returns:
            Dict with order result
        """
        try:
            # Check connection
            if not self.connection.is_connected():
                return {
                    "success": False,
                    "message": "Not connected to MT5"
                }
            
            # Check symbol info
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                return {
                    "success": False,
                    "message": f"Symbol {symbol} not found"
                }
            
            if not symbol_info.visible:
                if not mt5.symbol_select(symbol, True):
                    return {
                        "success": False,
                        "message": f"Failed to select symbol {symbol}"
                    }
            
            # Generate unique magic number for this OTO pair
            magic = int(time.time()) % 1000000
            
            # Determine if primary is market or pending order
            is_market_order = primary_type in [mt5.ORDER_TYPE_BUY, mt5.ORDER_TYPE_SELL]
            
            # Set appropriate action and price
            if is_market_order:
                action = mt5.TRADE_ACTION_DEAL
                if primary_type == mt5.ORDER_TYPE_BUY:
                    price = mt5.symbol_info_tick(symbol).ask
                else:  # SELL
                    price = mt5.symbol_info_tick(symbol).bid
            else:
                action = mt5.TRADE_ACTION_PENDING
                price = primary_price
            
            # Prepare primary order request
            request = {
                "action": action,
                "symbol": symbol,
                "volume": primary_volume,
                "type": primary_type,
                "price": price,
                "deviation": 10,
                "magic": magic,
                "comment": f"OTO-Primary: {comment}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_FOK
            }
            
            # Add SL/TP if provided
            if primary_sl is not None:
                request["sl"] = primary_sl
            if primary_tp is not None:
                request["tp"] = primary_tp
            
            # Place primary order
            result = mt5.order_send(request)
            if result is None or result.retcode != mt5.TRADE_RETCODE_DONE:
                return {
                    "success": False,
                    "message": f"Failed to place primary OTO order: {result.retcode if result else 'Unknown error'}",
                    "result": result._asdict() if result else None
                }
            
            # Store secondary order details to be placed when primary is filled
            # In a real implementation, you would need to set up a system to monitor for the fill
            # Here we'll store the information needed for the secondary order
            secondary_details = {
                "symbol": symbol,
                "type": secondary_type,
                "volume": secondary_volume,
                "price": secondary_price,
                "sl": secondary_sl,
                "tp": secondary_tp,
                "magic": magic,
                "comment": f"OTO-Secondary: {comment}"
            }
            
            # Store relationship for monitoring
            self._store_oto_pair(result.order, secondary_details, magic)
            
            return {
                "success": True,
                "message": "OTO primary order placed successfully",
                "primary_order": result._asdict(),
                "secondary_details": secondary_details,
                "magic": magic
            }
        except Exception as e:
            logger.exception(f"Exception placing OTO orders: {str(e)}")
            return {
                "success": False,
                "message": f"Exception placing OTO orders: {str(e)}"
            }
    
    def _store_oco_pair(self, order1: int, order2: int, symbol: str, magic: int):
        """Store OCO pair information for management."""
        # This would typically be stored in a database for persistence
        # For this implementation, we'll use the trade_history dictionary
        oco_key = f"oco_{magic}"
        self.trade_history[oco_key] = {
            'type': 'oco',
            'time': datetime.now().isoformat(),
            'symbol': symbol,
            'magic': magic,
            'order1': order1,
            'order2': order2,
            'status': 'active'
        }
        
        logger.info(f"Stored OCO pair {order1}/{order2} with magic {magic}")
    
    def _store_oto_pair(self, primary_order: int, secondary_details: Dict, magic: int):
        """Store OTO pair information for management."""
        # This would typically be stored in a database for persistence
        # For this implementation, we'll use the trade_history dictionary
        oto_key = f"oto_{magic}"
        self.trade_history[oto_key] = {
            'type': 'oto',
            'time': datetime.now().isoformat(),
            'primary_order': primary_order,
            'secondary_details': secondary_details,
            'magic': magic,
            'status': 'waiting_for_fill'  # Will change to 'filled' or 'secondary_placed'
        }
        
        logger.info(f"Stored OTO pair with primary order {primary_order} and magic {magic}")
    
    def _cancel_order(self, order_id: int) -> Dict[str, Any]:
        """
        Cancel a pending order.
        
        Args:
            order_id: Order ID to cancel
            
        Returns:
            Dict with cancellation result
        """
        try:
            # Check connection
            if not self.connection.is_connected():
                return {
                    "success": False,
                    "message": "Not connected to MT5"
                }
            
            # Get order info
            order = mt5.orders_get(ticket=order_id)
            if order is None or len(order) == 0:
                return {
                    "success": False,
                    "message": f"Order {order_id} not found"
                }
            
            # Prepare cancellation request
            request = {
                "action": mt5.TRADE_ACTION_REMOVE,
                "order": order_id,
            }
            
            # Send request
            result = mt5.order_send(request)
            if result is None or result.retcode != mt5.TRADE_RETCODE_DONE:
                return {
                    "success": False,
                    "message": f"Failed to cancel order: {result.retcode if result else 'Unknown error'}",
                    "result": result._asdict() if result else None
                }
            
            return {
                "success": True,
                "message": f"Order {order_id} cancelled successfully",
                "result": result._asdict()
            }
        except Exception as e:
            logger.exception(f"Exception cancelling order: {str(e)}")
            return {
                "success": False,
                "message": f"Exception cancelling order: {str(e)}"
            }

