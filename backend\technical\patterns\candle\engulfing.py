from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class EngulfingPatternIndicator(BaseIndicator):
    """Engulfing candlestick pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - body_ratio: Parameter description (default: 0.6)
        """
        default_params = {
            "body_ratio": 0.6,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)

    
    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        open_price = df['open'].values
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        body_ratio = self.params['body_ratio']
        
        # Calculate body sizes and ranges
        body_size = np.abs(close - open_price)
        total_range = high - low
        
        # Calculate previous candle's body
        prev_body_size = np.zeros_like(close)
        prev_body_size[1:] = np.abs(close[:-1] - open_price[:-1])
        
        # Calculate previous candle's high and low
        prev_high = np.zeros_like(high)
        prev_high[1:] = high[:-1]
        prev_low = np.zeros_like(low)
        prev_low[1:] = low[:-1]
        
        # Identify engulfing patterns
        is_bullish_engulfing = (
            (close > open_price) &  # Current candle is bullish
            (close[:-1] < open_price[:-1]) &  # Previous candle is bearish
            (open_price < close[:-1]) &  # Current open below previous close
            (close > open_price[:-1]) &  # Current close above previous open
            (body_size >= (total_range * body_ratio))  # Significant body size
        )
        
        is_bearish_engulfing = (
            (close < open_price) &  # Current candle is bearish
            (close[:-1] > open_price[:-1]) &  # Previous candle is bullish
            (open_price > close[:-1]) &  # Current open above previous close
            (close < open_price[:-1]) &  # Current close below previous open
            (body_size >= (total_range * body_ratio))  # Significant body size
        )
        
        # Classify engulfing types
        pattern_type = np.zeros_like(close)
        pattern_type[is_bullish_engulfing] = 1
        pattern_type[is_bearish_engulfing] = -1
        
        # Calculate pattern strength
        strength = np.zeros_like(close)
        strength[is_bullish_engulfing | is_bearish_engulfing] = body_size[is_bullish_engulfing | is_bearish_engulfing] / total_range[is_bullish_engulfing | is_bearish_engulfing]
        
        # Calculate trend context
        trend = np.zeros_like(close)
        for i in range(1, len(close)):
            if i >= 20:  # Use 20-period SMA for trend
                sma = np.mean(close[i-20:i])
                trend[i] = 1 if close[i] > sma else -1
        
        # Calculate pattern reliability
        reliability = np.zeros_like(close)
        for i in range(1, len(close)):
            if is_bullish_engulfing[i] or is_bearish_engulfing[i]:
                # Check if price moved in the expected direction
                if i < len(close)-1:
                    future_return = (close[i+1] - close[i]) / close[i]
                    if is_bullish_engulfing[i]:
                        reliability[i] = 1 if future_return > 0 else -1
                    else:  # Bearish engulfing
                        reliability[i] = 1 if future_return < 0 else -1
        
        return {
            'is_bullish_engulfing': is_bullish_engulfing.astype(int),
            'is_bearish_engulfing': is_bearish_engulfing.astype(int),
            'pattern_type': pattern_type,
            'strength': strength,
            'trend': trend,
            'reliability': reliability,
            'body_size': body_size,
            'prev_body_size': prev_body_size,
            'total_range': total_range
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if not 0 < self.params['body_ratio'] < 1:
            raise ValueError("Body ratio must be between 0 and 1")
        return True 