/**
 * Global error handling utilities
 */

/**
 * Safely patches the Number prototype to make toFixed more resilient
 * This is a last resort approach to prevent crashes from NaN.toFixed() calls
 * that might be in third-party libraries or bundled code
 */
export function patchNumberPrototype() {
  // Store the original toFixed method
  const originalToFixed = Number.prototype.toFixed;

  // Override with a safe version
  Number.prototype.toFixed = function(digits) {
    // Handle NaN, undefined, and null cases
    if (this === undefined || this === null || isNaN(this)) {
      console.warn(`Attempted to call toFixed on invalid value: ${this}`);
      return 'N/A';
    }
    
    try {
      return originalToFixed.call(this, digits);
    } catch (e) {
      console.error(`Error in toFixed with value ${this}:`, e);
      return 'N/A';
    }
  };

  // Also patch Chart.js formatters if available
  if (typeof window !== 'undefined' && window.Chart) {
    const originalFormatters = window.Chart.defaults.plugins.tooltip.callbacks;
    window.Chart.defaults.plugins.tooltip.callbacks = {
      ...originalFormatters,
      label: function(context) {
        try {
          if (originalFormatters.label) {
            return originalFormatters.label(context);
          }
          let label = context.dataset.label || '';
          if (label) {
            label += ': ';
          }
          if (context.parsed.y !== null && context.parsed.y !== undefined) {
            label += typeof context.parsed.y.toFixed === 'function' ? 
                    context.parsed.y.toFixed(2) : 'N/A';
          } else {
            label += 'N/A';
          }
          return label;
        } catch (e) {
          console.error('Error in Chart.js tooltip formatter:', e);
          return 'Error';
        }
      }
    };
  }

  console.log('Number.prototype.toFixed and Chart.js formatters patched for safety');
};

/**
 * Sets up a global error handler to catch unhandled errors
 */
export const setupGlobalErrorHandler = () => {
  window.onerror = (message, source, lineno, colno, error) => {
    console.error('Global error caught:', { message, source, lineno, colno });
    console.error('Error details:', error);
    
    // You could also display a user-friendly error message here
    // or send the error to a logging service
    
    // Return true to prevent the default browser error handling
    return true;
  };
  
  // Also catch unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
  });
  
  console.log('Global error handlers installed');
};
