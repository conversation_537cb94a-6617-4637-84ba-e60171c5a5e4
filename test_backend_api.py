import requests
import json
import time

def test_connection_status():
    print("Testing connection status endpoint...")
    try:
        response = requests.get("http://localhost:5001/api/connection/connection_status")
        print(f"Status code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Exception: {e}")

def test_connect():
    print("\nTesting connect endpoint...")
    try:
        payload = {
            "account": "*********",
            "password": "{2Rfj>0D",
            "server": "FBS-Demo",
            "path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe"
        }
        response = requests.post(
            "http://localhost:5001/api/connection/connect",
            json=payload
        )
        print(f"Status code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Exception: {e}")

def test_disconnect():
    print("\nTesting disconnect endpoint...")
    try:
        response = requests.post("http://localhost:5001/api/connection/disconnect")
        print(f"Status code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Exception: {e}")

def test_path():
    print("\nTesting path validation endpoint...")
    try:
        payload = {
            "path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe"
        }
        response = requests.post(
            "http://localhost:5001/api/connection/test_path",
            json=payload
        )
        print(f"Status code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Exception: {e}")

if __name__ == "__main__":
    print("Backend API Test")
    print("===============")
    
    # Test connection status
    test_connection_status()
    
    # Test path validation
    test_path()
    
    # Test connect
    test_connect()
    
    # Wait a bit
    time.sleep(2)
    
    # Test connection status again
    test_connection_status()
    
    # Test disconnect
    test_disconnect()
    
    # Wait a bit
    time.sleep(2)
    
    # Test connection status again
    test_connection_status()
    
    print("\nTest completed.")
