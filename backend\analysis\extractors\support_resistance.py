import pandas as pd
import logging
from typing import Dict, Any, Optional
from backend.technical.indicators.support_resistance.fractals import FractalsIndicator
from backend.technical.indicators.support_resistance.pivot_points import PivotPointsIndicator
from backend.technical.indicators.support_resistance.fibonacci_levels import FibonacciLevelsIndicator
from backend.technical.indicators.support_resistance.psychological_levels import PsychologicalLevelsIndicator
from backend.technical.indicators.support_resistance.sr_clusters import SRClusterIndicator
from backend.technical.indicators.support_resistance.supply_demand import SupplyDemandIndicator

logger = logging.getLogger("AnalysisEngine")

def analyze_support_resistance_comprehensive(data: pd.DataFrame, current_price: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze support and resistance levels comprehensively.

    Args:
        data: DataFrame containing price data
        current_price: Dictionary with current price information (bid, ask, last)

    Returns:
        Dictionary with support and resistance analysis results
    """
    logger.debug("Starting S/R comprehensive analysis...")
    try:
        # Enhanced safety checks and logging for current_price
        if not current_price:
            logger.debug("Returning empty S/R result due to missing current_price")
            logger.error("current_price is None or empty")
            return get_empty_sr_result()

        if not isinstance(current_price, dict):
            logger.debug(f"Returning empty S/R result due to invalid current_price type: {type(current_price)}")
            logger.error(f"current_price has invalid type: {type(current_price)}")
            return get_empty_sr_result()

        if 'bid' not in current_price:
            logger.debug(f"Returning empty S/R result due to missing 'bid' key in current_price. Keys: {current_price.keys()}")
            logger.error(f"current_price missing 'bid' key. Keys present: {current_price.keys()}")
            return get_empty_sr_result()

        try:
            # Validate bid value can be converted to float
            current_bid = float(current_price['bid'])
            if current_bid <= 0:
                logger.debug(f"Returning empty S/R result due to invalid bid price value: {current_bid}")
                logger.error(f"Invalid bid price value: {current_bid}")
                return get_empty_sr_result()
            logger.debug(f"Using valid current bid price: {current_bid}")
        except (ValueError, TypeError) as e: # Catch TypeError as well
            logger.debug(f"Returning empty S/R result due to error converting bid price: {e}")
            logger.error(f"Error converting bid price to float: {e}")
            return get_empty_sr_result()

        # If we reach here, current_price and current_bid are valid
        logger.debug("S/R Comp: Passed initial price checks.")

        # --- Main S/R Logic ---
        # Check if data is sufficient
        if data.empty or len(data) < 20: # Need enough data for indicators
            logger.warning("Insufficient data for comprehensive S/R analysis.")
            return get_empty_sr_result(error="Insufficient data")

        logger.debug("S/R Comp: Attempting to log input data info...")
        # Safely log DataFrame info
        try:
            import io
            buffer = io.StringIO()
            data.info(buf=buffer)
            info_str = buffer.getvalue()
            logger.debug(f"S/R Input DataFrame Info:\n{info_str}")
        except Exception as info_e:
            logger.error(f"Could not log DataFrame info: {info_e}")

        logger.debug("S/R Comp: Attempting to get current bid/ask/last...")
        # Ensure we have bid/ask for nearest level calculations
        current_ask = current_price.get('ask')
        current_last = current_price.get('last') # Use last if available, fallback to bid

        # Convert ask/last to float if they exist, handle potential errors
        try:
            if current_ask is not None:
                current_ask = float(current_ask)
            if current_last is not None:
                current_last = float(current_last)
        except (ValueError, TypeError) as price_conv_e:
             logger.warning(f"Could not convert ask/last price to float: {price_conv_e}. Using fallbacks.")
             current_ask = None # Reset to trigger fallback
             current_last = None # Reset to trigger fallback

        if current_ask is None or current_ask <= 0:
            logger.warning(f"Invalid or missing ask price ({current_ask}), using bid for nearest resistance calc.")
            current_ask = current_bid # Fallback, less ideal

        if current_last is None or current_last <= 0:
             logger.debug(f"Invalid or missing last price ({current_last}), using bid for calculations.")
             current_last = current_bid # Fallback

        logger.debug("S/R Comp: Bid/Ask check passed.")

        # Initialize indicators
        fractal_indicator = FractalsIndicator() # Removed invalid 'order' argument
        pivot_indicator = PivotPointsIndicator(method='classic') # Changed 'standard' to 'classic'
        fibo_indicator = FibonacciLevelsIndicator()

        # Determine appropriate precision factor based on price magnitude
        price_magnitude = len(str(int(current_bid)))
        if price_magnitude >= 5:  # Large numbers like 84685
            psych_precision = 0.001  # Will find levels at 84000, 85000, etc.
        elif price_magnitude >= 3:  # Medium numbers like 123
            psych_precision = 0.01  # Will find levels at 100, 120, 130, etc.
        else:  # Small numbers
            psych_precision = 0.1  # Will find levels at 1.0, 1.1, etc.

        logger.debug(f"Using psychological precision factor: {psych_precision} for price {current_bid}")
        psych_indicator = PsychologicalLevelsIndicator(precision_factor=psych_precision, num_levels=5)
        cluster_indicator = SRClusterIndicator(window=30, threshold_multiplier=0.5)
        supply_demand_indicator = SupplyDemandIndicator(atr_period=14, score_threshold=2) # Use new indicator

        logger.debug("S/R Comp: Initializing level lists...")
        all_resistance = []
        all_support = []
        level_details = {} # Store details like type, strength

        # --- Calculate Levels from Each Indicator ---
        logger.debug("S/R Comp: Starting indicator calculations (Fractals)...")
        # 1. Fractals
        try:
            fractals = fractal_indicator.calculate(data)
            if 'resistance' in fractals:
                for level in fractals['resistance']:
                    if pd.notna(level):
                        all_resistance.append(level)
                        level_details[level] = level_details.get(level, {'types': [], 'strengths': []})
                        level_details[level]['types'].append('Fractal')
                        level_details[level]['strengths'].append(1) # Base strength
            if 'support' in fractals:
                for level in fractals['support']:
                     if pd.notna(level):
                        all_support.append(level)
                        level_details[level] = level_details.get(level, {'types': [], 'strengths': []})
                        level_details[level]['types'].append('Fractal')
                        level_details[level]['strengths'].append(1)
            logger.debug(f"Fractals added: R={len(fractals.get('resistance',[]))}, S={len(fractals.get('support',[]))}")
        except Exception as e:
            logger.error(f"Error calculating Fractals: {e}", exc_info=True)

        # 2. Pivot Points
        logger.debug("S/R Comp: Calculating Pivot Points...")
        try:
            pivots = pivot_indicator.calculate(data)
            pivot_levels = [
                ('R3', pivots.get('r3')), ('R2', pivots.get('r2')), ('R1', pivots.get('r1')),
                ('PP', pivots.get('pp')),
                ('S1', pivots.get('s1')), ('S2', pivots.get('s2')), ('S3', pivots.get('s3'))
            ]
            for name, level in pivot_levels:
                if level is not None and pd.notna(level):
                    strength = 3 if name in ['R3', 'S3'] else (2 if name in ['R2', 'S2'] else 1)
                    if level > current_bid:
                        all_resistance.append(level)
                        level_details[level] = level_details.get(level, {'types': [], 'strengths': []})
                        level_details[level]['types'].append(f'Pivot_{name}')
                        level_details[level]['strengths'].append(strength)
                    else:
                        all_support.append(level)
                        level_details[level] = level_details.get(level, {'types': [], 'strengths': []})
                        level_details[level]['types'].append(f'Pivot_{name}')
                        level_details[level]['strengths'].append(strength)
            logger.debug(f"Pivot Points added: {[name for name, level in pivot_levels if level is not None]}")
        except Exception as e:
            logger.error(f"Error calculating Pivot Points: {e}", exc_info=True)

        # 3. Fibonacci Levels
        logger.debug("S/R Comp: Calculating Fibonacci Levels...")
        try:
            fibos = fibo_indicator.calculate(data)
            if 'levels' in fibos:
                for level_type, level_value in fibos['levels'].items():
                    if pd.notna(level_value):
                        strength = 2 # Assign moderate strength
                        if level_value > current_bid:
                            all_resistance.append(level_value)
                            level_details[level_value] = level_details.get(level_value, {'types': [], 'strengths': []})
                            level_details[level_value]['types'].append(f'Fib_{level_type}')
                            level_details[level_value]['strengths'].append(strength)
                        else:
                            all_support.append(level_value)
                            level_details[level_value] = level_details.get(level_value, {'types': [], 'strengths': []})
                            level_details[level_value]['types'].append(f'Fib_{level_type}')
                            level_details[level_value]['strengths'].append(strength)
                logger.debug(f"Fibonacci Levels added: {len(fibos['levels'])}")
        except Exception as e:
            logger.error(f"Error calculating Fibonacci Levels: {e}", exc_info=True)

        # 4. Psychological Levels
        logger.debug("S/R Comp: Calculating Psychological Levels...")
        try:
            psych_levels = psych_indicator.calculate(data, current_price=current_bid)

            # Store the psychological levels for direct inclusion in the final result
            psychological_levels_result = []

            # Process resistance levels
            if 'resistance' in psych_levels and isinstance(psych_levels['resistance'], list):
                for level_obj in psych_levels['resistance']:
                    if isinstance(level_obj, dict) and 'price' in level_obj:
                        # Add to the direct result list
                        psychological_levels_result.append(level_obj)

                        # Also add to the all_resistance list for clustering
                        level = level_obj['price']
                        if pd.notna(level):
                            all_resistance.append(level)
                            level_details[level] = level_details.get(level, {'types': [], 'strengths': []})
                            level_details[level]['types'].append('Psychological')
                            level_details[level]['strengths'].append(1) # Base strength

            # Process support levels
            if 'support' in psych_levels and isinstance(psych_levels['support'], list):
                for level_obj in psych_levels['support']:
                    if isinstance(level_obj, dict) and 'price' in level_obj:
                        # Add to the direct result list
                        psychological_levels_result.append(level_obj)

                        # Also add to the all_support list for clustering
                        level = level_obj['price']
                        if pd.notna(level):
                            all_support.append(level)
                            level_details[level] = level_details.get(level, {'types': [], 'strengths': []})
                            level_details[level]['types'].append('Psychological')
                            level_details[level]['strengths'].append(1)

            logger.debug(f"Psychological Levels added: R={len([l for l in psychological_levels_result if l.get('price', 0) > current_bid])}, S={len([l for l in psychological_levels_result if l.get('price', 0) < current_bid])}")
        except Exception as e:
            logger.error(f"Error calculating Psychological Levels: {e}", exc_info=True)
            psychological_levels_result = []

        # 5. Supply/Demand Zones (using the new indicator)
        logger.debug("S/R Comp: Calculating Supply/Demand Zones...")
        try:
            # Ensure 'atr' column exists, even if it's all NaN (added earlier)
            if 'atr' not in data.columns:
                 logger.warning("ATR column missing before SupplyDemand calc, adding NaN column.")
                 data['atr'] = pd.Series(index=data.index, dtype=float)

            sd_zones = supply_demand_indicator.calculate(data)
            if 'demand_zones' in sd_zones:
                for zone in sd_zones['demand_zones']:
                    # Use the zone's range (e.g., average or distal line) as support levels
                    level = zone['distal'] # Or zone['proximal'], or average
                    strength = zone.get('strength_score', 1) # Use score if available
                    if pd.notna(level):
                        all_support.append(level)
                        level_details[level] = level_details.get(level, {'types': [], 'strengths': []})
                        level_details[level]['types'].append('DemandZone')
                        level_details[level]['strengths'].append(strength)
            if 'supply_zones' in sd_zones:
                for zone in sd_zones['supply_zones']:
                    level = zone['distal'] # Or zone['proximal'], or average
                    strength = zone.get('strength_score', 1)
                    if pd.notna(level):
                        all_resistance.append(level)
                        level_details[level] = level_details.get(level, {'types': [], 'strengths': []})
                        level_details[level]['types'].append('SupplyZone')
                        level_details[level]['strengths'].append(strength)
            logger.debug(f"Supply/Demand Zones added: Supply={len(sd_zones.get('supply_zones',[]))}, Demand={len(sd_zones.get('demand_zones',[]))}")
        except Exception as e:
            logger.error(f"Error calculating Supply/Demand Zones: {e}", exc_info=True)


        # --- Cluster Analysis ---
        logger.debug("S/R Comp: Starting Cluster Analysis...")
        try:
            # Combine all potential levels before clustering
            combined_levels = list(set(all_resistance + all_support))
            if not combined_levels:
                 logger.warning("No levels found before clustering.")
                 clusters = {'resistance_clusters': [], 'support_clusters': []}

                 # Generate some basic levels if none found
                 if not all_resistance and not all_support:
                     logger.warning("No levels found at all. Generating basic levels around current price.")
                     # Generate some basic levels around current price
                     # Adjust step size based on price magnitude
                     price_magnitude = len(str(int(current_bid)))
                     if price_magnitude >= 5:  # Large numbers like 84685
                         base_step = 1000  # Use fixed steps for large numbers
                     elif price_magnitude >= 3:  # Medium numbers
                         base_step = 10  # Use fixed steps for medium numbers
                     else:  # Small numbers
                         base_step = current_bid * 0.01  # 1% step for small numbers

                     # Generate increasingly spaced levels
                     for i in range(1, 6):  # Generate 5 levels above and below
                         # Use exponential spacing for better distribution
                         spacing_factor = i * 1.5  # Increase spacing as we move away from price
                         resistance_level = current_bid + (spacing_factor * base_step)
                         support_level = current_bid - (spacing_factor * base_step)

                         all_resistance.append(resistance_level)
                         level_details[resistance_level] = {'types': ['Generated'], 'strengths': [1]}

                         all_support.append(support_level)
                         level_details[support_level] = {'types': ['Generated'], 'strengths': [1]}

                     # Try clustering again with generated levels
                     combined_levels = list(set(all_resistance + all_support))
                     clusters = cluster_indicator.calculate_from_levels(combined_levels, current_bid, level_details)
            else:
                # Create a temporary DataFrame for the cluster indicator
                # It expects 'high' and 'low', we can use combined levels for this purpose
                # or pass the original data if the indicator is adapted.
                # Assuming cluster_indicator can work with a list of levels:
                # Note: The SRClusterIndicator might need adaptation to accept a list
                # or we need to format data appropriately. Let's assume it's adapted.
                # If it needs OHLC, pass data.copy()
                clusters = cluster_indicator.calculate_from_levels(combined_levels, current_bid, level_details) # Assuming modified method

            final_resistance = []
            final_support = []

            # Add clustered levels with aggregated strength
            if 'resistance_clusters' in clusters:
                for cluster in clusters['resistance_clusters']:
                    final_resistance.append(cluster) # cluster should be dict like {'level': X, 'strength': Y, 'types': [...]}
            if 'support_clusters' in clusters:
                 for cluster in clusters['support_clusters']:
                    final_support.append(cluster)

            # Add significant single levels (e.g., strong pivots, fibs) if they weren't clustered
            # This requires checking if a level from level_details is already part of a cluster
            clustered_values = set(c['level'] for c in final_resistance + final_support)
            for level, details in level_details.items():
                if level not in clustered_values:
                    avg_strength = sum(details['strengths']) / len(details['strengths']) if details['strengths'] else 0
                    # Add if strength is high enough or type is significant (e.g., Pivot R3/S3)
                    is_significant_pivot = any(p in ['Pivot_R3', 'Pivot_S3'] for p in details['types'])
                    if avg_strength >= 2 or is_significant_pivot: # Example threshold
                         level_info = {'level': level, 'strength': round(avg_strength, 2), 'types': details['types']}
                         if level > current_bid:
                             final_resistance.append(level_info)
                         else:
                             final_support.append(level_info)

            # If we still don't have any support or resistance levels, add all unclustered levels
            if not final_resistance and all_resistance:
                logger.warning("No resistance clusters found. Adding all unclustered resistance levels.")
                for r in sorted(list(set(all_resistance))):
                    final_resistance.append({
                        'level': r,
                        'strength': round(sum(level_details[r]['strengths'])/len(level_details[r]['strengths']),2),
                        'types': level_details[r]['types']
                    })

            if not final_support and all_support:
                logger.warning("No support clusters found. Adding all unclustered support levels.")
                for s in sorted(list(set(all_support)), reverse=True):
                    final_support.append({
                        'level': s,
                        'strength': round(sum(level_details[s]['strengths'])/len(level_details[s]['strengths']),2),
                        'types': level_details[s]['types']
                    })

            # Sort final levels
            final_resistance.sort(key=lambda x: x['level'])
            final_support.sort(key=lambda x: x['level'], reverse=True)

            logger.debug(f"Clustering complete. Final R={len(final_resistance)}, Final S={len(final_support)}")

        except Exception as e:
             logger.error(f"Error during Clustering: {e}", exc_info=True)
             # Fallback: Use unclustered levels if clustering fails
             final_resistance = [{'level': r, 'strength': round(sum(level_details[r]['strengths'])/len(level_details[r]['strengths']),2), 'types': level_details[r]['types']} for r in sorted(list(set(all_resistance)))]
             final_support = [{'level': s, 'strength': round(sum(level_details[s]['strengths'])/len(level_details[s]['strengths']),2), 'types': level_details[s]['types']} for s in sorted(list(set(all_support)), reverse=True)]
             logger.warning("Using unclustered levels due to clustering error.")

             # If we still don't have any support or resistance levels, generate some basic ones
             if not final_resistance or not final_support:
                 logger.warning("Still missing support or resistance levels. Generating basic levels.")
                 if not final_resistance:
                     # Generate some basic resistance levels
                     # Adjust step size based on price magnitude
                     price_magnitude = len(str(int(current_bid)))
                     if price_magnitude >= 5:  # Large numbers like 84685
                         base_step = 1000  # Use fixed steps for large numbers
                     elif price_magnitude >= 3:  # Medium numbers
                         base_step = 10  # Use fixed steps for medium numbers
                     else:  # Small numbers
                         base_step = current_bid * 0.01  # 1% step for small numbers

                     # Generate increasingly spaced levels
                     for i in range(1, 6):  # Generate 5 levels above
                         # Use exponential spacing for better distribution
                         spacing_factor = i * 1.5  # Increase spacing as we move away from price
                         resistance_level = current_bid + (spacing_factor * base_step)
                         final_resistance.append({
                             'level': resistance_level,
                             'strength': 1,
                             'types': ['Generated']
                         })

                 if not final_support:
                     # Generate some basic support levels
                     # Adjust step size based on price magnitude
                     price_magnitude = len(str(int(current_bid)))
                     if price_magnitude >= 5:  # Large numbers like 84685
                         base_step = 1000  # Use fixed steps for large numbers
                     elif price_magnitude >= 3:  # Medium numbers
                         base_step = 10  # Use fixed steps for medium numbers
                     else:  # Small numbers
                         base_step = current_bid * 0.01  # 1% step for small numbers

                     # Generate increasingly spaced levels
                     for i in range(1, 6):  # Generate 5 levels below
                         # Use exponential spacing for better distribution
                         spacing_factor = i * 1.5  # Increase spacing as we move away from price
                         support_level = current_bid - (spacing_factor * base_step)
                         final_support.append({
                             'level': support_level,
                             'strength': 1,
                             'types': ['Generated']
                         })

                 # Sort the generated levels
                 final_resistance.sort(key=lambda x: x['level'])
                 final_support.sort(key=lambda x: x['level'], reverse=True)


        # --- Find Nearest Levels ---
        logger.debug("S/R Comp: Finding nearest levels...")
        nearest_resistance = None
        min_diff_res = float('inf')

        # If we have resistance levels, find the nearest one
        if final_resistance:
            # First try to find resistance above current price
            for r_info in final_resistance:
                level = r_info['level']
                # Use ask price to check if we are below resistance
                if level > current_ask: # Price needs to overcome Ask to break resistance
                    diff = level - current_ask
                    if diff < min_diff_res:
                        min_diff_res = diff
                        nearest_resistance = r_info

            # If no resistance found above price, just take the closest one
            if nearest_resistance is None and final_resistance:
                logger.debug("No resistance above price, using closest resistance level")
                nearest_resistance = min(final_resistance, key=lambda x: abs(x['level'] - current_ask))
                logger.debug(f"Selected nearest resistance: {nearest_resistance}")

            # Log the nearest resistance for debugging
            logger.info(f"Found nearest resistance: {nearest_resistance}")

        nearest_support = None
        min_diff_sup = float('inf')

        # If we have support levels, find the nearest one
        if final_support:
            # First try to find support below current price
            for s_info in final_support:
                level = s_info['level']
                # Use bid price to check if we are above support
                if level < current_bid: # Price needs to break Bid to break support
                    diff = current_bid - level
                    if diff < min_diff_sup:
                        min_diff_sup = diff
                        nearest_support = s_info

            # If no support found below price, just take the closest one
            if nearest_support is None and final_support:
                logger.debug("No support below price, using closest support level")
                nearest_support = min(final_support, key=lambda x: abs(x['level'] - current_bid))
                logger.debug(f"Selected nearest support: {nearest_support}")

            # Log the nearest support for debugging
            logger.info(f"Found nearest support: {nearest_support}")

        logger.debug(f"Nearest R: {nearest_resistance}, Nearest S: {nearest_support}")

        # --- Prepare Final Result ---
        # Convert the format to match what the frontend expects
        formatted_resistance = []
        formatted_support = []

        # Format resistance levels
        for r in final_resistance:
            formatted_resistance.append({
                "price": r["level"],  # Frontend expects 'price' instead of 'level'
                "source": ", ".join(r["types"]) if "types" in r else "Unknown",
                "strength": r.get("strength", 1)
            })

        # Format support levels
        for s in final_support:
            formatted_support.append({
                "price": s["level"],  # Frontend expects 'price' instead of 'level'
                "source": ", ".join(s["types"]) if "types" in s else "Unknown",
                "strength": s.get("strength", 1)
            })

        # Format nearest resistance
        formatted_nearest_resistance = None
        if nearest_resistance:
            formatted_nearest_resistance = {
                "price": nearest_resistance["level"],
                "source": ", ".join(nearest_resistance["types"]) if "types" in nearest_resistance else "Unknown",
                "strength": nearest_resistance.get("strength", 1)
            }

        # Format nearest support
        formatted_nearest_support = None
        if nearest_support:
            formatted_nearest_support = {
                "price": nearest_support["level"],
                "source": ", ".join(nearest_support["types"]) if "types" in nearest_support else "Unknown",
                "strength": nearest_support.get("strength", 1)
            }

        # Ensure we have supply and demand zones
        supply_zones = sd_zones.get('supply_zones', []) if 'sd_zones' in locals() and isinstance(sd_zones, dict) else []
        demand_zones = sd_zones.get('demand_zones', []) if 'sd_zones' in locals() and isinstance(sd_zones, dict) else []

        # If we still don't have supply zones, generate some based on resistance levels
        if not supply_zones and formatted_resistance:
            logger.warning("No supply zones found, generating from resistance levels")
            # Generate supply zones from resistance levels
            for i, r in enumerate(formatted_resistance[:3]):  # Use top 3 resistance levels
                price = r.get('price', 0)
                if price > 0:
                    # Create a small zone around each resistance level
                    zone_width = price * 0.005  # 0.5% of price
                    supply_zones.append({
                        'top': price + zone_width/2,
                        'bottom': price - zone_width/2,
                        'distal': price + zone_width/2,
                        'proximal': price - zone_width/2,
                        'strength_score': r.get('strength', 1.0)
                    })

        # If we still don't have demand zones, generate some based on support levels
        if not demand_zones and formatted_support:
            logger.warning("No demand zones found, generating from support levels")
            # Generate demand zones from support levels
            for i, s in enumerate(formatted_support[:3]):  # Use top 3 support levels
                price = s.get('price', 0)
                if price > 0:
                    # Create a small zone around each support level
                    zone_width = price * 0.005  # 0.5% of price
                    demand_zones.append({
                        'top': price + zone_width/2,
                        'bottom': price - zone_width/2,
                        'distal': price - zone_width/2,
                        'proximal': price + zone_width/2,
                        'strength_score': s.get('strength', 1.0)
                    })

        # Create the final result with all the keys expected by the frontend
        final_results = {
            "resistance_levels": formatted_resistance,
            "support_levels": formatted_support,
            "nearest_resistance": formatted_nearest_resistance,
            "nearest_support": formatted_nearest_support,
            "sr_cluster_zones": clusters.get('sr_cluster_zones', []) if 'clusters' in locals() and isinstance(clusters, dict) else [],
            "psychological_levels": psychological_levels_result if 'psychological_levels_result' in locals() else [],
            "supply_zones": supply_zones,
            "demand_zones": demand_zones,
            "current_price": current_price  # Include the price used for analysis
        }
        logger.debug(f"Returning final S/R results. Type={type(final_results)}, Value={final_results}")
        return final_results

    except Exception as e: # Catch exceptions from the main S/R logic
        logger.exception(f"Exception during S/R comprehensive analysis internals: {str(e)}")
        logger.debug(f"Returning S/R error due to exception: {str(e)}")
        # Use the helper function to return a consistent error structure
        return get_empty_sr_result(error=f"Internal S/R Exception: {str(e)}")

    finally: # Add finally block correctly indented relative to the inner try
        logger.debug("Exiting analyze_support_resistance_comprehensive function.")


def get_empty_sr_result(error: Optional[str] = "Invalid or missing current price data") -> Dict[str, Any]:
    """
    Return empty S/R analysis result structure, optionally with an error message.

    Args:
        error: Optional error message

    Returns:
        Dictionary with empty S/R analysis structure
    """
    return {
        "error": error, # Use the provided error message
        "resistance_levels": [],
        "support_levels": [],
        "nearest_support": None,
        "nearest_resistance": None,
        "supply_zones": [], # Ensure these keys are present even in error case
        "demand_zones": [],
        "psychological_levels": [],
        "sr_cluster_zones": [],
        "current_price": None
    }
