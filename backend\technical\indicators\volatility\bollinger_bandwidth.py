from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.analysis.technical.indicators.trend.bollinger import BollingerBandsIndicator # Depends on BBands
from src.core.models.market_data import MarketData

class BollingerBandwidthIndicator(BaseIndicator):
    """Bollinger Bandwidth indicator."""

    def __init__(self, period: int = 20, num_std_dev: float = 2.0,
                 source: str = 'close', ma_type: str = 'sma'):
        """
        Initialize Bollinger Bandwidth indicator.

        Args:
            period: The period for the underlying Bollinger Bands calculation.
            num_std_dev: The number of standard deviations for the bands.
            source: The price source for the bands.
            ma_type: The type of moving average for the middle band.
        """
        super().__init__({
            'period': period,
            'num_std_dev': num_std_dev,
            'source': source,
            'ma_type': ma_type
        })
        # Internal Bollinger Bands indicator
        self._bb_indicator = BollingerBandsIndicator(
            period=period, num_std_dev=num_std_dev, source=source, ma_type=ma_type
        )

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Bollinger Bandwidth."""
        df = data.to_dataframe()
        # Check length requirement based on underlying BBands
        if df.empty or len(df) < self.params['period']:
             return {'bandwidth': np.array([])}

        # Calculate Bollinger Bands first
        bb_result = self._bb_indicator.calculate(data)
        upper_band = pd.Series(bb_result['upper_band'], index=df.index)
        middle_band = pd.Series(bb_result['middle_band'], index=df.index)
        lower_band = pd.Series(bb_result['lower_band'], index=df.index)

        # Calculate Bandwidth = (Upper - Lower) / Middle * 100
        # Avoid division by zero
        middle_band_safe = middle_band.replace(0, np.nan)
        bandwidth_values = ((upper_band - lower_band) / middle_band_safe) * 100
        bandwidth_values = bandwidth_values.fillna(0) # Fill initial NaNs

        self._values = {
            'bandwidth': bandwidth_values.values
            # Optionally return underlying bands as well
            # 'upper_band': upper_band.values,
            # 'middle_band': middle_band.values,
            # 'lower_band': lower_band.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters by validating underlying BBands."""
        # Reuse validation from BollingerBandsIndicator
        return self._bb_indicator.validate_params()