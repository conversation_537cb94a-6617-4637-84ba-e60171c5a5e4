from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class UlcerIndexIndicator(BaseIndicator):
    """Ulcer Index indicator for measuring downside risk."""
    
    def __init__(self, period: int = 14):
        """Initialize Ulcer Index indicator."""
        super().__init__({'period': period})
    
    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Ulcer Index values."""
        df = data.to_dataframe()
        if df.empty:
            return {}
        
        close = df['close'].values
        period = self.params['period']
        
        # Calculate drawdowns
        rolling_max = pd.Series(close).rolling(window=period, min_periods=1).max()
        drawdowns = (close - rolling_max) / rolling_max * 100
        
        # Calculate squared drawdowns
        squared_drawdowns = drawdowns ** 2
        
        # Calculate Ulcer Index
        ulcer_index = pd.Series(squared_drawdowns).rolling(window=period).mean() ** 0.5
        
        self._values = {
            'ulcer_index': ulcer_index.values,
            'drawdowns': drawdowns.values,
            'squared_drawdowns': squared_drawdowns.values
        }
        return self._values 