from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class AwesomeOscillatorIndicator(BaseIndicator):
    """Awesome Oscillator (AO) indicator."""

    def __init__(self, fast_period: int = 5, slow_period: int = 34):
        """
        Initialize Awesome Oscillator indicator.

        Args:
            fast_period: The period for the fast SMA (typically 5).
            slow_period: The period for the slow SMA (typically 34).
        """
        super().__init__({
            'fast_period': fast_period,
            'slow_period': slow_period
        })

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Awesome Oscillator values."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['slow_period']:
             return {'ao': np.array([])}

        fast_period = self.params['fast_period']
        slow_period = self.params['slow_period']

        # Calculate Median Price (High + Low) / 2
        median_price = (df['high'] + df['low']) / 2

        # Calculate SMAs of Median Price
        fast_sma = median_price.rolling(window=fast_period).mean()
        slow_sma = median_price.rolling(window=slow_period).mean()

        # Calculate Awesome Oscillator
        ao_values = fast_sma - slow_sma

        self._values = {
            'ao': ao_values.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['fast_period'] >= self.params['slow_period']:
            raise ValueError("Fast period must be less than slow period")
        if self.params['fast_period'] < 1:
            raise ValueError("Fast period must be greater than 0")
        if self.params['slow_period'] < 1:
            raise ValueError("Slow period must be greater than 0")
        return True