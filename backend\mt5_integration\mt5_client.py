"""
Main MT5 integration client.
"""

import logging
from typing import Dict, List, Optional, Union, Any
from datetime import datetime

from .components import ConnectionComponent, AccountComponent, SymbolComponent
from .components.trading import TradingComponent
from .types import ConnectionState, AccountInfo, SymbolInfo
from .exceptions import MT5Error

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("mt5_integration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("MT5Integration")

class MT5Integration:
    """
    Main class for integrating with MetaTrader 5.

    This class uses components to handle different aspects of MT5 integration.
    """

    def __init__(self):
        """Initialize the MT5 integration client."""
        self.connection = ConnectionComponent()
        self.account = AccountComponent(self.connection)
        self.symbols = SymbolComponent(self.connection)
        self.trading = TradingComponent(self.connection)

    @property
    def state(self) -> ConnectionState:
        """Get the current connection state."""
        return self.connection.state

    def initialize(self, path: Optional[str] = None, login: Optional[int] = None,
                  password: Optional[str] = None, server: Optional[str] = None,
                  timeout: Optional[int] = None, settings_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Initialize connection to MetaTrader 5 terminal.

        Connection details can be provided directly as arguments or loaded from settings file.
        Arguments take precedence over settings file values.
        """
        return self.connection.initialize(
            path=path,
            login=login,
            password=password,
            server=server,
            timeout=timeout,
            settings_path=settings_path
        )

    def disconnect(self) -> Dict[str, Any]:
        """Disconnect from MetaTrader 5 terminal."""
        return self.connection.disconnect()

    def is_connected(self) -> bool:
        """
        Check if currently connected to MT5.

        This does a real check of the connection, not just the stored state.
        """
        return self.connection.is_connected()

    def get_account_info(self, use_cache: bool = True) -> Optional[AccountInfo]:
        """
        Get current account information, with optional caching.

        Args:
            use_cache: Whether to use cached account info (if available and not expired)

        Returns:
            AccountInfo tuple if successful, None if failed or not connected
        """
        return self.account.get_account_info(use_cache=use_cache)

    def get_connection_info(self) -> Dict[str, Any]:
        """Get current connection state and details."""
        info = self.connection.get_connection_info()

        # Add account info if connected
        if info["connected"]:
            try:
                enhanced_account_info = self.account.get_enhanced_account_info()
                if enhanced_account_info and "error" not in enhanced_account_info:
                    info["account_info"] = enhanced_account_info
                else:
                    info["account_info_error"] = enhanced_account_info.get("error", "Unknown error")
            except Exception as acc_err:
                logger.error(f"Error getting account info: {acc_err}")
                info["account_info_error"] = str(acc_err)

        return info

    def get_symbols(self, include_metadata: bool = False, use_cache: bool = True,
                   force_refresh: bool = False) -> Union[List[str], List[SymbolInfo]]:
        """
        Get list of available trading symbols from MT5.

        Args:
            include_metadata: If True, returns full SymbolInfo objects instead of just symbol names
            use_cache: Whether to use cached symbol info (if available and not expired)
            force_refresh: Force refresh of cache even if not expired

        Returns:
            List of symbol names or SymbolInfo objects if include_metadata=True
        """
        return self.symbols.get_symbols(
            include_metadata=include_metadata,
            use_cache=use_cache,
            force_refresh=force_refresh
        )

    def get_current_price(self, symbol: str) -> Dict[str, Any]:
        """
        Get the current bid/ask price for a symbol.

        Args:
            symbol (str): The trading symbol (e.g., "EURUSD")

        Returns:
            Dict with bid, ask, and last prices
        """
        return self.symbols.get_current_price(symbol)

    def fetch_ohlc_data(self, symbol: str, timeframe: int,
                       start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """
        Fetch OHLC data for a given symbol and timeframe.

        Args:
            symbol: The trading symbol (e.g., 'EURUSD')
            timeframe: The timeframe in seconds (e.g., mt5.TIMEFRAME_M1 for 1 minute)
            start_time: The start time for the data range
            end_time: The end time for the data range

        Returns:
            A list of dictionaries containing OHLC data for the specified range.
        """
        return self.symbols.fetch_ohlc_data(
            symbol=symbol,
            timeframe=timeframe,
            start_time=start_time,
            end_time=end_time
        )

    def get_status(self) -> Dict[str, Any]:
        """
        Get current connection status.
        This is an alias for get_connection_info() to maintain API compatibility.

        Returns:
            Dict containing connection status information
        """
        try:
            return self.get_connection_info()
        except Exception as e:
            logger.error(f"Error in get_status: {e}")
            return {
                "state": ConnectionState.ERROR.value,
                "connected": False,
                "last_error": str(e),
                "error_location": "get_status"
            }

    def check_connection(self) -> Dict[str, Any]:
        """
        Check if connected to MT5.

        Returns:
            Dict with connection status
        """
        try:
            connected = self.is_connected()
            return {
                "success": True,
                "connected": connected
            }
        except Exception as e:
            logger.error(f"Error checking connection: {e}")
            return {
                "success": False,
                "connected": False,
                "message": str(e)
            }

    def get_open_positions(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """
        Get open positions.

        Args:
            symbol: Optional symbol filter

        Returns:
            Dict with positions
        """
        return self.trading.get_open_positions(symbol)

    def place_market_order(self, symbol: str, order_type: int, volume: float,
                          sl: Optional[float] = None, tp: Optional[float] = None,
                          deviation: int = 10, magic: int = 0,
                          comment: str = "") -> Dict[str, Any]:
        """
        Place a market order.

        Args:
            symbol: Symbol name
            order_type: Order type (mt5.ORDER_TYPE_BUY or mt5.ORDER_TYPE_SELL)
            volume: Order volume (lot size)
            sl: Stop loss price
            tp: Take profit price
            deviation: Maximum price deviation
            magic: Magic number (identifier)
            comment: Order comment

        Returns:
            Dict with order result
        """
        return self.trading.place_market_order(
            symbol=symbol,
            order_type=order_type,
            volume=volume,
            sl=sl,
            tp=tp,
            deviation=deviation,
            magic=magic,
            comment=comment
        )

    def calculate_lot_size(self, symbol: str, risk_percent: float, sl_points: int) -> Dict[str, Any]:
        """
        Calculate lot size based on risk percentage and stop loss points.

        Args:
            symbol: Symbol name
            risk_percent: Risk percentage (0-100)
            sl_points: Stop loss in points

        Returns:
            Dict with calculated lot size
        """
        return self.trading.calculate_lot_size(symbol, risk_percent, sl_points)

    def close_position(self, ticket: int) -> Dict[str, Any]:
        """
        Close an open position.

        Args:
            ticket: Position ticket

        Returns:
            Dict with close result
        """
        return self.trading.close_position(ticket)

    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """
        Get symbol information.

        Args:
            symbol: Symbol name

        Returns:
            Dict with symbol information
        """
        try:
            # Check connection
            if not self.is_connected():
                return {
                    "success": False,
                    "message": "Not connected to MT5"
                }

            # Get symbol info
            symbol_info = self.symbols.get_symbol_info(symbol)
            if symbol_info is None:
                return {
                    "success": False,
                    "message": f"Symbol {symbol} not found"
                }

            return {
                "success": True,
                "symbol_info": symbol_info._asdict() if hasattr(symbol_info, '_asdict') else symbol_info
            }
        except Exception as e:
            logger.error(f"Error getting symbol info: {e}")
            return {
                "success": False,
                "message": f"Error getting symbol info: {str(e)}"
            }
