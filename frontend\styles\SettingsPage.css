.settings-page {
  padding: 20px;
}

.settings-section {
  background-color: var(--card);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
}

.section-title {
  font-size: 1.3rem;
  margin-bottom: 10px;
  font-weight: 600;
  color: var(--text-primary);
}

.section-description {
  color: var(--text-secondary);
  margin-bottom: 20px;
  font-size: 0.9rem;
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: var(--text-primary);
}

.input-with-button {
  display: flex;
  gap: 10px;
}

.input-with-button input {
  flex: 1;
}

.test-message {
  font-size: 0.9rem;
  padding: 8px;
  border-radius: 4px;
  margin-top: 5px;
}

.test-message.testing {
  background-color: var(--info-bg);
  color: var(--info-text);
}

.test-message.success {
  background-color: var(--success-bg);
  color: var(--success-text);
}

.test-message.error {
  background-color: var(--error-bg);
  color: var(--error-text);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.theme-selector {
  display: flex;
  gap: 15px;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 10px;
  border-radius: 6px;
  border: 2px solid transparent;
  background: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.theme-option.active {
  border-color: var(--primary);
  background-color: rgba(var(--primary-rgb), 0.1);
}

.theme-preview {
  width: 80px;
  height: 50px;
  border-radius: 4px;
  border: 1px solid var(--border);
  display: flex;
  flex-direction: column;
}

.theme-preview.dark {
  background-color: #0f172a;
  border: 1px solid #334155;
}

.theme-preview.dark::before {
  content: '';
  width: 100%;
  height: 15px;
  background-color: #3b82f6;
}

.theme-preview.dark::after {
  content: '';
  width: 80%;
  height: 10px;
  margin: 5px auto;
  background-color: #1e293b;
  border-radius: 2px;
}

.theme-preview.light {
  background-color: #f8fafc;
  border: 1px solid #cbd5e1;
}

.theme-preview.light::before {
  content: '';
  width: 100%;
  height: 15px;
  background-color: #2563eb;
}

.theme-preview.light::after {
  content: '';
  width: 80%;
  height: 10px;
  margin: 5px auto;
  background-color: #ffffff;
  border-radius: 2px;
  border: 1px solid #e5e7eb;
}
