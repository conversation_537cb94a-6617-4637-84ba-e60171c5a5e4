/**
 * Chart.js utility functions and configurations
 * Provides safe defaults and helpers for chart components
 */

import { safeToFixed } from './numberUtils';

/**
 * Creates safe tooltip callbacks that prevent errors when values are undefined
 * @returns {Object} Tooltip callback configuration
 */
export const createSafeTooltipCallbacks = () => {
  return {
    label: function(context) {
      let label = context.dataset.label || '';
      if (label) {
        label += ': ';
      }
      if (context.parsed.y !== null && context.parsed.y !== undefined) {
        label += safeToFixed(context.parsed.y, 5, 'N/A');
      } else {
        label += 'N/A';
      }
      return label;
    }
  };
};

/**
 * Creates safe axis tick formatters that prevent errors when values are undefined
 * @param {string} symbol - The trading symbol to determine decimal places
 * @returns {Function} Safe formatter function
 */
export const createSafeAxisFormatter = (symbol = '') => {
  return function(value) {
    if (value === undefined || value === null) {
      return 'N/A';
    }
    if (symbol.includes('JPY')) {
      return safeToFixed(value, 3, 'N/A'); // 3 decimals for JPY pairs
    } else if (symbol.includes('BTC')) {
      return safeToFixed(value, 0, 'N/A'); // No decimals for BTC
    } else if (symbol.includes('XAU')) {
      return safeToFixed(value, 2, 'N/A'); // 2 decimals for gold
    } else {
      return safeToFixed(value, 5, 'N/A'); // 5 decimals for most forex pairs
    }
  };
};

/**
 * Default safe chart options that prevent common errors
 * @param {string} symbol - The trading symbol
 * @returns {Object} Chart.js options object
 */
export const createSafeChartOptions = (symbol = '') => {
  return {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      tooltip: {
        callbacks: createSafeTooltipCallbacks()
      }
    },
    scales: {
      y: {
        ticks: {
          callback: createSafeAxisFormatter(symbol)
        }
      }
    }
  };
};
