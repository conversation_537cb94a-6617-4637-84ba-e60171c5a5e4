from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class VolumeOscillatorIndicator(BaseIndicator):
    """Volume Oscillator indicator."""

    def __init__(self, short_period: int = 5, long_period: int = 10, ma_type: str = 'sma'):
        """
        Initialize Volume Oscillator indicator.

        Args:
            short_period: The period for the short-term volume moving average.
            long_period: The period for the long-term volume moving average.
            ma_type: The type of moving average ('sma' or 'ema').
        """
        super().__init__({
            'short_period': short_period,
            'long_period': long_period,
            'ma_type': ma_type
        })

    def _ma(self, series: pd.Series, period: int, ma_type: str) -> pd.Series:
        """Helper function for MA calculation."""
        if ma_type == 'sma':
            return series.rolling(window=period).mean()
        else: # ema
            return series.ewm(span=period, adjust=False).mean()

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Volume Oscillator."""
        df = data.to_dataframe()
        min_len = max(self.params['short_period'], self.params['long_period'])
        if df.empty or 'volume' not in df.columns or len(df) < min_len:
             return {'volume_oscillator': np.array([])}

        short_period = self.params['short_period']
        long_period = self.params['long_period']
        ma_type = self.params['ma_type'].lower()
        volume = df['volume']

        # Calculate short and long term MAs of volume
        short_ma = self._ma(volume, short_period, ma_type)
        long_ma = self._ma(volume, long_period, ma_type)

        # Calculate Volume Oscillator = 100 * (Short MA - Long MA) / Long MA
        # Avoid division by zero
        long_ma_safe = long_ma.replace(0, np.nan)
        vo_values = 100 * (short_ma - long_ma) / long_ma_safe
        vo_values = vo_values.fillna(0) # Fill initial NaNs

        self._values = {
            'volume_oscillator': vo_values.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_ma_types = ['sma', 'ema']
        if self.params['ma_type'].lower() not in valid_ma_types:
            raise ValueError(f"MA type must be one of {valid_ma_types}")
        if self.params['short_period'] >= self.params['long_period']:
            raise ValueError("Short period must be less than long period")
        if self.params['short_period'] < 1:
            raise ValueError("Short period must be greater than 0")
        if self.params['long_period'] < 1:
            raise ValueError("Long period must be greater than 0")
        return True