# Autonomous Trading Risk Management System

## 📋 Table of Contents
- [Overview](#overview)
- [Risk Management Layers](#risk-management-layers)
- [Configuration Parameters](#configuration-parameters)
- [Risk Switchers](#risk-switchers)
- [Entry Spacing System](#entry-spacing-system)
- [Real-World Examples](#real-world-examples)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## 🎯 Overview

The Garuda Algo V2 autonomous trading system features a **7-layer risk management system** designed to protect your trading capital through multiple independent safety mechanisms. This system provides institutional-grade risk control with user-friendly interfaces.

### Key Features
- **Multi-layer protection**: 7 independent risk controls
- **Flexible risk modes**: Percentage or fixed amount options
- **Real-time calculations**: Live equivalent value displays
- **Currency awareness**: Automatic currency detection (IDR, USD, EUR, etc.)
- **Professional UI**: Intuitive switchers and controls

## 🛡️ Risk Management Layers

### Layer 1: Signal Quality Filter
**Parameter**: Minimum Signal Confidence (30% - 95%)
- **Purpose**: Only execute trades with sufficient confidence levels
- **How it works**: Filters out low-quality trading signals
- **Example**: 60% minimum = only trades with 60%+ confidence are executed
- **Risk benefit**: Reduces false signals and poor-quality trades

### Layer 2: Individual Trade Risk Control
**Parameter**: Risk Per Trade (Percentage or Fixed Amount)
- **Purpose**: Controls position size for each individual trade
- **Modes**:
  - Percentage Mode: 1% of account balance
  - Amount Mode: Fixed Rp100,000 per trade
- **How it works**: Calculates lot size so maximum loss = specified risk
- **Example**: 1% risk on Rp10M account = max Rp100,000 loss per trade

### Layer 3: Position Quantity Limit
**Parameter**: Max Open Trades (1-10)
- **Purpose**: Limits number of concurrent positions
- **How it works**: Prevents opening new trades when limit reached
- **Example**: Max 3 trades = maximum 3 positions open simultaneously
- **Risk benefit**: Prevents over-trading and excessive exposure

### Layer 4: Total Portfolio Risk Cap
**Parameter**: Max Total Risk (Percentage or Fixed Amount)
- **Purpose**: **MASTER SAFETY CONTROL** - prevents excessive total exposure
- **Modes**:
  - Percentage Mode: 5% of account balance
  - Amount Mode: Fixed Rp500,000 total risk
- **How it works**: Sums potential loss from ALL open positions
- **Critical function**: Stops new trades if total risk exceeds limit

### Layer 5: Price Spacing Protection
**Parameter**: ATR-Based Entry Spacing
- **Purpose**: Prevents trades too close together in price
- **How it works**: Uses Average True Range (ATR) to calculate minimum distance
- **Risk benefit**: Reduces correlation risk between similar price levels
- **Adaptive**: Automatically adjusts to market volatility

### Layer 6: Time Spacing Protection
**Parameter**: Time-Based Entry Spacing
- **Purpose**: Enforces minimum time between trade entries
- **How it works**: Cooldown periods between trades
- **Adaptive cooldown**: Increases wait time after consecutive losses
- **Risk benefit**: Prevents rapid-fire trading during volatile periods

### Layer 7: Loss Streak Protection
**Parameter**: Anti-Martingale System
- **Purpose**: Automatically reduces risk during losing streaks
- **How it works**: Decreases position size after consecutive losses
- **Volume multipliers**: [1.0, 0.9, 0.8, 0.7, 0.6, 0.5]
- **Risk benefit**: Protects capital when strategy is underperforming

## ⚙️ Configuration Parameters

### Basic Trading Parameters
| Parameter | Range | Default | Description |
|-----------|-------|---------|-------------|
| Strategy | Selection | - | Choose trading strategy |
| Symbol | Selection | EURUSD | Trading instrument |
| Timeframe | M1-MN1 | H1 | Chart timeframe for analysis |

### Risk Control Parameters
| Parameter | Range | Default | Description |
|-----------|-------|---------|-------------|
| Risk Per Trade | 0.1%-10% or Fixed Amount | 1% | Individual trade risk |
| Max Open Trades | 1-10 | 3 | Maximum concurrent positions |
| Min Signal Confidence | 30%-95% | 60% | Minimum signal quality threshold |
| Max Total Risk | 1%-20% or Fixed Amount | 5% | Total portfolio risk limit |

### Advanced Risk Parameters
| Parameter | Range | Default | Description |
|-----------|-------|---------|-------------|
| Breakeven Trigger | 0.1%-5% or Pips | 0.5% | Move SL to breakeven threshold |
| Trailing Stop Trigger | 0.1%-5% or Pips | 1.0% | Start trailing stop threshold |
| Trailing Stop Distance | 0.1%-5% or Pips | 0.5% | Trailing stop distance |
| Max Profit Target | 0+ | 100 | Stop bot after profit target |
| Max Loss Limit | 0+ | 50 | Stop bot after loss limit |

## 🔄 Risk Switchers

### Risk Per Trade Switcher
**Purpose**: Choose between percentage-based or fixed-amount risk per trade

**Percentage Mode**:
- Input: `1%`
- Calculation: 1% of account balance
- Example: 1% of Rp10,000,000 = Rp100,000 risk per trade
- Display: `≈ Rp100,000`

**Amount Mode**:
- Input: `Rp50,000`
- Calculation: Fixed amount regardless of balance
- Example: Always risk exactly Rp50,000 per trade
- Display: `≈ 0.50% of balance`

### Max Total Risk Switcher
**Purpose**: Choose between percentage-based or fixed-amount total portfolio risk

**Percentage Mode**:
- Input: `5%`
- Calculation: 5% of account balance
- Example: 5% of Rp10,000,000 = Rp500,000 total risk limit
- Display: `≈ Rp500,000`

**Amount Mode**:
- Input: `Rp300,000`
- Calculation: Fixed total risk amount
- Example: Never exceed Rp300,000 total exposure
- Display: `≈ 3.00% of balance`

### When to Use Each Mode

**Use Percentage Mode When**:
- Account balance changes frequently
- Want consistent risk relative to account size
- Prefer traditional risk management (1%, 2%, etc.)
- Account size is small to medium

**Use Amount Mode When**:
- Want fixed, predictable risk amounts
- Account balance is large and stable
- Prefer absolute dollar/currency control
- Using specific money management strategies

## 📏 Entry Spacing System

### ATR-Based Spacing
- **Purpose**: Prevent trades at similar price levels
- **Calculation**: Uses Average True Range for market-adaptive spacing
- **Benefits**: Reduces correlation between trades
- **Configuration**: ATR period (14), multiplier (2.0), minimum pips (15)

### Time-Based Spacing
- **Purpose**: Enforce minimum time between entries
- **Base cooldown**: 5 minutes between trades
- **Loss multiplier**: Increases cooldown after losses
- **Maximum cooldown**: 1 hour maximum wait time
- **Benefits**: Prevents overtrading during volatile periods

### Anti-Martingale Settings
- **Spacing multipliers**: [1.0, 1.5, 2.0, 2.5, 3.0, 4.0]
- **Volume multipliers**: [1.0, 0.9, 0.8, 0.7, 0.6, 0.5]
- **Reset on win**: Resets multipliers after profitable trade
- **Track by symbol**: Independent tracking per trading pair

## 💡 Real-World Examples

### Example 1: Conservative IDR Account
**Account**: Rp10,000,000 IDR
**Configuration**:
- Risk Per Trade: 0.5% (Rp50,000)
- Max Open Trades: 2
- Max Total Risk: 2% (Rp200,000)
- Min Confidence: 70%

**Result**: Very safe, high-quality trades only

### Example 2: Moderate USD Account
**Account**: $10,000 USD
**Configuration**:
- Risk Per Trade: 1% ($100)
- Max Open Trades: 3
- Max Total Risk: 5% ($500)
- Min Confidence: 60%

**Result**: Balanced risk/reward approach

### Example 3: Aggressive Fixed Amount
**Account**: Rp50,000,000 IDR
**Configuration**:
- Risk Per Trade: Fixed Rp200,000 (0.4%)
- Max Open Trades: 5
- Max Total Risk: Fixed Rp800,000 (1.6%)
- Min Confidence: 50%

**Result**: More trades, fixed risk amounts

## 📚 Best Practices

### Risk Management
1. **Start Conservative**: Begin with lower risk percentages
2. **Test Thoroughly**: Use demo accounts before live trading
3. **Monitor Performance**: Regular review of risk metrics
4. **Adjust Gradually**: Make small incremental changes
5. **Respect Limits**: Never override safety mechanisms

### Configuration Tips
1. **Match Risk Tolerance**: Align settings with your comfort level
2. **Consider Market Conditions**: Adjust for volatility
3. **Account for Correlation**: Use entry spacing effectively
4. **Plan for Drawdowns**: Set appropriate loss limits
5. **Regular Reviews**: Update settings based on performance

### Common Mistakes to Avoid
1. **Over-leveraging**: Setting risk too high
2. **Ignoring Correlation**: Too many similar trades
3. **Frequent Changes**: Constantly adjusting parameters
4. **Emotion-Based Decisions**: Overriding system rules
5. **Inadequate Testing**: Insufficient backtesting

## 🔧 Troubleshooting

### Common Issues

**Issue**: No trades being executed
**Causes**:
- Min confidence too high
- Entry spacing too restrictive
- Max total risk reached
**Solutions**: Lower confidence threshold, adjust spacing, check risk exposure

**Issue**: Too many losing trades
**Causes**:
- Min confidence too low
- Poor market conditions
- Strategy not suitable for current market
**Solutions**: Increase confidence threshold, review strategy performance

**Issue**: Risk calculations seem wrong
**Causes**:
- Account balance not updated
- Currency conversion issues
- Incorrect lot size calculations
**Solutions**: Refresh account data, check MT5 connection, verify symbol specifications

### Support and Updates
- Check system logs for detailed error messages
- Monitor the autonomous trading dashboard
- Review entry spacing statistics
- Verify MT5 connection status
- Contact support for persistent issues

## 🎓 Tutorial Scenarios

### Scenario 1: New Trader Setup
**Goal**: Safe introduction to autonomous trading
**Steps**:
1. Set Risk Per Trade to 0.5% (Percentage Mode)
2. Set Max Open Trades to 1
3. Set Max Total Risk to 1% (Percentage Mode)
4. Set Min Confidence to 80%
5. Enable all entry spacing protections
6. Start with major pairs (EURUSD, GBPUSD)

### Scenario 2: Experienced Trader
**Goal**: Balanced risk/reward optimization
**Steps**:
1. Set Risk Per Trade to 1-2% (Percentage Mode)
2. Set Max Open Trades to 3-5
3. Set Max Total Risk to 5-8% (Percentage Mode)
4. Set Min Confidence to 60-70%
5. Fine-tune entry spacing based on strategy
6. Monitor and adjust based on performance

### Scenario 3: Large Account Management
**Goal**: Fixed amount risk control
**Steps**:
1. Set Risk Per Trade to Fixed Amount (e.g., $500)
2. Set Max Open Trades to 5-10
3. Set Max Total Risk to Fixed Amount (e.g., $2000)
4. Set Min Confidence to 50-60%
5. Use amount mode for predictable risk
6. Scale amounts based on account growth

## 📊 Risk Calculation Formulas

### Position Size Calculation
```
Percentage Mode:
Position Size = (Account Balance × Risk %) / (Entry Price - Stop Loss Price)

Amount Mode:
Position Size = Risk Amount / (Entry Price - Stop Loss Price)
```

### Total Risk Calculation
```
Total Risk = Σ(Position Size × |Entry Price - Stop Loss Price|)
```

### Equivalent Value Calculations
```
Amount from Percentage:
Amount = (Percentage / 100) × Account Balance

Percentage from Amount:
Percentage = (Amount / Account Balance) × 100
```

## 🌍 Multi-Currency Support

### Supported Currencies
- **Major**: USD ($), EUR (€), GBP (£), JPY (¥), CHF, CAD (C$), AUD (A$), NZD (NZ$)
- **ASEAN**: IDR (Rp), MYR (RM), PHP (₱), VND (₫), THB (฿), SGD (S$)
- **Others**: CNY (¥), INR (₹), BRL (R$), ZAR (R), KRW (₩), TRY (₺)

### Currency Detection
- Automatic detection from MT5 account
- Real-time currency symbol display
- Proper formatting for each currency
- Support for different decimal places

## 🔍 Monitoring and Analytics

### Key Metrics to Watch
1. **Current Risk Exposure**: Real-time total risk percentage
2. **Win Rate**: Percentage of profitable trades
3. **Average Risk per Trade**: Actual vs. configured risk
4. **Drawdown**: Maximum consecutive loss period
5. **Risk-Adjusted Returns**: Profit relative to risk taken

### Dashboard Indicators
- **Green**: System operating normally
- **Yellow**: Approaching risk limits
- **Red**: Risk limits reached or system issues
- **Blue**: Information and statistics

### Performance Analysis
- Daily/weekly/monthly P&L tracking
- Risk exposure over time
- Entry spacing effectiveness
- Signal confidence correlation with success

## 🚨 Emergency Procedures

### Immediate Actions
1. **Stop All Trading**: Use emergency stop button
2. **Close Positions**: Manual position closure if needed
3. **Check Logs**: Review system logs for errors
4. **Verify Connection**: Ensure MT5 connectivity
5. **Contact Support**: If issues persist

### Risk Limit Breaches
- System automatically stops new trades
- Existing positions remain active
- Manual intervention may be required
- Review and adjust risk parameters

### System Recovery
1. Identify root cause of issues
2. Adjust risk parameters if needed
3. Test with minimal risk settings
4. Gradually return to normal operation
5. Monitor closely after restart

---

**Last Updated**: December 2024
**Version**: Garuda Algo V2
**Author**: Garuda Trading System Team

**Disclaimer**: Trading involves substantial risk of loss. Past performance does not guarantee future results. Always trade with money you can afford to lose.
