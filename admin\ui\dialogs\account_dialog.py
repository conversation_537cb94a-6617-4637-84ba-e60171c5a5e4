"""
Dialog for adding or editing authorized accounts in Firebase.
"""
from typing import Optional, Dict # Import required types
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QFormLayout, QLineEdit,
                             QComboBox, QSpinBox, QDialogButtonBox, QMessageBox,
                             QDateEdit, QHBoxLayout, QLabel, QFileDialog) # Re-add missing imports
from PyQt5.QtCore import QDate, QDateTime, Qt
from datetime import datetime, timedelta, timezone

class AccountDialog(QDialog):
    """Dialog to input/edit authorized account details."""
    def __init__(self, account_data=None, parent=None):
        """
        Initialize the dialog.
        Args:
            account_data (dict, optional): Existing data to pre-fill for editing. Defaults to None (for adding).
            parent (QWidget, optional): Parent widget.
        """
        super().__init__(parent)
        self.account_data = account_data or {} # Store existing data if editing
        self._init_ui()
        self._populate_data()

    def _init_ui(self):
        self.setWindowTitle("Add/Edit Authorized Account")
        layout = QVBoxLayout(self)
        form_layout = QFormLayout()

        self.account_number_input = QLineEdit()
        self.server_name_input = QLineEdit()
        self.user_name_input = QLineEdit()
        self.status_combo = QComboBox()
        self.status_combo.addItems(["active", "inactive", "expired"])
        self.plan_type_input = QLineEdit()
        self.plan_duration_spin = QSpinBox() # For adding/extending duration
        self.plan_duration_spin.setRange(1, 3650) # 1 day to 10 years
        self.plan_duration_spin.setValue(30) # Default duration
        self.plan_duration_spin.setSuffix(" days")
        self.plan_end_date_display = QLineEdit() # Display only for existing end date
        self.plan_end_date_display.setReadOnly(True)

        form_layout.addRow("Account Number:", self.account_number_input)
        form_layout.addRow("Server Name:", self.server_name_input)
        form_layout.addRow("User Name:", self.user_name_input)
        form_layout.addRow("Status:", self.status_combo)
        form_layout.addRow("Plan Type:", self.plan_type_input)
        # Show duration input for adding/editing, end date display for editing
        if self.account_data: # Editing mode
             form_layout.addRow("Extend Plan By:", self.plan_duration_spin)
             form_layout.addRow("Current Plan End:", self.plan_end_date_display)
        else: # Adding mode
             form_layout.addRow("Plan Duration:", self.plan_duration_spin)


        layout.addLayout(form_layout)

        # Dialog Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def _populate_data(self):
        """Fill fields if editing existing data."""
        if not self.account_data:
            return # Nothing to populate for adding

        self.account_number_input.setText(self.account_data.get('account_number', ''))
        self.server_name_input.setText(self.account_data.get('server_name', ''))
        self.user_name_input.setText(self.account_data.get('user_name', ''))
        self.status_combo.setCurrentText(self.account_data.get('status', 'inactive'))
        self.plan_type_input.setText(self.account_data.get('plan_type', ''))

        # Display existing end date
        end_date_str = self.account_data.get('plan_end_date_str', 'N/A')
        self.plan_end_date_display.setText(end_date_str)

        # Disable editing account number/server when editing? Optional.
        # self.account_number_input.setReadOnly(True)
        # self.server_name_input.setReadOnly(True)

        # Set duration spinbox title differently for editing
        form_layout = self.layout().itemAt(0).layout() # Get the form layout
        label_widget = form_layout.labelForField(self.plan_duration_spin)
        if label_widget:
            label_widget.setText("Extend Plan By:")


    def get_data(self) -> Optional[Dict]:
        """Get the entered/edited data from the dialog fields."""
        account_number = self.account_number_input.text().strip()
        server_name = self.server_name_input.text().strip()
        user_name = self.user_name_input.text().strip()
        status = self.status_combo.currentText()
        plan_type = self.plan_type_input.text().strip()
        plan_duration_days = self.plan_duration_spin.value()

        if not account_number or not server_name:
            QMessageBox.warning(self, "Input Error", "Account Number and Server Name are required.")
            return None

        data = {
            'account_number': account_number,
            'server_name': server_name,
            'user_name': user_name,
            'status': status,
            'plan_type': plan_type,
            'plan_duration_days': plan_duration_days # Pass duration for add/update logic
        }
        return data
