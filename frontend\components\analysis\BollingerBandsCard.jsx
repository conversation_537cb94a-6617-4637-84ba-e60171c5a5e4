import React from 'react';
import TrendArrow from './TrendArrow';
import { safeToFixed, renderIndicatorValue } from '../../utils/analysisUtils';
import '../../styles/BentoComponents.css';
import '../../styles/AnalysisBento.css';

/**
 * BollingerBandsCard component displays Bollinger Bands analysis
 *
 * @param {Object} bollingerBands - The Bollinger Bands data
 * @returns {JSX.Element} - The rendered Bollinger Bands card
 */
const BollingerBandsCard = ({ bollingerBands }) => {
  const direction = bollingerBands?.price_position === 'Above Upper'
    ? 'SELL'
    : bollingerBands?.price_position === 'Below Lower'
      ? 'BUY'
      : 'NEUTRAL';

  return (
    <div className="bento-card bento-span-4">
      <div className="bento-card-header">
        <h3 className="bento-card-title">Bollinger Bands</h3>
        <div className="signal-with-arrow">
          {renderIndicatorValue(direction, 'signal')}
          <TrendArrow direction={direction} />
        </div>
      </div>
      <div className="bento-card-content analysis-indicator-details">
        <p><span>Upper:</span> <span>{safeToFixed(bollingerBands?.upper, 5)}</span></p>
        <p><span>Middle:</span> <span>{safeToFixed(bollingerBands?.middle, 5)}</span></p>
        <p><span>Lower:</span> <span>{safeToFixed(bollingerBands?.lower, 5)}</span></p>
      </div>
    </div>
  );
};

export default BollingerBandsCard;
