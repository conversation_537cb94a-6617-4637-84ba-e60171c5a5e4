import pandas as pd
import logging
from typing import Dict, Any
from backend.technical.indicators.momentum.rsi import RSIIndicator
from backend.technical.indicators.trend.macd import MACDIndicator

logger = logging.getLogger("AnalysisEngine")

def extract_rsi(data: pd.DataFrame) -> Dict[str, Any]:
    """
    Extract and analyze RSI values.
    
    Args:
        data: DataFrame containing price data
        
    Returns:
        Dictionary with RSI analysis results
    """
    try:
        if data.empty or len(data) < 15:  # Check based on custom indicator needs
            return {"error": "Insufficient data for RSI analysis"}

        rsi_indicator = RSIIndicator(period=14, source='close')  # Use default period=14
        rsi_result = rsi_indicator.calculate(data)  # Pass DataFrame

        if 'rsi' not in rsi_result or len(rsi_result['rsi']) == 0:
            return {"error": "Custom RSI calculation failed or returned empty"}

        # Get the full RSI array
        rsi_values = rsi_result['rsi']
        
        # Get the latest RSI value
        latest_rsi = rsi_values[-1]
        if pd.isna(latest_rsi):
            return {"error": "Latest custom RSI is NaN"}

        # Determine level based on the calculated RSI
        rsi_level = "BUY"  # Changed to align with frontend expectations
        if latest_rsi < 30:
            rsi_level = "STRONG_BUY"
        elif latest_rsi > 70:
            rsi_level = "SELL"
        elif latest_rsi > 60:
            rsi_level = "WEAK_SELL"
        elif latest_rsi < 40:
            rsi_level = "WEAK_BUY"

        # Get divergence from the indicator
        divergence = rsi_result.get('divergence', 'None')
        # Convert divergence signal to trading signal
        if divergence == "Regular Bullish":
            divergence_signal = "BUY"
        elif divergence == "Regular Bearish":
            divergence_signal = "SELL"
        elif divergence == "Hidden Bullish":
            divergence_signal = "WEAK_BUY"
        elif divergence == "Hidden Bearish":
            divergence_signal = "WEAK_SELL"
        else:
            divergence_signal = "NEUTRAL"

        return {
            "current": latest_rsi,
            "value": rsi_values.tolist(),  # Include full RSI array
            "level": rsi_level,
            "overbought": 70,  # Standard RSI overbought level
            "oversold": 30,    # Standard RSI oversold level
            "divergence": divergence,
            "divergence_signal": divergence_signal
        }
    except Exception as e:
        logger.exception(f"Exception extracting custom RSI: {str(e)}")
        return {"error": f"Exception extracting custom RSI: {str(e)}"}

def extract_macd(data: pd.DataFrame) -> Dict[str, Any]:
    """
    Extract and analyze MACD values.
    
    Args:
        data: DataFrame containing price data
        
    Returns:
        Dictionary with MACD analysis results
    """
    try:
        if data.empty or len(data) < 27:  # Check based on custom indicator needs
            return {"error": "Insufficient data for MACD analysis"}

        macd_indicator = MACDIndicator()  # Use default periods (12, 26, 9)
        macd_result = macd_indicator.calculate(data)

        required_keys = ['macd', 'signal', 'histogram', 'signal_cross']
        if not all(key in macd_result for key in required_keys) or \
           any(len(macd_result[key]) == 0 for key in required_keys):
            return {"error": "Custom MACD calculation failed or returned empty/incomplete"}

        # Get the full arrays
        macd_values = macd_result['macd']
        signal_values = macd_result['signal']
        histogram_values = macd_result['histogram']
        
        # Get latest values
        latest_macd = macd_values[-1]
        latest_signal = signal_values[-1]
        latest_hist = histogram_values[-1]
        latest_cross_raw = macd_result['signal_cross'][-1]  # Get raw cross value (1, -1, 0)

        if any(pd.isna(v) for v in [latest_macd, latest_signal, latest_hist, latest_cross_raw]):
            return {"error": "Latest custom MACD values contain NaN"}

        # Convert raw cross value to string
        macd_cross = "Bullish" if latest_cross_raw == 1 else \
                    ("Bearish" if latest_cross_raw == -1 else "None")

        # Determine histogram status
        hist_status = "Positive" if latest_hist > 0 else "Negative"

        # Histogram reversal (would need previous hist value)
        hist_reversal = "N/A"

        return {
            "current": latest_macd,
            "signal_current": latest_signal,
            "histogram_current": latest_hist,
            "macd": macd_values.tolist(),      # Include full MACD array
            "signal": signal_values.tolist(),  # Include full signal array 
            "histogram": histogram_values.tolist(),  # Include full histogram array
            "cross": macd_cross,
            "histogram_status": hist_status,
            "histogram_reversal": hist_reversal,
            "fast_period": 12,  # Default values used by the indicator
            "slow_period": 26,
            "signal_period": 9
        }
    except Exception as e:
        logger.exception(f"Exception extracting custom MACD: {str(e)}")
        return {"error": f"Exception extracting custom MACD: {str(e)}"}
