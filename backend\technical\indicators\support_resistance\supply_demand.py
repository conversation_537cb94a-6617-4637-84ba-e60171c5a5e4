import pandas as pd
from typing import Dict, List, Tuple
from backend.technical.base_indicator import BaseIndicator
import logging

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

logger = logging.getLogger(__name__)
# Force DEBUG level and add a handler if none exists for this logger
if not logger.handlers:
    handler = logging.StreamHandler() # Log to console
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
logger.setLevel(logging.DEBUG)
logger.propagate = False # Prevent messages from propagating to the root logger if it has restrictive handlers


class SupplyDemandIndicator(BaseIndicator):
    """
    Identifies potential Supply and Demand zones based on price action patterns.
     Looks for Drop-Base-Rally (Demand) and Rally-Base-Drop (Supply) patterns.
     """

    def __init__(self, atr_multiplier: float = 0.5, lookback: int = 100, base_max_candles: int = 6,
                 leg_strength_factor: float = 0.6, base_body_ratio: float = 0.5, min_zone_distance_atr: float = 1.5,
                 volume_confirm_threshold: float = 1.2, max_age_candles: int = 10000, atr_period: int = 14,
                 score_threshold: float = 2.0): # Added atr_period and score_threshold
        """
        Initialize Supply/Demand Zone indicator.

        Args:
            atr_multiplier: Multiplier for ATR (for zone width adjustment).
            lookback: How many candles back to look for zone formation.
            base_max_candles: Maximum number of candles allowed in a 'base' formation (1-6 recommended).
            leg_strength_factor: How much stronger the 'leg' candle's body should be compared to ATR.
            base_body_ratio: Maximum body/range ratio for a candle to be considered part of a base.
            min_zone_distance_atr: Minimum distance between zones in ATR units.
            volume_confirm_threshold: Min volume increase for leg moves vs base average.
            max_age_candles: Maximum age of zones to keep (older ones are removed).
        """
        super().__init__({
            'atr_multiplier': atr_multiplier,
            'lookback': lookback,
            'base_max_candles': base_max_candles,
            'leg_strength_factor': leg_strength_factor,
            'base_body_ratio': base_body_ratio,
            'min_zone_distance_atr': min_zone_distance_atr,
            'volume_confirm_threshold': volume_confirm_threshold,
            'max_age_candles': max_age_candles,
            'atr_period': atr_period,
            'score_threshold': score_threshold
        })
        self._required_columns = ['open', 'high', 'low', 'close', 'atr'] # Volume is optional
        self._optional_columns = ['volume']

    def _calculate_strength_metrics(self, leg_in_candle: pd.Series, leg_out_candle: pd.Series,
                                  base_candles: pd.DataFrame, avg_atr: float) -> Dict[str, float]:
        """Calculate various strength metrics for the zone."""
        # Base formation quality (0-1)
        base_ranges = base_candles['high'] - base_candles['low']
        base_bodies = abs(base_candles['close'] - base_candles['open'])
        base_quality = 1 - (base_bodies.mean() / base_ranges.mean()) # Higher quality = smaller bodies

        # Leg moves strength (0-1)
        leg_in_range = abs(leg_in_candle['close'] - leg_in_candle['open']) / avg_atr
        leg_out_range = abs(leg_out_candle['close'] - leg_out_candle['open']) / avg_atr
        leg_strength = min(1.0, (leg_in_range + leg_out_range) / 4) # Normalize to 0-1

        # Volume confirmation (0-1)
        if 'volume' in base_candles.columns:
            base_vol_avg = base_candles['volume'].mean()
            vol_increase = max(
                leg_in_candle['volume'] / base_vol_avg if base_vol_avg > 0 else 0,
                leg_out_candle['volume'] / base_vol_avg if base_vol_avg > 0 else 0
            )
            volume_score = min(1.0, vol_increase / self.params['volume_confirm_threshold'])
        else:
            volume_score = 0.5 # Neutral if no volume data

        # Overall strength (weighted average)
        overall = (base_quality * 0.4 + leg_strength * 0.4 + volume_score * 0.2)

        logger.debug(f"Strength Metrics - BaseQ: {base_quality:.2f}, LegS: {leg_strength:.2f}, VolS: {volume_score:.2f}, Overall: {overall:.2f}")
        return {
            "base_quality": base_quality,
            "leg_strength": leg_strength,
            "volume_score": volume_score,
            "overall": overall
        }

    def _is_strong_candle(self, candle: pd.Series, avg_atr: float, strength_factor: float) -> int:
        """ Check if a candle represents a strong move relative to ATR. Returns 1 for bullish, -1 for bearish, 0 otherwise. """
        if pd.isna(avg_atr) or avg_atr <= 0: return 0
        body = abs(candle['close'] - candle['open'])
        threshold = avg_atr * strength_factor
        is_strong = body >= threshold
        logger.debug(f"Strong check: Body={body:.5f}, Threshold={threshold:.5f}, IsStrong={is_strong}")
        if is_strong:
            return 1 if candle['close'] > candle['open'] else -1
        return 0

    def _is_base_candle(self, candle: pd.Series, body_ratio_threshold: float) -> bool:
        """ Check if a candle has a small body relative to its range. """
        candle_range = candle['high'] - candle['low']
        if pd.isna(candle_range) or candle_range <= 0: return True # Treat zero-range candles as base
        body = abs(candle['close'] - candle['open'])
        body_ratio = body / candle_range
        is_base = body_ratio <= body_ratio_threshold
        logger.debug(f"Base check: Body={body:.5f}, Range={candle_range:.5f}, Ratio={body_ratio:.2f}, Threshold={body_ratio_threshold:.2f}, IsBase={is_base}")
        return is_base

    def _get_base_range(self, df_slice: pd.DataFrame) -> Tuple[float, float]:
        """ Get the high and low of the base candles """
        return df_slice['high'].max(), df_slice['low'].min()

    def calculate(self, data: pd.DataFrame) -> Dict[str, List[Dict[str, float]]]:
        """
        Calculate Supply and Demand zones.
        """
        logger.debug("--- Starting SupplyDemandIndicator calculate method ---") # Early debug message
        if not all(col in data.columns for col in self._required_columns):
            missing = [col for col in self._required_columns if col not in data.columns]
            logger.warning(f"SupplyDemandIndicator missing required columns: {missing}. ATR must be pre-calculated.")
            return {'supply_zones': [], 'demand_zones': []}

        df = data.copy().iloc[-self.params['lookback']:]
        if len(df) < self.params['base_max_candles'] + 2:
             logger.debug(f"Not enough data for S/D calc: {len(df)} < {self.params['base_max_candles'] + 2}")
             return {'supply_zones': [], 'demand_zones': []}

        supply_zones = []
        demand_zones = []
        base_max = self.params['base_max_candles']
        leg_strength = self.params['leg_strength_factor']
        base_body_ratio = self.params['base_body_ratio']
        avg_atr = df['atr'].mean()
        logger.debug(f"SupplyDemand - Avg ATR: {avg_atr:.5f}, Lookback: {len(df)}, LegStrengthFactor: {leg_strength}, BaseBodyRatio: {base_body_ratio}")
        if pd.isna(avg_atr) or avg_atr <= 0:
            logger.warning("SupplyDemand - Average ATR is zero or NaN, cannot reliably detect zones.")
            return {'supply_zones': [], 'demand_zones': []}

        # Iterate backwards, checking for LegOut -> Base -> LegIn
        for i in range(len(df) - 2, base_max -1 , -1):
            leg_out_candle = df.iloc[i+1]
            leg_out_strength = self._is_strong_candle(leg_out_candle, avg_atr, leg_strength)
            logger.debug(f"Checking index i={i} (candle time {df.index[i]}): LegOut candle index {i+1}, Strength={leg_out_strength}")

            if leg_out_strength == 0:
                continue

            # Look for base candles preceding the leg-out
            for base_len in range(1, base_max + 1):
                base_start_index = i - base_len + 1
                if base_start_index -1 < 0:
                    continue

                base_candles = df.iloc[base_start_index : i + 1]
                leg_in_candle = df.iloc[base_start_index - 1]

                is_valid_base = all(self._is_base_candle(base_candles.iloc[j], base_body_ratio) for j in range(len(base_candles)))
                logger.debug(f"  BaseLen={base_len}: StartIndex={base_start_index}, IsValidBase={is_valid_base}")

                if not is_valid_base:
                    continue

                leg_in_strength = self._is_strong_candle(leg_in_candle, avg_atr, leg_strength)
                logger.debug(f"  BaseLen={base_len}: LegIn candle index {base_start_index - 1}, Strength={leg_in_strength}")

                if leg_in_strength == 0:
                    continue

                # --- Pattern Found ---
                base_high, base_low = self._get_base_range(base_candles)

                # Rally-Base-Drop (Supply Zone)
                if leg_in_strength == 1 and leg_out_strength == -1:
                    logger.info(f"+++ Potential Supply Zone found: Base ending at index {i} (len {base_len}) +++")
                    # Add distal and proximal properties for frontend compatibility
                    supply_zones.append({
                        'top': base_high,
                        'bottom': base_low,
                        'distal': base_high,  # Top of zone is distal line for supply
                        'proximal': base_low,  # Bottom of zone is proximal line for supply
                        'index': df.index[i],
                        'pos': i,
                        'base_len': base_len
                    })
                    break

                # Drop-Base-Rally (Demand Zone)
                if leg_in_strength == -1 and leg_out_strength == 1:
                    logger.info(f"+++ Potential Demand Zone found: Base ending at index {i} (len {base_len}) +++")
                    # Add distal and proximal properties for frontend compatibility
                    demand_zones.append({
                        'top': base_high,
                        'bottom': base_low,
                        'distal': base_low,  # Bottom of zone is distal line for demand
                        'proximal': base_high,  # Top of zone is proximal line for demand
                        'index': df.index[i],
                        'pos': i,
                        'base_len': base_len
                    })
                    break

            # if found_zone_for_i: i -= base_len # Optional: Skip candles already part of a found base/leg

        # First pass: filter by recency
        filtered_supply = list({z['index']: z for z in sorted(supply_zones, key=lambda x: x['index'])}.values())
        filtered_demand = list({z['index']: z for z in sorted(demand_zones, key=lambda x: x['index'])}.values())

        # Add strength metrics to zones
        for zone in filtered_supply + filtered_demand:
            try:
                zone_pos = zone['pos']
                base_len = zone['base_len']
                base_start_pos = zone_pos - base_len + 1
                leg_in_pos = base_start_pos - 1
                leg_out_pos = zone_pos + 1

                # Check bounds
                if leg_in_pos < 0 or leg_out_pos >= len(df):
                    logger.warning(f"Skipping strength calc for zone at pos {zone_pos}: leg candles out of bounds.")
                    zone['strength'] = None # Mark as unable to calculate
                    continue

                leg_in_candle = df.iloc[leg_in_pos]
                leg_out_candle = df.iloc[leg_out_pos]
                base_candles = df.iloc[base_start_pos : zone_pos + 1]

                zone['strength'] = self._calculate_strength_metrics(
                    leg_in_candle=leg_in_candle,
                    leg_out_candle=leg_out_candle,
                    base_candles=base_candles,
                    avg_atr=avg_atr
                )
                zone['time'] = zone['index'] # Store time for age filtering
            except Exception as e:
                 logger.error(f"Error calculating strength for zone at pos {zone.get('pos', 'N/A')}: {e}")
                 zone['strength'] = None # Mark as failed

        # Remove zones where strength calculation failed
        filtered_supply = [z for z in filtered_supply if z.get('strength') is not None]
        filtered_demand = [z for z in filtered_demand if z.get('strength') is not None]
        logger.debug(f"Zones after strength calc (Supply): {filtered_supply}")
        logger.debug(f"Zones after strength calc (Demand): {filtered_demand}")

        # Calculate minimum zone separation distance and filter
        min_zone_distance = avg_atr * self.params['min_zone_distance_atr']
        current_time = df.index[-1]

        # Final filtering: age, distance, and merging
        final_supply_zones = []
        final_demand_zones = []

        for zone_list in [filtered_supply, filtered_demand]:
            processed_zones = []
            # Sort zones by strength
            sorted_zones = sorted(zone_list, key=lambda x: x.get('strength', {}).get('overall', 0), reverse=True)

            for zone in sorted_zones:
                # Check and score zone age
                age_minutes = (current_time - zone['time']).total_seconds() / 60
                logger.debug(f"Zone age check: {age_minutes:.0f} minutes old (max {self.params['max_age_candles']})")
                if age_minutes > self.params['max_age_candles']:
                    logger.debug(f"Filtering out zone - too old: {zone}")
                    continue

                # Add freshness boost to strength (newer zones get higher boost)
                age_factor = max(0, 1 - (age_minutes / self.params['max_age_candles']))
                zone['strength']['freshness'] = age_factor
                zone['strength']['overall'] = (zone['strength']['overall'] * 0.8) + (age_factor * 0.2)
                logger.debug(f"Age factor: {age_factor:.2f}, New overall strength: {zone['strength']['overall']:.2f}")

                # Check if too close to existing zones
                too_close = False
                for existing in processed_zones:
                    if abs(zone['top'] - existing['top']) < min_zone_distance and \
                       abs(zone['bottom'] - existing['bottom']) < min_zone_distance:
                        # Merge zones if they overlap, keeping the stronger one's properties
                        if zone.get('strength', {}).get('overall', 0) > existing.get('strength', {}).get('overall', 0):
                            existing.update(zone)
                        too_close = True
                        break

                if not too_close:
                    processed_zones.append(zone)

            # Add to appropriate list (and remove temporary keys)
            if zone_list is filtered_supply:
                final_supply_zones = [{k: v for k, v in z.items() if k not in ['index', 'pos', 'base_len']} for z in processed_zones]
            else:
                final_demand_zones = [{k: v for k, v in z.items() if k not in ['index', 'pos', 'base_len']} for z in processed_zones]

        logger.info(f"Found {len(final_supply_zones)} supply zones and {len(final_demand_zones)} demand zones after final filtering.")
        logger.debug(f"Final Supply Zones: {final_supply_zones}")
        logger.debug(f"Final Demand Zones: {final_demand_zones}")

        # Add strength_score to each zone for frontend compatibility
        for zone in final_supply_zones + final_demand_zones:
            if 'strength' in zone and 'overall' in zone['strength']:
                zone['strength_score'] = zone['strength']['overall'] * self.params['score_threshold']
            else:
                zone['strength_score'] = 1.0

        # If no supply zones were found, generate some synthetic ones based on price
        if not final_supply_zones and len(df) > 0:
            logger.info("No supply zones found, generating synthetic ones")
            current_price = df['close'].iloc[-1]
            price_magnitude = len(str(int(current_price)))

            # Determine appropriate step size based on price magnitude
            if price_magnitude >= 5:  # Large numbers like 84685
                step_size = 1000
            elif price_magnitude >= 3:  # Medium numbers
                step_size = 10
            else:  # Small numbers
                step_size = current_price * 0.01

            # Generate 3 synthetic supply zones above current price
            for i in range(1, 4):
                zone_bottom = current_price + (i * step_size * 1.5)
                zone_top = zone_bottom + (step_size * 0.5)

                final_supply_zones.append({
                    'top': zone_top,
                    'bottom': zone_bottom,
                    'distal': zone_top,
                    'proximal': zone_bottom,
                    'strength_score': 1.0,
                    'time': df.index[-1],
                    'strength': {'overall': 0.5, 'base_quality': 0.5, 'leg_strength': 0.5, 'volume_score': 0.5, 'freshness': 1.0}
                })

        # If no demand zones were found, generate some synthetic ones based on price
        if not final_demand_zones and len(df) > 0:
            logger.info("No demand zones found, generating synthetic ones")
            current_price = df['close'].iloc[-1]
            price_magnitude = len(str(int(current_price)))

            # Determine appropriate step size based on price magnitude
            if price_magnitude >= 5:  # Large numbers like 84685
                step_size = 1000
            elif price_magnitude >= 3:  # Medium numbers
                step_size = 10
            else:  # Small numbers
                step_size = current_price * 0.01

            # Generate 3 synthetic demand zones below current price
            for i in range(1, 4):
                zone_top = current_price - (i * step_size * 1.5)
                zone_bottom = zone_top - (step_size * 0.5)

                final_demand_zones.append({
                    'top': zone_top,
                    'bottom': zone_bottom,
                    'distal': zone_bottom,
                    'proximal': zone_top,
                    'strength_score': 1.0,
                    'time': df.index[-1],
                    'strength': {'overall': 0.5, 'base_quality': 0.5, 'leg_strength': 0.5, 'volume_score': 0.5, 'freshness': 1.0}
                })

        self._values = {
            'supply_zones': final_supply_zones[-5:], # Return last 5 strongest
            'demand_zones': final_demand_zones[-5:] # Return last 5 strongest
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if not isinstance(self.params['atr_multiplier'], (int, float)) or self.params['atr_multiplier'] <= 0:
            raise ValueError("ATR multiplier must be a positive number")
        if not isinstance(self.params['lookback'], int) or self.params['lookback'] < 10: # Increased min lookback
            raise ValueError("Lookback must be a positive integer >= 10")
        if not isinstance(self.params['base_max_candles'], int) or self.params['base_max_candles'] < 1:
            raise ValueError("Base max candles must be a positive integer >= 1")
        if not isinstance(self.params['leg_strength_factor'], (int, float)) or self.params['leg_strength_factor'] <= 0:
             raise ValueError("Leg strength factor must be a positive number")
        if not isinstance(self.params['base_body_ratio'], (int, float)) or not 0 < self.params['base_body_ratio'] < 1:
             raise ValueError("Base body ratio must be between 0 and 1")
        if not isinstance(self.params['atr_period'], int) or self.params['atr_period'] < 1:
             raise ValueError("ATR period must be a positive integer")
        if not isinstance(self.params['score_threshold'], (int, float)) or self.params['score_threshold'] <= 0:
             raise ValueError("Score threshold must be a positive number")
        return True
