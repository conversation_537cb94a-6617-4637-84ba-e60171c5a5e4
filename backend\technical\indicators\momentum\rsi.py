from typing import Dict, Any
import numpy as np
import pandas as pd
from backend.technical.base_indicator import BaseIndicator

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

class RSIIndicator(BaseIndicator):
    """Relative Strength Index (RSI) indicator."""

    def __init__(self, period: int = 14, source: str = 'close'):
        """
        Initialize RSI indicator.

        Args:
            period: The period for calculating RSI (default: 14)
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4')
        """
        super().__init__({
            'period': period,
            'source': source
        })
        self.name = 'RSI'

    def calculate(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """Calculate RSI values."""
        try:
            if data.empty or len(data) < self.params['period']: 
                return {
                    'rsi': np.array([]),
                    'source': np.array([])
                }

            # Get source data
            source = self.params['source'].lower()
            if source == 'hl2':
                source_data = (data['high'] + data['low']) / 2
            elif source == 'hlc3':
                source_data = (data['high'] + data['low'] + data['close']) / 3
            elif source == 'ohlc4':
                source_data = (data['open'] + data['high'] + data['low'] + data['close']) / 4
            else:
                source_data = data[source]

            # Calculate price changes
            changes = source_data.diff()
            gains = changes.copy()
            losses = changes.copy()
            gains[gains < 0] = 0
            losses[losses > 0] = 0
            losses = abs(losses)

            # Calculate average gains and losses
            period = self.params['period']
            avg_gains = gains.rolling(window=period).mean()
            avg_losses = losses.rolling(window=period).mean()

            # Calculate RS and RSI
            rs = avg_gains / avg_losses
            rsi = 100 - (100 / (1 + rs))

            # Calculate divergence
            divergence = self._calculate_divergence(source_data.values, rsi.values)

            return {
                'rsi': rsi.values,
                'source': source_data.values,
                'divergence': divergence
            }

        except Exception as e:
            print(f"Error calculating RSI: {str(e)}")
            return {
                'rsi': np.array([]),
                'source': np.array([])
            }

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        return True

    def _calculate_divergence(self, price: np.ndarray, rsi: np.ndarray, lookback: int = 20) -> str:
        """
        Calculate RSI divergence by comparing price action with RSI movements.
        
        Args:
            price: Array of price values
            rsi: Array of RSI values
            lookback: Number of periods to look back for divergence (default: 20)
            
        Returns:
            String indicating divergence type: "Regular Bullish", "Regular Bearish",
            "Hidden Bullish", "Hidden Bearish", or "None"
        """
        try:
            if len(price) < lookback or len(rsi) < lookback:
                return "None"

            # Get the section we want to analyze
            price_section = price[-lookback:]
            rsi_section = rsi[-lookback:]

            # Find local extremes
            price_highs = []
            price_lows = []
            rsi_highs = []
            rsi_lows = []

            for i in range(1, len(price_section) - 1):
                # Price extremes
                if price_section[i] > price_section[i-1] and price_section[i] > price_section[i+1]:
                    price_highs.append((i, price_section[i]))
                if price_section[i] < price_section[i-1] and price_section[i] < price_section[i+1]:
                    price_lows.append((i, price_section[i]))

                # RSI extremes
                if rsi_section[i] > rsi_section[i-1] and rsi_section[i] > rsi_section[i+1]:
                    rsi_highs.append((i, rsi_section[i]))
                if rsi_section[i] < rsi_section[i-1] and rsi_section[i] < rsi_section[i+1]:
                    rsi_lows.append((i, rsi_section[i]))

            # Need at least 2 extremes to compare
            if len(price_highs) >= 2 and len(rsi_highs) >= 2:
                # Check bearish divergence
                if price_highs[-1][1] > price_highs[-2][1] and rsi_highs[-1][1] < rsi_highs[-2][1]:
                    return "Regular Bearish"
                if price_highs[-1][1] < price_highs[-2][1] and rsi_highs[-1][1] > rsi_highs[-2][1]:
                    return "Hidden Bearish"

            if len(price_lows) >= 2 and len(rsi_lows) >= 2:
                # Check bullish divergence
                if price_lows[-1][1] < price_lows[-2][1] and rsi_lows[-1][1] > rsi_lows[-2][1]:
                    return "Regular Bullish"
                if price_lows[-1][1] > price_lows[-2][1] and rsi_lows[-1][1] < rsi_lows[-2][1]:
                    return "Hidden Bullish"

            return "None"
            
        except Exception as e:
            print(f"Error calculating RSI divergence: {str(e)}")
            return "None"
