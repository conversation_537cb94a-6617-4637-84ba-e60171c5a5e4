import React, { useState } from 'react';
import { formatPrice } from '../../utils/formatters';

const TradeExecutionDialog = ({
  visible,
  signalType,
  symbol,
  entry,
  stopLoss,
  takeProfit,
  onClose,
  onExecute,
  isProcessing = false
}) => {
  // Simplified state - just use fixed values for now to eliminate potential causes of blinking
  const [lotSize, setLotSize] = useState(0.01);
  
  // Fixed values for display
  const fixedRisk = 1; // 1% risk
  const fixedSL = stopLoss;
  const fixedTP = takeProfit;
  
  // Simplified calculation functions with fixed values
  const getRiskReward = () => {
    return '1.5'; // Fixed value for now
  };

  const getDollarRisk = () => {
    return '100.00'; // Fixed value for now
  };

  const getDollarReward = () => {
    return '150.00'; // Fixed value for now
  };

  const handleExecute = () => {
    if (isProcessing) return;
    const orderData = {
      symbol,
      signalType,
      lotSize,
      entry: entry?.price,
      stopLoss: fixedSL,
      takeProfit: fixedTP
    };
    onExecute(orderData);
  };

  if (!visible) return null;

  return (
    <div className="trade-execution-modal-overlay">
      <div className="trade-execution-modal-content">
        <h2>Execute {signalType} Signal</h2>
        
        {/* Signal Info Section */}
        <div className="modal-fullwidth">
          <div className="signal-header">
            <div className="signal-title">
              <h2 className="no-margin">Execute {signalType}</h2>
              <div className="symbol-badge">{symbol}</div>
            </div>
            <div className="signal-type-container">
              <div className={`signal-type ${signalType?.toLowerCase()}`}>{signalType}</div>
              <div className="price-tag">@ {formatPrice(entry?.price)}</div>
            </div>
          </div>
          
          <div className="trade-details">
            <div className="lot-size-container">
              <label>Lot Size</label>
              <input 
                type="number" 
                min="0.01" 
                step="0.01" 
                value={lotSize}
                onChange={(e) => setLotSize(parseFloat(e.target.value) || 0.01)}
              />
            </div>
            
            <div className="levels-container">
              <div className="level-item">
                <span className="level-label">Stop Loss:</span>
                <span className="level-value">{formatPrice(fixedSL)}</span>
              </div>
              
              <div className="level-item">
                <span className="level-label">Take Profit:</span>
                <span className="level-value">{formatPrice(fixedTP)}</span>
              </div>
            </div>
            
            <div className="risk-reward-container">
              <div className="risk-item">
                <span>Risk:</span>
                <span>${getDollarRisk()}</span>
              </div>
              <div className="reward-item">
                <span>Reward:</span>
                <span>${getDollarReward()}</span>
              </div>
              <div className="ratio-item">
                <span>R/R:</span>
                <span>{getRiskReward()}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="action-buttons">
          <button 
            className="cancel-button" 
            onClick={onClose}
            disabled={isProcessing}
          >
            Cancel
          </button>
          <button 
            className={`execute-button ${signalType?.toLowerCase()}-button`}
            onClick={handleExecute}
            disabled={isProcessing}
          >
            {isProcessing ? 'Processing...' : `Execute ${signalType}`}
          </button>
        </div>
      </div>
    </div>
  );
};

export default TradeExecutionDialog;
