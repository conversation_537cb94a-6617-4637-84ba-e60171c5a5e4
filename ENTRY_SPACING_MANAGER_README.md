# Entry Spacing Manager

The Entry Spacing Manager is an advanced risk management system designed to optimize trade entry timing based on market volatility (ATR) and trading performance history. It implements anti-martingale strategies to reduce risk after consecutive losses while maintaining profit potential.

## Features

### 1. ATR-Based Entry Spacing
- Uses Average True Range (ATR) to determine optimal spacing between entries
- Configurable ATR period and multiplier
- Minimum and maximum spacing limits to prevent extreme values
- Adapts to market volatility automatically

### 2. Progressive Scaling (Anti-Martingale)
- **Spacing Multipliers**: Increases spacing after consecutive losses
- **Volume Multipliers**: Decreases volume after consecutive losses
- Configurable multipliers for up to 6 consecutive losses
- Helps preserve capital during losing streaks

### 3. Time-Based Spacing
- Minimum time delays between entries
- Progressive cooldown periods after losses
- Configurable base delay and loss multipliers
- Prevents over-trading during volatile periods

### 4. Statistical Tracking
- Tracks performance per symbol
- Consecutive loss counting
- Win rate and profit analysis
- ATR analysis for traded symbols

## Configuration

### Basic Settings
```json
{
  "entry_spacing": {
    "enabled": true,
    "atr_period": 14,
    "atr_multiplier": 2.0,
    "min_spacing_points": 10,
    "max_spacing_points": 500,
    "max_consecutive_losses": 5
  }
}
```

### Progressive Scaling
```json
{
  "progressive_spacing_multipliers": [1.0, 1.5, 2.0, 2.5, 3.0, 4.0],
  "progressive_volume_multipliers": [1.0, 0.9, 0.8, 0.7, 0.6, 0.5]
}
```

### Time-Based Spacing
```json
{
  "time_based_spacing": {
    "enabled": true,
    "base_minutes": 5,
    "loss_cooldown_multipliers": [1, 2, 3, 4, 5, 6]
  }
}
```

## How It Works

### Entry Decision Process

1. **Check if Entry Spacing is Enabled**
   - If disabled, all entries are allowed

2. **ATR Calculation**
   - Calculate current ATR for the symbol
   - Determine base spacing: `ATR × multiplier`
   - Apply min/max limits

3. **Progressive Scaling**
   - Count consecutive losses for the symbol
   - Apply spacing multiplier based on loss count
   - Calculate final spacing: `base_spacing × spacing_multiplier`

4. **Time-Based Check**
   - Check time since last entry
   - Apply cooldown multiplier based on consecutive losses
   - Required delay: `base_minutes × cooldown_multiplier`

5. **Price Distance Check**
   - Compare current price to last entry price
   - Entry allowed if distance ≥ required spacing

### Volume Adjustment

When an entry is allowed, the volume is adjusted:
```python
adjusted_volume = base_volume × volume_multiplier[consecutive_losses]
```

### Trade Result Recording

After each trade closes:
1. Record profit/loss and entry details
2. Update consecutive loss counter
3. Reset counter on winning trade
4. Update symbol statistics

## Integration with Autonomous Trading

### In Strategy Code
```python
from backend.entry_spacing_manager import EntrySpacingManager

# Initialize manager
entry_spacing = EntrySpacingManager('autonomous_config.json')

# Before placing a trade
symbol = "EURUSD"
current_price = 1.1000

if entry_spacing.is_entry_allowed(symbol, current_price):
    # Get adjusted volume
    base_volume = 0.1
    adjusted_volume = entry_spacing.get_adjusted_volume(symbol, base_volume)
    
    # Place trade with adjusted volume
    place_trade(symbol, adjusted_volume)
    
    # Record entry time
    entry_spacing.record_entry(symbol, current_price)

# After trade closes
profit = 10.50  # or -15.25 for loss
entry_time = "2024-01-01T12:00:00"
entry_spacing.record_trade_result(symbol, profit, entry_time)
```

### API Endpoints

- `GET /api/entry_spacing/settings` - Get current settings
- `POST /api/entry_spacing/settings` - Update settings
- `GET /api/entry_spacing/statistics` - Get performance statistics
- `POST /api/entry_spacing/reset` - Reset all data
- `POST /api/entry_spacing/check_entry_allowed` - Check if entry is allowed
- `POST /api/entry_spacing/record_trade` - Record trade result

## Frontend Integration

The Entry Spacing Settings component provides:
- Real-time configuration of all parameters
- Performance statistics display
- Symbol-specific status monitoring
- ATR analysis visualization
- Trade history and statistics

### Accessing the Interface

1. Navigate to Autonomous Trading page
2. Click on "Entry Spacing" tab
3. Configure settings in the left panel
4. Monitor statistics in the right panel

## Benefits

### Risk Management
- **Reduces Risk**: Decreases position size after losses
- **Prevents Over-trading**: Enforces time and price-based spacing
- **Market Adaptation**: Uses ATR to adapt to volatility

### Performance Optimization
- **Drawdown Reduction**: Anti-martingale approach limits losses
- **Capital Preservation**: Progressive volume reduction protects capital
- **Emotional Control**: Systematic approach removes emotional decisions

### Statistical Insights
- **Performance Tracking**: Detailed statistics per symbol
- **Pattern Recognition**: Identify optimal spacing parameters
- **Backtesting Support**: Historical data for strategy optimization

## Example Scenarios

### Scenario 1: Normal Trading
- Symbol: EURUSD
- ATR: 0.0012
- No consecutive losses
- Spacing required: 0.0012 × 2.0 = 24 points
- Volume multiplier: 1.0 (no reduction)

### Scenario 2: After 2 Consecutive Losses
- Symbol: EURUSD
- ATR: 0.0012
- 2 consecutive losses
- Spacing required: 0.0012 × 2.0 × 2.0 = 48 points
- Volume multiplier: 0.8 (20% reduction)
- Time delay: 5 × 3 = 15 minutes

### Scenario 3: High Volatility Period
- Symbol: GBPJPY
- ATR: 0.0200 (high volatility)
- 1 consecutive loss
- Spacing required: 0.0200 × 2.0 × 1.5 = 600 points (capped at 500)
- Volume multiplier: 0.9 (10% reduction)

## Best Practices

### Configuration Tips
1. **ATR Period**: Use 14 for most timeframes, adjust for strategy
2. **ATR Multiplier**: Start with 2.0, increase for more conservative approach
3. **Progressive Multipliers**: Gradual increases work better than aggressive jumps
4. **Time Delays**: Consider market hours and volatility patterns

### Monitoring
1. **Regular Review**: Check statistics weekly
2. **Parameter Adjustment**: Fine-tune based on performance data
3. **Market Conditions**: Adjust for different market phases
4. **Symbol-Specific**: Different symbols may need different settings

### Integration
1. **Gradual Implementation**: Start with conservative settings
2. **Backtesting**: Test with historical data before live trading
3. **Position Sizing**: Coordinate with overall risk management
4. **Strategy Alignment**: Ensure compatibility with trading strategy

## Troubleshooting

### Common Issues

1. **Entries Always Blocked**
   - Check ATR multiplier (may be too high)
   - Verify min_spacing_points setting
   - Review consecutive loss count

2. **No Volume Adjustment**
   - Ensure consecutive losses are being recorded
   - Check volume multiplier arrays
   - Verify trade result recording

3. **Time Delays Not Working**
   - Confirm time_based_spacing is enabled
   - Check system time synchronization
   - Verify last entry time recording

### Debug Information

Enable detailed logging to see:
- Entry spacing calculations
- ATR values and multipliers
- Time delay calculations
- Trade result recording

### Support

For issues or questions:
1. Check log files for error messages
2. Verify configuration file format
3. Test with API endpoints directly
4. Review statistics for data integrity

## Future Enhancements

Planned improvements:
- Machine learning-based parameter optimization
- Market session-specific settings
- Correlation-based multi-symbol spacing
- Advanced volatility measures beyond ATR
- Integration with economic calendar events
