from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class CounterattackPatternIndicator(BaseIndicator):
    """Counterattack Line pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """Initialize the indicator.
        
        Args:
            params: Dictionary of parameters
                - body_ratio: Minimum ratio of body to total range (default: 0.6)
                - shadow_ratio: Maximum ratio of shadow to total range (default: 0.1)
                - open_tolerance: Maximum difference between opens as ratio of first body (default: 0.001)
        """
        default_params = {
            "body_ratio": 0.6,
            "shadow_ratio": 0.1,
            "open_tolerance": 0.001,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)

    def _is_strong_bar(self, open_price: float, high: float,
                      low: float, close: float) -> bool:
        """Check if bar has a strong body with small shadows."""
        total_range = high - low
        if total_range == 0:
            return False
            
        body = abs(close - open_price)
        upper_shadow = high - max(open_price, close)
        lower_shadow = min(open_price, close) - low
        
        body_ratio = body / total_range
        shadow_ratio = max(upper_shadow, lower_shadow) / total_range
        
        return (body_ratio >= self.params['body_ratio'] and 
                shadow_ratio <= self.params['shadow_ratio'])

    def _is_counterattack(self, opens: np.ndarray, highs: np.ndarray,
                         lows: np.ndarray, closes: np.ndarray, idx: int) -> tuple:
        """Identify Counterattack Line patterns."""
        if idx < 1:
            return False, 0
            
        # First candle characteristics
        first_bullish = closes[idx-1] > opens[idx-1]
        first_strong = self._is_strong_bar(opens[idx-1], highs[idx-1],
                                         lows[idx-1], closes[idx-1])
        
        # Second candle characteristics
        second_bullish = closes[idx] > opens[idx]
        second_strong = self._is_strong_bar(opens[idx], highs[idx],
                                          lows[idx], closes[idx])
        
        # Check if opens are similar
        open_diff = abs(opens[idx] - opens[idx-1])
        open_threshold = abs(closes[idx-1] - opens[idx-1]) * self.params['open_tolerance']
        similar_opens = open_diff <= open_threshold
        
        # Bullish Counterattack: Strong bearish first + Strong bullish second with similar open
        if (not first_bullish and first_strong and second_bullish and
            second_strong and similar_opens):
            return True, 1
            
        # Bearish Counterattack: Strong bullish first + Strong bearish second with similar open
        if (first_bullish and first_strong and not second_bullish and
            second_strong and similar_opens):
            return True, -1
            
        return False, 0

    def calculate(self, market_data: MarketData) -> Dict[str, Any]:
        """Calculate Counterattack Line pattern values.
        
        Args:
            market_data: MarketData object containing OHLCV data
            
        Returns:
            Dictionary containing:
                - is_pattern: Boolean indicating if pattern is present
                - pattern_type: String indicating pattern type ('bullish' or 'bearish')
                - pattern_id: String identifying the pattern
                - strength: Float between 0 and 1 indicating pattern strength
                - trend: Integer (-1 for bearish, 1 for bullish)
                - reliability: Integer (-1 for unreliable, 1 for reliable)
        """
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        opens = df['open'].values
        highs = df['high'].values
        lows = df['low'].values
        closes = df['close'].values
        
        is_pattern = np.zeros(len(df), dtype=bool)
        pattern_type = np.full(len(df), '')
        pattern_id = np.full(len(df), '')
        strength = np.zeros(len(df))
        trend = np.zeros(len(df))
        reliability = np.zeros(len(df))
        
        # Scan for patterns
        for i in range(1, len(closes)):
            is_counterattack, pattern = self._is_counterattack(opens, highs,
                                                             lows, closes, i)
            if is_counterattack:
                window = slice(i-1, i+1)
                is_pattern[window] = True
                pattern_type[window] = 'bullish' if pattern > 0 else 'bearish'
                pattern_id[window] = f'counterattack_{"bull" if pattern > 0 else "bear"}_{i}'
                
                # Calculate pattern strength based on:
                # 1. Size of both bars
                # 2. Similarity of opens
                # 3. Strength of both bodies
                first_range = highs[i-1] - lows[i-1]
                second_range = highs[i] - lows[i]
                if first_range > 0 and second_range > 0:
                    first_body = abs(closes[i-1] - opens[i-1])
                    second_body = abs(closes[i] - opens[i])
                    open_diff = abs(opens[i] - opens[i-1])
                    open_score = 1 - (open_diff / first_body)
                    strength[window] = min(1.0, (
                        first_body/first_range + 
                        second_body/second_range +
                        open_score
                    ) / 3)
                
                trend[window] = pattern
                
                # Calculate reliability based on future price movement
                if i < len(closes)-1:
                    future_return = (closes[i+1] - closes[i]) / closes[i]
                    reliability[window] = 1 if (pattern > 0 and future_return > 0) or (pattern < 0 and future_return < 0) else -1
        
        # Calculate trend context using 20-period SMA
        sma20 = df['close'].rolling(window=20).mean()
        trend = np.where(df['close'] > sma20, 1, -1)
        
        return {
            'is_pattern': is_pattern,
            'pattern_type': pattern_type,
            'pattern_id': pattern_id,
            'strength': strength,
            'trend': trend,
            'reliability': reliability
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters.
        
        Returns:
            Boolean indicating if parameters are valid
        """
        if not self.params:
            return True
            
        body_ratio = self.params.get('body_ratio', 0.6)
        shadow_ratio = self.params.get('shadow_ratio', 0.1)
        open_tolerance = self.params.get('open_tolerance', 0.001)
        
        return (0 < body_ratio <= 1 and
                0 < shadow_ratio <= 1 and
                0 < open_tolerance <= 0.1) 