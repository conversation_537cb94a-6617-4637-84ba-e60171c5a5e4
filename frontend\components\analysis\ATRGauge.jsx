import React from 'react';
import { isValidNumber, safeToFixed } from '../../utils/numberUtils';
import '../../styles/GaugeIndicator.css';

/**
 * ATRGauge component displays a gauge visualization for ATR values
 * 
 * @param {number} value - The ATR value
 * @param {number} percent - The ATR percent value for scaling
 * @returns {JSX.Element} - The rendered ATR gauge
 */
const ATRGauge = ({ value, percent }) => {
  // For ATR, we'll use the percent value to position the needle (0-100%)
  // If percent is not available, we'll use a normalized scale based on typical ATR values
  const isValidPercent = isValidNumber(percent);
  const isValidValue = isValidNumber(value);

  const normalizedValue = isValidPercent ? percent : (isValidValue ? Math.min(value * 10, 100) : 50);
  const needleRotation = normalizedValue * 1.8; // Convert to degrees (0-180)
  const needleHeight = 50;

  return (
    <div className="gauge-container">
      <div className="gauge-outer"></div>
      <div className="gauge-inner">{safeToFixed(value, 5)}</div>
      <div
        className="gauge-needle"
        style={{
          height: `${needleHeight}px`,
          transform: `rotate(${needleRotation}deg)`
        }}
      ></div>
      <div className="gauge-labels">
        <span className="gauge-label-oversold">Low</span>
        <span className="gauge-label-neutral">Med</span>
        <span className="gauge-label-overbought">High</span>
      </div>
    </div>
  );
};

export default ATRGauge;
