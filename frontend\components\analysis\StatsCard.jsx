import React from 'react';

/**
 * StatsCard - Displays performance metrics for the selected symbol/timeframe.
 * @param {Object} stats - Object with performance statistics
 */
function StatsCard({ stats = {} }) {
  // Handle both cases: frontend naming (camelCase) and backend API naming (snake_case)
  const winRate = stats.win_rate !== undefined ? stats.win_rate : (stats.winRate !== undefined ? stats.winRate : null);
  const profitFactor = stats.profit_factor !== undefined ? stats.profit_factor : (stats.profitFactor !== undefined ? stats.profitFactor : null);
  const avgReturn = stats.avg_return !== undefined ? stats.avg_return : (stats.avgReturn !== undefined ? stats.avgReturn : null);
  
  // Format values with proper percentage or decimal places
  const formatValue = (value, isPercent = false, decimals = 2) => {
    if (value === null || value === undefined) return '-';
    const formattedValue = typeof value === 'number' ? value.toFixed(decimals) : value;
    return isPercent ? `${formattedValue}%` : formattedValue;
  };

  // Get color based on value type and threshold
  const getValueColor = (value, type) => {
    if (value === null || value === undefined) return 'var(--text-secondary, #888)';
    
    switch(type) {
      case 'winRate':
        return value > 65 ? 'var(--success, #2ecc40)' : 
               value > 50 ? 'var(--warning, #ffdc00)' : 
               'var(--error, #ff4136)';
      case 'profitFactor':
        return value > 1.5 ? 'var(--success, #2ecc40)' : 
               value > 1.0 ? 'var(--warning, #ffdc00)' : 
               'var(--error, #ff4136)';
      case 'avgReturn':
        return value > 0 ? 'var(--success, #2ecc40)' : 'var(--error, #ff4136)';
      default:
        return 'var(--text-secondary, #888)';
    }
  };
  
  return (
    <div className="analysis-card stats-card" style={{ 
      padding: '15px', 
      borderRadius: '8px', 
      background: 'var(--card, #1e2130)', 
      border: '1px solid var(--border, #2a2f45)' 
    }}>
      <h3 style={{ color: 'var(--text, #fff)' }}>Performance Stats</h3>
      <ul style={{ listStyle: 'none', padding: 0 }}>
        <li style={{ 
          marginBottom: '10px', 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          color: 'var(--text, #fff)' 
        }}>
          <strong>Win Rate:</strong> 
          <span style={{ fontWeight: 'bold', color: getValueColor(winRate, 'winRate') }}>
            {formatValue(winRate, true)}
          </span>
        </li>
        <li style={{ 
          marginBottom: '10px', 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          color: 'var(--text, #fff)' 
        }}>
          <strong>Profit Factor:</strong> 
          <span style={{ fontWeight: 'bold', color: getValueColor(profitFactor, 'profitFactor') }}>
            {formatValue(profitFactor)}
          </span>
        </li>
        <li style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          color: 'var(--text, #fff)' 
        }}>
          <strong>Avg Return:</strong> 
          <span style={{ fontWeight: 'bold', color: getValueColor(avgReturn, 'avgReturn') }}>
            {formatValue(avgReturn)}
          </span>
        </li>
      </ul>
    </div>
  );
}

export default StatsCard;
