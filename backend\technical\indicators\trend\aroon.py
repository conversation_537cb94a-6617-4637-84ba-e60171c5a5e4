from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class AroonIndicator(BaseIndicator):
    """Aroon Up/Down and Oscillator indicator."""

    def __init__(self, period: int = 25):
        """
        Initialize Aroon indicator.

        Args:
            period: The lookback period for finding highest high and lowest low.
        """
        super().__init__({'period': period})

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Aroon Up, Aroon Down, and Aroon Oscillator."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period']:
             return {'aroon_up': np.array([]), 'aroon_down': np.array([]), 'oscillator': np.array([])}

        period = self.params['period']
        n = len(df)

        # Calculate days since highest high and lowest low in the period
        rolling_high_idx = df['high'].rolling(window=period).apply(lambda x: x.argmax(), raw=True)
        rolling_low_idx = df['low'].rolling(window=period).apply(lambda x: x.argmin(), raw=True)

        # Days since high/low (index position relative to end of window)
        # The argmax/argmin gives the index within the window (0 to period-1)
        # Days since = period - (index_within_window + 1)
        days_since_high = period - (rolling_high_idx + 1)
        days_since_low = period - (rolling_low_idx + 1)

        # Calculate Aroon Up and Aroon Down
        aroon_up = 100 * (period - days_since_high) / period
        aroon_down = 100 * (period - days_since_low) / period

        # Calculate Aroon Oscillator
        aroon_oscillator = aroon_up - aroon_down

        self._values = {
            'aroon_up': aroon_up.values,
            'aroon_down': aroon_down.values,
            'oscillator': aroon_oscillator.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        return True