from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class FisherTransformIndicator(BaseIndicator):
    """Fisher Transform indicator."""

    def __init__(self, period: int = 10, source: str = 'hl2'):
        """
        Initialize Fisher Transform indicator.

        Args:
            period: The lookback period for normalization and smoothing.
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4').
                    Typically applied to a value that oscillates, like HL2 price normalized.
        """
        super().__init__({
            'period': period,
            'source': source
        })

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate Fisher Transform values."""
        df = data.to_dataframe()
        if df.empty or len(df) < self.params['period']:
             return {'fisher': np.array([]), 'trigger': np.array([])} # Trigger is fisher shifted

        period = self.params['period']
        source_col = self.params['source'].lower()

        # Get source data
        if source_col == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source_col == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source_col == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source_col]

        # Normalize data to -1 to 1 range using rolling min/max
        highest = source_data.rolling(window=period).max()
        lowest = source_data.rolling(window=period).min()
        price_range = highest - lowest
        # Avoid division by zero
        normalized = (2 * ((source_data - lowest) / price_range.replace(0, np.nan)) - 1)
        # Bound values slightly below +/- 1 for the transform
        normalized = normalized.clip(-0.999, 0.999).fillna(0)

        # Apply Fisher Transform calculation (simplified Ehlers version from reference)
        # This smoothing seems specific to Ehlers' implementation in the reference file.
        # A pure Fisher transform might skip this smoothing.
        # Let's follow the reference file's smoothing approach.
        fisher = normalized.copy() * 0 # Initialize with zeros
        for i in range(1, len(df)): # Start from 1 as we use i-1
             # Use .iloc for position-based access, check bounds
             if i < len(fisher):
                 prev_fisher = fisher.iloc[i-1] if i > 0 else 0
                 fisher.iloc[i] = 0.5 * 0.33 * (normalized.iloc[i] + prev_fisher) + 0.67 * prev_fisher
                 # The formula in reference seems slightly off, typical Ehlers uses:
                 # value1 = 0.33 * normalized + 0.67 * value1[1]
                 # fisher = 0.5 * log((1 + value1) / (1 - value1)) + 0.5 * fisher[1]
                 # Let's try the more standard Ehlers approach:

        # Standard Ehlers Fisher Transform calculation:
        value1 = pd.Series(np.zeros(len(df)), index=df.index)
        fisher_transformed = pd.Series(np.zeros(len(df)), index=df.index)

        for i in range(1, len(df)):
             value1.iloc[i] = 0.33 * normalized.iloc[i] + 0.67 * value1.iloc[i-1]
             # Bound value1 as well
             value1_bounded = max(min(value1.iloc[i], 0.999), -0.999)
             fisher_transformed.iloc[i] = 0.5 * np.log((1 + value1_bounded) / (1 - value1_bounded)) + 0.5 * fisher_transformed.iloc[i-1]


        # Trigger line is simply the Fisher Transform shifted by 1 period
        trigger = fisher_transformed.shift(1)

        self._values = {
            'fisher': fisher_transformed.values,
            'trigger': trigger.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 2: # Need at least 2 for rolling min/max
            raise ValueError("Period must be at least 2 for Fisher Transform")
        return True