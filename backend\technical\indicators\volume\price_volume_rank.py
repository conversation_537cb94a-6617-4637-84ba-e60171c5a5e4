from typing import Dict, Any
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class PriceVolumeRankIndicator(BaseIndicator):
    """Price Volume Rank indicator."""

    def __init__(self, period: int = 10):
        """
        Initialize Price Volume Rank indicator.

        Args:
            period: The lookback period for rank calculation.
        """
        super().__init__({'period': period})

    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate rolling Price Rank and Volume Rank."""
        df = data.to_dataframe()
        if df.empty or 'volume' not in df.columns or len(df) < self.params['period']:
             return {'price_rank': np.array([]), 'volume_rank': np.array([])}

        period = self.params['period']
        close = df['close']
        volume = df['volume']

        # Calculate Price Rank (percentile rank within the window)
        price_rank = close.rolling(window=period).apply(
            lambda x: pd.Series(x).rank(pct=True).iloc[-1], raw=False
        )

        # Calculate Volume Rank (percentile rank within the window)
        volume_rank = volume.rolling(window=period).apply(
            lambda x: pd.Series(x).rank(pct=True).iloc[-1], raw=False
        )

        self._values = {
            'price_rank': price_rank.values,
            'volume_rank': volume_rank.values
        }
        return self._values

    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['period'] < 1:
            raise ValueError("Period must be greater than 0")
        return True