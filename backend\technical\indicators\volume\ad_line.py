from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class ADLineIndicator(BaseIndicator):
    """Accumulation/Distribution Line indicator."""
    
    def __init__(self, ma_period: int = 20, ma_type: str = 'sma'):
        """
        Initialize A/D Line indicator.
        
        Args:
            ma_period: The period for the signal line
            ma_type: The type of moving average ('sma' or 'ema')
        """
        super().__init__({
            'ma_period': ma_period,
            'ma_type': ma_type
        })
    
    def calculate(self, data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate A/D Line values."""
        df = data.to_dataframe()
        if df.empty:
            return {}
        
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        volume = df['volume'].values
        ma_period = self.params['ma_period']
        ma_type = self.params['ma_type'].lower()
        
        # Calculate Money Flow Multiplier
        mf_multiplier = ((close - low) - (high - close)) / (high - low)
        mf_multiplier = np.where(high == low, 0, mf_multiplier)  # Handle division by zero
        
        # Calculate Money Flow Volume
        mf_volume = mf_multiplier * volume
        
        # Calculate A/D Line
        ad_line = np.zeros_like(volume)
        ad_line[0] = mf_volume[0]
        
        for i in range(1, len(volume)):
            ad_line[i] = ad_line[i-1] + mf_volume[i]
        
        # Calculate signal line
        if ma_type == 'sma':
            signal = pd.Series(ad_line).rolling(window=ma_period).mean()
        else:  # ema
            signal = pd.Series(ad_line).ewm(span=ma_period, adjust=False).mean()
        
        # Calculate divergences
        price_high = pd.Series(close).rolling(window=5).max()
        price_low = pd.Series(close).rolling(window=5).min()
        ad_high = pd.Series(ad_line).rolling(window=5).max()
        ad_low = pd.Series(ad_line).rolling(window=5).min()
        
        bullish_div = (price_low < price_low.shift(1)) & (ad_low > ad_low.shift(1))
        bearish_div = (price_high > price_high.shift(1)) & (ad_high < ad_high.shift(1))
        
        divergence = np.where(bullish_div, 1, np.where(bearish_div, -1, 0))
        
        # Calculate momentum
        momentum = ad_line - pd.Series(ad_line).shift(ma_period)
        
        # Calculate trend
        trend = np.zeros_like(ad_line)
        trend[ad_line > signal] = 1
        trend[ad_line < signal] = -1
        
        # Calculate crossovers
        signal_cross = np.where(
            (ad_line > signal) & (pd.Series(ad_line).shift(1) <= signal.shift(1)), 1,
            np.where((ad_line < signal) & (pd.Series(ad_line).shift(1) >= signal.shift(1)), -1, 0)
        )
        
        # Calculate volume trend
        volume_sma = pd.Series(volume).rolling(window=ma_period).mean()
        volume_trend = np.zeros_like(volume)
        volume_trend[volume > volume_sma] = 1
        volume_trend[volume < volume_sma] = -1
        
        # Calculate strength
        strength = np.zeros_like(ad_line)
        ad_std = pd.Series(ad_line).rolling(window=ma_period).std()
        ad_mean = pd.Series(ad_line).rolling(window=ma_period).mean()
        
        z_score = (ad_line - ad_mean) / ad_std
        
        strength[(z_score >= 1) & (z_score < 2)] = 1     # Strong
        strength[z_score >= 2] = 2                        # Very Strong
        strength[(z_score <= -1) & (z_score > -2)] = -1   # Weak
        strength[z_score <= -2] = -2                      # Very Weak
        
        # Calculate accumulation/distribution phases
        phase = np.zeros_like(ad_line)
        phase[(trend == 1) & (volume_trend == 1)] = 2    # Strong accumulation
        phase[(trend == 1) & (volume_trend == -1)] = 1   # Weak accumulation
        phase[(trend == -1) & (volume_trend == 1)] = -1  # Weak distribution
        phase[(trend == -1) & (volume_trend == -1)] = -2 # Strong distribution
        
        # Calculate acceleration
        acceleration = momentum - pd.Series(momentum).shift(1)
        
        self._values = {
            'ad_line': ad_line,
            'signal': signal.values,
            'mf_multiplier': mf_multiplier,
            'mf_volume': mf_volume,
            'divergence': divergence,
            'momentum': momentum.values,
            'trend': trend,
            'signal_cross': signal_cross,
            'volume_trend': volume_trend,
            'strength': strength,
            'phase': phase,
            'acceleration': acceleration.values,
            'z_score': z_score.values
        }
        return self._values
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_ma_types = ['sma', 'ema']
        if self.params['ma_type'].lower() not in valid_ma_types:
            raise ValueError(f"MA type must be one of {valid_ma_types}")
        if self.params['ma_period'] < 1:
            raise ValueError("MA period must be greater than 0")
        return True 