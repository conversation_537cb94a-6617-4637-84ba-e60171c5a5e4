/* Horizontal Price Level Visualization */
.price-level-visualization {
  margin: 20px 0;
  padding: 10px;
  font-family: Arial, sans-serif;
}

.price-level-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.price-level-title {
  font-weight: bold;
  font-size: 1rem;
  color: #fff;
}

.price-level-current {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

/* Price level bar */
.price-level-bar {
  position: relative;
  height: 30px;
  margin-bottom: 40px;
  border-radius: 4px;
  overflow: visible;
}

/* Color zones */
.price-zone-container {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
}

.price-zone {
  flex: 1;
  height: 100%;
}

.support-zone {
  background-color: rgba(0, 204, 0, 0.2);
  border-radius: 4px 0 0 4px;
}

.neutral-zone {
  background-color: rgba(230, 230, 0, 0.2);
}

.resistance-zone {
  background-color: rgba(255, 77, 77, 0.2);
  border-radius: 0 4px 4px 0;
}

/* Price markers */
.price-marker {
  position: absolute;
  top: 0;
  left: 50%; /* Default position, will be overridden */
  transform: translateX(-50%);
}

.marker-line {
  width: 3px;
  height: 50px;
  background-color: white;
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
}

.marker-label {
  position: absolute;
  top: 45px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 0.8rem;
  white-space: nowrap;
}

/* Current marker */
.current-marker .marker-line {
  background-color: white;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

/* Price level information */
.price-level-info {
  margin: 20px 0;
}

.price-level-row {
  margin-bottom: 15px;
}

.price-level-label {
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
}

.price-level-values {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.price-level-item {
  display: flex;
  flex-direction: column;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 8px 12px;
  border-radius: 4px;
  min-width: 150px;
}

.item-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 4px;
}

.item-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

/* Legend */
.price-level-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 6px;
}

.legend-color.support {
  background-color: rgba(0, 204, 0, 0.7);
}

.legend-color.current {
  background-color: white;
}

.legend-color.resistance {
  background-color: rgba(255, 77, 77, 0.7);
}
