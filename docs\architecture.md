# Post-Login UI Architecture

## 1. Component Structure

### 1.1 High-Level Hierarchy
```
App (Root)
├── ConnectionTab (Handles MT5 login)
├── Dashboard (Default view)
│   ├── AccountSummary
│   ├── PositionsTable
│   ├── TradeHistory
│   └── QuickActions
└── AnalysisView (Specialized view)
    ├── ChartContainer
    ├── AnalysisTools
    └── TimeframeSelector
```

### 1.2 Component Responsibilities
- **ConnectionTab**: 
  - Manages MT5 connection state
  - Handles post-login redirection logic
  - Emits connection status events

- **Dashboard**:
  - Fetches and displays account data
  - Shows real-time position updates
  - Provides quick action buttons

- **AnalysisView**:
  - Parses URL parameters (symbol, timeframe)
  - Fetches and displays historical data
  - Renders technical analysis tools

## 2. Data Flow Architecture

### 2.1 State Management
- **Redux** recommended for:
  - Connection state (global)
  - Account data (global)
  - UI preferences (global)
  - Analysis parameters (local to AnalysisView)

### 2.2 Data Flow Patterns
1. **Dashboard Flow**:
   - ConnectionTab → Redux (connection status)
   - Dashboard subscribes to account data
   - API calls via Redux middleware

2. **Analysis Flow**:
   - URL params → AnalysisView component state
   - Data requests via custom hooks
   - Chart data cached in Redux

## 3. API Integration

### 3.1 Key Endpoints
| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/positions` | GET | Account positions |
| `/api/ohlc` | GET | Historical OHLC data |

### 3.2 Integration Strategy
- **Dashboard**:
  - Initial load: Single API call to `/api/positions`
  - Updates: WebSocket for real-time changes

- **AnalysisView**:
  - Data fetching via `useEffect` hooks
  - Request deduplication with React Query
  - Error boundaries for failed requests

## 4. Performance Considerations

### 4.1 Optimizations
- **Dashboard**:
  - Virtualized lists for positions/history
  - Memoized selectors for Redux
  - Lazy loading of non-critical components

- **AnalysisView**:
  - Chart data caching
  - Request throttling
  - WebWorker for indicator calculations

### 4.2 Measurement
- Implement React.memo for pure components
- Use React DevTools for profiling
- Monitor API response times

## 5. Security Considerations
- JWT for API authentication
- Secure Electron IPC channels
- Input sanitization for URL params
