from flask import Blueprint, request, jsonify
import os
import logging
from typing import Dict, Any, Tuple

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
connection_test_bp = Blueprint('connection_test', __name__)

def error_response(message: str, status_code: int = 400) -> Tuple[Dict[str, Any], int]:
    """Helper function to create error responses"""
    return {
        "status": "error",
        "error": message
    }, status_code

@connection_test_bp.route('/test_path', methods=['POST'])
def test_path():
    """
    Test if the provided MT5 terminal path exists.
    
    Expected request body:
    {
        "path": "C:\\Program Files\\MetaTrader 5\\terminal64.exe"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return error_response("No data provided", 400)
            
        # Validate required fields
        if 'path' not in data:
            return error_response("Missing required field: path", 400)
            
        path = data['path']
        
        # Check if the path exists
        if not os.path.exists(path):
            return error_response(f"Path does not exist: {path}", 404)
            
        # Check if the path is a file
        if not os.path.isfile(path):
            return error_response(f"Path is not a file: {path}", 400)
            
        # Check if the file is an executable
        if not path.lower().endswith('.exe'):
            return error_response(f"File is not an executable: {path}", 400)
            
        # Check if the file name contains 'metatrader' or 'terminal'
        file_name = os.path.basename(path).lower()
        if 'metatrader' not in file_name and 'terminal' not in file_name:
            return error_response(f"File does not appear to be a MetaTrader terminal: {path}", 400)
            
        # All checks passed
        return jsonify({
            "status": "success",
            "message": "MT5 terminal path is valid"
        }), 200
            
    except Exception as e:
        logger.exception("Unexpected error in test_path endpoint")
        return error_response(f"Internal server error: {str(e)}", 500)
