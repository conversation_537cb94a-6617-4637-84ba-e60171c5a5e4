.current-price-display {
  background-color: #1e1e2d;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-container {
  display: flex;
  flex-direction: column;
}

.symbol {
  font-size: 1.2rem;
  font-weight: bold;
  color: #8a8a8a;
  margin-bottom: 5px;
}

.price {
  font-size: 2.5rem;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 5px;
}

.price.up {
  color: #4caf50;
}

.price.down {
  color: #f44336;
}

.change {
  font-size: 1.1rem;
  font-weight: bold;
}

.change.up {
  color: #4caf50;
}

.change.down {
  color: #f44336;
}

.change.neutral {
  color: #8a8a8a;
}

.price-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.value.success {
  color: #4caf50;
  font-weight: bold;
}

.value.error {
  color: #f44336;
  font-weight: bold;
}

.detail {
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.label {
  font-weight: bold;
  color: #8a8a8a;
}

.value {
  color: #ffffff;
}

.value.error {
  color: #f44336;
  font-weight: bold;
}

.error-message {
  color: #f44336;
  font-size: 0.9rem;
  margin-top: 5px;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .current-price-display {
    flex-direction: column;
    align-items: flex-start;
  }

  .price-details {
    margin-top: 15px;
    width: 100%;
  }

  .price {
    font-size: 2rem;
  }
}
