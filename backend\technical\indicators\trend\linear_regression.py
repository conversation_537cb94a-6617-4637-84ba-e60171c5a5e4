from typing import Dict, Any, List
import numpy as np
import pandas as pd
from scipy import stats

from ..base_indicator import BaseIndicator
from .......core.models.market_data import MarketData

class LinearRegressionIndicator(BaseIndicator):
    """Linear Regression indicator with channels and forecast."""
    
    def __init__(self, period: int = 20, deviations: float = 2.0,
                 forecast_period: int = 5, source: str = 'close'):
        """
        Initialize Linear Regression indicator.
        
        Args:
            period: The lookback period for regression
            deviations: Number of standard deviations for channels
            forecast_period: Number of periods to forecast
            source: The price source ('close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4')
        """
        super().__init__('LR', {
            'period': period,
            'deviations': deviations,
            'forecast_period': forecast_period,
            'source': source
        })
    
    def calculate(self, data: List[MarketData]) -> Dict[str, np.ndarray]:
        """Calculate Linear Regression values."""
        df = self._prepare_data()
        if df.empty:
            return {}
        
        # Get source data
        source = self.params['source'].lower()
        if source == 'hl2':
            source_data = (df['high'] + df['low']) / 2
        elif source == 'hlc3':
            source_data = (df['high'] + df['low'] + df['close']) / 3
        elif source == 'ohlc4':
            source_data = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            source_data = df[source]
        
        period = self.params['period']
        deviations = self.params['deviations']
        forecast_period = self.params['forecast_period']
        
        # Initialize arrays
        regression = np.zeros_like(source_data)
        upper = np.zeros_like(source_data)
        lower = np.zeros_like(source_data)
        r_squared = np.zeros_like(source_data)
        slope = np.zeros_like(source_data)
        forecast = np.zeros_like(source_data)
        
        # Calculate rolling regression
        for i in range(period, len(source_data)):
            y = source_data[i-period:i]
            x = np.arange(period)
            
            # Calculate regression
            slope_val, intercept, r_val, _, std_err = stats.linregress(x, y)
            
            # Calculate regression line
            line = (slope_val * x) + intercept
            regression[i] = line[-1]
            
            # Calculate channels
            std = np.std(y - line)
            upper[i] = regression[i] + (deviations * std)
            lower[i] = regression[i] - (deviations * std)
            
            # Store R-squared and slope
            r_squared[i] = r_val ** 2
            slope[i] = slope_val
            
            # Calculate forecast
            if i < len(source_data) - forecast_period:
                forecast_x = np.arange(period, period + forecast_period)
                forecast[i:i+forecast_period] = (slope_val * forecast_x) + intercept
        
        # Calculate trend strength
        trend_strength = np.abs(slope) * r_squared
        
        # Calculate price deviation
        deviation = ((source_data - regression) / regression) * 100
        
        # Calculate reversal signals
        reversal = np.zeros_like(source_data)
        reversal[source_data >= upper] = -1  # Overbought
        reversal[source_data <= lower] = 1   # Oversold
        
        self._values = {
            'regression': regression,
            'upper': upper,
            'lower': lower,
            'r_squared': r_squared,
            'slope': slope,
            'forecast': forecast,
            'trend_strength': trend_strength,
            'deviation': deviation,
            'reversal': reversal,
            'source': source_data
        }
        return self._values
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        valid_sources = ['close', 'open', 'high', 'low', 'hl2', 'hlc3', 'ohlc4']
        if self.params['source'].lower() not in valid_sources:
            raise ValueError(f"Source must be one of {valid_sources}")
        if self.params['period'] < 2:
            raise ValueError("Period must be greater than 1")
        if self.params['deviations'] <= 0:
            raise ValueError("Deviations must be greater than 0")
        if self.params['forecast_period'] < 1:
            raise ValueError("Forecast period must be greater than 0")
        return True 