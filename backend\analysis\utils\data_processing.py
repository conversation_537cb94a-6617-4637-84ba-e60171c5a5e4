import math
from typing import Any, Dict, List

def replace_nan_with_none(obj: Any) -> Any:
    """
    Recursively replace NaN values with None in dictionaries and lists.
    
    Args:
        obj: The object to process (can be a dict, list, or scalar value)
        
    Returns:
        The processed object with NaN values replaced by None
    """
    if isinstance(obj, dict):
        return {k: replace_nan_with_none(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [replace_nan_with_none(elem) for elem in obj]
    elif isinstance(obj, float) and math.isnan(obj):
        return None
    return obj
