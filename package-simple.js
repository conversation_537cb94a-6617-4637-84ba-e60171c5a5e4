const { build } = require('electron-builder');
const path = require('path');
const fs = require('fs');

// Clean up previous builds
console.log('Cleaning previous builds...');
try {
  if (fs.existsSync('release-new')) {
    fs.rmSync('release-new', { recursive: true, force: true });
  }
} catch (err) {
  console.error('Error cleaning previous builds:', err);
}

// Verify backend executable exists
if (!fs.existsSync(path.join('dist', 'garuda_backend.exe'))) {
  console.error('ERROR: Backend executable not found at dist/garuda_backend.exe');
  process.exit(1);
}

// Copy backend executable to project root
console.log('Copying backend executable...');
try {
  fs.copyFileSync(
    path.join('dist', 'garuda_backend.exe'),
    'garuda_backend.exe'
  );
} catch (err) {
  console.error('Error copying backend executable:', err);
  process.exit(1);
}

// Build configuration
const config = {
  appId: 'com.garudaalgo.app',
  productName: 'GarudaAlgo Trader',
  directories: {
    output: 'release-new',
    buildResources: 'assets'
  },
  files: [
    'main.js',
    'frontend/preload.js',
    'frontend/dist/**/*',
    '!node_modules/.bin',
    '!*.map'
  ],
  extraResources: [
    {
      from: 'garuda_backend.exe',
      to: 'garuda_backend.exe'
    }
  ],
  win: {
    target: ['dir'],
    icon: 'assets/app_icon.png'
  }
};

// Build the app
console.log('Building application...');
build({
  targets: 'dir',
  config: config
})
  .then(() => {
    console.log('Build successful!');
    // Clean up copied files
    try {
      fs.unlinkSync('garuda_backend.exe');
    } catch (err) {
      console.error('Error cleaning up:', err);
    }
  })
  .catch((err) => {
    console.error('Build failed:', err);
    process.exit(1);
  });
