from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class SeparatingLinesPatternIndicator(BaseIndicator):
    """Pattern indicator for Separating Lines patterns."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize Separating Lines pattern indicator.
        
        Args:
            params: Dictionary containing parameters:
                - body_ratio: Minimum ratio of body to total range for strong bars (default: 0.6)
                - shadow_ratio: Maximum ratio of shadows for strong bars (default: 0.1)
                - gap_threshold: Minimum gap size as fraction of first bar's range (default: 0.001)
        """
        super().__init__(params)
        self.body_ratio = self.params.get('body_ratio', 0.6)
        self.shadow_ratio = self.params.get('shadow_ratio', 0.1)
        self.gap_threshold = self.params.get('gap_threshold', 0.001)

    def _is_strong_bar(self, open_price: float, high: float,
                      low: float, close: float) -> bool:
        """Check if bar has a strong body with small shadows."""
        total_range = high - low
        if total_range == 0:
            return False
            
        body = abs(close - open_price)
        upper_shadow = high - max(open_price, close)
        lower_shadow = min(open_price, close) - low
        
        body_ratio = body / total_range
        shadow_ratio = max(upper_shadow, lower_shadow) / total_range
        
        return (body_ratio >= self.body_ratio and 
                shadow_ratio <= self.shadow_ratio)

    def _has_gap(self, first_high: float, first_low: float,
                 second_high: float, second_low: float) -> bool:
        """Check if there's a gap between two candles."""
        first_range = first_high - first_low
        min_gap = first_range * self.gap_threshold
        
        # Upward gap
        if second_low - first_high >= min_gap:
            return True
        # Downward gap
        if first_low - second_high >= min_gap:
            return True
            
        return False

    def _is_separating_lines(self, opens: np.ndarray, highs: np.ndarray,
                            lows: np.ndarray, closes: np.ndarray, idx: int) -> tuple:
        """Identify Separating Lines patterns."""
        if idx < 1:
            return False, 0
            
        # First candle characteristics
        first_bullish = closes[idx-1] > opens[idx-1]
        first_strong = self._is_strong_bar(opens[idx-1], highs[idx-1],
                                         lows[idx-1], closes[idx-1])
        
        # Second candle characteristics
        second_bullish = closes[idx] > opens[idx]
        second_strong = self._is_strong_bar(opens[idx], highs[idx],
                                          lows[idx], closes[idx])
        
        # Check for gap between candles
        has_gap = self._has_gap(highs[idx-1], lows[idx-1],
                               highs[idx], lows[idx])
        
        # Bullish Separating Lines:
        # - First candle: Strong bearish
        # - Second candle: Strong bullish opening near first candle's open
        # - Gap between candles
        if (not first_bullish and first_strong and second_bullish and
            second_strong and has_gap and
            abs(opens[idx] - opens[idx-1]) <= abs(closes[idx-1] - opens[idx-1]) * 0.1):
            return True, 1
            
        # Bearish Separating Lines:
        # - First candle: Strong bullish
        # - Second candle: Strong bearish opening near first candle's open
        # - Gap between candles
        if (first_bullish and first_strong and not second_bullish and
            second_strong and has_gap and
            abs(opens[idx] - opens[idx-1]) <= abs(closes[idx-1] - opens[idx-1]) * 0.1):
            return True, -1
            
        return False, 0

    def calculate(self, market_data: MarketData) -> Dict[str, Any]:
        """Calculate Separating Lines pattern values.
        
        Args:
            market_data: MarketData object containing OHLCV data
            
        Returns:
            Dictionary containing:
                - is_pattern: Boolean indicating if pattern is present
                - pattern_type: String indicating pattern type ('bullish' or 'bearish')
                - pattern_id: String identifying the pattern
                - strength: Float between 0 and 1 indicating pattern strength
                - trend: Integer (-1 for bearish, 1 for bullish)
                - reliability: Integer (-1 for unreliable, 1 for reliable)
        """
        df = market_data.to_dataframe()
        
        opens = df['open'].values
        highs = df['high'].values
        lows = df['low'].values
        closes = df['close'].values
        
        is_pattern = np.zeros(len(df), dtype=bool)
        pattern_type = np.full(len(df), '')
        pattern_id = np.full(len(df), '')
        strength = np.zeros(len(df))
        trend = np.zeros(len(df))
        reliability = np.zeros(len(df))
        
        # Scan for patterns
        for i in range(1, len(closes)):
            is_separating, pattern = self._is_separating_lines(opens, highs,
                                                             lows, closes, i)
            if is_separating:
                window = slice(i-1, i+1)
                is_pattern[window] = True
                pattern_type[window] = 'bullish' if pattern > 0 else 'bearish'
                pattern_id[window] = f'separating_lines_{"bull" if pattern > 0 else "bear"}_{i}'
                
                # Calculate pattern strength based on:
                # 1. Size of both bars
                # 2. Size of gap
                # 3. Similarity of opens
                first_range = highs[i-1] - lows[i-1]
                second_range = highs[i] - lows[i]
                if first_range > 0 and second_range > 0:
                    first_body = abs(closes[i-1] - opens[i-1])
                    second_body = abs(closes[i] - opens[i])
                    gap_size = min(abs(lows[i] - highs[i-1]),
                                 abs(lows[i-1] - highs[i]))
                    gap_score = min(1.0, gap_size / first_range)
                    open_diff = abs(opens[i] - opens[i-1])
                    open_score = 1 - (open_diff / first_body)
                    strength[window] = min(1.0, (
                        first_body/first_range + 
                        second_body/second_range +
                        gap_score + open_score
                    ) / 4)
                
                trend[window] = pattern
                
                # Calculate reliability based on future price movement
                if i < len(closes)-1:
                    future_return = (closes[i+1] - closes[i]) / closes[i]
                    reliability[window] = 1 if (pattern > 0 and future_return > 0) or (pattern < 0 and future_return < 0) else -1
        
        # Calculate trend context using 20-period SMA
        sma20 = df['close'].rolling(window=20).mean()
        trend = np.where(df['close'] > sma20, 1, -1)
        
        return {
            'is_pattern': is_pattern,
            'pattern_type': pattern_type,
            'pattern_id': pattern_id,
            'strength': strength,
            'trend': trend,
            'reliability': reliability
        }
    
    def validate_params(self) -> None:
        """
        Validate indicator parameters.
        
        Raises:
            ValueError: If parameters are invalid
        """
        if not 0 < self.body_ratio < 1:
            raise ValueError("body_ratio must be between 0 and 1")
        if not 0 < self.shadow_ratio < 1:
            raise ValueError("shadow_ratio must be between 0 and 1")
        if not 0 < self.gap_threshold < 0.1:
            raise ValueError("gap_threshold must be between 0 and 0.1") 