from typing import Dict, Any, List
import numpy as np
import pandas as pd

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class MarubozuPatternIndicator(BaseIndicator):
    """Marubozu candlestick pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - shadow_ratio: Parameter description (default: 0.1)
        """
        default_params = {
            "shadow_ratio": 0.1,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)

    
    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        open_price = df['open'].values
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        shadow_ratio = self.params['shadow_ratio']
        
        # Calculate body size and total range
        body_size = np.abs(close - open_price)
        total_range = high - low
        
        # Calculate shadows
        upper_shadow = high - np.maximum(open_price, close)
        lower_shadow = np.minimum(open_price, close) - low
        
        # Identify marubozu patterns
        is_marubozu = (
            (body_size >= (total_range * 0.9)) &  # Large body
            (upper_shadow <= (total_range * shadow_ratio)) &  # Small upper shadow
            (lower_shadow <= (total_range * shadow_ratio))  # Small lower shadow
        )
        
        # Classify marubozu types
        marubozu_type = np.zeros_like(close)
        marubozu_type[is_marubozu & (close > open_price)] = 1  # Bullish marubozu
        marubozu_type[is_marubozu & (close < open_price)] = -1  # Bearish marubozu
        
        # Calculate pattern strength
        strength = np.zeros_like(close)
        strength[is_marubozu] = body_size[is_marubozu] / total_range[is_marubozu]
        
        # Calculate trend context
        trend = np.zeros_like(close)
        for i in range(1, len(close)):
            if i >= 20:  # Use 20-period SMA for trend
                sma = np.mean(close[i-20:i])
                trend[i] = 1 if close[i] > sma else -1
        
        # Calculate pattern reliability
        reliability = np.zeros_like(close)
        for i in range(1, len(close)):
            if is_marubozu[i]:
                # Check if price moved in the expected direction
                if i < len(close)-1:
                    future_return = (close[i+1] - close[i]) / close[i]
                    if close[i] > open_price[i]:  # Bullish marubozu
                        reliability[i] = 1 if future_return > 0 else -1
                    else:  # Bearish marubozu
                        reliability[i] = 1 if future_return < 0 else -1
        
        return {
            'is_marubozu': is_marubozu.astype(int),
            'marubozu_type': marubozu_type,
            'strength': strength,
            'trend': trend,
            'reliability': reliability,
            'body_size': body_size,
            'upper_shadow': upper_shadow,
            'lower_shadow': lower_shadow,
            'total_range': total_range
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if not 0 < self.params['shadow_ratio'] < 1:
            raise ValueError("Shadow ratio must be between 0 and 1")
        return True 