from typing import Dict, Any, List
import numpy as np
import pandas as pd
from scipy.signal import find_peaks

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class ElliottWavePatternIndicator(BaseIndicator):
    """Elliott Wave chart pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - min_wave_height: Parameter description (default: 0.02)
                - min_wave_length: Parameter description (default: 5)
                - price_tolerance: Parameter description (default: 0.02)
                - fibonacci_tolerance: Parameter description (default: 0.1)
        """
        default_params = {
            "min_wave_height": 0.02,
            "min_wave_length": 5,
            "price_tolerance": 0.02,
            "fibonacci_tolerance": 0.1,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)


    def _find_waves(self, prices: np.ndarray, peaks: np.ndarray,
                   troughs: np.ndarray) -> tuple:
        """Find potential Elliott Wave points."""
        if len(peaks) < 3 or len(troughs) < 3:
            return None, None
            
        wave_points = []
        wave_types = []  # 1 for impulse waves, -1 for corrective waves
        
        # Find potential wave 1 (impulse)
        for i in range(len(peaks)):
            peak1 = peaks[i]
            if peak1 < self.params['min_wave_length']:
                continue
                
            # Find potential wave 2 (corrective)
            valid_troughs = troughs[troughs > peak1]
            if len(valid_troughs) == 0:
                continue
                
            trough1 = valid_troughs[0]
            wave1_height = prices[peak1] - prices[0]
            if wave1_height / prices[0] < self.params['min_wave_height']:
                continue
                
            # Find potential wave 3 (impulse)
            valid_peaks = peaks[peaks > trough1]
            if len(valid_peaks) == 0:
                continue
                
            peak2 = valid_peaks[0]
            wave2_height = prices[peak1] - prices[trough1]
            if wave2_height / prices[trough1] < self.params['min_wave_height']:
                continue
                
            # Find potential wave 4 (corrective)
            valid_troughs = troughs[troughs > peak2]
            if len(valid_troughs) == 0:
                continue
                
            trough2 = valid_troughs[0]
            wave3_height = prices[peak2] - prices[trough1]
            if wave3_height / prices[trough1] < self.params['min_wave_height']:
                continue
                
            # Find potential wave 5 (impulse)
            valid_peaks = peaks[peaks > trough2]
            if len(valid_peaks) == 0:
                continue
                
            peak3 = valid_peaks[0]
            wave4_height = prices[peak2] - prices[trough2]
            if wave4_height / prices[trough2] < self.params['min_wave_height']:
                continue
                
            wave5_height = prices[peak3] - prices[trough2]
            if wave5_height / prices[trough2] < self.params['min_wave_height']:
                continue
                
            # Check Fibonacci relationships
            wave_lengths = [
                peak1,  # Wave 1
                peak1 - trough1,  # Wave 2
                peak2 - trough1,  # Wave 3
                peak2 - trough2,  # Wave 4
                peak3 - trough2   # Wave 5
            ]
            
            # Wave 3 should be longest among impulse waves
            if wave_lengths[2] <= wave_lengths[0] or wave_lengths[2] <= wave_lengths[4]:
                continue
                
            # Wave 4 shouldn't retrace more than 100% of Wave 3
            if wave_lengths[3] >= wave_lengths[2]:
                continue
                
            # Wave 2 shouldn't retrace more than 100% of Wave 1
            if wave_lengths[1] >= wave_lengths[0]:
                continue
                
            # Store wave points and types
            wave_points.extend([0, peak1, trough1, peak2, trough2, peak3])
            wave_types.extend([1, 1, -1, 1, -1, 1])  # Impulse and corrective waves
            
            # Look for ABC correction
            valid_troughs = troughs[troughs > peak3]
            if len(valid_troughs) == 0:
                continue
                
            trough3 = valid_troughs[0]  # Wave A
            valid_peaks = peaks[peaks > trough3]
            if len(valid_peaks) == 0:
                continue
                
            peak4 = valid_peaks[0]  # Wave B
            valid_troughs = troughs[troughs > peak4]
            if len(valid_troughs) == 0:
                continue
                
            trough4 = valid_troughs[0]  # Wave C
            
            # Add correction waves
            wave_points.extend([trough3, peak4, trough4])
            wave_types.extend([-1, -1, -1])
            
            return np.array(wave_points), np.array(wave_types)
            
        return None, None

    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        close = df['close'].values
        is_pattern = np.zeros_like(close)
        pattern_type = np.zeros_like(close)
        wave_number = np.zeros_like(close)
        
        # Find peaks and troughs
        peaks, _ = find_peaks(close, distance=self.params['min_wave_length'])
        troughs, _ = find_peaks(-close, distance=self.params['min_wave_length'])
        
        # Find Elliott Wave patterns
        wave_points, wave_types = self._find_waves(close, peaks, troughs)
        
        if wave_points is not None and wave_types is not None:
            # Mark the pattern
            for i in range(len(wave_points)-1):
                start = wave_points[i]
                end = wave_points[i+1]
                is_pattern[start:end+1] = 1
                pattern_type[start:end+1] = wave_types[i]
                wave_number[start:end+1] = i + 1
        
        # Calculate pattern strength
        strength = np.zeros_like(close)
        for i in range(len(close)):
            if is_pattern[i]:
                # Calculate the height of the current wave relative to price
                wave_idx = int(wave_number[i] - 1)
                if wave_idx < len(wave_points) - 1:
                    start = wave_points[wave_idx]
                    end = wave_points[wave_idx + 1]
                    wave_height = abs(close[end] - close[start])
                    strength[i] = wave_height / close[i]
        
        # Calculate trend context
        trend = np.full_like(close, -1, dtype=int)  # Default to -1
        for i in range(20, len(close)):  # Start from 20 to have enough data for SMA
            sma = np.mean(close[i-20:i])
            trend[i] = 1 if close[i] > sma else -1
        
        # Calculate pattern reliability
        reliability = np.full_like(close, -1, dtype=float)  # Default to -1 for no pattern
        future_window = 20
        
        for i in range(len(close) - future_window):
            if is_pattern[i]:
                future_returns = (df['close'].iloc[i+1:i+future_window+1].values - 
                                df['close'].iloc[i]) / df['close'].iloc[i]
                
                if pattern_type[i] > 0:  # Impulse wave
                    max_return = np.max(future_returns)
                    reliability[i] = 1 if max_return > 0 else -1
                else:  # Corrective wave
                    min_return = np.min(future_returns)
                    reliability[i] = 1 if min_return < 0 else -1
        
        return {
            'is_pattern': is_pattern.astype(int),
            'pattern_type': pattern_type,  # 1 for impulse waves, -1 for corrective waves
            'wave_number': wave_number,
            'strength': strength,
            'trend': trend,
            'reliability': reliability,
            'wave_points': wave_points if wave_points is not None else np.array([]),
            'wave_types': wave_types if wave_types is not None else np.array([])
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if not 0 < self.params['min_wave_height'] < 1:
            raise ValueError("Minimum wave height must be between 0 and 1")
        if self.params['min_wave_length'] < 3:
            raise ValueError("Minimum wave length must be at least 3 periods")
        if not 0 < self.params['price_tolerance'] < 1:
            raise ValueError("Price tolerance must be between 0 and 1")
        if not 0 < self.params['fibonacci_tolerance'] < 1:
            raise ValueError("Fibonacci tolerance must be between 0 and 1")
        return True 