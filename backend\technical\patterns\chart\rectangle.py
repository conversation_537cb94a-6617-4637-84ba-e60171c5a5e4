from typing import Dict, Any, List
import numpy as np
import pandas as pd
from scipy.signal import find_peaks

from src.analysis.technical.base_indicator import BaseIndicator
from src.core.models.market_data import MarketData

class RectanglePatternIndicator(BaseIndicator):
    """Rectangle chart pattern indicator."""
    
    def __init__(self, params: Dict[str, Any] = None):
        """
        Initialize pattern indicator.

        Args:
            params: Dictionary containing parameters:
                - min_touches: Parameter description (default: 4)
                - min_width: Parameter description (default: 20)
                - price_tolerance: Parameter description (default: 0.02)
                - touch_tolerance: Parameter description (default: 0.01)
        """
        default_params = {
            "min_touches": 4,
            "min_width": 20,
            "price_tolerance": 0.02,
            "touch_tolerance": 0.01,
        }
        if params:
            default_params.update(params)
        super().__init__(default_params)


    def _find_boundaries(self, prices: np.ndarray, peaks: np.ndarray,
                        troughs: np.ndarray) -> tuple:
        """Find potential rectangle boundaries."""
        if len(peaks) < 2 or len(troughs) < 2:
            return None, None, 0, 0
            
        # Calculate potential resistance levels from peaks
        peak_prices = prices[peaks]
        resistance_clusters = []
        current_cluster = [peak_prices[0]]
        
        for price in peak_prices[1:]:
            if abs(price - np.mean(current_cluster)) / price <= self.params['price_tolerance']:
                current_cluster.append(price)
            else:
                if len(current_cluster) >= 2:
                    resistance_clusters.append(current_cluster)
                current_cluster = [price]
                
        if len(current_cluster) >= 2:
            resistance_clusters.append(current_cluster)
            
        # Calculate potential support levels from troughs
        trough_prices = prices[troughs]
        support_clusters = []
        current_cluster = [trough_prices[0]]
        
        for price in trough_prices[1:]:
            if abs(price - np.mean(current_cluster)) / price <= self.params['price_tolerance']:
                current_cluster.append(price)
            else:
                if len(current_cluster) >= 2:
                    support_clusters.append(current_cluster)
                current_cluster = [price]
                
        if len(current_cluster) >= 2:
            support_clusters.append(current_cluster)
            
        # Find best resistance and support pair
        best_score = 0
        best_resistance = None
        best_support = None
        best_start = 0
        best_end = 0
        
        for resistance_cluster in resistance_clusters:
            resistance_level = np.mean(resistance_cluster)
            
            for support_cluster in support_clusters:
                support_level = np.mean(support_cluster)
                
                if support_level >= resistance_level:
                    continue
                    
                # Find pattern boundaries
                pattern_peaks = peaks[np.abs(prices[peaks] - resistance_level) / resistance_level <= self.params['touch_tolerance']]
                pattern_troughs = troughs[np.abs(prices[troughs] - support_level) / support_level <= self.params['touch_tolerance']]
                
                if len(pattern_peaks) + len(pattern_troughs) >= self.params['min_touches']:
                    start_idx = min(min(pattern_peaks), min(pattern_troughs))
                    end_idx = max(max(pattern_peaks), max(pattern_troughs))
                    
                    if end_idx - start_idx >= self.params['min_width']:
                        score = len(pattern_peaks) + len(pattern_troughs)
                        if score > best_score:
                            best_score = score
                            best_resistance = resistance_level
                            best_support = support_level
                            best_start = start_idx
                            best_end = end_idx
                            
        return best_resistance, best_support, best_start, best_end

    def calculate(self, market_data: MarketData) -> Dict[str, np.ndarray]:
        """Calculate pattern values."""
        df = market_data.to_dataframe()
        if df.empty:
            return {}
        
        close = df['close'].values
        is_pattern = np.zeros_like(close)
        pattern_type = np.zeros_like(close)
        
        # Find peaks and troughs
        peaks, _ = find_peaks(close, distance=5)
        troughs, _ = find_peaks(-close, distance=5)
        
        # Scan for rectangle patterns
        resistance, support, start, end = self._find_boundaries(close, peaks, troughs)
        
        if resistance is not None and support is not None:
            # Mark the pattern
            is_pattern[start:end+1] = 1
            
            # Determine pattern type based on breakout direction
            if end + 1 < len(close):
                if close[end + 1] > resistance:
                    pattern_type[start:end+1] = 1  # Bullish breakout
                elif close[end + 1] < support:
                    pattern_type[start:end+1] = -1  # Bearish breakout
        
        # Calculate pattern strength
        strength = np.zeros_like(close)
        for i in range(len(close)):
            if is_pattern[i] and resistance is not None and support is not None:
                # Calculate height relative to price
                height = (resistance - support) / close[i]
                strength[i] = height
        
        # Calculate trend context
        trend = np.full_like(close, -1, dtype=int)  # Default to -1
        for i in range(20, len(close)):  # Start from 20 to have enough data for SMA
            sma = np.mean(close[i-20:i])
            trend[i] = 1 if close[i] > sma else -1
        
        # Calculate pattern reliability
        reliability = np.full_like(close, -1, dtype=float)  # Default to -1 for no pattern
        future_window = 20
        
        for i in range(len(close) - future_window):
            if is_pattern[i]:
                future_returns = (df['close'].iloc[i+1:i+future_window+1].values - 
                                df['close'].iloc[i]) / df['close'].iloc[i]
                
                if pattern_type[i] > 0:  # Bullish pattern
                    max_return = np.max(future_returns)
                    reliability[i] = 1 if max_return > 0 else -1
                else:  # Bearish pattern
                    min_return = np.min(future_returns)
                    reliability[i] = 1 if min_return < 0 else -1
        
        # Create resistance and support arrays
        resistance_array = np.full_like(close, resistance if resistance is not None else 0)
        support_array = np.full_like(close, support if support is not None else 0)
        
        return {
            'is_pattern': is_pattern.astype(int),
            'pattern_type': pattern_type,
            'strength': strength,
            'trend': trend,
            'reliability': reliability,
            'resistance': resistance_array,
            'support': support_array
        }
    
    def validate_params(self) -> bool:
        """Validate indicator parameters."""
        if self.params['min_touches'] < 4:
            raise ValueError("Minimum touches must be at least 4")
        if self.params['min_width'] < 10:
            raise ValueError("Minimum width must be at least 10 periods")
        if not 0 < self.params['price_tolerance'] < 1:
            raise ValueError("Price tolerance must be between 0 and 1")
        if not 0 < self.params['touch_tolerance'] < 1:
            raise ValueError("Touch tolerance must be between 0 and 1")
        return True 