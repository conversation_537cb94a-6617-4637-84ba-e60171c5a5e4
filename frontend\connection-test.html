<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MT5 Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
        }
        button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 15px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        button.red {
            background-color: #f44336;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
        }
        .connected {
            background-color: #dff0d8;
            color: #3c763d;
            border: 1px solid #d6e9c6;
        }
        .disconnected {
            background-color: #f2dede;
            color: #a94442;
            border: 1px solid #ebccd1;
        }
        .loading {
            background-color: #fcf8e3;
            color: #8a6d3b;
            border: 1px solid #faebcc;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        input, select {
            width: 100%;
            padding: 8px;
            margin: 5px 0 15px;
            display: inline-block;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }
        label {
            font-weight: bold;
        }
        .form-group {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>MT5 Connection Test</h1>
    
    <div class="card">
        <h2>Connection Status</h2>
        <div id="status-display" class="status disconnected">
            <span id="status-text">Disconnected</span>
        </div>
        
        <button id="check-status-btn">Check Connection Status</button>
        <button id="connect-btn">Connect to MT5</button>
        <button id="disconnect-btn" class="red" disabled>Disconnect</button>
    </div>
    
    <div class="card">
        <h2>Connection Form</h2>
        <form id="connection-form">
            <div class="form-group">
                <label for="mt5-path">MT5 Path:</label>
                <input type="text" id="mt5-path" placeholder="C:\Program Files\MetaTrader 5\terminal64.exe" required>
            </div>
            
            <div class="form-group">
                <label for="login">Login:</label>
                <input type="text" id="login" placeholder="Your MT5 account number" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" placeholder="Your MT5 password" required>
            </div>
            
            <div class="form-group">
                <label for="server">Server:</label>
                <input type="text" id="server" placeholder="Your MT5 server name" required>
            </div>
            
            <button type="submit" id="connect-submit">Connect</button>
        </form>
    </div>
    
    <div class="card">
        <h2>API Response</h2>
        <pre id="response-data">No data yet</pre>
    </div>

    <script>
        // DOM Elements
        const statusDisplay = document.getElementById('status-display');
        const statusText = document.getElementById('status-text');
        const checkStatusBtn = document.getElementById('check-status-btn');
        const connectBtn = document.getElementById('connect-btn');
        const disconnectBtn = document.getElementById('disconnect-btn');
        const connectionForm = document.getElementById('connection-form');
        const responseData = document.getElementById('response-data');
        
        // Connection status check
        async function checkConnectionStatus() {
            statusDisplay.className = 'status loading';
            statusText.textContent = 'Checking connection...';
            
            try {
                const response = await fetch('http://localhost:5001/api/connection/connection_status');
                const data = await response.json();
                
                // Update the response data display
                responseData.textContent = JSON.stringify(data, null, 2);
                
                if (data.status === 'connected') {
                    statusDisplay.className = 'status connected';
                    statusText.textContent = 'Connected';
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                } else {
                    statusDisplay.className = 'status disconnected';
                    statusText.textContent = 'Disconnected';
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                }
            } catch (error) {
                console.error('Error checking connection status:', error);
                statusDisplay.className = 'status disconnected';
                statusText.textContent = 'Error: ' + error.message;
                responseData.textContent = error.toString();
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }
        
        // Handle connect button click
        connectBtn.addEventListener('click', () => {
            statusDisplay.className = 'status loading';
            statusText.textContent = 'Opening connection form...';
            // You could show a modal here if you prefer
        });
        
        // Handle disconnect button click
        async function handleDisconnect() {
            statusDisplay.className = 'status loading';
            statusText.textContent = 'Disconnecting...';
            disconnectBtn.disabled = true;
            
            try {
                const response = await fetch('http://localhost:5001/api/connection/disconnect', {
                    method: 'POST'
                });
                const data = await response.json();
                
                // Update the response data display
                responseData.textContent = JSON.stringify(data, null, 2);
                
                // Check status again to update UI
                checkConnectionStatus();
            } catch (error) {
                console.error('Error disconnecting:', error);
                statusDisplay.className = 'status disconnected';
                statusText.textContent = 'Error disconnecting: ' + error.message;
                responseData.textContent = error.toString();
                checkConnectionStatus();
            }
        }
        disconnectBtn.addEventListener('click', handleDisconnect);
        
        // Handle connection form submission
        async function handleConnectionSubmit(event) {
            event.preventDefault();
            
            statusDisplay.className = 'status loading';
            statusText.textContent = 'Connecting...';
            document.getElementById('connect-submit').disabled = true;
            
            const formData = {
                path: document.getElementById('mt5-path').value.trim(),
                login: document.getElementById('login').value.trim(),
                password: document.getElementById('password').value,
                server: document.getElementById('server').value.trim()
            };
            
            try {
                const response = await fetch('http://localhost:5001/api/connection/connect', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                // Update the response data display
                responseData.textContent = JSON.stringify(data, null, 2);
                
                if (response.ok && data.success) {
                    statusDisplay.className = 'status connected';
                    statusText.textContent = 'Connected successfully!';
                    
                    // Update UI elements
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    document.getElementById('connect-submit').disabled = false;
                    
                    // Check connection status to get full details
                    setTimeout(checkConnectionStatus, 1000);
                } else {
                    statusDisplay.className = 'status disconnected';
                    statusText.textContent = 'Connection failed: ' + (data.error || 'Unknown error');
                    document.getElementById('connect-submit').disabled = false;
                }
            } catch (error) {
                console.error('Connection error:', error);
                statusDisplay.className = 'status disconnected';
                statusText.textContent = 'Connection error: ' + error.message;
                responseData.textContent = error.toString();
                document.getElementById('connect-submit').disabled = false;
            }
        }
        connectionForm.addEventListener('submit', handleConnectionSubmit);
        
        // Check status button
        checkStatusBtn.addEventListener('click', checkConnectionStatus);
        
        // Initial status check
        checkConnectionStatus();
    </script>
</body>
</html> 