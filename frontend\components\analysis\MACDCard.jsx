import React from 'react';
import TrendArrow from './TrendArrow';
import { safeToFixed, renderIndicatorValue } from '../../utils/analysisUtils';
import '../../styles/BentoComponents.css';
import '../../styles/AnalysisBento.css';

/**
 * MACDCard component displays MACD analysis
 *
 * @param {Object} macd - The MACD data
 * @returns {JSX.Element} - The rendered MACD card
 */
const MACDCard = ({ macd }) => {
  return (
    <div className="bento-card bento-span-4">
      <div className="bento-card-header">
        <h3 className="bento-card-title">MACD</h3>
        <div className="signal-with-arrow">
          {renderIndicatorValue(macd?.cross, 'signal')}
          <TrendArrow direction={macd?.cross} />
        </div>
      </div>
      <div className="bento-card-content analysis-indicator-details">
        <p><span>MACD:</span> <span>{safeToFixed(macd?.macd, 5)}</span></p>
        <p><span>Signal:</span> <span>{safeToFixed(macd?.signal, 5)}</span></p>
        <p><span>Histogram:</span> <span>{safeToFixed(macd?.histogram, 5)}</span></p>
      </div>
    </div>
  );
};

export default MACDCard;
