/**
 * Safely formats a number with a fixed number of decimal places.
 * Returns 'N/A' for null, undefined, NaN, or non-number values.
 * 
 * This is a standalone version for use in non-React files like renderer.js
 * 
 * @param {any} value - The value to format
 * @param {number} decimals - Number of decimal places (default: 2)
 * @param {string} fallback - Fallback string for invalid values (default: 'N/A')
 * @returns {string} Formatted number or fallback string
 */
function safeToFixed(value, decimals = 2, fallback = 'N/A') {
  // Check if value is a valid number
  if (value === null || value === undefined || isNaN(value) || typeof value !== 'number') {
    return fallback;
  }
  
  try {
    return value.toFixed(decimals);
  } catch (error) {
    console.warn(`Error formatting value: ${value}`, error);
    return fallback;
  }
}

// Export for use in renderer.js
window.safeToFixed = safeToFixed;
