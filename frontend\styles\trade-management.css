.trade-management-panel {
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.trade-management-panel h4 {
  color: var(--text-primary);
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 8px;
}

.trade-ops-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

@media (max-width: 768px) {
  .trade-ops-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .trade-ops-grid {
    grid-template-columns: 1fr;
  }
}

.trade-ops-section {
  margin-bottom: 16px;
}

.trade-ops-section h5 {
  margin-top: 0;
  margin-bottom: 8px;
  color: var(--text-secondary);
  font-size: 14px;
}

/* Toggle Switch Styles */
.mode-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  padding: 8px;
  background-color: #1a2433;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.mode-toggle span {
  padding: 0 10px;
  font-size: 13px;
  color: #a0aec0;
  font-weight: 500;
}

.mode-toggle .active-mode {
  color: #3498db;
  font-weight: bold;
}

/* The switch - the box around the slider */
.switch {
  position: relative;
  display: inline-block;
  width: 52px;
  height: 26px;
  margin: 0 8px;
}

/* Hide default HTML checkbox */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #2c3e50;
  transition: .4s;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

input:checked + .slider {
  background-color: #3498db;
}

input:focus + .slider {
  box-shadow: 0 0 1px #3498db;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

/* Input fields styling for SL/TP */
.input-button-group {
  display: flex;
  margin-bottom: 10px;
  align-items: stretch;
}

.input-with-label {
  position: relative;
  width: 70%;
}

.input-button-group input {
  border: 1px solid #2c3e50;
  background-color: #1e2a3a;
  color: white;
  border-radius: 4px 0 0 4px;
  padding: 10px;
  width: 100%;
  box-sizing: border-box;
  height: 100%;
}

.input-button-group input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 1px #3498db;
}

.input-mode-label {
  position: absolute;
  top: -8px;
  left: 10px;
  background-color: #1a2433;
  padding: 0 5px;
  font-size: 10px;
  color: #3498db;
  border-radius: 2px;
}

.input-button-group button {
  border-radius: 0 4px 4px 0;
  width: 30%;
  border: none;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.input-button-group .sl-button {
  background-color: #e74c3c;
}

.input-button-group .sl-button:hover {
  background-color: #c0392b;
}

.input-button-group .tp-button {
  background-color: #2ecc71;
}

.input-button-group .tp-button:hover {
  background-color: #27ae60;
}

.input-button-group button:disabled {
  background-color: #7f8c8d;
  cursor: not-allowed;
}

.both-button {
  background: linear-gradient(135deg, #3498db, #2980b9);
  margin-top: 5px;
  margin-bottom: 15px;
  font-weight: bold;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.both-button:hover {
  background: linear-gradient(135deg, #2980b9, #2c3e50);
}

.both-button .icon {
  margin-right: 8px;
}

.trade-ops-button {
  display: block;
  width: 100%;
  padding: 10px 14px;
  background-color: #2c3e50;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  text-align: center;
  margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.trade-ops-button:hover {
  background-color: #34495e;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.trade-ops-button:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.trade-ops-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: 0.5s;
}

.trade-ops-button:hover::before {
  left: 100%;
}

.trade-ops-button.dangerous {
  background-color: #e74c3c;
  color: white;
}

.trade-ops-button.dangerous:hover {
  background-color: #c0392b;
}

.trade-ops-button.positive {
  background-color: #2ecc71;
  color: white;
}

.trade-ops-button.positive:hover {
  background-color: #27ae60;
}

.trade-ops-button.neutral {
  background-color: #3498db;
  color: white;
}

.trade-ops-button.neutral:hover {
  background-color: #2980b9;
}

.trade-ops-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.bulk-modify-form {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  margin-bottom: 10px;
}

.bulk-modify-form input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #34495e;
  border-radius: 6px;
  background-color: #1a2533;
  color: white;
  font-size: 14px;
  transition: all 0.3s ease;
}

.bulk-modify-form input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

.bulk-modify-form button {
  padding: 10px 16px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.bulk-modify-form button:hover {
  background-color: #2980b9;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.bulk-modify-form button:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.results-message {
  margin-top: 8px;
  padding: 8px;
  border-radius: 4px;
  font-size: 14px;
}

.results-message.success {
  background-color: rgba(40, 167, 69, 0.1);
  border: 1px solid rgba(40, 167, 69, 0.2);
  color: var(--success-color);
}

.results-message.error {
  background-color: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.2);
  color: var(--danger-color);
}

/* Trailing Stop Configuration Styles */
.config-loading {
  text-align: center;
  padding: 20px;
  color: var(--text-secondary);
  font-style: italic;
}

.config-info {
  margin-top: 8px;
  padding: 8px;
  background-color: rgba(52, 152, 219, 0.1);
  border: 1px solid rgba(52, 152, 219, 0.2);
  border-radius: 4px;
  text-align: center;
}

.config-info small {
  color: #3498db;
  font-size: 12px;
  line-height: 1.4;
}


