/* Trade Execution Styles */

/* New Trade Execution Page Styles */
.execution-page {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.execution-content {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

/* Tabs */
.execution-tabs {
  display: flex;
  border-bottom: 1px solid var(--border);
  margin-bottom: 20px;
}

.tab-button {
  padding: 10px 20px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  font-weight: 500;
  color: var(--text-secondary);
}

.tab-button.active {
  color: var(--primary);
  border-bottom: 2px solid var(--primary);
}

/* Order Form */
.order-form-container {
  flex: 1;
  min-width: 300px;
  background-color: var(--card);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px var(--shadow);
}

.order-type-toggle {
  display: flex;
  margin-bottom: 20px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid var(--border);
}

.toggle-button {
  flex: 1;
  padding: 8px 12px;
  border: none;
  background-color: var(--background);
  cursor: pointer;
  transition: all 0.2s;
}

.toggle-button.active {
  background-color: var(--primary);
  color: white;
}

.order-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-group label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.form-group input,
.form-group select {
  padding: 8px 12px;
  border: 1px solid var(--border);
  border-radius: 4px;
  background-color: var(--input-bg);
  color: var(--text);
}

.form-group input:focus,
.form-group select:focus {
  border-color: var(--primary);
  outline: none;
}

/* Risk Calculator */
.risk-calculator {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border);
}

.risk-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.risk-toggle label {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
}

.risk-toggle input[type="radio"] {
  margin: 0;
}

.risk-inputs {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.risk-inputs .form-group {
  flex: 1;
}

.calculate-button {
  width: 100%;
  padding: 8px;
  background-color: var(--secondary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.calculate-button:hover {
  background-color: var(--secondary-dark);
}

/* Positions and Orders */
.positions-orders-container {
  flex: 2;
  min-width: 400px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.positions-panel,
.orders-panel {
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.positions-list,
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 15px;
}

.position-card,
.order-card {
  border: 1px solid var(--border);
  border-radius: 6px;
  overflow: hidden;
}

.position-header,
.order-header {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background-color: var(--card-header-bg);
  border-bottom: 1px solid var(--border);
}

.position-type,
.order-type {
  font-weight: 600;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  margin-right: 10px;
}

.position-type.buy,
.order-type.buy,
.order-type.buy_limit,
.order-type.buy_stop {
  background-color: rgba(0, 200, 83, 0.15);
  color: var(--success);
}

.position-type.sell,
.order-type.sell,
.order-type.sell_limit,
.order-type.sell_stop {
  background-color: rgba(255, 53, 53, 0.15);
  color: var(--danger);
}

.position-symbol,
.order-symbol {
  flex: 1;
  font-weight: 500;
}

.close-button,
.cancel-button {
  padding: 4px 8px;
  background-color: var(--danger);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.position-details,
.order-details {
  padding: 15px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.detail-value {
  font-weight: 500;
}

.detail-value.positive {
  color: var(--success);
}

.detail-value.negative {
  color: var(--danger);
}

.no-data {
  color: var(--text-secondary);
  text-align: center;
  padding: 20px;
  font-style: italic;
}

/* Refresh button */
.refresh-button {
  align-self: flex-end;
  padding: 6px 12px;
  background-color: var(--secondary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.refresh-button:hover {
  background-color: var(--secondary-dark);
}

.refresh-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.refresh-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .execution-content {
    flex-direction: column;
  }

  .order-form-container,
  .positions-orders-container {
    width: 100%;
  }
}

/* Original Trade Execution Styles */
.signal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 15px;
}

.signal-execute-btn {
  padding: 10px 20px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  transition: all 0.2s;
}

.signal-execute-btn:hover {
  opacity: 0.9;
}

.signal-execute-btn:active {
  transform: translateY(1px);
}

.buy-btn {
  background-color: var(--success);
  color: white;
}

.buy-btn:hover {
  background-color: var(--success);
  opacity: 0.9;
}

.sell-btn {
  background-color: var(--error);
  color: white;
}

.sell-btn:hover {
  background-color: var(--error);
  opacity: 0.9;
}

/* Position Management Styles (Assuming these are needed outside the dialog) */
.positions-container {
  background-color: var(--card);
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
}

.positions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border);
}

.positions-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--text);
}

.positions-table {
  width: 100%;
  border-collapse: collapse;
}

.positions-table th,
.positions-table td {
  padding: 10px 15px;
  text-align: left;
  border-bottom: 1px solid var(--border);
}

.positions-table th {
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.positions-table td {
  color: var(--text);
}

.positions-table tr:last-child td {
  border-bottom: none;
}

.profit {
  color: var(--success);
  font-weight: 500;
}

.loss {
  color: var(--error);
  font-weight: 500;
}

.close-position-btn {
  padding: 5px 10px;
  background-color: var(--background-secondary);
  border: 1px solid var(--border);
  border-radius: 4px;
  cursor: pointer;
  color: var(--text);
}

.close-position-btn:hover {
  background-color: var(--card-hover);
}

.no-positions {
  text-align: center;
  padding: 20px;
  color: var(--text-secondary);
}

/* ==== NEW Trade Execution Modal Styles ==== */

.trade-execution-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--modal-backdrop);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.trade-execution-modal-content {
  background-color: var(--card);
  border-radius: 8px;
  padding: 20px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px var(--shadow);
}

.trade-execution-modal-content h2 {
  margin-top: 0;
  color: var(--text);
  border-bottom: 1px solid var(--border);
  padding-bottom: 10px;
}

/* Full width container */
.modal-fullwidth {
  width: 100%;
  max-width: 600px;
  padding: 0 20px;
}

/* Signal header with title and type */
.signal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.signal-title {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.no-margin {
  margin: 0;
}

.symbol-badge {
  padding: 5px 10px;
  background-color: var(--background-secondary);
  border-radius: 4px;
  color: var(--text);
  font-weight: 600;
}

.signal-type-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 15px 0;
  gap: 10px;
}

.signal-type {
  padding: 6px 12px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 1.1rem;
}

.signal-type.buy {
  background-color: rgba(0, 200, 83, 0.2);
  color: var(--success);
}

.signal-type.sell {
  background-color: rgba(255, 61, 113, 0.2);
  color: var(--error);
}

.price-tag {
  font-weight: 500;
  color: var(--text-secondary);
  margin-left: 8px;
}

/* Trade details section */
.trade-details {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 25px;
  margin-bottom: 30px;
}

/* Lot size input */
.lot-size-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.lot-size-container label {
  font-size: 0.9rem;
  color: #b0b8c5;
}

.lot-size-container input {
  background-color: var(--background-secondary);
  border: 1px solid var(--border);
  border-radius: 4px;
  padding: 8px 12px;
  color: var(--text);
  width: 100%;
}

.lot-size-container input:focus {
  border-color: var(--primary);
  outline: none;
}

/* Levels container */
.levels-container {
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.level-item {
  flex: 1;
  background-color: #2a2f45;
  padding: 12px;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.level-label {
  font-size: 0.85rem;
  color: #b0b8c5;
}

.level-value {
  font-size: 1.1rem;
  font-weight: 600;
}

/* Risk/reward container */
.risk-reward-container {
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.risk-item, .reward-item, .ratio-item {
  flex: 1;
  background-color: #2a2f45;
  padding: 12px;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.risk-item span:first-child,
.reward-item span:first-child,
.ratio-item span:first-child {
  font-size: 0.85rem;
  color: #b0b8c5;
}

.risk-item span:last-child,
.reward-item span:last-child,
.ratio-item span:last-child {
  font-size: 1.1rem;
  font-weight: 600;
}

.risk-item span:last-child {
  color: var(--error);
}

.reward-item span:last-child {
  color: var(--success);
}

/* Action buttons */
.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  gap: 15px;
}

.cancel-button,
.execute-button {
  flex: 1;
  padding: 10px 0;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  transition: all 0.2s;
}

.cancel-button {
  background-color: var(--background-secondary);
  color: var(--text);
  border: 1px solid var(--border);
}

.execute-button {
  color: white;
}

.buy-button {
  background-color: var(--success);
}

.sell-button {
  background-color: var(--error);
}

/* Form Elements */
.form-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-group {
  display: flex;
  flex-direction: column;
  width: 48%;
}

.form-group label {
  margin-bottom: 5px;
  color: #8a93a2;
  font-size: 0.9rem;
}

.trade-execution-modal-content input[type="number"],
.trade-execution-modal-content input[type="text"],
.trade-execution-modal-content select {
  background-color: var(--background-secondary);
  border: 1px solid var(--border);
  border-radius: 4px;
  padding: 8px 12px;
  color: var(--text);
  width: 100%;
}

.trade-execution-modal-content input[type="number"]:focus,
.trade-execution-modal-content input[type="text"]:focus,
.trade-execution-modal-content select:focus {
  border-color: var(--primary);
  outline: none;
}

/* Level rows with risk/profit indicators */
.level-row {
  display: grid;
  grid-template-columns: 80px 1fr 60px;
  gap: 10px;
  align-items: center;
}

.risk-value {
  color: var(--error);
  font-weight: 500;
  font-size: 0.9rem;
}

.profit-value {
  color: var(--success);
  font-weight: 500;
  font-size: 0.9rem;
}

/* Summary section */
.summary-section .summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.95rem;
}

.summary-section .summary-row:last-child {
  margin-bottom: 0;
}

.summary-section .summary-row span:first-child {
  color: var(--text-secondary);
}

.summary-section .summary-row span:last-child {
  font-weight: 500;
  color: var(--text);
}

/* Signal badge styling */
.signal-badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 0.85rem;
  font-weight: 600;
}

.signal-badge.buy {
  background-color: var(--success);
  color: white;
}

.signal-badge.sell {
  background-color: var(--error);
  color: white;
}

/* ==== End NEW Modal Styles ==== */
