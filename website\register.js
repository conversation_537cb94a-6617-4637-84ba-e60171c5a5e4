/* global firebase, auth, db */
document.addEventListener('DOMContentLoaded', async function() {
  // Validate Firebase initialization
  try {
    if (!firebase.app()) {
      console.error('Firebase not initialized');
      toastSystem.error('Error initializing application. Please refresh the page.');
      return;
    }
    console.log('Firebase initialized successfully');
  } catch (error) {
    console.error('Firebase initialization error:', error);
    toastSystem.error('Error initializing application. Please refresh the page.');
    return;
  }
  /* global firebase, auth, db */ // Assuming Firebase SDKs are loaded globally and initialized
  // Add transition styles
  const styleSheet = document.createElement('style');
  styleSheet.textContent = `
    #registration-form, #success-message {
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    #success-message {
      opacity: 0;
      transform: translateY(20px);
      background: linear-gradient(135deg,
        rgba(46, 213, 115, 0.1),
        rgba(46, 213, 115, 0.05)
      );
      backdrop-filter: blur(8px);
      border: 1px solid rgba(46, 213, 115, 0.2);
    }

    .step {
      transition: all 0.3s ease-out;
    }

    .step.active {
      transform: scale(1.05);
    }

    .step.active .step-number {
      background: var(--primary);
      border-color: var(--primary-light);
      box-shadow: 0 0 15px var(--primary-light);
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }

    .success-icon {
      animation: pulse 2s infinite;
    }
  `;
  document.head.appendChild(styleSheet);

  // Core DOM elements
  const registrationForm = document.getElementById('registration-form');
  const emailInput = document.getElementById('email');
  const passwordInput = document.getElementById('password');
  const confirmPasswordInput = document.getElementById('confirm-password');
  const successMessage = document.getElementById('success-message');

  // Error elements
  const emailError = document.getElementById('email-error');
  const passwordError = document.getElementById('password-error');
  const confirmPasswordError = document.getElementById('confirm-password-error');

  // Validation and UI helper functions
  function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  }

  function validatePassword(password) {
    const minLength = password.length >= 8;
    const hasNumber = /\d/.test(password);
    const hasSpecial = /[!@#$%^&*]/.test(password);
    const hasUpper = /[A-Z]/.test(password);
    const hasLower = /[a-z]/.test(password);

    const score = [minLength, hasNumber, hasSpecial, hasUpper, hasLower]
      .filter(Boolean).length;

    return {
      isValid: minLength,
      score: score,
      strength: score < 3 ? 'weak' : score < 4 ? 'medium' : 'strong'
    };
  }

  function validatePasswordMatch(password, confirmPassword) {
    return password === confirmPassword;
  }

  function updateInputState(inputEl, iconEl, isValid) {
    inputEl.classList.toggle('valid', isValid);
    inputEl.classList.toggle('invalid', !isValid);
    
    if (iconEl) {
      iconEl.className = `input-icon fas ${isValid ? 'fa-check' : 'fa-times'}`;
      iconEl.style.color = isValid ? '#2ed573' : '#ff5757';
    }
  }

  function updatePasswordStrength(result) {
    const strengthBar = document.querySelector('.password-strength-bar');
    strengthBar.className = 'password-strength-bar';
    
    if (result.score > 0) {
      strengthBar.classList.add(`strength-${result.strength}`);
    }
  }

  function showError(element, show) {
    element.style.display = show ? 'block' : 'none';
    element.closest('.form-group').classList.toggle('invalid', show);
  }

  // Password visibility toggle
  const togglePassword = document.getElementById('toggle-password');
  // passwordInput is already defined at line 49 and is in scope here.

  togglePassword.addEventListener('click', () => {
    const type = passwordInput.type === 'password' ? 'text' : 'password';
    passwordInput.type = type;
    togglePassword.className = `input-icon fas ${type === 'password' ? 'fa-eye' : 'fa-eye-slash'}`;
  });

  // Input references
  const emailIcon = emailInput.nextElementSibling;
  const confirmPasswordIcon = confirmPasswordInput.nextElementSibling;

  // Real-time validation with UI feedback
  emailInput.addEventListener('input', debounce(function() {
    const isValid = validateEmail(this.value);
    updateInputState(this, emailIcon, isValid);
    showError(emailError, !isValid && this.value.length > 0);
  }, 300));

  passwordInput.addEventListener('input', debounce(function() {
    const result = validatePassword(this.value);
    updatePasswordStrength(result);
    showError(passwordError, !result.isValid && this.value.length > 0);
    
    // Update confirm password validation if it has a value
    if (confirmPasswordInput.value) {
      const isMatch = validatePasswordMatch(this.value, confirmPasswordInput.value);
      updateInputState(confirmPasswordInput, confirmPasswordIcon, isMatch);
      showError(confirmPasswordError, !isMatch);
    }
  }, 300));

  confirmPasswordInput.addEventListener('input', debounce(function() {
    const isMatch = validatePasswordMatch(passwordInput.value, this.value);
    updateInputState(this, confirmPasswordIcon, isMatch);
    showError(confirmPasswordError, !isMatch && this.value.length > 0);
  }, 300));

  // Helper function to debounce input events
  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func.apply(this, args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  // Debug logger for registration process
  // Debug and error handling utilities
  const debugLog = {
    log: [],
    add(step, data = null) {
      const entry = { step, timestamp: new Date().toISOString(), data };
      this.log.push(entry);
      console.log(`Registration Step: ${step}`, data || '');
    },
    clear() {
      this.log = [];
    },
    getFullLog() {
      return JSON.stringify(this.log, null, 2);
    }
  };

  // Toast notifications system
  const toastSystem = {
    container: null,
    init() {
      // Add styles
      const style = document.createElement('style');
      style.textContent = `
        .toast-container {
          position: fixed;
          top: 1rem;
          right: 1rem;
          z-index: 1000;
        }
        .toast {
          background: #fff;
          box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
          border-radius: 0.5rem;
          padding: 1rem;
          margin-bottom: 0.5rem;
          min-width: 300px;
          max-width: 90vw;
          display: flex;
          align-items: center;
          gap: 0.75rem;
          animation: slideIn 0.3s ease-out;
        }
        .toast.error {
          background: #fee2e2;
          border-left: 4px solid #ef4444;
          color: #b91c1c;
        }
        .toast.success {
          background: #dcfce7;
          border-left: 4px solid #22c55e;
          color: #15803d;
        }
        .toast .icon {
          flex-shrink: 0;
          width: 20px;
          height: 20px;
        }
        .toast .message {
          flex-grow: 1;
          font-size: 0.875rem;
          line-height: 1.25rem;
        }
        .toast .close {
          flex-shrink: 0;
          cursor: pointer;
          opacity: 0.5;
          transition: opacity 0.2s;
          padding: 0.25rem;
        }
        .toast .close:hover {
          opacity: 1;
        }
        @keyframes slideIn {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
          from { transform: translateX(0); opacity: 1; }
          to { transform: translateX(100%); opacity: 0; }
        }
      `;
      document.head.appendChild(style);

      // Create container
      this.container = document.createElement('div');
      this.container.className = 'toast-container';
      document.body.appendChild(this.container);
    },
    show(message, type = 'error', duration = 5000) {
      if (!this.container) this.init();

      const toast = document.createElement('div');
      toast.className = `toast ${type}`;

      // Create elements programmatically for security and proper event handling
      const iconDiv = document.createElement('div');
      iconDiv.className = 'icon';
      iconDiv.innerHTML = type === 'error' ? '<i class="fas fa-exclamation-circle"></i>' : '<i class="fas fa-check-circle"></i>';

      const messageDiv = document.createElement('div');
      messageDiv.className = 'message';
      messageDiv.textContent = message; // Use textContent for security (prevents XSS from message content)

      const closeDiv = document.createElement('div');
      closeDiv.className = 'close';
      closeDiv.innerHTML = '<i class="fas fa-times"></i>';
      
      toast.appendChild(iconDiv);
      toast.appendChild(messageDiv);
      toast.appendChild(closeDiv);
      
      this.container.appendChild(toast);

      const removeToast = () => {
        toast.style.animation = 'slideOut 0.3s ease-out forwards';
        // Ensure toast is removed from DOM after animation
        toast.addEventListener('animationend', () => toast.remove(), { once: true });
        // Fallback removal if animationend doesn't fire (e.g., if display: none is set too early)
        setTimeout(() => { if (toast.parentNode) toast.remove(); }, 350);
      };

      const autoRemoveTimeout = setTimeout(removeToast, duration);

      closeDiv.addEventListener('click', () => {
        clearTimeout(autoRemoveTimeout); // Clear auto-remove if manually closed
        removeToast();
      });

      return toast;
    },
    error(message) {
      return this.show(message, 'error');
    },
    success(message) {
      return this.show(message, 'success');
    }
  };

  // Firebase availability check
  function checkFirebaseAvailability() {
    if (!window.firebase) {
      throw new Error('Firebase SDK not loaded');
    }
    if (!firebase.auth) {
      throw new Error('Firebase Auth module not loaded');
    }
    if (!firebase.firestore) {
      throw new Error('Firebase Firestore module not loaded');
    }
    console.log('Firebase services available:', {
      auth: !!firebase.auth,
      firestore: !!firebase.firestore,
      initialized: !!firebase.app()
    });
    return true;
  }

  // Error handler with retry logic
  async function retryOperation(operation, maxAttempts = 3) {
    let lastError;
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        debugLog.add(`Attempt ${attempt} of ${maxAttempts}`);
        return await operation();
      } catch (error) {
        lastError = error;
        debugLog.add(`Attempt ${attempt} failed`, { error: error.message });
        if (attempt === maxAttempts) break;
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }
    throw lastError;
  }

  // Loading state management
  function setLoading(isLoading) {
    const submitBtn = registrationForm.querySelector('button[type="submit"]');
    submitBtn.disabled = isLoading;
    submitBtn.innerHTML = isLoading ?
      '<i class="fas fa-spinner fa-spin"></i> Registering...' :
      'Register & Verify Email';
    Array.from(registrationForm.elements).forEach(el => el.disabled = isLoading);
  }

  // Form submission with rate limiting
  let lastSubmitTime = 0;
  const SUBMIT_COOLDOWN = 2000; // 2 seconds

  registrationForm.addEventListener('submit', async function(e) {
    e.preventDefault();

    try {
      // Rate limiting check
      const now = Date.now();
      if (now - lastSubmitTime < SUBMIT_COOLDOWN) {
        toastSystem.error('Please wait a moment before trying again');
        return;
      }
      lastSubmitTime = now;

      // Start fresh log and set loading state
      debugLog.clear();
      debugLog.add('Form submission started');
      setLoading(true);

    // Inner 'try {' (originally at line 341) removed.
    // The following code block is now part of the outer try (started at line 327).
    // The catch (line 474) and finally (line 486) will correctly associate with that outer try.
      // Check Firebase availability first
      checkFirebaseAvailability();

      // Get and validate form values
      const email = emailInput.value.trim();
      const password = passwordInput.value;
      const confirmPassword = confirmPasswordInput.value;
      debugLog.add('Form values collected', { email });

      // Validate fields
      const isEmailValid = validateEmail(email);
      const isPasswordValid = validatePassword(password);
      const isPasswordMatch = validatePasswordMatch(password, confirmPassword);
      debugLog.add('Validation results', { isEmailValid, isPasswordValid, isPasswordMatch });

      // Show validation errors
      showError(emailError, !isEmailValid);
      showError(passwordError, !isPasswordValid);
      showError(confirmPasswordError, !isPasswordMatch);

      // If all validations pass
      if (isEmailValid && isPasswordValid && isPasswordMatch) {
        debugLog.add('Starting Firebase registration process');

        // Create user with retry logic
        debugLog.add('Starting Firebase user creation');
        const userCred = await retryOperation(async () => {
          try {
            if (!auth) {
              throw new Error('Firebase Auth is not initialized');
            }
            const cred = await auth.createUserWithEmailAndPassword(email, password);
            debugLog.add('User created in Firebase Auth', {
              uid: cred.user.uid,
              emailVerified: cred.user.emailVerified,
              email: email
            });
            return cred;
          } catch (error) {
            debugLog.add('Firebase user creation failed', {
              code: error.code,
              message: error.message,
              email: email // Log email to help debug registration issues
            });
            // Enhanced error handling for specific Firebase errors
            if (error.code === 'auth/network-request-failed') {
              throw new Error('Network connection failed. Please check your internet connection and try again.');
            }
            throw error;
          }
        });

        // Save user data with retry logic
        await retryOperation(async () => {
          await db.collection('users').doc(userCred.user.uid).set({
            email: email,
            created: firebase.firestore.FieldValue.serverTimestamp(),
            verified: false
          });
          debugLog.add('User data saved to Firestore');
        });

        // Send verification email with retry logic
        await retryOperation(async () => {
          await userCred.user.sendEmailVerification({
            url: window.location.origin + '/login.html'
          });
          debugLog.add('Verification email sent');
        });

        // Update registration steps
        document.querySelectorAll('.step').forEach((step, index) => {
          setTimeout(() => {
            step.classList.add('active');
          }, index * 500);
        });

        // Success animation sequence
        registrationForm.style.opacity = '0';
        registrationForm.style.transform = 'translateY(20px)';
        debugLog.add('Starting success animation');

        setTimeout(() => {
          registrationForm.style.display = 'none';
          successMessage.style.display = 'flex';
          
          requestAnimationFrame(() => {
            successMessage.style.opacity = '1';
            successMessage.style.transform = 'translateY(0)';
            registrationForm.reset();
            debugLog.add('Success message displayed, form reset');

            // Update success message content
            const successTitle = successMessage.querySelector('h3');
            const successText = successMessage.querySelector('p');
            successTitle.textContent = 'Registration Complete! 🎉';
            // Use textContent and DOM manipulation for security (prevents XSS from emailInput.value)
            successText.textContent = "We've sent a verification link to ";
            const emailStrong = document.createElement('strong');
            emailStrong.textContent = emailInput.value;
            successText.appendChild(emailStrong);
            successText.appendChild(document.createTextNode("."));
            successText.appendChild(document.createElement('br'));
            successText.appendChild(document.createTextNode("Please verify your email to complete the registration process."));

            // Show success notification
            toastSystem.success('Registration successful! Please check your email.');
          });

          // Setup and start redirect countdown
          const countdownEl = document.createElement('div');
          countdownEl.className = 'countdown';
          countdownEl.style.cssText = `
            margin-top: 1rem;
            font-size: 0.875rem;
            color: var(--text-secondary);
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease-out;
          `;
          successMessage.appendChild(countdownEl);

          // Fade in countdown after a brief delay
          setTimeout(() => {
            countdownEl.style.opacity = '1';
            countdownEl.style.transform = 'translateY(0)';
          }, 500);

          // Start countdown animation
          let countdown = 5;
          const updateCountdown = () => {
            if (countdown <= 0) {
              countdownEl.style.opacity = '0';
              debugLog.add('Redirecting to login');
              toastSystem.success('Redirecting to login page...');
              
              setTimeout(() => {
                window.location.href = 'login.html';
              }, 300);
            } else {
              countdownEl.innerHTML = `
                <i class="fas fa-clock"></i>
                Redirecting to login in <strong>${countdown}</strong> seconds...
              `;
              countdown--;
              setTimeout(updateCountdown, 1000);
            }
          };
          
          updateCountdown();
        }, 300);

      } else {
        debugLog.add('Validation failed - form not submitted');
        toastSystem.error('Please correct the errors in the form before submitting.');
      }

    } catch (error) {
      debugLog.add('Registration failed', {
        error: error.message,
        code: error.code || 'unknown',
        stack: error.stack
      });
      
      // Enhanced error handling with more specific messages
      let errorMessage;
      if (error.code === 'auth/network-request-failed') {
        errorMessage = 'Network error. Please check your internet connection and try again.';
        console.error('Network error during registration:', error);
      } else if (error.code === 'auth/email-already-in-use') {
        errorMessage = 'This email is already registered. Please login or use a different email.';
        console.error('Email already in use:', error);
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'Please enter a valid email address.';
        console.error('Invalid email format:', error);
      } else if (error.code === 'auth/operation-not-allowed') {
        errorMessage = 'Email/password registration is currently disabled.';
        console.error('Registration disabled:', error);
      } else {
        errorMessage = getFirebaseErrorMessage(error.code) || 'Registration failed. Please try again.';
        console.error('Registration error:', {
          error: error,
          code: error.code,
          message: error.message,
          stack: error.stack,
          logs: debugLog.getFullLog()
        });
      }
      toastSystem.error(errorMessage);
    } finally {
      // Always reset loading state
      setLoading(false);
    }

    // The transition styles are already defined at the beginning of the script (lines 4-7).
    // This block (originally lines 491-504) for adding styles with id 'register-transitions'
    // is likely redundant or could conflict with the initial, more comprehensive transition setup.
    // Removing it to avoid potential issues and simplify style management.
    // If specific transition behavior is needed here, consider class-based toggling
    // or ensuring the initial styles (lines 4-43) are comprehensive and correctly applied.
  });

  // Firebase error message mapping
  function getFirebaseErrorMessage(code) {
    const messages = {
      'auth/email-already-in-use': 'This email is already registered. Please log in or use a different email.',
      'auth/invalid-email': 'Please enter a valid email address.',
      'auth/operation-not-allowed': 'Email/password registration is not enabled.',
      'auth/weak-password': 'Please choose a stronger password (at least 8 characters).',
      'auth/network-request-failed': 'Network error. Please check your connection and try again.'
    };
    return messages[code] || 'An unexpected error occurred. Please try again.';
  }
});
