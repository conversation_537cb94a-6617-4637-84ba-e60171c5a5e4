"""
Entry Spacing Manager for Autonomous Trading
Combines ATR-based spacing with Martingale/Anti-Martingale controls
"""

import json
import time
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import MetaTrader5 as mt5
import numpy as np

logger = logging.getLogger(__name__)

class EntrySpacingManager:
    """
    Manages entry spacing using ATR-based calculations and Martingale/Anti-Martingale controls.
    """
    
    def __init__(self, config_path: str = "autonomous_config.json"):
        self.config_path = config_path
        self.config = self._load_config()
        self.trade_history = {}  # Per symbol trade history
        self.last_entry_times = {}  # Per symbol last entry time
        self.last_entry_prices = {}  # Per symbol last entry price
        self._initialize_storage()
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file."""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, "r") as f:
                    config = json.load(f)
                    
                # Ensure entry_spacing section exists with defaults
                if "entry_spacing" not in config:
                    config["entry_spacing"] = self._get_default_entry_spacing_config()
                    self._save_config(config)
                    
                return config
            else:
                # Create default config if file doesn't exist
                default_config = {
                    "symbols": ["XAUUSD"],
                    "timeframes": ["M30"],
                    "entry_spacing": self._get_default_entry_spacing_config()
                }
                self._save_config(default_config)
                return default_config
                
        except Exception as e:
            logger.error(f"Error loading config: {str(e)}")
            return {"entry_spacing": self._get_default_entry_spacing_config()}
    
    def _get_default_entry_spacing_config(self) -> Dict[str, Any]:
        """Get default entry spacing configuration."""
        return {
            "enabled": True,
            "use_atr_spacing": True,
            "atr_period": 14,
            "base_atr_multiplier": 2.0,
            "min_distance_pips": 10,
            
            "martingale_settings": {
                "type": "anti_martingale",  # "martingale" | "anti_martingale" | "disabled"
                "enabled": True,
                "max_consecutive_losses": 5,
                "spacing_multipliers": [1.0, 1.5, 2.0, 2.5, 3.0, 4.0],
                "volume_multipliers": [1.0, 0.9, 0.8, 0.7, 0.6, 0.5],
                "reset_on_win": True,
                "max_multiplier": 5.0,
                "track_by_symbol": True
            },
            
            "time_based_spacing": {
                "enabled": True,
                "min_time_between_entries": 300,  # 5 minutes
                "loss_cooldown_multiplier": 1.5,
                "max_cooldown": 3600  # 1 hour
            }
        }
    
    def _save_config(self, config: Dict[str, Any]):
        """Save configuration to file."""
        try:
            with open(self.config_path, "w") as f:
                json.dump(config, f, indent=4)
        except Exception as e:
            logger.error(f"Error saving config: {str(e)}")
    
    def _initialize_storage(self):
        """Initialize storage for trade history and tracking."""
        # Load existing trade history if available
        history_file = "trade_history_spacing.json"
        if os.path.exists(history_file):
            try:
                with open(history_file, "r") as f:
                    data = json.load(f)
                    self.trade_history = data.get("trade_history", {})
                    self.last_entry_times = data.get("last_entry_times", {})
                    self.last_entry_prices = data.get("last_entry_prices", {})
            except Exception as e:
                logger.error(f"Error loading trade history: {str(e)}")
    
    def _save_storage(self):
        """Save trade history and tracking data."""
        try:
            data = {
                "trade_history": self.trade_history,
                "last_entry_times": self.last_entry_times,
                "last_entry_prices": self.last_entry_prices,
                "last_updated": datetime.now().isoformat()
            }
            with open("trade_history_spacing.json", "w") as f:
                json.dump(data, f, indent=4)
        except Exception as e:
            logger.error(f"Error saving trade history: {str(e)}")
    
    def can_enter_trade(self, symbol: str, current_price: float, 
                       signal_type: str, timeframe: str = "H1") -> Tuple[bool, str]:
        """
        Check if entry is allowed based on spacing rules.
        
        Args:
            symbol: Trading symbol
            current_price: Current market price
            signal_type: "buy" or "sell"
            timeframe: Timeframe for ATR calculation
            
        Returns:
            Tuple[bool, str]: (can_enter, reason)
        """
        if not self.config["entry_spacing"]["enabled"]:
            return True, "Entry spacing disabled"
            
        # Check time-based spacing
        time_check, time_reason = self._check_time_spacing(symbol)
        if not time_check:
            return False, time_reason
            
        # Check price-based spacing
        price_check, price_reason = self._check_price_spacing(
            symbol, current_price, signal_type, timeframe
        )
        if not price_check:
            return False, price_reason
            
        return True, "Entry allowed"
    
    def _check_time_spacing(self, symbol: str) -> Tuple[bool, str]:
        """Check if enough time has passed since last entry."""
        if not self.config["entry_spacing"]["time_based_spacing"]["enabled"]:
            return True, "Time spacing disabled"
            
        if symbol not in self.last_entry_times:
            return True, "No previous entry recorded"
            
        current_time = time.time()
        last_entry_time = self.last_entry_times[symbol]
        time_elapsed = current_time - last_entry_time
        
        # Calculate required waiting time
        base_wait_time = self.config["entry_spacing"]["time_based_spacing"]["min_time_between_entries"]
        
        # Apply loss-based multiplier
        consecutive_losses = self._get_consecutive_losses(symbol)
        if consecutive_losses > 0:
            loss_multiplier = self.config["entry_spacing"]["time_based_spacing"]["loss_cooldown_multiplier"]
            wait_time = base_wait_time * (loss_multiplier ** consecutive_losses)
            
            # Cap at max cooldown
            max_cooldown = self.config["entry_spacing"]["time_based_spacing"]["max_cooldown"]
            wait_time = min(wait_time, max_cooldown)
        else:
            wait_time = base_wait_time
        
        if time_elapsed >= wait_time:
            return True, f"Time spacing OK: {time_elapsed:.0f}s >= {wait_time:.0f}s"
        else:
            remaining = wait_time - time_elapsed
            return False, f"Time spacing: {remaining:.0f}s remaining (losses: {consecutive_losses})"
    
    def _check_price_spacing(self, symbol: str, current_price: float, 
                           signal_type: str, timeframe: str) -> Tuple[bool, str]:
        """Check if price spacing requirements are met."""
        if symbol not in self.last_entry_prices:
            return True, "No previous entry price recorded"
            
        last_price = self.last_entry_prices[symbol]
        
        # Calculate required distance
        if self.config["entry_spacing"]["use_atr_spacing"]:
            base_distance_pips = self._calculate_atr_distance(symbol, timeframe)
        else:
            base_distance_pips = self.config["entry_spacing"]["min_distance_pips"]
        
        # Apply base ATR multiplier
        base_distance_pips *= self.config["entry_spacing"]["base_atr_multiplier"]
        
        # Apply martingale spacing multiplier
        spacing_multiplier, _ = self.get_current_multipliers(symbol)
        required_distance_pips = base_distance_pips * spacing_multiplier
        
        # Convert to price distance
        pip_size = self._get_pip_size(symbol)
        required_price_distance = required_distance_pips * pip_size
        actual_distance = abs(current_price - last_price)
        
        if actual_distance >= required_price_distance:
            return True, f"Price distance OK: {actual_distance/pip_size:.1f} pips >= {required_distance_pips:.1f} pips"
        else:
            return False, f"Insufficient price distance: {actual_distance/pip_size:.1f} pips < {required_distance_pips:.1f} pips"
    
    def _calculate_atr_distance(self, symbol: str, timeframe: str) -> float:
        """Calculate ATR-based minimum distance in pips."""
        try:
            # Get ATR period from config
            atr_period = self.config["entry_spacing"]["atr_period"]
            
            # Convert timeframe string to MT5 constant
            timeframe_mt5 = self._string_to_timeframe(timeframe)
            
            # Get historical data for ATR calculation
            rates = mt5.copy_rates_from_pos(symbol, timeframe_mt5, 0, atr_period + 10)
            
            if rates is None or len(rates) < atr_period:
                logger.warning(f"Insufficient data for ATR calculation: {symbol}")
                return self.config["entry_spacing"]["min_distance_pips"]
            
            # Calculate True Range
            high = np.array([r['high'] for r in rates])
            low = np.array([r['low'] for r in rates])
            close = np.array([r['close'] for r in rates])
            
            # True Range calculation
            tr1 = high[1:] - low[1:]
            tr2 = np.abs(high[1:] - close[:-1])
            tr3 = np.abs(low[1:] - close[:-1])
            
            true_range = np.maximum(tr1, np.maximum(tr2, tr3))
            
            # Calculate ATR (Simple Moving Average of True Range)
            atr = np.mean(true_range[-atr_period:])
            
            # Convert to pips
            pip_factor = self._get_pip_factor(symbol)
            atr_pips = atr * pip_factor
            
            logger.info(f"{symbol} ATR ({timeframe}): {atr:.6f} ({atr_pips:.1f} pips)")
            return atr_pips
            
        except Exception as e:
            logger.error(f"Error calculating ATR for {symbol}: {str(e)}")
            return self.config["entry_spacing"]["min_distance_pips"]
    
    def _string_to_timeframe(self, timeframe: str) -> int:
        """Convert timeframe string to MT5 constant."""
        timeframe_map = {
            "M1": mt5.TIMEFRAME_M1,
            "M5": mt5.TIMEFRAME_M5,
            "M15": mt5.TIMEFRAME_M15,
            "M30": mt5.TIMEFRAME_M30,
            "H1": mt5.TIMEFRAME_H1,
            "H4": mt5.TIMEFRAME_H4,
            "D1": mt5.TIMEFRAME_D1,
            "W1": mt5.TIMEFRAME_W1,
            "MN1": mt5.TIMEFRAME_MN1
        }
        return timeframe_map.get(timeframe, mt5.TIMEFRAME_H1)
    
    def _get_pip_factor(self, symbol: str) -> float:
        """Get pip factor for converting price to pips."""
        if 'JPY' in symbol:
            return 100  # JPY pairs
        else:
            return 10000  # Most other pairs
    
    def _get_pip_size(self, symbol: str) -> float:
        """Get pip size for the symbol."""
        try:
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info:
                point = symbol_info.point
                if 'JPY' in symbol:
                    return point * 100  # JPY pairs
                else:
                    return point * 10   # Most other pairs
            else:
                # Default pip sizes
                if 'JPY' in symbol:
                    return 0.01
                else:
                    return 0.0001
        except Exception as e:
            logger.error(f"Error getting pip size for {symbol}: {str(e)}")
            return 0.0001
    
    def record_trade_result(self, symbol: str, entry_price: float, 
                          result: str, profit_loss: float = 0.0):
        """
        Record trade result for martingale tracking.
        
        Args:
            symbol: Trading symbol
            entry_price: Entry price of the trade
            result: "win", "loss", "breakeven", or "pending"
            profit_loss: Profit or loss amount
        """
        if symbol not in self.trade_history:
            self.trade_history[symbol] = []
            
        trade_record = {
            "timestamp": datetime.now().isoformat(),
            "entry_price": entry_price,
            "result": result,
            "profit_loss": profit_loss
        }
        
        # Only add if it's not a pending trade or if updating existing pending trade
        if result != "pending":
            # Remove any pending trades and add the final result
            self.trade_history[symbol] = [
                t for t in self.trade_history[symbol] if t["result"] != "pending"
            ]
        
        self.trade_history[symbol].append(trade_record)
        self.last_entry_times[symbol] = time.time()
        self.last_entry_prices[symbol] = entry_price
        
        # Keep only recent history (last 100 trades per symbol)
        if len(self.trade_history[symbol]) > 100:
            self.trade_history[symbol] = self.trade_history[symbol][-100:]
        
        # Save to file
        self._save_storage()
        
        logger.info(f"Recorded trade result for {symbol}: {result}, P/L: {profit_loss}")
    
    def get_current_multipliers(self, symbol: str) -> Tuple[float, float]:
        """
        Get current spacing and volume multipliers based on loss streak.
        
        Returns:
            Tuple[float, float]: (spacing_multiplier, volume_multiplier)
        """
        if not self.config["entry_spacing"]["martingale_settings"]["enabled"]:
            return 1.0, 1.0
            
        consecutive_losses = self._get_consecutive_losses(symbol)
        martingale_config = self.config["entry_spacing"]["martingale_settings"]
        
        spacing_multipliers = martingale_config["spacing_multipliers"]
        volume_multipliers = martingale_config["volume_multipliers"]
        
        # Get multiplier index (capped at max available)
        multiplier_index = min(consecutive_losses, len(spacing_multipliers) - 1)
        
        spacing_mult = spacing_multipliers[multiplier_index]
        volume_mult = volume_multipliers[multiplier_index]
        
        # Apply max multiplier cap
        max_mult = martingale_config["max_multiplier"]
        spacing_mult = min(spacing_mult, max_mult)
        
        logger.info(f"{symbol}: {consecutive_losses} consecutive losses, "
                   f"spacing_mult={spacing_mult:.2f}, volume_mult={volume_mult:.2f}")
        
        return spacing_mult, volume_mult
    
    def _get_consecutive_losses(self, symbol: str) -> int:
        """Get the number of consecutive losses for a symbol."""
        if symbol not in self.trade_history or not self.trade_history[symbol]:
            return 0
        
        consecutive_losses = 0
        
        # Count from most recent trades backwards
        for trade in reversed(self.trade_history[symbol]):
            if trade["result"] == "loss":
                consecutive_losses += 1
            elif trade["result"] in ["win", "breakeven"]:
                break  # Stop counting at first non-loss
            # Skip "pending" trades
        
        return consecutive_losses
    
    def get_symbol_statistics(self, symbol: str) -> Dict[str, Any]:
        """Get trading statistics for a symbol."""
        if symbol not in self.trade_history:
            return {
                "total_trades": 0,
                "wins": 0,
                "losses": 0,
                "breakeven": 0,
                "win_rate": 0.0,
                "consecutive_losses": 0,
                "total_profit_loss": 0.0,
                "current_spacing_multiplier": 1.0,
                "current_volume_multiplier": 1.0
            }
        
        trades = [t for t in self.trade_history[symbol] if t["result"] != "pending"]
        
        wins = len([t for t in trades if t["result"] == "win"])
        losses = len([t for t in trades if t["result"] == "loss"])
        breakeven = len([t for t in trades if t["result"] == "breakeven"])
        total_trades = len(trades)
        
        win_rate = (wins / total_trades * 100) if total_trades > 0 else 0.0
        total_profit_loss = sum(t["profit_loss"] for t in trades)
        
        spacing_mult, volume_mult = self.get_current_multipliers(symbol)
        
        return {
            "total_trades": total_trades,
            "wins": wins,
            "losses": losses,
            "breakeven": breakeven,
            "win_rate": win_rate,
            "consecutive_losses": self._get_consecutive_losses(symbol),
            "total_profit_loss": total_profit_loss,
            "current_spacing_multiplier": spacing_mult,
            "current_volume_multiplier": volume_mult,
            "last_entry_time": self.last_entry_times.get(symbol),
            "last_entry_price": self.last_entry_prices.get(symbol)
        }
    
    def reset_symbol_history(self, symbol: str):
        """Reset trade history for a symbol."""
        if symbol in self.trade_history:
            del self.trade_history[symbol]
        if symbol in self.last_entry_times:
            del self.last_entry_times[symbol]
        if symbol in self.last_entry_prices:
            del self.last_entry_prices[symbol]
        
        self._save_storage()
        logger.info(f"Reset trade history for {symbol}")
    
    def update_config(self, config_updates: Dict[str, Any]):
        """Update entry spacing configuration."""
        if "entry_spacing" in config_updates:
            self.config["entry_spacing"].update(config_updates["entry_spacing"])
            self._save_config(self.config)
            logger.info("Updated entry spacing configuration")
